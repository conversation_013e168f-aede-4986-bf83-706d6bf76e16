import { DATA_GRID_FILTERS_SUFFIX } from "./dataGridConsts";

export const RESOURCES_PAGE_ACTIONS = {
    EDIT_RESOURCE: 'resources-page-edit',
    TOGGLE_FILTER_PANE : "resources-page-toggle-filter-pane",
    CREATE_CLIENT: 'resources-page-create-client',
    CREATE_JOB: 'resources-page-create-job',
    MANAGE_CLIENTS: 'resources-page-manage-client',
    TOGGLE_BASE_FILTER: 'resources-page-toggle-base-filter',
    CREATE_ROLE_GROUP: 'create-role-group',
    VIEW_SETTINGS: 'list-view-settings',
}

export const RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS = {
    VIEW_RESOURCE_DETAILS: 'viewResourceDetails',
    EDIT_RESOURCE: 'editResource',
    GO_TO_PROFILE: 'goToProfile'
};

export const RESOURCES_PAGE_ALIAS = 'resourcesPage';

export const RESOURCESPAGE_FILTER_ALIAS = `${RESOURCES_PAGE_ALIAS}_${DATA_GRID_FILTERS_SUFFIX}`;

export const RESOURCES_PAGE_ENABLE_MULTIPLE_SELECTION = true;

export const PAGINATION_KEY = 'paginationKey';

export const RESOURCES_PAGE_DP_ALIAS = 'resourcesPageDPAlias';

export const RESOURCES_PAGE_COMMAND_BAR_ALIAS = 'resourcesPageCommandBar';

export const DEFAULT_RESOURCES_UI_OPTIONS = {
    pageNumber: 1,
    density: 'default'
}