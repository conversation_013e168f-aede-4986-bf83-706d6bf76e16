export const DEFAULT_MAX_UPLOADS_ALLOWED = 3;
export const DEFAULT_MAX_FILE_SIZE_MB = 2;
export const DEFAULT_ALLOWED_FILE_TYPES = '.pdf,.doc,.docx';
export const DEFAULT_FILE_EXPIRY_DAYS = 60;
export const DEFAULT_MAX_UPLOAD_ALLOWED_WITH_TALENT_PROFILE_PAGE_FEATURE_FLAG_ENABLED = 10;
export const ATTACHMENT_TRUNCATION_LENGTH = 15;

export const formattedAcceptedFileTypes = DEFAULT_ALLOWED_FILE_TYPES.replace(/\./g, ' ');

export const ATTACHMENT_LABELS = {
    UPLOAD_BUTTON: 'Upload documents',
    DELETE_ATTACHMENT_BUTTON: 'Delete',
    DOWNLOAD_ATTACHMENT_BUTTON: 'Download'
};

export const ATTACHMENT_MESSAGES = {
    FILE_TOO_LARGE: 'Document upload failed: File is too large',
    FILE_TYPE_NOT_ALLOWED: 'Document upload failed: File type is not allowed',
    NO_FILES_UPLOADED: 'You don\'t have any documents uploaded',
    UPLOADS_LIMIT_REACHED: 'Document limit reached: delete documents to upload more',

    // Actual ALLOWED_FORMATS, MAX_FILE_SIZE and MAX_UPLOAD_ALLOWED will be implemented in the licensing PBI
    ALLOWED_FORMATS: `Documents can be in the following formats:${formattedAcceptedFileTypes}`,
    MAX_FILE_SIZE: `There is a maximum file size of ${DEFAULT_MAX_FILE_SIZE_MB}MB for each document`,
    MAX_UPLOADS_ALLOWED: `You can upload a maximum of ${DEFAULT_MAX_UPLOADS_ALLOWED} documents`,
    MAX_UPLOAD_ALLOWED_NEW: `You can have a maximum of ${DEFAULT_MAX_UPLOAD_ALLOWED_WITH_TALENT_PROFILE_PAGE_FEATURE_FLAG_ENABLED} files on your profile`,
    CHANGE_TYPE: (changeTo) => `Move to '${changeTo}'`,
    NO_ATTACHMENT_UPLOADED: 'No documents uploaded'
};

export const attachmentTypes = {
    CERTIFICATION: 'Certification',
    OTHER: 'Other'
};

export const attachmentExpiryStatus = {
    EXPIRED: 'Expired',
    EXPIRING_SOON: 'Expiring soon'
};
