import { ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE } from "./jobsPageConsts";

export const breadcrumbLevelsMap = { 
    rolegroupListPage: ['jobName'],
    rolegroupDetailsPage: ['jobName', 'rolegroupName']
};

export const subPagesOptionsMap = {
    jobs: {
        navLinks:[
            'rolegrouplist',
            'rolegroupdetails'
        ],
        aliases:[
            ROLE_GROUP_LIST_PAGE,
            ROLE_GROUP_DETAILS_PAGE
        ]
    },
    lists: {
        navLinks:[
            'rolegrouplist',
            'rolegroupdetails'
        ],
        aliases:[
            ROLE_GROUP_LIST_PAGE,
            ROLE_GROUP_DETAILS_PAGE
        ]
    }
};
