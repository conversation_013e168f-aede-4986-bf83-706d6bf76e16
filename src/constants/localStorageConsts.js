import { CURRENT_WORKSPACE_VERSION } from '../state/tableViewWorkspace';

export const USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY = 'unassigned-roles-hidden-by-default';
export const TABLE_VIEW_WORKSPACE_LOCAL_STORAGE_KEY = `tableViewWorkspacesSettingsReducer_${CURRENT_WORKSPACE_VERSION}`;
export const JOBS_PAGE_PERSIST_LOCAL_STORAGE_KEY = 'jobsPagePersist';
export const DETAILS_PANE_LOCAL_STORAGE_KEY = 'detailsPane';
export const PERSIST_DATA_LOCAL_STORAGE_KEY = 'persistData';
export const LAST_PATH_BEFORE_REFRESH = 'lastPathBeforeRefresh';
export const SUGGESTION_PANE_INFO_BANNER_DISMISSED_KEY = 'suggestedPaneInfoBannerDismissed';
export const CELL_HOURS_VALIDATE_TOOLTIP_LOCAL_STORAGE_KEY = 'cellHoursValidateTooltip';
export const USER_AI_SUGGESTIONS_ENABLED_LOCAL_STORAGE_KEY = 'userAISuggestionsEnabled';
export const USER_AI_SUGGESTIONS_SORT_TYPE_LOCAL_STORAGE_KEY = 'userAISuggestionsSortType';
export const AI_TOGGLE_TOOLTIP_LOCAL_STORAGE_KEY = 'aiToggleTooltipDisplayed';