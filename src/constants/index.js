import {
    PLANNER_LAYER_TYPES, PLANNER_VIEW_MODES, PLANNER_VIEW_MODE_OPTIONS, DATE_FORMATS, DATE_PICKER_RANGE_INFO,
    RECORDS_LIST_PAGE_SIZE, CONTEXT_MENU_BOOKING_ACTION_TYPES, CONTEXT_MENU_SELECTION_ACTION_TYPES,
    CONTEXT_MENU_MR_SELECTION_ACTION_TYPES, CONTEXT_MENU_CELL_ACTION_TYPES, PLANNER_ACTORS, CONTEXT_MENU_ACTIONS,
    RECORDS_LIST_RES_VIEW_ROW_DEFAULT_EXPANDED, RECORDS_LIST_JOB_VIEW_ROW_DEFAULT_EXPANDED, RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES, PLANNER_ROWS_DENSITY_KEYS, FILTER_PANE_ACTORS, UNASSIGNED_BOOKINGS_RESOURCE, PLANNER_SELECTION_TYPE, DAY_OPTIONS_KEYS, WEEK_OPTIONS_KEYS,
    MONTH_OPTIONS_KEYS, YEAR_OPTIONS_KEYS, PLANNER_PAGE_ALIAS, PLANNER_TABLE_DATAS_SUFFIX, UNASSIGNED_ROLE_GROUP_NAME
} from './plannerConsts';
import {
    TableAccessUrl, PagingTableAccessUrl, PagingKeepAliveUrl, FilteredTableAccessUrl, NamedTableAccessUrl, ValidateTableAccessUrl, ValidateRichFieldsTableAccessUrl,
    IDTableAccessUrl, DBStructureUrl, DBStructureRichUrl, DBSctuctureFieldNamesUrl,
    DBSctuctureFieldNamesRichUrl, AdminSettingMenuOptionUrl, AdminSettingGetSettingValueUrl, AdminSettingUpdateSettingValueUrl, AdminSettingGetOptionURL,
    SECTION_CREATE_URL, SECTION_DUPLICATE_URL, SECTION_RENAME_URL, SECTION_DELETE_URL, LOCALE_EN,
    DAY_TYPES_CREATE, DAY_TYPES_DELETE, WORK_PATTERN_CREATE, WORK_PATTERN_DELETE, ADMIN_SETTING_BASE_URL_API, DIARY_CALENDAR, CHARGE_CODE_URL, CURRENCY_API_URL,
    CHARGE_RATE_URL, FILTER_TYPES, OPERATORS, COLOURSCHEME_TABLEFIELD_URL, GET_ALL_COLOUR_SCHEME, GET_BY_ID_COLOUR_SCHEME, UPDATE_COLOUR_SCHEME, ENTITY_CONFIGURATION_URL, FIELD_LOOKUP_VALUES_URL, FIELD_PROPERTY_URL,
    FIELD_PROPERTY_BYID_URL, SkillSectionsUrl, SkillCategoriesUrl, SkillsUrl, FilterSkillsUrl, SkillByIdUrl, KEY_CODES, WORKSPACES_URL, WORKSPACES_MOST_RECENTLY_USED_URL, IDWorkspaceUrl,
    IDWorkspacesUrl, IDMostRecentlyUsedUrl, USER_MANAGEMENT, ClaimsUrl, PermissionsUrl, CONFLICTS_API_URL, SkillSectionByIdUrl, ResourceSkillsByIdUrl, ResourceSkillsRecommendationByIdUrl, ResourceSkillsApprovalsPendingByIdUrl, ResourceSkillsApprovalsHistoryByIdUrl, SkillPreferenceUrl,
    PRE_VALIDATE_IMPORT, GET_ENTITY_TEMPLATE, UPLOAD_ENTITY_IMPORT, FilteredResourceSkillsUrl, WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS, ENTITY_IMPORT, DiaryUrl,
    TABLE_NAMES, REPORT_BASE_URL, GET_REPORTS_DATA_URL, CREATE_REPORT_URL, SAVE_AS_REPORT_URL, DELETE_REPORT_URL, UPDATE_REPORT_URL, NOTIFY_REPORT_EVENT_URL, JOIN_TYPES, FILTER_FIELD_NAMES,
    USER_SECURITYPROFILE_OPTION_URL, COMPANY_INFORMATION_API_URL, IMAGE_EXTENTIONS, SECURITY_PROFILES_URL, SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL, ENTITY_ACCESS_RULES_URL,
    FUNCTIONAL_ACCESS_RULES_URL, FunctionalAccessConsumerApi, AVATARS_URL, IDAvatarsUrl, ENTITY_CONFIGURATION_CONSUMER_URL, StandardTaxonomiesUrl,
    MAX_INPUT_DIGITS_COUNT, LICENSE_INFO_BY_KEY, LICENSE_INFO, SKILL_ENTITY_DETAILS_URL, SkillStructureUrl, NamedTableAccessBatchUrl, GET_LICENSE_INFO, DEFAULT_BASE_FILTER_OPTION, WORKSPACE_STRUCTURE_CONSTANTS, GET_REPORT_SETTINGS_DATA, UPDATE_REPORT_SETTINGS_DATA, REPORT_SETTINGS_URL,
    TIMESHEETS_BASE_URL, TIMESHEET_UPSERT_URL, TIMESHEET_DELETE_URL, DATE_UNITS, SkillEntityTypesUrl, AllSkillCategoriesUrl, AllSkillSubCategoriesUrl, FetchSkillsUrl, DEPARTMENT_API_URL, DIVISION_API_URL, SKILL_CERTIFICATION, getLabelDescription,
    MAX_SELECTED_CRITERIA_FIELDS, MAX_LABEL_LENGTH, REPORT_SERVICE_URL, DATASET_REFRESH_URL, DATASET_REFRESH_HISTORY_URL
} from './globalConsts';
import { PAGINATOR_KEY_NOT_FOUND, ERROR_STATUS, SUCCESS_STATUS, OBJECT_KEY_NOT_SET_TO_INSTANCE_ERR, BAD_REQUEST_STATUS, NOT_FOUND_STATUS, DIARY_IN_USE, ACTIVE_DIARY_TYPE, ACTIVE_FIELD_TYPE } from './apiConsts';
import { FIELD_DATA_TYPES, FIELD_DATA_CATEGORIES } from './fieldConsts';
import * as adminSettingConsts from './adminSettingConsts';
import { IMAGES, ICONS } from './assetsConsts';
import { SORT_ASCENDING, SORT_DESCENDING, SORT_ASC, SORT_DESC, SORT_DEFAULT } from './selectionConsts';
import { GREY1, GREY3, GREY4, PRIMARY } from './colorsConsts';
import { ENTITY_WINDOW_MODULES, DETAILS_PANE_TAB_KEYS } from './entityWindowConsts';
import { PAGE_NAMES } from './pageConsts';
import { HOT_KEYS_HELP_WINDOW_SECTIONS, HOT_KEYS_STRATEGY_NAMES } from './hotKeysConsts';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, JOB_DETAILS_PANE_TAB_KEYS, ROLE_GROUP_DETAILS_PAGE_TAB_KEYS, ROLE_GROUP_LIST_PAGE } from './jobsPageConsts';
import { ROLE_INBOX_PAGE_ALIAS } from './roleInboxPageConsts';
import { ROLE_REQUEST_FORM, ROLE_DETAILS_PANE, ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS } from './roleGroupDetailsPageConsts';
import { DATA_GRID_DENSITY_KEYS } from './dataGridConsts';
import { COMMAND_BAR_MENUS_SECTION_KEYS } from './commandBarConsts';

export {
    PLANNER_LAYER_TYPES,
    PLANNER_VIEW_MODES,
    PLANNER_VIEW_MODE_OPTIONS,
    DETAILS_PANE_TAB_KEYS,
    JOB_DETAILS_PANE_TAB_KEYS,
    PLANNER_ACTORS,
    DATE_UNITS,
    DATE_FORMATS,
    TableAccessUrl,
    PagingTableAccessUrl,
    PagingKeepAliveUrl,
    FilteredTableAccessUrl,
    NamedTableAccessUrl,
    ValidateTableAccessUrl,
    ValidateRichFieldsTableAccessUrl,
    IDTableAccessUrl,
    RECORDS_LIST_PAGE_SIZE,
    PAGINATOR_KEY_NOT_FOUND,
    OBJECT_KEY_NOT_SET_TO_INSTANCE_ERR,
    ERROR_STATUS,
    DBStructureUrl,
    DBStructureRichUrl,
    DBSctuctureFieldNamesUrl,
    DBSctuctureFieldNamesRichUrl,
    ClaimsUrl,
    PermissionsUrl,
    FIELD_DATA_TYPES,
    FIELD_DATA_CATEGORIES,
    CONTEXT_MENU_BOOKING_ACTION_TYPES,
    FILTER_PANE_ACTORS,
    CONTEXT_MENU_SELECTION_ACTION_TYPES,
    CONTEXT_MENU_MR_SELECTION_ACTION_TYPES,
    CONTEXT_MENU_CELL_ACTION_TYPES,
    CONTEXT_MENU_ACTIONS,
    PLANNER_ROWS_DENSITY_KEYS,
    adminSettingConsts,
    AdminSettingMenuOptionUrl,
    AdminSettingGetSettingValueUrl,
    AdminSettingUpdateSettingValueUrl,
    IMAGES, ICONS,
    AdminSettingGetOptionURL,
    SECTION_CREATE_URL,
    SECTION_DUPLICATE_URL,
    SECTION_RENAME_URL,
    SECTION_DELETE_URL,
    SUCCESS_STATUS,
    RECORDS_LIST_RES_VIEW_ROW_DEFAULT_EXPANDED,
    RECORDS_LIST_JOB_VIEW_ROW_DEFAULT_EXPANDED,
    RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES,
    LOCALE_EN,
    SORT_ASCENDING,
    SORT_DESCENDING,
    SORT_ASC,
    SORT_DESC,
    SORT_DEFAULT,
    GREY1,
    GREY3,
    GREY4,
    PRIMARY,
    DAY_TYPES_CREATE,
    DAY_TYPES_DELETE,
    WORK_PATTERN_CREATE,
    WORK_PATTERN_DELETE,
    ADMIN_SETTING_BASE_URL_API,
    CHARGE_RATE_URL,
    DIARY_CALENDAR,
    CHARGE_CODE_URL,
    CURRENCY_API_URL,
    ENTITY_WINDOW_MODULES,
    ENTITY_CONFIGURATION_URL,
    FIELD_LOOKUP_VALUES_URL,
    FILTER_TYPES,
    OPERATORS,
    COLOURSCHEME_TABLEFIELD_URL,
    GET_ALL_COLOUR_SCHEME,
    GET_BY_ID_COLOUR_SCHEME,
    UPDATE_COLOUR_SCHEME,
    FIELD_PROPERTY_URL,
    FIELD_PROPERTY_BYID_URL,
    SkillStructureUrl,
    SkillSectionsUrl,
    SkillCategoriesUrl,
    SkillEntityTypesUrl,
    AllSkillCategoriesUrl,
    AllSkillSubCategoriesUrl,
    SkillsUrl,
    FilterSkillsUrl,
    SkillSectionByIdUrl,
    SkillByIdUrl,
    ResourceSkillsByIdUrl,
    ResourceSkillsRecommendationByIdUrl,
    ResourceSkillsApprovalsPendingByIdUrl,
    ResourceSkillsApprovalsHistoryByIdUrl,
    SkillPreferenceUrl,
    FilteredResourceSkillsUrl,
    KEY_CODES,
    WORKSPACES_URL,
    WORKSPACES_MOST_RECENTLY_USED_URL,
    IDWorkspaceUrl,
    IDWorkspacesUrl,
    IDMostRecentlyUsedUrl,
    CONFLICTS_API_URL,
    USER_MANAGEMENT,
    UNASSIGNED_BOOKINGS_RESOURCE,
    UNASSIGNED_ROLE_GROUP_NAME,
    TABLE_NAMES,
    WORKSPACE_ACCESS_TYPES,
    WORKSPACE_EDIT_RIGHTS,
    ENTITY_IMPORT,
    PRE_VALIDATE_IMPORT,
    GET_ENTITY_TEMPLATE,
    UPLOAD_ENTITY_IMPORT,
    DiaryUrl,
    PLANNER_SELECTION_TYPE,
    REPORT_BASE_URL,
    GET_REPORTS_DATA_URL,
    CREATE_REPORT_URL,
    SAVE_AS_REPORT_URL,
    DELETE_REPORT_URL,
    UPDATE_REPORT_URL,
    NOTIFY_REPORT_EVENT_URL,
    PAGE_NAMES,
    JOIN_TYPES,
    HOT_KEYS_HELP_WINDOW_SECTIONS,
    HOT_KEYS_STRATEGY_NAMES,
    DAY_OPTIONS_KEYS,
    WEEK_OPTIONS_KEYS,
    MONTH_OPTIONS_KEYS,
    YEAR_OPTIONS_KEYS,
    JOBS_PAGE_ALIAS,
    DATA_GRID_DENSITY_KEYS,
    COMMAND_BAR_MENUS_SECTION_KEYS,
    FILTER_FIELD_NAMES,
    USER_SECURITYPROFILE_OPTION_URL,
    COMPANY_INFORMATION_API_URL,
    IMAGE_EXTENTIONS,
    SECURITY_PROFILES_URL,
    SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL,
    ENTITY_ACCESS_RULES_URL,
    FUNCTIONAL_ACCESS_RULES_URL,
    FunctionalAccessConsumerApi,
    AVATARS_URL,
    IDAvatarsUrl,
    ENTITY_CONFIGURATION_CONSUMER_URL,
    MAX_INPUT_DIGITS_COUNT,
    PLANNER_PAGE_ALIAS,
    PLANNER_TABLE_DATAS_SUFFIX,
    LICENSE_INFO_BY_KEY,
    LICENSE_INFO,
    SKILL_ENTITY_DETAILS_URL,
    NamedTableAccessBatchUrl,
    BAD_REQUEST_STATUS,
    NOT_FOUND_STATUS,
    GET_LICENSE_INFO,
    ROLE_GROUP_DETAILS_PAGE_TAB_KEYS,
    ROLE_REQUEST_FORM,
    ROLE_DETAILS_PANE,
    ROLE_GROUP_DETAILS_PAGE,
    ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS,
    ROLE_GROUP_LIST_PAGE,
    DEFAULT_BASE_FILTER_OPTION,
    WORKSPACE_STRUCTURE_CONSTANTS,
    ACTIVE_DIARY_TYPE,
    DIARY_IN_USE,
    ACTIVE_FIELD_TYPE,
    GET_REPORT_SETTINGS_DATA,
    UPDATE_REPORT_SETTINGS_DATA,
    REPORT_SETTINGS_URL,
    TIMESHEETS_BASE_URL,
    TIMESHEET_UPSERT_URL,
    TIMESHEET_DELETE_URL,
    ROLE_INBOX_PAGE_ALIAS,
    DATE_PICKER_RANGE_INFO,
    FetchSkillsUrl,
    DEPARTMENT_API_URL,
    DIVISION_API_URL,
    SKILL_CERTIFICATION,
    getLabelDescription,
    StandardTaxonomiesUrl,
    MAX_SELECTED_CRITERIA_FIELDS,
    MAX_LABEL_LENGTH,
    REPORT_SERVICE_URL,
    DATASET_REFRESH_URL,
    DATASET_REFRESH_HISTORY_URL

};
