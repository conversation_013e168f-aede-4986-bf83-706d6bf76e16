import { CHARGE_RATE_CONTEXT_MENU, DIARY_CONTEXT_MENU } from '../actions/actionTypes';
import { BOOLEAN_FIELD_DB_VALUES, RESOURCE_ISSERVICEACCOUNT } from './fieldConsts';
import { OPERATORS, TABLE_NAMES } from './globalConsts';

const BASE_SUBNAVIGATION_URL = '/admin-settings';

const AliasConstants = {
    SUBNAVIGATION: 'subnavigation',
    PAGENAMES: 'pagenames',
    FUNCTIONALACCESS: 'functionalaccess',
    ADMINCOMMANDBARBUTTON: 'admincommandbarbutton',
    DATABASETABLES: 'databasetables',
    DAY_TYPE: 'daytypes',
    WORK_PATTERN: 'workpatters',
    COLOURSCHEME:'colourscheme',
    ADMINCOMMANDBAR:'adminCommandBar',
    CONFLICTS : 'Conflicts',
    USERMANAGMENT: 'UM_ALIAS',
    ADMIN_NOTIFICATIONS_SETTINGS: 'adminNotificationSettings'
};

const AREA_CONSTANTS = {
    SYSTEM_SETTINGS: 'System Settings',
    SECURITY_PROFILES: 'Security Profiles',
    PLANS:'Plans'
};

const SECTION_CONSTANTS = {
    PAGENAMES: 'Page Names',
    DATABASE_TABLES: 'Database Tables',
    USER_ROLES: 'User Roles',
    FUNCTIONAL_ACCESS: 'Functional Access',
    DAY_TYPES: 'Day types',
    DAYS:'Days',
    WORK_PATTERNS: 'Work patterns',
    CHARGE_RATES:'Charge rates',
    DIARY: 'Diary',
    CHARGE_CODE: 'Charge Codes',
    CHARGE_TYPES: 'Charge types',
    CURRENCY : 'Currencies',
    COLOURSCHEME:'Colour theme',
    SKILLS_CONFIGURATION: 'Skills',
    SECURITY_PROFILES_CONFIGURATION: 'Security profiles', // need to check of needs to be deleted
    FIELD_CONFIGURATION : 'Fields',
    ENTITY_CONFIGURATION : 'Entity Configuration',
    FIELD_LOOKUP_VALUES : 'Values',
    CONFLICTS : 'conflicts',
    USER_MANAGEMENT: 'User management',
    BOOKING: 'Booking',
    JOB:'Job',
    RESOURCE:'Resource',
    CLIENT:'Client',
    ROLE_REQUEST:'Role',
    ROLE_REQUEST_GROUP:'Scenario',
    DEPARTMENT: 'Department',
    DIVISION: 'Division',
    ENTITY_IMPORT:'Import Data',
    COMPANY_INFORMATION:'Company Information',
    PROCESS_FORM: 'Process Form',
    UPLOAD_FORM: 'Upload Form',
    SECURITY_PROFILES: 'Security profiles',
    REPORTING: 'Reporting',
    REPORT_SETTINGS_CONFIGURATION: 'Report Settings Configuration',
    ROLES_BY_NAME_WORKFLOW: 'Roles by name',
    ROLES_BY_REQUIREMENTS_WORKFLOW: 'Roles by requirement',
    NOTIFICATIONS_SETTINGS: 'Notifications',
    SERVICE_ACCOUNT_MANAGEMENT: 'Service account management',
    MASS_DUPLICATE_JOBS: 'Duplicate Data',
    DUPLICATE_JOBS: 'Duplicate Jobs',
    SKILL:'Skill'
};

const ENTITIES_WITH_TARGET_BILLABILITY = [
    SECTION_CONSTANTS.DEPARTMENT,
    SECTION_CONSTANTS.DIVISION
];

// Collection under Diaries API
const DIARIES_COLLECTION = {
    [SECTION_CONSTANTS.DAY_TYPES]: 'DayType',
    [SECTION_CONSTANTS.WORK_PATTERNS]: 'WorkPattern',
    [`${SECTION_CONSTANTS.WORK_PATTERNS}_UPDATE`]: 'WorkPattern/BatchUpdate',
    [SECTION_CONSTANTS.DIARY]: 'Calendar',
    [`${SECTION_CONSTANTS.DIARY}_UPDATE`]: 'Calendar/BatchUpdate',
    [SECTION_CONSTANTS.DAYS]: 'Days'
};

const CONTEXT_MENU_ACTIONS = {
    CREATE: 'Create',
    RENAME: 'Rename',
    DUPLICATE: 'Duplicate',
    DELETE: 'Delete',
    DUPLICATE_USING_API: 'DUPLICATE_USING_API'
};

const DIARY_CONSTANTS = {
    TotalHourSumErrorMessage: 'Total hours must be less than or equal to 24 hours',
    HoursAginstDurationError: 'Start and end time must be inclusive of the number of hours assigned',
    SuccessStatus: 'success',
    ErrorStatus: 'error',
    CustomDayIsInUseMessage:'This overlaps with an existing custom day',
    CustomPeriodIsInUseMessage:'This overlaps with an existing custom period',
    CustomGridDateRequiredMessage: 'Please provide date',
    CustomGridRangeRequiredMessage: 'Please provide date range',
    CustomGridNameRequiredMessage: 'Please provide name',
    CustomGridDayTypesRequiredMessage: 'Please provide day type',
    CustomGridWorkPatternsRequiredMessage: 'Please provide work pattern',
    editDiary:'editDiary',
    titleFieldName: 'name',
    gridType: 'CONTEXTUAL_EDIT',
    sortAscending: 'ascend',
    sortDescending: 'descend',
    CustomDayNamePlaceholder: 'Name of custom day',
    CustomPeriodNamePlaceholder: 'Name of custom period',
    CustomDayTypePlaceholder: 'Day type of custom day',
    CustomWorkPatternPlaceholder: 'Work pattern of custom period'
};

const ENTITY_CONFIGURATION_CONSTANTS = {
    singularAlias: 'singularAlias',
    pluralAlias: 'pluralAlias',
    validationMessage: 'Please specify both Singular and Plural versions of the Alias.'
};

const CHARGE_RATE_CONSTANTS = {
    editName: 'editName',
    editDescription: 'editDescription',
    noDescriptionMsg: 'Click here to add a description',
    descriptionFieldName: 'chargeRateDescription',
    titleFieldName: 'chargeRateName'
};

const COMMANDBAR_CONSTANTS = {
    PAGE_TITLE : 'PAGE_TITLE',
    CONTEXTUAL_EDIT : 'CONTEXTUAL_EDIT',
    ACTION_BUTTON : 'ACTION_BUTTON',
    ACTION_BUTTON_DROPDOWN: 'ACTION_BUTTON_DROPDOWN',
    BULK_EDIT_USER_ACTION_MENU: 'BULK_EDIT_USER_ACTION_MENU'
};

const DAY_TYPES_CONSTANTS = {
    editDayType:'editDayType',
    titleFieldName: 'name'
};

const WORK_PATTERN_CONSTANTS = {
    editWorkPattern:'editWorkPattern',
    titleFieldName: 'name'
};

const SECURITY_PROFILES_CONSTANTS = {
    EDIT_SECURITY_PROFILE_NAME: 'editSecurityProfileName',
    SECURITY_PROFILE_NAME: 'securityProfileName',
    GENERAL:'General',
    READ_ONLY:'Read only',
    EDITABLE: 'Editable',
    BUILT_IN_READ_ONLY:'builtin-system-readonly',
    BUILT_IN_CALCULATED: 'builtin-calculated',
    BUILT_IN_SYSTEM_REQUIRED:'builtin-system-required',
    SYSTEM_MANDATORY:'system-mandatory',
    TABLE_DATAS_ALIAS: 'SECURITY_PROFILES_TABLE_DATAS_ALIAS',
    AUTOCOMPLETE_ALIAS: 'SECURITY_PROFILES_AUTOCOMPLETE_ALIAS'
};

const VALUES_CONSTANTS = {
    editValues:'editValues',
    titleFieldName: 'name',
    FIELD_LOOKUP_CATEGORY: {
        SYSTEM:'system',
        CUSTOM:'custom'
    }
};

const COLOUR_SCHEMES_CONSTANTS = {
    EDIT_COLOUR_SCHEME_NAME: 'editColourScheme',
    TITLE_COLOUR_SCHEME_NAME: 'colourSchemeName',
    COLOUR_SCHEME_BOOKING_TAB: 'Bookings',
    COLOUR_SCHEME_ROLE_TAB: 'Roles',
    BOOKING_SELECTED_FIELD: 'bookingSelectedField',
    BOOKING_SELECTED_TABLE: 'bookingSelectedTable',
    ROLES_SELECTED_FIELD: 'rolesSelectedField',
    ROLES_SELECTED_TABLE: 'rolesSelectedTable'
};

const EMAIL_OPERATIONS = {
    USER_ACTIVATION: 'USER_ACTIVATION',
    RESET_PASSPHRASE: 'RESET_PASSPHRASE'
};

const FILE_TYPE = {
    EXCEL:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;',
    PNG:'image/png',
    SVG:'image/svg+xml',
    PDF:'application/pdf;charset=utf-8'
};

const API_OPERATIONS = {
    DELETE:'Delete',
    UPDATE:'Update',
    ADD:'Add'
};

const USER_STATUS = {
    ACTIVE: 'Active',
    INACTIVE: 'Inactive'
};

const ITEM_LIST_VIEWS = {
    SKILLS: 'SKILLS',
    PROFILES: 'PROFILES'
};

const DATE_TIME_CONSTANTS = {
    ZERO_HHMM: '00:00',
    ZERO_HHMMSS: '00:00:00'
};

const REUSABLE_GRID_CONSTANTS = {
    DIRECT_EDIT: 'DIRECT_EDIT',
    DELETE_OP: 'Delete',
    UPDATE_OP: 'Update',
    formItemLayout: { labelCol: { span: 20 }, wrapperCol: { span: 20 } },
    rowFormatStyles:{
        'compact': 'dataGridCompactRow',
        'default': 'dataGridDefaultRow',
        'expanded': 'dataGridExpandedRow'
    }
};

const STYLE_CONSTANTS = {
    SIDER : {
        WIDTH: '230px'
    }
};

const SKILLS_CONFIGURATION_CONSTANTS = {
    FETCH_SKILLS_CONFGIGURATION_SECTIONS: 'FETCH_SKILLS_CONFGIGURATION_SECTIONS',
    FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID: 'FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID',
    UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS: 'UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS',
    EDIT_SKILL_CATEGORY_DETAILS:'EDIT_SKILL_CATEGORY_DETAILS',
    REMOVE_SKILLS_ROW: 'REMOVE_SKILLS_ROW',
    CANCEL_REMOVE_SKILLS_ROW: 'CANCEL_REMOVE_SKILLS_ROW',
    EDITED_SKILL_INFO: 'EDITED_SKILL_INFO',
    SET_SKILLS_TABLE_ROW_SORT_ORDER: 'SET_SKILLS_TABLE_ROW_SORT_ORDER',
    CANCEL_DELETE_LABEL: 'Cancel deletion',
    MARK_FOR_DELETION_MESSAGE: ' Marked for deletion. Will be deleted when you confirm changes',
    ON_EXPAND_KEY_SET: 'ON_EXPAND_KEY_SET',
    SET_TYPE_FILTERS:'SET_TYPE_FILTERS',
    CLEAR_TYPE_FILTERS:'CLEAR_TYPE_FILTERS',
    titleFieldName: 'skillTitleName',
    editTitleName: 'editTitleName',
    LEVELS_DROPDOWN_OPTIONS: {
        NO_LEVELS:'No levels',
        CUSTOM_LEVELS:'Custom levels',
        USE_LEVELS_FROM:'Use levels from'
    },
    SKILLS:'Skills',
    SKILL_LEVELS:'SkillLevels',
    SKILL_FIELDS:'SkillFields',
    SKILLS_AND_LEVELS:'SkillsAndLevels',
    LEVELS_AND_FIELDS:'LevelsAndFields',
    SKILLS_AND_FIELDS:'SkillsAndFields',
    SKILLS_LEVELS_AND_FIELDS:'SkillsLevelsAndFields',
    POPUP_HEADING:'PopupHeading',
    POPUP_WARNING:'PopupWarningMessage',
    DELETE_ITEMS_LABEL:'deleteItemsButtonLabel',
    KEEP_ITEMS_LABEL:'keepItemsButtonLabel',
    DELETE_CONST:'delete',
    DELETE_MODIFY_CONST:'deleteAndModify',
    KEEP_CONST:'keep',
    BUTTON_LABEL:'ButtonLabel'
};

const SECTION_INTERNAL_NAME_CONSTANTS = {
    PAGENAMES: 'Page Names',
    DAY_TYPES: 'daytypes',
    WORK_PATTERNS: 'workpatterns',
    CHARGE_RATES:'chargerates',
    DIARY: 'diary',
    CHARGE_CODE: 'chargecodes',
    CHARGE_TYPES: 'chargetypes',
    CURRENCY : 'currency',
    COLOURSCHEME:'Colour Scheme',
    SKILLS_CONFIGURATION: 'skills',
    SECURITY_PROFILES_CONFIGURATION: 'securityprofiles',
    FIELD_CONFIGURATION : 'Fields',
    ENTITY_CONFIGURATION : 'Entity Configuration',
    FIELD_LOOKUP_VALUES : 'Values',
    CONFLICTS : 'Conflicts',
    USER_MANAGEMENT: 'usermanagement',
    BOOKING: 'bookings',
    JOB:'jobs',
    RESOURCE:'resources',
    CLIENT:'clients',
    ROLE_REQUEST:'rolerequest',
    ROLE_REQUEST_GROUP:'rolerequestgroup',
    ENTITY_IMPORT:'importdata',
    COMPANY_INFORMATION:'companyinformation',
    SECURITY_PROFILES: 'securityprofiles',
    DUPLICATE_JOBS: 'Duplicate Jobs'
};

const FORMATTING_FIELDS_CONSTANTS = {
    NO_FORMATTING: 'No formatting',
    CURRENCY: 'Currency',
    PERCENTAGE: 'Percentage',
    CURRENCY_PER_HOUR: 'Currency per hour',
    HOURS: 'Hours',
    DAYS: 'Days',
    FTE: 'FTE',
    HOURS_PER_DAY: 'Hours per day'
};

const FIELD_SUFFIXES_BY_FORMAT_TYPE = {
    [FORMATTING_FIELDS_CONSTANTS.CURRENCY_PER_HOUR]: '/h',
    [FORMATTING_FIELDS_CONSTANTS.PERCENTAGE]: '%',
    [FORMATTING_FIELDS_CONSTANTS.HOURS]: 'h',
    [FORMATTING_FIELDS_CONSTANTS.HOURS_PER_DAY]: 'h/d',
    [FORMATTING_FIELDS_CONSTANTS.DAYS]: 'd',
    [FORMATTING_FIELDS_CONSTANTS.FTE]: 'fte'
};

const DEFAULT_EXAMPLE_NUMBER = '9';

const BUILD_IN_ALIAS = 'BuiltIn';

const FIELD_VALUES_SUFFIX_CONSTANTS = {
    MAXIMUM: 'maximum',
    MINIMUM: 'minimum',
    DESCRIPTION: 'description',
    LABEL: 'label',
    LENGTH: 'length',
    IS_MANDATORY: 'isMandatory',
    HIDE_IN_UI: 'hideInUI',
    DECIMAL_PLACE: 'decimalPlace',
    FIELD_FORMAT_GUID: 'fieldFormatGuid'
};

const ADMIN_PAGE_CREATE_SECTION_KEYS_CONSTANTS = {
    ADD_NEW_ADMIN_SETTING_SECTION_ITEM: 'n'
};

const ADMIN_SETTINGS_ALIAS = 'adminSettings';
const ADMIN_SETTING_ALIAS = 'adminSetting';

const ADMIN_SETTINGS_API_CONSTANTS = {
    [CHARGE_RATE_CONTEXT_MENU.DUPLICATE_CHARGE_RATE_USING_API] : 'fetchChargeRateContentById$',
    [DIARY_CONTEXT_MENU.DUPLICATE_DIARY_USING_API]: 'fetchDiaryContentById$'
};

const ROUTE_ADMIN_PAGE_CONSTANTS = {
    DEFAULT_PARAM : 'id',
    QUERY_PARAMS : {
        PAGE_NUMBER : 'page',
        PAGE_SIZE : 'size',
        TAB : 'tab'
    }
};

const SCENARIO_CONSTS = {
    SCENARIO: 'Scenario',
    SCENARIOS_LOWERCASE: 'scenarios'
};

const ROLE_GROUP_CONSTS = {
    ROLE_GROUP: 'Role group',
    ROLE_GROUP_LOWERCASE: 'role groups'
};

const SKILL_CONSTS = {
    SKILL: 'Skill',
    SKILL_LOWERCASE: 'skill'
};

const PLANNING_DATA = 'Planning data';

const USER_MANAGEMENT_FIELD_CONSTANTS = {
    name: 'resource_description',
    email:'resource_email',
    firstname:'resource_firstname',
    lastname:'resource_lastname',
    userStatusString:'resource_userstatus',
    securityProfileString:'resource_securityprofile_guid',
    userLastLoginString: 'resource_last_login'
};

const USER_MANAGEMENT_COMMANDBAR_ACTIONS = {
    EDIT_ACTION:'EDIT_ACTION',
    USER_STATUS_ACTION: {
        SET_ACTIVE_ACTION: 'SET_ACTIVE_ACTION',
        SET_INACTIVE_ACTION: 'SET_INACTIVE_ACTION'
    },
    BULK_RESEND_EMAIL_ACTION:'BULK_RESEND_EMAIL_ACTION',
    BULK_RESET_PASSPHRASE:'BULK_RESET_PASSPHRASE',
    SEND_C_ME_SURVEY:'SEND_C_ME_SURVEY'
};

const FIELD_ACCESS_LEVEL_CONSTS = {
    READ_ONLY: 'ReadOnly',
    HIDDEN:'Hidden'
};

const REMAP_ENTITY_TITLES = {
    //need to do this as role and role group name is not as per alias
    'Booking': 'Booking',
    'BookingSeries': 'BookingSeries',
    'Job':'Job',
    'Resource':'Resource',
    'Client':'Client',
    'Role':'rolerequest',
    'Scenario':'rolerequestgroup',
    'bookingseries': 'booking',
    'Skill': 'Skill'
};

const MAP_ENTITY_TITLES = {
    //need to do this as role and role group name is not as per alias
    'rolerequest':'role',
    'rolerequestgroup':'role group',
    'Booking': 'booking',
    'Job':'job',
    'Resource':'resource',
    'Client':'client',
    'Skill':'skill'
};

const STATUS_CODE = {
    CONFLICT_STATUS_CODE: 409,
    USER_ACCESS_CODE: 403
};

const VALIDATE_FIELD_CONST = {
    email: 'resource_email'
};

const PAGINATION_CONST = {
    DEFAULT_PAGE_NUMBER: 1,
    DEFAULT_PAGE_SIZE_1: 10,
    DEFAULT_PAGE_SIZE_2: 50,
    DEFAULT_PAGE_SIZES_OPTIONS_1: ['10', '20', '30'],
    DEFAULT_PAGE_SIZES_OPTIONS_2: ['50', '100', '150']
};

export const FNAS_PER_TABLENAME = {
    [TABLE_NAMES.BOOKING]: ['ReadBooking', 'CreateBooking', 'EditBooking', 'DeleteBooking'],
    [TABLE_NAMES.RESOURCE]: ['ReadResource','CreateResource', 'EditResource', 'DeleteResource'],
    [TABLE_NAMES.CLIENT]: ['ReadClient', 'CreateClient', 'EditClient', 'DeleteClient'],
    [TABLE_NAMES.JOB]: ['ReadJob', 'CreateJob', 'EditJob', 'DeleteJob'],
    [TABLE_NAMES.ROLEREQUESTGROUP]: ['ReadRoleGroup','CreateRoleGroup', 'EditRoleGroup', 'DeleteRoleGroup'],
    [TABLE_NAMES.ROLEREQUEST]: ['ReadRoleRequest'],
    [TABLE_NAMES.SKILL]:['ReadSkill']
};

export const FNAS_KEY = {
    VIEW_BUDGET_DETAILS:'ViewBudgetDetails',
    MANAGER_APPROVAL:'ManagerApproval',
    WORKFLOW_TAB_LINK:'workflowTabLink'
};

export const FNAS_PER_TABLENAME_SECURITY_INFO = {
    [TABLE_NAMES.ROLEREQUEST]: [FNAS_KEY.VIEW_BUDGET_DETAILS],
    [TABLE_NAMES.SKILL]:[FNAS_KEY.MANAGER_APPROVAL]
};

export const FUNCTIONAL_ACCESS_LEVEL_TYPES = {
    CREATE: 'Create',
    EDIT: 'Edit',
    DELETE: 'Delete',
    READ: 'Read'
};

export const ENTITY_ACCESS_CONDITION_TYPES = {
    PREDEFINED: 'Predefined condition type',
    CUSTOM: 'Custom condition type',
    JSON: 'Json condition type'
};


const ENTITY_ACCESS_CONDITION_TYPE_SERVER_FETCH_VALUES = {
    PREDEFINED: 1,
    CUSTOM: 2,
    JSON: 3
};

//TODO:Remove once enum issue is fixed in server
export const ENTITY_ACCESS_CONDITION_TYPE_ENUM_VALUES = {
    PREDEFINED: 'Predefined',
    CUSTOM: 'Custom',
    JSON: 'CustomViaJson'
};

export const ENTITY_ACCESS_CONDITION_TYPE_MAP = {
    [ENTITY_ACCESS_CONDITION_TYPE_ENUM_VALUES.PREDEFINED]: ENTITY_ACCESS_CONDITION_TYPES.PREDEFINED,
    [ENTITY_ACCESS_CONDITION_TYPE_ENUM_VALUES.CUSTOM]: ENTITY_ACCESS_CONDITION_TYPES.CUSTOM,
    [ENTITY_ACCESS_CONDITION_TYPE_ENUM_VALUES.JSON]: ENTITY_ACCESS_CONDITION_TYPES.JSON
};

const REPORT_SETTINGS_PAGE_CONSTANTS = {
    BILLABILITY: 'Billability',
    BILLABILITY_CODE: 'BillableTarget',
    JOB_OPPORTUNITY_CODE: 'JobOpportunityThreshold'
};

const COMPANY_INFORMATION_CONST = {
    COMPANY_NAME_MAX_LENGTH: 255
};

const ENTITY_IMPORT_LABELS = {
    JOBS: 'Jobs',
    CLIENTS: 'Clients',
    RESOURCES: 'Resources',
    SKILLS: 'Skills'
};
const USER_LIST_SORT_CONST = {
    SORT_BY_ASCENDING: 'ascend',
    SORT_BY_DESCENDING: 'descend'
};

const USER_MANAGEMENT_FILTER_LINES = {
    RESOURCE_ISSERVICEACCOUNT: {
        field: RESOURCE_ISSERVICEACCOUNT,
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        value: BOOLEAN_FIELD_DB_VALUES.FALSE
    }
};

const SERVICE_ACCOUNT_FILTER_LINES = {
    RESOURCE_ISSERVICEACCOUNT: {
        field: RESOURCE_ISSERVICEACCOUNT,
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        value: BOOLEAN_FIELD_DB_VALUES.TRUE
    }
};

export {
    BASE_SUBNAVIGATION_URL,
    AREA_CONSTANTS,
    SECTION_CONSTANTS,
    AliasConstants,
    CONTEXT_MENU_ACTIONS,
    DIARY_CONSTANTS,
    DIARIES_COLLECTION,
    ENTITY_CONFIGURATION_CONSTANTS,
    CHARGE_RATE_CONSTANTS,
    COMMANDBAR_CONSTANTS,
    DAY_TYPES_CONSTANTS,
    WORK_PATTERN_CONSTANTS,
    EMAIL_OPERATIONS,
    FILE_TYPE,
    API_OPERATIONS,
    ITEM_LIST_VIEWS,
    DATE_TIME_CONSTANTS,
    REUSABLE_GRID_CONSTANTS,
    STYLE_CONSTANTS,
    SKILLS_CONFIGURATION_CONSTANTS,
    SECURITY_PROFILES_CONSTANTS,
    ADMIN_PAGE_CREATE_SECTION_KEYS_CONSTANTS,
    ADMIN_SETTINGS_ALIAS,
    SECTION_INTERNAL_NAME_CONSTANTS,
    FORMATTING_FIELDS_CONSTANTS,
    FIELD_SUFFIXES_BY_FORMAT_TYPE,
    DEFAULT_EXAMPLE_NUMBER,
    FIELD_VALUES_SUFFIX_CONSTANTS,
    BUILD_IN_ALIAS,
    ADMIN_SETTINGS_API_CONSTANTS,
    VALUES_CONSTANTS,
    COLOUR_SCHEMES_CONSTANTS,
    ROUTE_ADMIN_PAGE_CONSTANTS,
    USER_MANAGEMENT_FIELD_CONSTANTS,
    FIELD_ACCESS_LEVEL_CONSTS,
    USER_MANAGEMENT_COMMANDBAR_ACTIONS,
    MAP_ENTITY_TITLES,
    REMAP_ENTITY_TITLES,
    STATUS_CODE,
    VALIDATE_FIELD_CONST,
    PAGINATION_CONST,
    ENTITIES_WITH_TARGET_BILLABILITY,
    REPORT_SETTINGS_PAGE_CONSTANTS,
    COMPANY_INFORMATION_CONST,
    ENTITY_IMPORT_LABELS,
    USER_STATUS,
    USER_LIST_SORT_CONST,
    ADMIN_SETTING_ALIAS,
    USER_MANAGEMENT_FILTER_LINES,
    SERVICE_ACCOUNT_FILTER_LINES,
    SCENARIO_CONSTS,
    ROLE_GROUP_CONSTS,
    PLANNING_DATA,
    SKILL_CONSTS
};