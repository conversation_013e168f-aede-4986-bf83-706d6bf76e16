import { TALENT_PROFILE } from '../actions/actionTypes';
import getServerinfo from '../serverInfo';
const BaseURL = getServerinfo().ApiServerBase;
const LRTBaseURL = getServerinfo().LongRunningOperationApiServerBase;
const WorkspaceBaseURL = getServerinfo().WorkspaceApiServerBase;
const APIBaseURL = BaseURL + '/api';
const TableAccessUrl = APIBaseURL + '/tableaccess';
const PagingTableAccessUrl = (tableName) => `${TableAccessUrl}/${tableName}/paging/paged`;
const PagingKeepAliveUrl = (tableName) => `${TableAccessUrl}/${tableName}/paging/keepalive`;
const FilteredTableAccessUrl = (tableName) => `${TableAccessUrl}/${tableName}/filter`;
const NamedTableAccessUrl = (tableName) => `${TableAccessUrl}/${tableName}`;
const NamedTableAccessBatchUrl = (tableName) => `${TableAccessUrl}/${tableName}/batch`;
const NamedTableAccessMergeUrl = (tableName) => `${TableAccessUrl}/${tableName}/merge`;
const ValidateTableAccessUrl = (tableName) => `${TableAccessUrl}/${tableName}/validate`;
const ValidateRichFieldsTableAccessUrl = (tableName) => `${TableAccessUrl}/${tableName}/validate/richfields`;
const IDTableAccessUrl = (tableName, id) => `${TableAccessUrl}/${tableName}/${id}`;
const TableViewUrl = APIBaseURL + '/tableView';
const DBStructureUrl = APIBaseURL + '/structure';
const DBStructureRichUrl = DBStructureUrl + '/tablestructure';
const DBSctuctureFieldNamesUrl = DBStructureUrl + '/fieldnames';
const DBSctuctureFieldNamesRichUrl = DBStructureUrl + '/richfieldstructurefortables';
const ClaimsUrl = APIBaseURL + '/claims';
const PermissionsUrl = APIBaseURL + '/permissions';
const SurrogateIdResolverUrl = APIBaseURL + '/surrogateidresolver';
const EntityIdByFakeIdUrl = (tableName, surrogateId) => `${SurrogateIdResolverUrl}/getid/${tableName}/${surrogateId}`;
const SummaryWidgetSettingsUrl = `${APIBaseURL}/summary/widgetsettings`;
const FeatureManagementUrl = APIBaseURL + '/featuremanagement';

const UnconfirmedBookingsNotificationSettings = `${APIBaseURL}/BookingTypeSetting/unconfirmed`;

const SkillStructureBaseUrl = getServerinfo().ResourceSkillsServerBase;
const SkillStructureUrl = SkillStructureBaseUrl + '/api/skillstructure';
const SkillStructureAPIBaseUrl = getServerinfo().SkillsApiServerBase + '/api';
const SkillSectionsUrl = SkillStructureAPIBaseUrl + '/skilltype';
const SkillEntityTypesUrl = SkillStructureAPIBaseUrl + '/skillEntitytype';
const SkillCategoriesUrl = SkillStructureAPIBaseUrl + '/categories';
const AllSkillCategoriesUrl = SkillStructureAPIBaseUrl + '/skillcategory';
const AllSkillSubCategoriesUrl = SkillStructureAPIBaseUrl + '/skillsubcategory';
const SkillSectionByIdUrl = (id) => `${SkillSectionsUrl}/${id}`;
const SkillsUrl = SkillStructureAPIBaseUrl + '/skills';
const SkillByIdUrl = (id) => `${SkillsUrl}/${id}`;
const FilterSkillsUrl = SkillsUrl + '/filter';

const TaxonomyBaseUrl = getServerinfo().SkillsApiServerBase;
const FetchSkillsUrl = TaxonomyBaseUrl + '/api/skills';
const StandardTaxonomiesUrl = TaxonomyBaseUrl + '/api/standardtaxonomies/categories';

const SECURITY_PROFILE_STRUCTURE_API_BASE_URL = getServerinfo().SecurityProfilesApiServerBase + '/api';
const SecurityProfilesUrl = SECURITY_PROFILE_STRUCTURE_API_BASE_URL + '/securityprofiles';
const SecurityProfileByIdUrl = (id) => `${SecurityProfilesUrl}/${id}`;
const FunctionalAccessRulesUrl = SECURITY_PROFILE_STRUCTURE_API_BASE_URL + '/functionalaccessrules';
const SecurityProfileFNAsUrl = (id) => `${SECURITY_PROFILE_STRUCTURE_API_BASE_URL}/securityprofiles/${id}/functionalaccesses`;
const SecurityProfileEAsUrl = (id) => `${SECURITY_PROFILE_STRUCTURE_API_BASE_URL}/securityprofiles/${id}/entityaccess`;
const EntityAccessRulesUrl = `${SECURITY_PROFILE_STRUCTURE_API_BASE_URL}/entityaccessrules`;
const ENTITY_ACCESS_RULES_ENTITIES_URL = `${EntityAccessRulesUrl}/entities`;
const EntityAccessRulesAccessLevelsUrl = `${EntityAccessRulesUrl}/accesslevels`;
const EntityAccessRulesConditionsUrl = `${EntityAccessRulesUrl}/ruleconditions`;
const ResourceSkillsByIdUrl = (id) => `${SkillStructureBaseUrl}/api/resourceskills/${id}`;
const ResourceSkillsRecommendationByIdUrl = (id) => `${SkillStructureBaseUrl}/api/recommendation/${id}`;
const ResourceSkillsApprovalsPendingByIdUrl = (id) => `${SkillStructureBaseUrl}/api/approvalsPending/${id}`;
const ResourceSkillsApprovalsHistoryByIdUrl = (id) => `${SkillStructureBaseUrl}/api/approvalsHistory/${id}`;
const SkillPreferenceUrl = `${SkillStructureBaseUrl}/api/resourceskills/skillpreferences`;
const FilteredResourceSkillsUrl = `${SkillStructureBaseUrl}/api/skillsearch`;
const ValidateCustomConditionUrl = `${SecurityProfilesUrl}/validate`;
const AuthorizedResourceSkills = `${SkillStructureBaseUrl}/api/resources/authorizeSkills`;

const AdminSettingsConsumerAPIBaseUrl = getServerinfo().AdminSettingConsumerServerBase + '/api/adminmodule';
const TalentProfileConfigApi = `${AdminSettingsConsumerAPIBaseUrl}/talentprofileconfiguration`;
const DiaryUrl = `${AdminSettingsConsumerAPIBaseUrl}/diary`;
const PageConfigApi = `${AdminSettingsConsumerAPIBaseUrl}/pagename`;
const USER_SECURITYPROFILE_OPTION_URL = `${AdminSettingsConsumerAPIBaseUrl}/securityprofile`;
const FunctionalAccessConsumerApi = `${USER_SECURITYPROFILE_OPTION_URL}/functionalaccess`;
const ENTITY_CONFIGURATION_CONSUMER_URL = `${AdminSettingsConsumerAPIBaseUrl}/Entity`;
const CurrencyConsumerApi = `${AdminSettingsConsumerAPIBaseUrl}/currency`;
const ColourSchemeConsumerApi = `${AdminSettingsConsumerAPIBaseUrl}/colourtheme`;
const BookingConflictConsumerApi = `${AdminSettingsConsumerAPIBaseUrl}/bookingconflict`;

const ADMIN_SETTING_BASE_URL = getServerinfo().AdminSettingApiServerBase;
const ADMIN_SETTING_BASE_URL_API = ADMIN_SETTING_BASE_URL + '/api';
const AdminSettingAPIUrl = ADMIN_SETTING_BASE_URL_API + '/setting';
const AdminSettingMenuOptionUrl = AdminSettingAPIUrl + '/GetMenuOption';
const AdminSettingGetSettingValueUrl = AdminSettingAPIUrl + '/GetSettingValue';
const AdminSettingUpdateSettingValueUrl = AdminSettingAPIUrl;
const AdminSettingGetOptionURL = AdminSettingAPIUrl;
const ADMIN_SETTING_BASE_URL_SETTINGS = `${ADMIN_SETTING_BASE_URL_API}/Setting`;
const SECTION_CREATE_URL = `${ADMIN_SETTING_BASE_URL_SETTINGS}`;
const SECTION_DUPLICATE_URL = `${ADMIN_SETTING_BASE_URL_SETTINGS}/duplicate`;
const SECTION_RENAME_URL = `${ADMIN_SETTING_BASE_URL_SETTINGS}/rename`;
const SECTION_DELETE_URL = `${ADMIN_SETTING_BASE_URL_SETTINGS}`;
const DAY_TYPES_CREATE = `${ADMIN_SETTING_BASE_URL_API}/DayType`;
const DAY_TYPES_DELETE = `${ADMIN_SETTING_BASE_URL_API}/DayType`;
const WORK_PATTERN_CREATE = `${ADMIN_SETTING_BASE_URL_API}/WorkPattern`;
const WORK_PATTERN_DELETE = `${ADMIN_SETTING_BASE_URL_API}/WorkPattern`;
const CHARGE_RATE_URL = `${ADMIN_SETTING_BASE_URL_API}/ChargeRate`;
const CHARGE_CODE_URL = `${ADMIN_SETTING_BASE_URL_API}/ChargeType`;
const CURRENCY_API_URL = `${ADMIN_SETTING_BASE_URL_API}/Currency`;
const DIARY_CALENDAR = `${ADMIN_SETTING_BASE_URL_API}/Calendar`;
const COLOURSCHEME_TABLEFIELD_URL = `${ADMIN_SETTING_BASE_URL_API}/ColourTheme/GetPossibleValuesAsync`;
const GET_ALL_COLOUR_SCHEME = `${ADMIN_SETTING_BASE_URL_API}/ColourTheme`;
const GET_BY_ID_COLOUR_SCHEME = `${ADMIN_SETTING_BASE_URL_API}/ColourTheme`;
const UPDATE_COLOUR_SCHEME = `${ADMIN_SETTING_BASE_URL_API}/ColourTheme`;

const ENTITY_CONFIGURATION_URL = `${ADMIN_SETTING_BASE_URL_API}/Entity`;
const SKILL_ENTITY_DETAILS_URL = `${ENTITY_CONFIGURATION_URL}/GetEntityByName`;
const FIELD_PROPERTY_URL = ENTITY_CONFIGURATION_URL;
const FIELD_PROPERTY_BYID_URL = `${ADMIN_SETTING_BASE_URL_API}/Field`;
const FIELD_LOOKUP_VALUES_URL = `${ADMIN_SETTING_BASE_URL_API}/FieldLookup`;
const USER_MANAGEMENT = `${ADMIN_SETTING_BASE_URL_API}/User`;
const SERVICE_MANAGEMENT = `${ADMIN_SETTING_BASE_URL_API}/ServiceUser`;
const SEND_SERVICE_PASSWORD_RESET = `${SERVICE_MANAGEMENT}/sendservicepasswordreset`;
const ENTITY_IMPORT = `${ADMIN_SETTING_BASE_URL_API}/EntityImport`;
const PRE_VALIDATE_IMPORT = `${ENTITY_IMPORT}/PreValidateData`;
const GET_ENTITY_TEMPLATE = `${ENTITY_IMPORT}/GetTemplate`;
const UPLOAD_ENTITY_IMPORT = `${ENTITY_IMPORT}/ImportData`;
const LICENSE_INFO = `${ADMIN_SETTING_BASE_URL_API}/License`;
const LICENSE_INFO_BY_KEY = `${ADMIN_SETTING_BASE_URL_API}/License/GetLicensesByKey`;

const FUNCTIONAL_ACCESS_RULES_URL = `${SECURITY_PROFILE_STRUCTURE_API_BASE_URL}/FunctionalAccessRules`;
const ENTITY_ACCESS_RULES_URL = `${SECURITY_PROFILE_STRUCTURE_API_BASE_URL}/EntityAccessRules`;
const SECURITY_PROFILES_URL = `${SECURITY_PROFILE_STRUCTURE_API_BASE_URL}/SecurityProfiles`;
const SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL = `${SECURITY_PROFILES_URL}/access`;

const PLANNER_DATA_BASE_URL = getServerinfo().PlannerApiServerBase;
const PLANNER_DATA_BASE_URL_API = `${PLANNER_DATA_BASE_URL}/api`;
const PlannerDataUrl = `${PLANNER_DATA_BASE_URL_API}/plannerdata`;
const PlannerDataByViewUrl = (view) => `${PlannerDataUrl}/${view}`;

const WORKSPACES_BASE_URL_API = `${WorkspaceBaseURL}/api`;
const WORKSPACES_URL = `${WORKSPACES_BASE_URL_API}/Workspaces`;
const IDWorkspacesUrl = (id) => `${WORKSPACES_URL}/${id}`;
const WORKSPACES_MOST_RECENTLY_USED_URL = `${WORKSPACES_BASE_URL_API}/Workspaces/mostrecentlyused`;
const IDMostRecentlyUsedUrl = (id) => `${WORKSPACES_MOST_RECENTLY_USED_URL}/${id}`;
const IDWorkspaceUrl = (id) => `${WORKSPACES_URL}/${id}`;
const CONFLICTS_API_URL = `${ADMIN_SETTING_BASE_URL_API}/BookingConflict`;

const REPORT_BASE_URL = getServerinfo().ReportApiServerBase;
const REPORT_BASE_URL_API = `${REPORT_BASE_URL}/api`;
const REPORT_URL = `${REPORT_BASE_URL_API}/ReportData`;
const GET_REPORTS_DATA_URL = `${REPORT_URL}/GetReports`;
const CREATE_REPORT_URL = `${REPORT_URL}/AddNewReport`;
const SAVE_AS_REPORT_URL = `${REPORT_URL}/SaveAsReport`;
const DELETE_REPORT_URL = `${REPORT_URL}/DeleteReport`;
const UPDATE_REPORT_URL = `${REPORT_URL}/UpdateReport`;
const NOTIFY_REPORT_EVENT_URL = `${REPORT_URL}/NotifyReport`;

const REPORT_SETTINGS_BASE_URL = getServerinfo().ReportApiServerBase;
const REPORT_SETTINGS_BASE_URL_API = `${REPORT_SETTINGS_BASE_URL}/api`;
const REPORT_SETTINGS_URL = `${REPORT_SETTINGS_BASE_URL_API}/ReportSetting`;
const GET_REPORT_SETTINGS_DATA = `${REPORT_SETTINGS_URL}/GetReportSettings`;
const UPDATE_REPORT_SETTINGS_DATA = `${REPORT_SETTINGS_URL}/BatchUpdate`;

const REPORT_SERVICE_URL = `${REPORT_SETTINGS_BASE_URL_API}/ReportService`;
const DATASET_REFRESH_URL = `${REPORT_SERVICE_URL}/datasetrefresh`;
const DATASET_REFRESH_HISTORY_URL = `${REPORT_SERVICE_URL}/datasetrefreshhistory`;

const COMPANY_INFORMATION_API_URL = `${ADMIN_SETTING_BASE_URL_API}/Company`;
const DEPARTMENT_API_URL = `${ADMIN_SETTING_BASE_URL_API}/Department`;
const DIVISION_API_URL = `${ADMIN_SETTING_BASE_URL_API}/Division`;

const AVATARS_URL = `${APIBaseURL}/avatars`;
const IDAvatarsUrl = (id) => `${AVATARS_URL}/${id}`;

const AUDIT_BASE_URL = getServerinfo().AuditTrailApiServerBase;
const AUDIT_BASE_URL_API = AUDIT_BASE_URL + '/api/audit';
const AuditUrl = (id) => `${AUDIT_BASE_URL_API}/${id}`;

const ATTACHMENTS_BASE_URL = getServerinfo().AttachmentApiServerBase;
const ATTACHMENTS_BASE_URL_API = ATTACHMENTS_BASE_URL + '/api/attachments';
const AttachmentsUrl = (entityTable) => `${ATTACHMENTS_BASE_URL_API}/${entityTable}`;
const AttachmentsConfigUrl = (entityTable) => AttachmentsUrl(entityTable) + '/configuration';
const AttachmentsByEntityId = (entityTable, entityId) => `${ATTACHMENTS_BASE_URL_API}/${entityTable}/${entityId}`;
const AttachmentById = (entityTable, entityId, attachmentId) => AttachmentsByEntityId(entityTable, entityId) + `/${attachmentId}`;

const ROLE_BASE_URL = getServerinfo().RoleApiServerBase;
const ROLE_BASE_URL_API = ROLE_BASE_URL + '/api';
const RoleTemplateUrl = ROLE_BASE_URL_API + '/roletemplate';
const RoleTemplateGetUrl = `${RoleTemplateUrl}/get`;
const RoleTemplateRenameUrl = `${RoleTemplateUrl}/rename`;
const InsertRoleTemplateUrl = `${RoleTemplateUrl}/createfromscratch`;
const RoleRequestGroupAccessUrl = ROLE_BASE_URL_API + '/rolerequestgroup';
const RoleRequestFetchUrl = ROLE_BASE_URL_API + '/rolerequest/get';
const RoleRequestCrudUrl = ROLE_BASE_URL_API + '/rolerequest';
const RoleRequestPagingUrl = ROLE_BASE_URL_API + '/rolerequest/paging';
const RoleRequestPagingKeepAliveUrl = ROLE_BASE_URL_API + '/rolerequest/paging/keepalive';
const RoleRequestAssignUrl = ROLE_BASE_URL_API + '/rolerequest/assign';
const RoleRequestUnassignUrl = ROLE_BASE_URL_API + '/rolerequest/unassign';
const RoleRequestSuggestedResourcesUrl = ROLE_BASE_URL_API + '/rolerequest/suggest';
const RoleRequestAISuggestedResourcesUrl = ROLE_BASE_URL_API + '/rolerequest/ai-suggest';
const RoleRequestTransitionUrl = ROLE_BASE_URL_API + '/rolerequest/transition';
const RoleRequestWorkflowUrl = ROLE_BASE_URL_API + '/workflow';
const RoleRequestRoleByNameWorkflowUrl = RoleRequestWorkflowUrl + '/RolesByName';
const RoleRequestRoleByByRequirementWorkflowUrl = RoleRequestWorkflowUrl + '/RolesByRequirement';
const RoleMarketplacePublishUrl = ROLE_BASE_URL_API + '/rolerequest/marketplace';
const RoleRequestRoleApplyUrl = ROLE_BASE_URL_API + '/rolerequest/apply';
const RoleRequestRoleWithdraw = ROLE_BASE_URL_API + '/rolerequest/unapply';
const RoleRequestGroupAPIUrl = ROLE_BASE_URL_API + '/rolerequestgroup';

const MAINTENANCE_NOTIFICATION_URL = '/api/maintenancenotification';

const LongRunningTaskBaseUrl = LRTBaseURL + '/api/longRunningTask';
const LongRunningTaskRunUrl = LongRunningTaskBaseUrl + '/run';
const LongRunningTaskSummaryUrl = LongRunningTaskBaseUrl + '/Summary';

const LongRunningTaskGetStatusByTypeUrl = (type) => LongRunningTaskBaseUrl + `/${type}/isjobprocessing`;
const LongRunningTaskGetStatusUrl = (processGuid) => LongRunningTaskBaseUrl + `/status/${processGuid}`;
const LongRunningTaskGetUserTaskUrl = LongRunningTaskBaseUrl + '/getactiveusertasks';

const OperationsLogBaseUrl = LRTBaseURL + '/api/operationlog';
const OperationsLogPagingUrl = OperationsLogBaseUrl + '/paging';
const OperationsLogPagingKeepalive = (id) => OperationsLogPagingUrl + `/${id}/keepalive`;
const OperationsLogCancelTask = (taskid) => LongRunningTaskBaseUrl + `/undo/${taskid}`;
const ExpandedOperationsLogPagingURL = () => LRTBaseURL + '/api/expandedlog/paging';

const ACCEPT_COOKIE_POLICY_URL = `${APIBaseURL}/cookie/accept`;
const COOKIE_POLICY_STATUS_URL = `${APIBaseURL}/cookie/status`;

const GetResetServicePassphraseUrl = (queryParams) => `${getServerinfo().IdentityServerBase}/Account/Resetpassphrase${queryParams}`;

const UserEntityAccessUrl = 'TO BE DEFINED';

const LOCALE_EN = 'en';
export const APPLICATION_TIMEZONE = 'Etc/UTC';
export const APPLICATION_NAME = 'Retain Cloud';

const MAX_SELECTED_CRITERIA_FIELDS = 6;
const MAX_LABEL_LENGTH = 30;

//Licensing
const LICENSING_BASE_URL = getServerinfo().LicensingApiServerBase;
const GET_LICENSE_INFO = `${LICENSING_BASE_URL}/api/licence`;

const DB_OPERATORS = {
    LESS_THAN: 'LessThan',
    LESS_THAN_OR_EQUAL: 'LessThanOrEqual',
    EQUALS: 'Equals',
    GREATER_THAN_OR_EQUAL: 'GreaterThanOrEqual',
    GREATER_THAN: 'GreaterThan',
    LIKE: 'Like',
    CONTAINS: 'Contains',
    ASSIGNED: 'Assigned',
    IN: 'In',
    UNASSIGNED: 'Unassigned',
    RANGE: 'Range'
};

const FILTER_TYPES = {
    NUMERIC: 'NUMERIC',
    DATE: 'DATE',
    TEXT: 'TEXT',
    MULTY_VALUES: 'MULTY_VALUES',
    DATE_SENSITIVE: 'DATE_SENSITIVE',
    SKILL: 'SKILL',
    CHECKBOX: 'CHECKBOX',
    NUMERIC_PARAMETER: 'NUMERIC_PARAMETER',
    BOOLEAN: 'BOOLEAN'
};

const FILTER_GROUP_OPERATORS = {
    AND: 'And',
    OR: 'Or'
};

const FILTER_FIELD_NAMES = {
    SKILL: 'resource_has_skill_resource_skill_level',
    SKILL_NO_LEVEL: 'resource_has_skill',
    SKILL_LEVEL: 'resource_skill_level',
    RESOURCE_HAS_ANY_SKILL: 'resource_has_any_skills',
    RESOURCE_GUID: 'resource_guid',
    AVAILABILITY: 'availability',
    AVAILABLE_HOURS: 'available_time',
    UTILISATION: 'utilisation',
    RESOURCE_BOOKING_COUNT: 'resource_booking_count',
    RESOURCE_TYPE: 'resource_resourcetype_guid',
    RESOURCE_GRADE: 'resource_localgrade_guid',
    RESOURCE_DIVISION: 'resource_division_guid',
    RESOURCE_DEPARTMENT: 'resource_current_department_guid',
    RESOURCE_MANAGER: 'resource_manager_resource_guid',
    RESOURCE_MANAGER_NAME: 'resource_manager_resource_guid.resource_description',
    RESOURCE_LOCATION: 'resource_location_guid',
    RESOURCE_JOBTITLE: 'resource_rolename',
    RESOURCE_STAFFNO: 'resource_staffno',
    RESOURCE_SURROGATE_ID: 'resource_surrogate_id',
    RESOURCE_SUMMARY: 'resource_summary',
    RESOURCE_USERSTATUS: 'resource_userstatus',
    RESOURCE_SKILL_LAST_UPDATED: 'resource_resourceskill_lastupdated',
    RESOURCE_EDUCATION_LAST_UPDATED: 'resource_education_lastupdated',
    RESOURCE_EXPERIENCE_LAST_UPDATED: 'resource_experience_lastupdated',
    JOB_GUID: 'job_guid',
    JOB_START: 'job_start',
    JOB_END: 'job_end',
    JOB_FEE: 'job_fee',
    JOB_CLIENT_GUID: 'job_client_guid',
    JOB_CODE: 'job_code',
    JOB_ENGAGEMENTLEAD_GUID: 'job_engagementlead_resource_guid',
    JOB_LOCATION: 'job_location_guid',
    JOB_CURRENT_DEPARTMENT_GUID: 'job_current_department_guid',
    JOB_DIVISION_GUID: 'job_division_guid',
    JOB_PREVIOUS_JOB_GUID: 'job_previous_job_guid',
    JOB_NEXT_JOB_GUID: 'job_next_job_guid',
    JOB_STATUS: 'job_jobstatus_guid',
    JOB_OPPORTUNITY_PERCENT: 'job_opportunity_percent',
    JOB_CHARGE_CODE: 'job_chargetype_guid',
    JOB_FTE_REFERENCE_DIARY: 'job_diarygroup_guid',
    JOB_IS_CONFIDENTIAL: 'job_isconfidential',
    JOB_IS_EXCLUDE_FROM_UTILIZATION: 'job_isExcludeFromUtilisation',
    JOB_IS_EXCLUDE_FROM_BILLABILITY: 'job_isExcludeFromBillability',
    JOB_I_MANAGE: 'job_i_manage',
    JOB_REVENUE_TARGET: 'job_fixedprice',
    JOB_REVENUE_AS_BOOKED: 'job_totalrevenue',
    JOB_REVENUE_AS_PERCENTAGE_OF_TARGET: 'job_revenuepercentagetarget',
    JOB_COSTS_BUDGET: 'job_budget',
    JOB_COSTS_AS_BOOKED: 'job_totalcost',
    JOB_COSTS_AS_PERCENTAGE_OF_BUDGET: 'job_budgetconsumed',
    JOB_PROFIT_MARGIN_TARGET: 'job_profitmargintarget',
    JOB_PROFIT_MARGIN_AS_BOOKED: 'job_profitmargin',
    JOB_MARGIN_AS_PERCENTAGE_OF_TARGET: 'job_marginpercentagetarget',
    JOB_HOURS_BUDGET: 'job_hoursbudget',
    JOB_TOTAL_HOURS_BOOKED: 'job_totalhoursbooked',
    JOB_HOURS_AS_PERCENTAGE_OF_BUDGET: 'job_hourspercentagebudget',
    BOOKING_IS_ASSIGNED: 'booking_is_assigned',
    BOOKING_START: 'booking_start',
    BOOKING_END: 'booking_end',
    BOOKING_NOTES: 'booking_notes',
    BOOKING_STATUS: 'booking_bookingtype_guid',
    BOOKING_WORK_ACTIVITY: 'booking_workactivity_guid',
    WORK_ACTIVITY_DESCRIPTION: 'workactivity_description',
    BOOKING_TOTAL_COST: 'booking_totalcost',
    BOOKING_TOTAL_REVENUE: 'booking_totalrevenue',
    BOOKING_TOTAL_PROFIT: 'booking_totalprofit',
    BOOKING_FTE_ASSIGNED: 'booking_fte',
    JOB_ACTION_REQUIRED: 'job_totalactionablerequests',
    JOB_FTE_RANGE: 'job_fte_range',
    JOB_FTE_TOTAL: 'job_fte_total',
    ROLE_START: 'rolerequest_start',
    ROLE_END: 'rolerequest_end',
    ROLE_NOTES: 'rolerequest_notes',
    ROLE_STATUS: 'rolerequest_rolerequeststatus_guid',
    ROLE_WORK_ACTIVITY: 'rolerequest_workactivity_guid',
    ROLE_TOTAL_COST: 'rolerequest_totalcost',
    ROLE_TOTAL_REVENUE: 'rolerequest_totalrevenue',
    ROLE_TOTAL_PROFIT: 'rolerequest_totalprofit',
    ROLE_RESOURCE: 'rolerequest_resource_guid',
    ROLE_GROUP: 'rolerequest_rolerequestgroup_guid',
    ROLE_JOB: 'rolerequest_job_guid',
    ROLE_HASCRITERIA: 'rolerequest_hascriteria',
    ROLE_NAME: 'rolerequest_description',
    ROLE_APPLIED_TO: 'rolerequest_is_caller_applicant',
    ROLE_AVAILABLE_FOR: 'rolerequest_is_caller_available',
    ROLE_HASSTATUS: 'rolerequest_hasstatus'
};

const BASE_FILTER_OPTIONS = {
    ALL: 'All',
    I_MANAGE: 'I Manage',
    ACTION_REQUIRED: 'Action Required',
    ACTIVE: 'Active',
    INACTIVE: 'Inactive'
};

const APPLY_FILTER_OPTIONS = {
    ALL: 'All',
    APPLIED_TO: 'I have applied to',
    AVAILABLE_FOR: 'I am available for'
};

const APPLICATION_USER_RESOURCE_ROLE_GUID = 'ResourceRoleGuid';

const DEFAULT_FILTER = 'filters_default';

const DEFAULT_APPLY_FILTER_OPTION = APPLY_FILTER_OPTIONS.ALL;

const DEFAULT_BASE_FILTER_OPTION = BASE_FILTER_OPTIONS.ALL;

const NUMERIC_OPERATORS_ALIAS = {
    [DB_OPERATORS.LESS_THAN]: 'less than',
    [DB_OPERATORS.LESS_THAN_OR_EQUAL]: 'at most',
    [DB_OPERATORS.EQUALS]: 'equal to',
    [DB_OPERATORS.GREATER_THAN_OR_EQUAL]: 'at least',
    [DB_OPERATORS.GREATER_THAN]: 'more than'
};

const NUMERIC_PARAMETER_OPERATORS_ALIAS = {
    [DB_OPERATORS.LESS_THAN]: 'less than',
    [DB_OPERATORS.LESS_THAN_OR_EQUAL]: 'at most',
    [DB_OPERATORS.EQUALS]: 'equal to',
    [DB_OPERATORS.GREATER_THAN_OR_EQUAL]: 'at least',
    [DB_OPERATORS.GREATER_THAN]: 'more than'
};

const DATE_OPERATORS_ALIAS = {
    //[DB_OPERATORS.LESS_THAN]: 'before',
    [DB_OPERATORS.LESS_THAN_OR_EQUAL]: 'up to',
    //[DB_OPERATORS.EQUALS]: 'on',
    [DB_OPERATORS.GREATER_THAN_OR_EQUAL]: 'from'
    //[DB_OPERATORS.GREATER_THAN]: 'after'
};

const TEXT_OPERATORS_ALIAS = {
    [DB_OPERATORS.LIKE]: 'contains'
};

const MULTY_VALUES_OPERATORS_ALIAS = {
    [DB_OPERATORS.CONTAINS]: 'is one of'
};

const DATE_SENSITIVE_OPERATORS_ALIAS = {
    [DB_OPERATORS.GREATER_THAN_OR_EQUAL]: 'at least',
    [DB_OPERATORS.LESS_THAN_OR_EQUAL]: 'at most'
};

const SKILL_OPERATORS_ALIAS = {
    [DB_OPERATORS.EQUALS]: 'is'
};

const CHECKBOX_OPERATORS_ALIAS = {
    [DB_OPERATORS.EQUALS]: 'on'
};

const WORKSPACE_ACCESS_TYPES = {
    PUBLIC: 'public',
    PRIVATE: 'private',
    DEFAULT: 'default'
};

const WORKSPACE_EDIT_RIGHTS = {
    READ_ONLY: 'readonly',
    EDIT: 'edit'
};

const OPERATORS = {
    DB_OPERATORS,
    NUMERIC_OPERATORS_ALIAS,
    TEXT_OPERATORS_ALIAS,
    MULTY_VALUES_OPERATORS_ALIAS,
    DATE_OPERATORS_ALIAS,
    DATE_SENSITIVE_OPERATORS_ALIAS,
    SKILL_OPERATORS_ALIAS,
    CHECKBOX_OPERATORS_ALIAS,
    NUMERIC_PARAMETER_OPERATORS_ALIAS,
    FILTER_GROUP_OPERATORS
};

const TABLE_NAMES = {
    RESOURCE: 'resource',
    JOB: 'job',
    BOOKING: 'booking',
    BOOKING_SERIES: 'bookingseries',
    REPEAT_BOOKING: 'repeatbooking',
    RECURRENT_BOOKING: 'recurrentbooking',
    BOOKINGTYPE: 'bookingtype',
    CLIENT: 'client',
    RESOURCESKILL: 'resourceskill',
    WORKSPACE: 'workspace',
    ROLEREQUEST: 'rolerequest',
    ROLEREQUESTGROUP: 'rolerequestgroup',
    ROLEREQUESTSTATUS: 'rolerequeststatus',
    ROLEREQUESTREJECTREASON: 'rolerequestrejectreason',
    ROLEREQUESTPREDEFINEDREJECTREASON: 'rolerequestpredefinedrejectreason',
    ROLECRITERIA: 'rolecriteria',
    DEPARTMENT: 'department',
    JOBSTATUS: 'jobstatus',
    LOCALGRADE: 'localgrade',
    DIVISION: 'division',
    ROLESHORTLIST: 'roleshortlist',
    SECURITY_PROFILE: 'securityprofile',
    ROLE_WORKFLOW: 'roleworkflow',
    ROLE_WORKFLOW_ROLE_TYPE: 'roleworkflowRoletype',
    ROLE_WORKFLOW_CONDITION: 'roleworkflowCondition',
    ROLE_WORKFLOW_CONDITION_CONFIG: 'roleworkflowConditionConfig',
    ROLE_WORKFLOW_APPROVER_TYPE: 'roleworkflowApprovertype',
    ROLE_WORKFLOW_ASSIGNER_TYPE: 'roleworkflowAssignertype',
    ROLE_WORKFLOW_REQUESTER_TYPE: 'roleworkflowRequestertype',
    ROLEREQUESTRESOURCE: 'rolerequestresource',
    CHARGERATE: 'chargerate',
    NOTIFICATION_USER_PREFERENCE: 'notificationuserpreference',
    NOTIFICATION_ADMIN_PREFERENCE: 'notificationadminpreference',
    EMIAL_FREQUENCY_USER_SCHEDULE: 'emailfrequencyuserschedule',
    EMIAL_FREQUENCY_ADMIN_SCHEDULE: 'emailfrequencyadminschedule',
    EMAIL_FREQUENCY: 'emailfrequency',
    UNCONFIRMED_BOOKINGS_ADMIN_SCHEDULE: 'unconfirmedbookingsadminschedule',
    ROLEMARKETPLACE: 'rolemarketplace',
    INSTITUTION: 'institution',
    EDUCATION_FIELD: 'educationfield',
    DEGREE: 'degree',
    EDUCATION: 'education',
    EXPERIENCE: 'experience',
    BOOKINGBYWEEKVIEW: 'bookingbyweekview',
    ROLECATEGORY: 'rolecategory',
    DIARYGROUP: 'diarygroup',
    RAGHEALTH: 'raghealth',
    OPERATIONSLOG: 'operationsLog',
    CME: 'cMe',
    SKILL: 'skill'
};

const REPEAT_BOOKING_OPERATION = {
    CREATE: 'createRepeatBooking',
    EDIT: 'editRepeatBooking',
    DELETE: 'deleteRepeatBooking'
};

const EDIT_REPEAT_BOOKING_TYPE = {
    SELECTED_ONLY: 'SingleBooking',
    SELECTED_AND_FUTURE: 'FutureBookings',
    ALL: 'EntireSeries'
};

const COMPONENT_TYPES = {
    ROLE_TEMPLATES_ERROR: 'ROLE_TEMPLATES_ERROR'
};

const JOIN_TYPES = {
    INNER_JOIN: 'Inner',
    OUTER_JOIN: 'Outer'
};

const KEY_CODES = {
    ENTER: 13,
    COMMA: 188,
    E: 69,
    S: 83,
    DECIMAL_POINT: 110,
    PERIOD: 190,
    NUMPAD_ADD: 107,
    ADD: 187,
    ESC: 27,
    ARROW_DOWN: 40,
    ARROW_UP: 38,
    SPACE: 32,
    ALT: 18
};

const KEYBOARD_EVENT_KEYS = {
    ENTER: 'Enter'
};

const MOUSE_BUTTON_CODES = {
    MOUSE_LEFT: 0
};

const MAX_INPUT_FIELD_LENGTH = 255;

const INITIAL_FILTER_VALUE_COUNT = 20;

const ADDITIONAL_FILTERS_COUNT = 10;

const NODE_TYPE_CODES = {
    DOCUMENT: 9
};

const SHOW_IN_VIEW_SELECTION_LIMIT = 20;

const IMAGE_EXTENTIONS = {
    PNG: '.png',
    SVG: '.svg'
};

const FILE_EXTENSIONS = {
    DOC: 'doc',
    DOCX: 'docx',
    PDF: 'pdf'
};

const MIME_TYPES = {
    [FILE_EXTENSIONS.DOC]: 'application/msword',
    [FILE_EXTENSIONS.DOCX]: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    [FILE_EXTENSIONS.PDF]: 'application/pdf'
};

const HTTP_STATUS_CODES = {
    FORBIDDEN: '/403'
};

const ADMIN_FUNCTIONAL_RULES = {
    ADMIN_FUNCTIONAL_ACCESS: 'Administration settings',
    ADMIN_SETTINGS_NAME: 'Admin Settings',
    PLANS_NAME: 'Scheduler'
};

const ALLOW_CUSTOM_FIELDS = true;
const ALLOW_ENTITY_FIELDS = true;

const MAX_INPUT_DIGITS_COUNT = Number.MAX_SAFE_INTEGER.toString().length;

const DEFAULT_CURRENCY_SYMBOL = '€';
const PERCENT_SYMBOL = '%';
const TOTAL_SUFFIX = 'Total';
const HOURLY_LABEL_SUFFIX = 'hourly';

const PREFIX_FIELDS_TYPES = {
    CURRENCY_TYPE: 'CURRENCY_TYPE',
    PERCENT_TYPE: 'PERCENT_TYPE',
    ICON_TYPE: 'ICON_TYPE'
};

const SUFFIX_FIELDS_TYPES = {
    PERCENT_TYPE: 'PERCENT_TYPE',
    TOTAL_TYPE: 'TOTAL_TYPE',
    HOURLY_RATE_TYPE: 'HOURLY_RATE_TYPE'
};

const LANGUAGES_TO_BE_SUPPORTED = [
    {
        key: 'en-US', name: 'English (US)'
    },
    {
        key: 'en-UK', name: 'English (UK)'
    },
    {
        key: 'fr', name: 'French'
    },
    {
        key: 'de', name: 'German '
    },
    {
        key: 'es', name: 'Spanish'
    },
    {
        key: 'nl', name: 'Dutch'
    }
];

const LICENSE_KEYS_ADMIN_SETTINGS = {
    licenseSkillsType: 'skillType_subscribedskilltypecount',
    licenseKeySkills: 'skills_subscribedskillscount',
    licenseKeySkillLevels: 'skillLevels_subscribedskilllevelscount',
    licenseKeySkillFields: 'skillFields_subscribedskillfieldscount',
    licenseDiaryCount: 'diary_subscribeddiarycount',
    licenseUserManagementCnt: 'usermanagement_subscribedusercount',
    licenseCustomFieldCnt: 'customfield_subscribedcustomfieldcount',
    licenseAttachmentsEnabled: 'attachments_enabled',
    licenseTimesheetsEnabled: 'timesheets_enabled',
    liceseRoleMultipleAssigneesEnabled: 'criteriarole_multipleassignees_enabled',
    licensePlannerPageSize: 'planner_page_size',
    licensePlannerPageMaxInfiniteScrollRows: 'planner_page_maximum_infinite_scroll_rows',
    licenseTableViewPageSize: 'table_view_page_size',
    licenseAIResourceSuggestions: 'ai_suggestion_enabled',
    licenseReportsMaxCount: 'reports_subscribedreportcount',
    licenseCmeEnabled: 'c-me_enabled',
    licenseRetainLibrary: 'skill_taxonomy_enabled',
    licenseIsSsoEnabled: 'IsSsoEnabled',
    licenseDiaryYearsPersistedInHistory: 'diary_yearspersistedinhistory_value'
};

//app language key to antd5 locale key map
const ANTD5_LOCALES_MAP = {
    'en-US': 'en_US',
    'en-UK': 'en_GB',
    'fr': 'fr_FR',
    'de': 'de_DE',
    'es': 'es_ES',
    'nl': 'nl_NL'
};

//app language key to dayjs locale key map
const DAYJS_LOCALES_MAP = {
    'en-US': 'en',
    'en-UK': 'en-gb',
    'fr': 'fr',
    'de': 'de',
    'es': 'es',
    'nl': 'nl'
};

const ACTORS = {
    HOT_KEYS: 'HotKeys'
};

const COLOUR_SCHEME_PARAM_NAME = {
    CUSTOM_SCHEME: 'CustomField',
    MULTIPLE_SCHEME: 'ThemeID'
};

const CRUD_OPERATIONS = {
    CREATE: 'CREATE',
    READ: 'READ',
    UPDATE: 'UPDATE',
    DELETE: 'DELETE'
};

const ROLEREQUEST_OPERATIONS = {
    MOVE_RESOURCE: 'MOVE_RESOURCE',
    REMOVE_RESOURCE: 'REMOVE_RESOURCE',
    MOVE_FTE: 'MOVE_FTE',
    REMOVE_FTE: 'REMOVE_FTE'
};

const ROLEMARKETPLACE_OPERATIONS = {
    PUBLISH_ROLE: 'PUBLISH_ROLE',
    EDIT_ROLE_PUBLICATION: 'EDIT_ROLE_PUBLICATION',
    REMOVE_ROLE_PUBLICATION: 'REMOVE_ROLE_PUBLICATION'
};

const TEMPLATE_OPERATIONS = {
    CREATE_TEMPLATE: 'CREATE_TEMPLATE',
    DELETE_TEMPLATE: 'DELETE_TEMPLATE',
    EDIT_TEMPLATE: 'EDIT_TEMPLATE',
    INSERT_TEMPLATE: 'INSERT_TEMPLATE'
};

const COUNT_DISTINCTIONS = {
    PLURAL: 'plural',
    SINGULAR: 'singular',
    BOTH: 'both'
};

const TOASTER_MESSAGE_PROPS = {
    DURATION: 1.5,
    MARGIN_TOP: 24
};

const URL_PARAMS = {
    WORKSPACE_SURROGATE_ID: 'workspaceSurrogateId',
    WORKSPACE_NAME: 'workspaceName',
    PAGE: 'page',
    PAGE_SIZE: 'pagesize',
    RESOURCE_SURROGATE_ID: 'resourceSurrogateId',
    RESOURCE_NAME: 'resourceName',
    JOB_SURROGATE_ID: 'jobSurrogateId',
    JOB_NAME: 'jobName',
    JOB_ID: 'jobId',
    ROLE_GROUP_SURROGATE_ID: 'rolegroupSurrogateId',
    ROLE_GROUP_NAME: 'rolegroupName',
    TAB: 'tab',
    ROLE_SURROGATE_ID: 'roleSurrogateId',
    ROLE_NAME: 'roleName',
    SUMMARY: 'summary'
};

const MAX_SELECTED_YEAR = 9999;
const MIN_SELECTED_YEAR = 1;

const HTTP_RESPONSE_CODES = {
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    SUCCESS: 200,
    ACCEPTED: 202
};

//Maximum selection count
const MAX_ENTITIES_SELECTION_COUNT = 50;

const MAP_ENTITY_TITLES = {
    //need to do this as role and role group name is not as per alias
    Booking: 'Booking',
    Job: 'Job',
    Resource: 'Resource',
    Client: 'Client',
    rolerequest: 'Role',
    rolerequestgroup: 'Scenario',
    Department: 'Department',
    Division: 'Division',
    Skill: 'Skill'
};

const COMMAND_BAR_ACTION_ELEMENT_TYPES = {
    QUICK_ACTION: 'QuickAction',
    SWITCH_VIEW: 'SwitchView',
    BUTTON: 'Button',
    ACTION_WITH_COMPONENT: 'ActionWithComponent',
    MENU_WITH_COMPONENT: 'MenuItemWithComponent'
};

const HISTORY_FIELD_EMPTY_VALUE_GUID = 'HISTORY_FIELD_EMPTY_VALUE_GUID';

//false means feature changes will be seen
const ROLE_AND_REQUEST_FEATURE_SWITCH = false;

const BATCH_CREATE_ENTITY_ERROR_PROMPT_KEY = 'batchedCreateEntityErrorPrompt';

//false means feature changes will be seen
const COLOUR_SCHEME_FEATURE_SWITCH = false;

const WORKSPACE_STRUCTURE_CONSTANTS = {
    WORKSPACE_COLOURTHEME_GUID: 'workspace_colourtheme_guid',
    WORKSPACE_COLOUR_FIELD_NAME: 'workspace_colour_field_name',
    WORKSPACE_COLOUR_ENTITY_NAME: 'workspace_colour_entity_name',
    WORKSPACE_CUSTOM_COLOUR_FIELD: 'workspace_custom_colour_field'
};

//Notification
const NOTIFICATION_BASE_URL = getServerinfo().NotificationApiServerBase;
const NOTIFICATION_HUB_URL = `${NOTIFICATION_BASE_URL}/notifications`;
const NOTIFICATION_HISTORY_URL = `${ADMIN_SETTING_BASE_URL_API}/NotificationHistory`;
const UNREAD_NOTIFICATION_COUNT_URL = `${NOTIFICATION_HISTORY_URL}/GetUnreadNotificationCount`;
const NOTIFICATION_EMAIL_SETTINGS_URL = `${ADMIN_SETTING_BASE_URL_API}/NotificationEmailSetting`;
const NOTIFICATION_TOKEN_EXCHANGE_URL = `${getServerinfo().IdentityServerApiBase}/connect/token`;

//Timesheets
const TIMESHEETS_BASE_URL = getServerinfo().TimesheetApiServerBase + '/api/Timesheet';
const TIMESHEET_UPSERT_URL = TIMESHEETS_BASE_URL + '/upsert';
const TIMESHEET_DELETE_URL = TIMESHEETS_BASE_URL + '/delete';

const WEEKDAYS_CONSTANTS = {
    MONDAY: 'monday',
    TUESDAY: 'tuesday',
    WEDNESDAY: 'wednesday',
    THURSDAY: 'thursday',
    FRIDAY: 'friday',
    SATURDAY: 'saturday',
    SUNDAY: 'sunday'
};

export const HOURS = 'hours';
export const DATE = 'date';

export const USER_SELECTED_BROWSER_LANGUAGE = navigator.language;

export const DISPLAY_DATE_TIME_FORMATS = {
    FULL_YEAR: 'FULL_YEAR',
    SHORT_YEAR: 'SHORT_YEAR',
    SHORT_TIME: 'LT',
    LONG_TIME: 'LTS',
    NO_YEAR: 'NO_YEAR',
    NO_DAY: 'NO_DAY',
    _24_HOURS_SHORT_TIME: '_24_HOURS_SHORT_TIME',
    DAY_MONTH_SHORT_YEAR_SLASH: 'DAY_MONTH_SHORT_YEAR_SLASH'
};

const FILTER_SUBTYPES = {
    INPUT_NUMBER_HOURS: 'INPUT_NUMBER_HOURS'
};

const PREVIEW_ENTITY_KEY = 'previewEntity';
const ROLE_TEMPLATE_MODAL_ALIAS = 'roleTemplateModal';
const ROLE_GROUP_CREATION_MODAL_ALIAS = 'roleGroupCreationModal';

const DATE_UNITS = {
    DATE_UNIT_DAY: 'Day',
    DATE_UNIT_WEEK: 'Week',
    DATE_UNIT_MONTH: 'Month',
    DATE_UNIT_YEAR: 'Year'
};

const MAX_DATA_GRID_VISIBLE_RECORDS = 500;
const MAX_DATA_GRID_OPERATION_LOG_VISIBLE_RECORDS = 100;

const HIDDEN_FIELD_PLACEHOLDER_ALIAS = '#/HIDDEN_FIELD_PLACEHOLDER/#';
const NAN_ALIAS = 'NaN';

const SKILL_PREFERENCE_TYPES = {
    NO_PREFERENCE: 'NoPreference',
    PRIMARY_SKILL: 'PrimarySkill',
    SECONDARY_SKILL: 'SecondarySkill',
    PREFERRED: 'Preferred',
    LESS_PREFERRED: 'LessPreferred'
};

export {
    BaseURL,
    APIBaseURL,
    LRTBaseURL,
    TableAccessUrl,
    PlannerDataUrl,
    PlannerDataByViewUrl,
    PagingTableAccessUrl,
    PagingKeepAliveUrl,
    FilteredTableAccessUrl,
    NamedTableAccessUrl,
    RoleTemplateUrl,
    RoleTemplateGetUrl,
    InsertRoleTemplateUrl,
    RoleTemplateRenameUrl,
    ValidateTableAccessUrl,
    ValidateRichFieldsTableAccessUrl,
    IDTableAccessUrl,
    TableViewUrl,
    DBStructureUrl,
    DBStructureRichUrl,
    DBSctuctureFieldNamesUrl,
    DBSctuctureFieldNamesRichUrl,
    ClaimsUrl,
    PermissionsUrl,
    SkillStructureUrl,
    SkillSectionsUrl,
    SkillEntityTypesUrl,
    SkillCategoriesUrl,
    AllSkillCategoriesUrl,
    AllSkillSubCategoriesUrl,
    SkillsUrl,
    FilterSkillsUrl,
    SkillSectionByIdUrl,
    SkillByIdUrl,
    ResourceSkillsByIdUrl,
    ResourceSkillsRecommendationByIdUrl,
    ResourceSkillsApprovalsPendingByIdUrl,
    ResourceSkillsApprovalsHistoryByIdUrl,
    SkillPreferenceUrl,
    FilteredResourceSkillsUrl,
    ValidateCustomConditionUrl,
    AuthorizedResourceSkills,
    DiaryUrl,
    AdminSettingMenuOptionUrl,
    AdminSettingGetSettingValueUrl,
    AdminSettingUpdateSettingValueUrl,
    AdminSettingGetOptionURL,
    TalentProfileConfigApi,
    PageConfigApi,
    FunctionalAccessConsumerApi,
    ColourSchemeConsumerApi,
    BookingConflictConsumerApi,
    SECTION_CREATE_URL,
    SECTION_DUPLICATE_URL,
    SECTION_RENAME_URL,
    SECTION_DELETE_URL,
    LOCALE_EN,
    DAY_TYPES_CREATE,
    DAY_TYPES_DELETE,
    WORK_PATTERN_CREATE,
    WORK_PATTERN_DELETE,
    ADMIN_SETTING_BASE_URL_API,
    FILTER_TYPES,
    OPERATORS,
    CHARGE_RATE_URL,
    DIARY_CALENDAR,
    CHARGE_CODE_URL,
    CURRENCY_API_URL,
    FIELD_PROPERTY_URL,
    ENTITY_CONFIGURATION_URL,
    COLOURSCHEME_TABLEFIELD_URL,
    GET_ALL_COLOUR_SCHEME,
    GET_BY_ID_COLOUR_SCHEME,
    UPDATE_COLOUR_SCHEME,
    FIELD_PROPERTY_BYID_URL,
    FIELD_LOOKUP_VALUES_URL,
    KEY_CODES,
    KEYBOARD_EVENT_KEYS,
    MAX_INPUT_FIELD_LENGTH,
    INITIAL_FILTER_VALUE_COUNT,
    ADDITIONAL_FILTERS_COUNT,
    NODE_TYPE_CODES,
    WORKSPACES_URL,
    IDWorkspacesUrl,
    WORKSPACES_MOST_RECENTLY_USED_URL,
    IDWorkspaceUrl,
    SecurityProfilesUrl,
    SecurityProfileByIdUrl,
    FunctionalAccessRulesUrl,
    SecurityProfileFNAsUrl,
    SecurityProfileEAsUrl,
    ENTITY_ACCESS_RULES_ENTITIES_URL,
    EntityAccessRulesAccessLevelsUrl,
    EntityAccessRulesConditionsUrl,
    IDMostRecentlyUsedUrl,
    USER_MANAGEMENT,
    CONFLICTS_API_URL,
    TABLE_NAMES,
    REPEAT_BOOKING_OPERATION,
    EDIT_REPEAT_BOOKING_TYPE,
    COMPONENT_TYPES,
    WORKSPACE_ACCESS_TYPES,
    WORKSPACE_EDIT_RIGHTS,
    REPORT_BASE_URL,
    GET_REPORTS_DATA_URL,
    CREATE_REPORT_URL,
    SAVE_AS_REPORT_URL,
    DELETE_REPORT_URL,
    UPDATE_REPORT_URL,
    NOTIFY_REPORT_EVENT_URL,
    ENTITY_IMPORT,
    PRE_VALIDATE_IMPORT,
    GET_ENTITY_TEMPLATE,
    UPLOAD_ENTITY_IMPORT,
    JOIN_TYPES,
    FILTER_FIELD_NAMES,
    USER_SECURITYPROFILE_OPTION_URL,
    COMPANY_INFORMATION_API_URL,
    DEPARTMENT_API_URL,
    DIVISION_API_URL,
    IMAGE_EXTENTIONS,
    FILE_EXTENSIONS,
    MIME_TYPES,
    FUNCTIONAL_ACCESS_RULES_URL,
    ENTITY_ACCESS_RULES_URL,
    SECURITY_PROFILES_URL,
    SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL,
    AVATARS_URL,
    IDAvatarsUrl,
    HTTP_STATUS_CODES,
    ADMIN_FUNCTIONAL_RULES,
    ENTITY_CONFIGURATION_CONSUMER_URL,
    MAX_INPUT_DIGITS_COUNT,
    CurrencyConsumerApi,
    DEFAULT_CURRENCY_SYMBOL,
    PERCENT_SYMBOL,
    TOTAL_SUFFIX,
    HOURLY_LABEL_SUFFIX,
    PREFIX_FIELDS_TYPES,
    SUFFIX_FIELDS_TYPES,
    ALLOW_CUSTOM_FIELDS,
    LANGUAGES_TO_BE_SUPPORTED,
    ANTD5_LOCALES_MAP,
    DAYJS_LOCALES_MAP,
    ALLOW_ENTITY_FIELDS,
    AuditUrl,
    ACTORS,
    LICENSE_INFO,
    LICENSE_INFO_BY_KEY,
    SKILL_ENTITY_DETAILS_URL,
    AttachmentsConfigUrl,
    AttachmentsByEntityId,
    AttachmentById,
    LICENSE_KEYS_ADMIN_SETTINGS,
    COLOUR_SCHEME_PARAM_NAME,
    CRUD_OPERATIONS,
    NamedTableAccessBatchUrl,
    NamedTableAccessMergeUrl,
    COUNT_DISTINCTIONS,
    TOASTER_MESSAGE_PROPS,
    URL_PARAMS,
    MAX_SELECTED_YEAR,
    MIN_SELECTED_YEAR,
    GET_LICENSE_INFO,
    HTTP_RESPONSE_CODES,
    MAX_ENTITIES_SELECTION_COUNT,
    MAP_ENTITY_TITLES,
    EntityIdByFakeIdUrl,
    COMMAND_BAR_ACTION_ELEMENT_TYPES,
    RoleRequestGroupAccessUrl,
    RoleRequestFetchUrl,
    RoleRequestCrudUrl,
    RoleRequestPagingUrl,
    RoleRequestPagingKeepAliveUrl,
    RoleRequestAssignUrl,
    RoleRequestUnassignUrl,
    RoleRequestSuggestedResourcesUrl,
    RoleRequestAISuggestedResourcesUrl,
    RoleRequestTransitionUrl,
    RoleRequestWorkflowUrl,
    RoleRequestRoleByNameWorkflowUrl,
    RoleRequestRoleByByRequirementWorkflowUrl,
    RoleMarketplacePublishUrl,
    RoleRequestRoleApplyUrl,
    RoleRequestRoleWithdraw,
    RoleRequestGroupAPIUrl,
    LongRunningTaskBaseUrl,
    LongRunningTaskRunUrl,
    LongRunningTaskSummaryUrl,
    LongRunningTaskGetStatusByTypeUrl,
    LongRunningTaskGetStatusUrl,
    LongRunningTaskGetUserTaskUrl,
    OperationsLogPagingUrl,
    OperationsLogPagingKeepalive,
    OperationsLogCancelTask,
    ExpandedOperationsLogPagingURL,
    HISTORY_FIELD_EMPTY_VALUE_GUID,
    ROLE_AND_REQUEST_FEATURE_SWITCH,
    BATCH_CREATE_ENTITY_ERROR_PROMPT_KEY,
    BASE_FILTER_OPTIONS,
    DEFAULT_BASE_FILTER_OPTION,
    COLOUR_SCHEME_FEATURE_SWITCH,
    WORKSPACE_STRUCTURE_CONSTANTS,
    NOTIFICATION_BASE_URL,
    NOTIFICATION_HUB_URL,
    NOTIFICATION_TOKEN_EXCHANGE_URL,
    GET_REPORT_SETTINGS_DATA,
    UPDATE_REPORT_SETTINGS_DATA,
    REPORT_SETTINGS_URL,
    TIMESHEETS_BASE_URL,
    TIMESHEET_UPSERT_URL,
    TIMESHEET_DELETE_URL,
    WEEKDAYS_CONSTANTS,
    NOTIFICATION_HISTORY_URL,
    UNREAD_NOTIFICATION_COUNT_URL,
    FILTER_SUBTYPES,
    ROLEREQUEST_OPERATIONS,
    APPLY_FILTER_OPTIONS,
    APPLICATION_USER_RESOURCE_ROLE_GUID,
    DEFAULT_FILTER,
    DEFAULT_APPLY_FILTER_OPTION,
    NOTIFICATION_EMAIL_SETTINGS_URL,
    PREVIEW_ENTITY_KEY,
    ROLE_TEMPLATE_MODAL_ALIAS,
    ROLE_GROUP_CREATION_MODAL_ALIAS,
    DATE_UNITS,
    ROLEMARKETPLACE_OPERATIONS,
    TEMPLATE_OPERATIONS,
    MAX_DATA_GRID_VISIBLE_RECORDS,
    MAX_DATA_GRID_OPERATION_LOG_VISIBLE_RECORDS,
    SHOW_IN_VIEW_SELECTION_LIMIT,
    ACCEPT_COOKIE_POLICY_URL,
    COOKIE_POLICY_STATUS_URL,
    SERVICE_MANAGEMENT,
    SEND_SERVICE_PASSWORD_RESET,
    GetResetServicePassphraseUrl,
    MOUSE_BUTTON_CODES,
    HIDDEN_FIELD_PLACEHOLDER_ALIAS,
    NAN_ALIAS,
    MAINTENANCE_NOTIFICATION_URL,
    FetchSkillsUrl,
    SKILL_PREFERENCE_TYPES,
    SummaryWidgetSettingsUrl,
    FeatureManagementUrl,
    UnconfirmedBookingsNotificationSettings,
    StandardTaxonomiesUrl,
    MAX_SELECTED_CRITERIA_FIELDS,
    MAX_LABEL_LENGTH,
    REPORT_SERVICE_URL,
    DATASET_REFRESH_URL,
    DATASET_REFRESH_HISTORY_URL
};

export const lookUpOrderByDescription = [
    TABLE_NAMES.JOB,
    TABLE_NAMES.RESOURCE,
    TABLE_NAMES.CLIENT,
    'bookingtype',
    'chargerate',
    'chargetype',
    'jobstatus',
    'diarygroup',
    'securityprofile',
    'rolerequestgroup',
    'rolerequest',
    TABLE_NAMES.ROLEREQUESTSTATUS,
    TABLE_NAMES.EDUCATION_FIELD
];

export const NAV_MENU_POSITION_BOTTOM = 'bottom';
export const NAV_MENU_POSITION_MIDDLE = 'middle';
export const NAV_MENU_POSITION_TOP = 'top';


export const statusBadgeColors = {
    'In Progress': '#4DB04C',
    'Unconfirmed': 'gray', // "#F770EC" - dont' know if unconfirmed was meant to be pink?
    'Planned': 'gray',
    'Done': '#3072AB',
    'Cancelled': '#AB00FF'
};

export const statusBadgeIcons = {
    'In Progress': 'status-inprogress',
    'Unconfirmed': 'status-unconfirmed',
    'Planned': 'status-planned',
    'Done': 'status-done',
    'Cancelled': 'close'
};

export const statusBadgeIconsThemes = {
    'In Progress': 'filled',
    'Unconfirmed': 'filled',
    'Planned': 'filled',
    'Done': 'filled',
    'Cancelled': ''
};

export const suggestionPaneAliasKeys = {
    customPluralSuggestionAlias: 'suggestionsPlural',
    customSingularSuggestionAlias: 'suggestionsSingular'
};

export const SUMMARY_COMPONENT_CONST = {
    FIELD_DISPLAY_TYPE: {
        BOLD_FIELD: 'boldField',
        AVAILABILITY_FIELD: 'availabilityField',
        SUITABILITY_FIELD: 'suitabilityScoreField',
        MULTI_ROW_FIELD: 'multiRowField',
        COLOR_BADGES_FIELD: 'colorBadgesField',
        LAST_LOGIN_FIELD: 'lastLoginField'
    }
};

export const DECIMAL_NUMBER_ALIAS = 'Decimal number';
export const WHOLE_NUMBER_ALIAS = 'Whole Number';

export const FLOAT_FIXED_DIGITS_AFTER_DELIMITER = 2;
export const TOTAL_ROLE_GROUPS = 'job_totalrolegroups';
export const ROLEGROUPS_DEFAULT = 'roleGroups_default';

export const ROLE_ID = 'roleId';
export const ASSIGNEE_ID = 'assigneeId';

export const ROLE_ASSIGNEES_STATE_ALIAS = 'roleAssigneesState';

export const GO_TO_PAGE_ALIAS = 'Go to page:';

export const EMAIL_VALIDATION_REGEX = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export const DEFAULT_REFRESH_INTERVAL = 60 * 1000;

export const TAB_LABELS = {
    HISTORY: 'History'
};

export const STANDARD_COMPANY_DIARY = 'Standard company diary';
export const STANDARD_CHARGE_RATE = 'Standard rate';

export const OPEN_SCENARIO = 'openScenario';
export const PLANNER_TABLE_DATA_NAME = 'plannerTableDatas';
export const TABLE_DATA_NAME = 'tableDatas';

export const ARIA_ATTRIBUTES = {
    ARIA_LABEL: 'aria-label',
    ARIA_DESCRIBEDBY: 'aria-describedby',
    ARIA_LABELLEDBY: 'aria-labelledby',
    ARIA_REQUIRED: 'aria-required'
};

export const MAINTENANCE_STATUS_BANNER = 'maintenanceStatusBanner';
export const JOBS_PAGE_BOOKMARK_BANNER = 'jobsPageBookmarkBannerDismissed';

export const MINIMUM_PRECISION_STEP = 0.00001;

export const PHOENIX_TENANT = 'phoenixTenant';

export const TOKEN_EXPIRING_NOTIFICATION_TIME = 'tokenExpiringNotificationTime';

export const UNCATEGORISED = 'Uncategorised';

export const SKILL_CERTIFICATION = 'Skills & Certifications';

export const getLabelDescription = (labelName) => {
    switch (labelName.toLowerCase()) {
        case 'skill':
        case 'skills':
            return SKILL_CERTIFICATION;
        default: return labelName;
    }
};

// cMe consts
export const CME_PROFILING = 'cMeProfiling';
export const CME_TRAITS = 'C-Me Traits';

export const SYS_MAINTAINED_FIELD = 'sysMaintained';

export const WIDGET_TYPES = {
    ONE_BY_ONE: 'one-by-one',
    TWO_BY_ONE: 'two-by-one',
    TWO_BY_TWO: 'two-by-two'
};

export const WIDGET_CATEGORY_ALIAS = {
    PERSONAL: 'personal',
    BOOKINGS: 'bookings',
    JOBS: 'jobs',
    RESOURCES: 'resources',
    ROLES: 'roles'
};

export const WIDGET_CATEGORY_FNA_NAME = {
    BOOKINGS_WIDGETS: 'BookingWidgets',
    JOBS_WIDGETS: 'JobWidgets',
    RESOURCES_WIDGETS: 'ResourceWidgets',
    ROLES_WIDGETS: 'RoleWidgets'
};

export const WIDGET_ALIAS = {
    UPCOMING_BOOKINGS: 'upcomingBookings',
    UPCOMING_BOOKINGS_DETAILS: 'upcomingBookingsDetails',
    UPCOMING_BOOKINGS_DETAIL_TOTALS: 'upcomingBookingsDetailTotals',
    PLANNED_HOURS: 'plannedHours',
    UNASSIGNED_BOOKINGS: 'unassignedBookings',
    UNASSIGNED_BOOKINGS_DETAILS: 'unassignedBookingsDetails',
    UNASSIGNED_BOOKINGS_TOTALS: 'unassignedBookingsTotals',
    ONGOING_JOBS: 'ongoingJobs',
    JOBS_OVER_BUDGET_DETAILS: 'jobsOverBudgetDetails',
    UTILISATION: 'utilisation',
    CHARGEABLE_UTILISATION: 'chargeableUtilisation',
    PENDING_REQUESTS: 'pendingRequests',
    ACTION_REQUIRED: 'actionRequired',
    ACTION_REQUIRED_TOTALS: 'actionRequiredTotals',
    YOUR_REQUESTS: 'yourRequests'
};

export const WIDGET_CALC_FIELD_NAMES = {
    AVAILABLE_TIME: 'available_time',
    TOTAL_ONGOING_JOB: 'total_ongoing_job',
    TOTAL_UNASSIGNED_BOOKING: 'total_unassigned_booking',
    TOTAL_UNASSIGNED_JOB: 'total_unassigned_job',
    TOTAL_UPCOMING_BOOKING: 'total_upcoming_booking',
    TOTAL_PLANNED_HOURS: 'total_planned_hours',
    TOTAL_UNCONFIRMED_HOURS: 'total_unconfirmed_hours',
    CUMULATIVE_HOURS: 'cumulative_hours',
    TOTAL_ROLE_REQUEST_PENDING: 'total_rolerequest_pending',
    TOTAL_ROLE_REQEUST_JOBS: 'total_rolerequest_job',
    TOTAL_ROLE_REQUEST_HOURS: 'total_rolerequest_hours'
};

export const WIDGET_LIST_PAGING_SIZE = 20;

export const SUMMARY_SIDE_PANE_CONTENT_TYPE = {
    CONFIGURATION: 'configuration',
    WIDGET_DETAIL: 'widget_detail'
};

export const SKILL_EXPIRY_DAYS = {
    '90': 90,
    '30': 30,
    '7': 7,
    'No Notification': 0
};

// Feature Flags
export const FEATURE_FLAGS = {
    BOOKING_TYPE_NOTIFICATION: 'BookingTypeNotification',
    ROLE_CALC_IMPROVEMENTS: 'RoleCalcImprovements',
    DISABLE_BOOKING_BAR_INTERACTION: 'DisableBookingBarInteraction',
    BUDGET_HOURS_AND_REVENUE: 'BudgetHoursAndRevenue',
    LIST_PAGE_AND_BULK_UPDATE: 'EnableListPageAndBulkUpdate',
    SORT_CALC_FIEDLS: 'SortCalcFields',
    SKILL_NOTIFICATION: 'SkillNotification',
    RECOMMENDATIONS: 'Recommendations',
    SKILL_APPROVAL: 'SkillApproval',

    // Feature flag for skillFilterCascader in Talent Profile Page
    // If the feature is enabled the new skillFilterCasCader will be used else the old one will be used
    TALENT_PROFILE_PAGE_SKILLS_FILTER_CASCADER: 'TalentProfilePageSkillsFilterCascader',
    TALENT_PROFILE_PAGE_TRANSFORMED: 'TalentProfilePageTransformed',
    PLANNER_UNASSIGNED_ROLES_TOGGLE: 'PlannerUnassignedRolesToggle',
    HYBRID_TABLE: 'HybridTable',
    RETAIN_IMPORT_LIBRARY: 'RetainImportLibrary',
    RETAIN_IMPORT_LIBRARY_SEARCH_SKILL: 'RetainImportLibrarySearchSkill'
};

export const PAGE_VIEW_SETTINGS = 'viewSettings';
