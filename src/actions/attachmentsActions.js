import { API_SUFFIX, ATTACHMENTS } from './actionTypes';

const { SUCCESS, FAILURE } = API_SUFFIX;

export const loadAttachments = (alias, tableName, entityId) => {
    return {
        type: ATTACHMENTS.LOAD,
        payload: {
            alias,
            tableName,
            entityId,
            loading: true
        }
    };
};

export const loadAttachmentsSuccess = (
    moduleName,
    {
        alias,
        tableName,
        entityId
    },
    data
) => {
    return {
        type: ATTACHMENTS.LOAD_SUCCESS,
        payload: {
            alias,
            tableName,
            entityId,
            data,
            loading: false
        }
    };
};

export const loadAttachmentsError = () => {
    return {
        type: ATTACHMENTS.LOAD_ERROR
    };
};

export const insertAttachment = ({ alias, tableName, entityId, name, url, documentType, expiryDate, data }) => {
    return {
        type: ATTACHMENTS.INSERT,
        payload: {
            alias,
            tableName,
            entityId,
            name,
            url,
            documentType,
            expiryDate,
            data,
            loading: true
        }
    };
};

export const digestInsertAttachment = (alias, tableName, entityId, name, url, documentType, expiryDate) => {
    return {
        type: ATTACHMENTS.DIGEST_INSERT,
        payload: {
            alias,
            tableName,
            entityId,
            name,
            url,
            documentType,
            expiryDate
        }
    };
};

export const insertAttachmentSuccess = (
    moduleName,
    {
        alias,
        tableName,
        entityId,
        name,
        url,
        file = {}
    }
) => {
    return {
        type: ATTACHMENTS.INSERT_SUCCESS,
        payload: {
            alias: alias || moduleName,
            tableName,
            entityId,
            fileName: name,
            fileSize: file.size,
            data: file,
            url,
            loading: false
        }
    };
};

export const insertAttachmentError = (
    moduleName,
    {
        alias,
        tableName,
        entityId,
        name,
        url,
        size
    },
    serverError
) => {
    return {
        type: ATTACHMENTS.INSERT_ERROR,
        payload: {
            alias: alias || moduleName,
            tableName,
            entityId,
            fileName: name,
            fileSize: size,
            url,
            serverError,
            loading: false
        }
    };
};

export const getAttachmentData = (tableName, entityId, attachmentId, alias) => {
    return {
        type: ATTACHMENTS.GET_ATTACHMENT_DATA,
        payload: {
            tableName,
            entityId,
            attachmentId,
            alias
        }
    };
};

export const getAttachmentDataSuccess = () => {
    return {
        type: ATTACHMENTS.GET_ATTACHMENT_DATA_SUCCESS
    };
};

export const getAttachmentDataError = (moduleName, { alias, tableName, entityId, attachmentId }) => {
    return {
        type: ATTACHMENTS.GET_ATTACHMENT_DATA_ERROR,
        payload: {
            alias,
            tableName,
            attachmentId,
            entityId
        }
    };
};

export const deleteAttachment = (alias, tableName, entityId, attachmentId) => {
    return {
        type: ATTACHMENTS.DELETE,
        payload: {
            alias,
            tableName,
            attachmentId,
            entityId
        }
    };
};

export const deleteAttachmentSuccess = (
    moduleName,
    {
        alias,
        tableName,
        entityId,
        attachmentId
    }
) => {
    return {
        type: ATTACHMENTS.DELETE_SUCCESS,
        payload: {
            alias,
            tableName,
            entityId,
            attachmentId,
            loading: false
        }
    };
};

export const deleteAttachmentError = (
    moduleName,
    {
        alias,
        tableName,
        entityId,
        attachmentId
    }
) => {
    return {
        type: ATTACHMENTS.DELETE_ERROR,
        payload: {
            alias,
            tableName,
            entityId,
            attachmentId,
            loading: false
        }
    };
};

export const setAttachmentMessages = (alias, payload) => {
    return {
        type: `${ATTACHMENTS.SET_MESSAGES}_${alias}`,
        payload
    };
};

export const addAttachmentToUI = ({ alias, tableName, entityId, attachment, documentType, expiryDate }) => {
    return {
        type: ATTACHMENTS.UI_ADD,
        payload: {
            alias,
            tableName,
            entityId,
            attachment,
            documentType,
            expiryDate
        }
    };
};

export const removeAttachmentFromUI = (alias, tableName, entityId, attachmentId, attachmentUid) => {
    return {
        type: ATTACHMENTS.UI_REMOVE,
        payload: {
            alias,
            tableName,
            entityId,
            attachmentId,
            attachmentUid
        }
    };
};

export const discardAttachmentsChanges = (alias, entityId) => {
    return {
        type: ATTACHMENTS.DISCARD_CHANGES,
        payload: {
            alias,
            entityId
        }
    };
};

export const discardMultipleEntitiesAttachmentsChanges = (alias, entityIds) => {
    return {
        type: ATTACHMENTS.DISCARD_MULTIPLE_ENTITIES_CHANGES,
        payload: {
            alias,
            entityIds
        }
    };
};

/**
 * The action is used to initiate the change type of an attachment.
 */
export const moveAttachmentTo = (alias, tableName, entityId, attachmentId, moveDocumentTo) => {
    return {
        type: ATTACHMENTS.CHANGE_TYPE,
        payload: {
            alias,
            tableName,
            attachmentId,
            entityId,
            moveDocumentTo
        }
    };
};

/**
 * The action is used to handle success change type of an attachment.
 */
export const moveAttachmentToSuccess = (
    moduleName,
    {
        alias,
        tableName,
        entityId
    },
    data,
    attachmentId
) => ({
    type: `${ATTACHMENTS.CHANGE_ATTACHMENT_TYPE}_${SUCCESS}`,
    payload: {
        alias,
        tableName,
        entityId,
        loading: false,
        data,
        attachmentId
    }
});

/**
 * The action is used to handle the error change type of an attachment.
 */
export const moveAttachmentToError = () => {
    return {
        type: `${ATTACHMENTS.CHANGE_ATTACHMENT_TYPE}_${FAILURE}`
    };
};
