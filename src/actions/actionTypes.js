import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { TIMESHEETS_PAGE_ALIAS } from '../constants/timeSheetsPageConsts';
import { NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { MARKETPLACE_PAGE_ALIAS, PREVIEW_ENTITY_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';

export const LOAD_APPLICATION = 'LOAD_APPLICATION';
export const NAV_BAR_PAGE_CHANGE = 'NAV_BAR_PAGE_CHANGE';
export const NAV_BAR_COLLAPSE = 'NAV_BAR_COLLAPSE';
export const POPULATE_APPLICATION_PAGES = 'POPULATE_APPLICATION_PAGES';
export const LOAD_APPLICATION_PAGES = 'LOAD_APPLICATION_PAGES';
export const LOAD_APPLICATION_PAGES_SUCCESSFUL = 'LOAD_APPLICATION_PAGES_SUCCESSFUL';
export const OPEN_APPLICATION_HELP_PAGE = 'OPEN_APPLICATION_HELP_PAGE';
export const APPLICATION_HELP_PAGE_OPENED = 'APPLICATION_HELP_PAGE_OPENED';
export const PUSH_APPLICATION_URL = 'PUSH_APPLICATION_URL';
export const PUSH_APPLICATION_LOCATION = 'PUSH_APPLICATION_LOCATION';
export const REPLACE_APPLICATION_URL = 'REPLACE_APPLICATION_URL';
export const NAVIGATE_TO_PARENT_PAGE = 'NAVIGATE_TO_PARENT_PAGE';
export const MARKETPLACE_CALLER_MEETS_CRITERIA_FIELD = 'MARKETPLACE_CALLER_MEETS_CRITERIA_FIELD';

export const APP_STORE_ERROR = 'APP_STORE_ERROR';

export const UPDATE_ACTIVE_NOTIFICATION_TAB = 'UPDATE_ACTIVE_NOTIFICATION_TAB';

export const PAGE_ACTIONS = {
    OPEN: {
        talentProfilePage: 'OPEN_TALENT_PROFILE',
        plannerPage: 'OPEN_PLANNER_PAGE',
        jobsPage: 'OPEN_JOBS_PAGE',
        rolegroupListPage: 'OPEN_ROLEGROUPLIST_PAGE',
        rolegroupDetailsPage: 'OPEN_ROLEGROUPDETAILS_PAGE',
        [ROLE_INBOX_PAGE_ALIAS]: 'OPEN_ROLEINBOX_PAGE',
        [TIMESHEETS_PAGE_ALIAS]: 'OPEN_TIMESHEETS_PAGE',
        [NOTIFICATIONS_PAGE_ALIAS]: 'OPEN_NOTIFICATIONS_PAGE',
        [TIMESHEETS_PAGE_ALIAS]: 'OPEN_TIMESHEETS_PAGE',
        [MARKETPLACE_PAGE_ALIAS]: 'OPEN_MARKETPLACE_PAGE',
        [PREVIEW_ENTITY_PAGE_ALIAS]: 'OPEN_PREVIEW_ENTITY_PAGE',
        [TABLE_VIEW_PAGE_ALIAS]: 'OPEN_TABLE_VIEW_PAGE'
    },
    LOAD: {
        talentProfilePage: 'LOAD_TALENT_PROFILE',
        plannerPage: 'LOAD_PLANNER_PAGE',
        jobsPage: 'LOAD_JOBS_PAGE',
        rolegroupListPage: 'LOAD_ROLEGROUPLIST_PAGE',
        rolegroupDetailsPage: 'LOAD_ROLEGROUPDETAILS_PAGE',
        [ROLE_INBOX_PAGE_ALIAS]: 'LOAD_ROLEINBOX_PAGE',
        [TIMESHEETS_PAGE_ALIAS]: 'LOAD_TIMESHEETS_PAGE',
        [NOTIFICATIONS_PAGE_ALIAS]: 'LOAD_NOTIFICATIONS_PAGE',
        [TIMESHEETS_PAGE_ALIAS]: 'LOAD_TIMESHEETS_PAGE',
        [MARKETPLACE_PAGE_ALIAS]: 'LOAD_MARKETPLACE_PAGE',
        [PREVIEW_ENTITY_PAGE_ALIAS]: 'LOAD_PREVIEW_ENTITY_PAGE',
        [TABLE_VIEW_PAGE_ALIAS]: 'LOAD_TABLE_VIEW_PAGE'
    },
    CLOSE: {
        rolegroupListPage: 'CLOSE_ROLEGROUPLIST_PAGE',
        rolegroupDetailsPage: 'CLOSE_ROLEGROUPDETAILS_PAGE',
        [ROLE_INBOX_PAGE_ALIAS]: 'CLOSE_ROLEINBOX_PAGE',
        [MARKETPLACE_PAGE_ALIAS]: 'CLOSE_MARKETPLACE_PAGE',
        [TABLE_VIEW_PAGE_ALIAS]: 'CLOSE_TABLE_VIEW_PAGE'
    }
};

export const EXECUTE_INITIAL_LOAD_PLANNER_PAGE = 'EXECUTE_INITIAL_LOAD_PLANNER_PAGE';

export const PAGE_STATE_ACTIONS = {
    SET_PAGE_STATE: 'SET_PAGE_STATE',
    UPDATE_PAGE_PARAMS: 'UPDATE_PAGE_PARAMS',
    SET_PAGE_DATA_DIRTY: 'SET_PAGE_DATA_DIRTY',
    RESET_PAGE_PARAMS: 'RESET_PAGE_PARAMS'
};

export const SAVE_AS_TEMPLATE = 'SAVE_AS_TEMPLATE';
export const CREATE_ROLE_FROM_TEMPLATE = 'CREATE_ROLE_FROM_TEMPLATE';
export const LOAD_ROLE_TEMPLATES_ERROR = 'LOAD_ROLE_TEMPLATES_ERROR';
export const LOAD_ROLE_TEMPLATES_SUCCESS = 'LOAD_ROLE_TEMPLATES_SUCCESS';

export const LOAD_DATA = 'LOAD_DATA';
export const LOAD_DATA_SUCCESSFUL = 'LOAD_DATA_SUCCESSFUL';
export const LOAD_DATA_ERROR = 'LOAD_DATA_ERROR';

export const SET_PAGED_DATA_LOADING = 'SET_PAGED_DATA_LOADING';
export const PAGED_DATA_KEEP_ALIVE_SUCCESS = 'PAGED_DATA_KEEP_ALIVE_SUCCESS';
export const LOAD_PAGED_ACCESS_DATA = 'LOAD_PAGED_ACCESS_DATA';
export const LOAD_PAGED_ACCESS_DATA_SUCCESSFUL = 'LOAD_PAGED_ACCESS_DATA_SUCCESSFUL';
export const LOAD_PAGED_ACCESS_DATA_ERROR = 'LOAD_PAGED_ACCESS_DATA_ERROR';
export const PAGED_DATA_REGISTER_KEEP_ALIVE = 'PAGED_DATA_REGISTER_KEEP_ALIVE';
export const PAGED_DATA_UNREGISTER_KEEP_ALIVE = 'PAGED_DATA_UNREGISTER_KEEP_ALIVE';

export const LOAD_PLANNER_DATA = 'LOAD_PLANNER_DATA';
export const LOAD_PLANNER_DATA_SUCCESSFUL = 'LOAD_PLANNER_DATA_SUCCESSFUL';

export const SCROLL_PLANNER_DATA = 'SCROLL_PLANNER_DATA';
export const LOAD_PLANNER_DATA_PAGE = 'LOAD_PLANNER_DATA_PAGE';
export const OPEN_PLANNER_CACHED_PAGE = 'OPEN_PLANNER_CACHED_PAGE';
export const SET_PLANNER_DATA_PAGE = 'SET_PLANNER_DATA_PAGE';

export const RELOAD_PLANNER_DATA = 'RELOAD_PLANNER_DATA';

export const UPDATE_PLANNER_DATA = 'UPDATE_PLANNER_DATA';
export const PLANNER_DATA_UPDATED = 'PLANNER_DATA_UPDATED';
export const PLANNER_DATA_PATCH = 'PLANNER_DATA_PATCH';

export const PLANNER_DATA_LOADED = 'PLANNER_DATA_LOADED';
export const PLANNER_DATA_SCROLLED = 'PLANNER_DATA_SCROLLED';
export const PLANNER_DATA_PAGE_LOADED = 'PLANNER_DATA_PAGE_LOADED';

export const PLANNER_VER_SCROLL_POS_CHANGED = 'PLANNER_VER_SCROLL_POS_CHANGED';
export const PLANNER_VER_SCROLL_POS_RESET = 'PLANNER_VER_SCROLL_POS_RESET';

export const PLANNER_LOAD_RESOURCE_SKILLS = 'PLANNER_LOAD_RESOURCE_SKILLS';
export const PLANNER_LOAD_SKILL_PREFERENCES = 'PLANNER_LOAD_SKILL_PREFERENCES';

export const LOAD_TABLE_DATA = 'LOAD_TABLE_DATA';
export const DIGEST_LOAD_TABLE_DATA_SUCCESSFUL = 'DIGEST_LOAD_TABLE_DATA_SUCCESSFUL';
export const LOAD_TABLE_DATA_SUCCESSFUL = 'LOAD_TABLE_DATA_SUCCESSFUL';
export const LOAD_TABLE_DATA_ERROR = 'LOAD_TABLE_DATA_ERROR';

export const LOAD_MORE_TABLE_DATA_SUCCESSFUL = 'LOAD_MORE_TABLE_DATA_SUCCESSFUL';

export const LOAD_GROUPPED_TABLE_DATA = 'LOAD_GROUPPED_TABLE_DATA';
export const DIGEST_LOAD_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DIGEST_LOAD_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const LOAD_GROUPPED_TABLE_DATA_SUCCESSFUL = 'LOAD_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const LOAD_GROUPPED_TABLE_DATA_ERROR = 'LOAD_GROUPPED_TABLE_DATA_ERROR';

export const LOAD_MORE_GROUPPED_TABLE_DATA = 'LOAD_MORE_GROUPPED_TABLE_DATA';
export const DIGEST_LOAD_MORE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DIGEST_LOAD_MORE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const LOAD_MORE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'LOAD_MORE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const LOAD_MORE_GROUPPED_TABLE_DATA_ERROR = 'LOAD_MORE_GROUPPED_TABLE_DATA_ERROR';

export const LOAD_TABLE_DATA_ROW = 'LOAD_TABLE_DATA_ROW';
export const DIGEST_LOAD_TABLE_DATA_ROW_SUCCESSFUL = 'DIGEST_LOAD_TABLE_DATA_ROW_SUCCESSFUL';
export const LOAD_TABLE_DATA_ROW_SUCCESSFUL = 'LOAD_TABLE_DATA_ROW_SUCCESSFUL';
export const LOAD_TABLE_DATA_ROW_ERROR = 'LOAD_TABLE_DATA_ROW_ERROR';

export const LOAD_GROUPPED_TABLE_DATA_ROW = 'LOAD_GROUPPED_TABLE_DATA_ROW';
export const DIGEST_LOAD_GROUPPED_TABLE_DATA_ROW_SUCCESSFUL = 'DIGEST_LOAD_GROUPPED_TABLE_DATA_ROW_SUCCESSFUL';
export const LOAD_GROUPPED_TABLE_DATA_ROW_SUCCESSFUL = 'LOAD_GROUPPED_TABLE_DATA_ROW_SUCCESSFUL';
export const LOAD_GROUPPED_TABLE_DATA_ROW_ERROR = 'LOAD_GROUPPED_TABLE_DATA_ROW_ERROR';

export const LOAD_GROUPPED_TABLE_DATA_ROWS = 'LOAD_GROUPPED_TABLE_DATA_ROWS';
export const DIGEST_LOAD_GROUPPED_TABLE_DATA_ROWS_SUCCESSFUL = 'DIGEST_LOAD_GROUPPED_TABLE_DATA_ROWS_SUCCESSFUL';
export const LOAD_GROUPPED_TABLE_DATA_ROWS_SUCCESSFUL = 'LOAD_GROUPPED_TABLE_DATA_ROWS_SUCCESSFUL';
export const LOAD_GROUPPED_TABLE_DATA_ROWS_ERROR = 'LOAD_GROUPPED_TABLE_DATA_ROWS_ERROR';

export const INSERT_TABLE_DATA = 'INSERT_TABLE_DATA';
export const DIGEST_INSERT_TABLE_DATA_SUCCESSFUL = 'DIGEST_INSERT_TABLE_DATA_SUCCESSFUL';
export const INSERT_TABLE_DATA_SUCCESSFUL = 'INSERT_TABLE_DATA_SUCCESSFUL';
export const DIGEST_INSERT_TABLE_DATA_ERROR = 'DIGEST_INSERT_TABLE_DATA_ERROR';
export const INSERT_TABLE_DATA_ERROR = 'INSERT_TABLE_DATA_ERROR';

export const INSERT_GROUPPED_TABLE_DATA = 'INSERT_GROUPPED_TABLE_DATA';
export const DIGEST_INSERT_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DIGEST_INSERT_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const INSERT_GROUPPED_TABLE_DATA_SUCCESSFUL = 'INSERT_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const INSERT_GROUPPED_TABLE_DATA_ERROR = 'INSERT_GROUPPED_TABLE_DATA_ERROR';
export const INSERT_PLANNER_GROUPPED_TABLE_DATA_SUCCESSFUL = 'INSERT_PLANNER_GROUPPED_TABLE_DATA_SUCCESSFUL';

export const DELETE_TABLE_DATA = 'DELETE_TABLE_DATA';
export const DIGEST_DELETE_TABLE_DATA_SUCCESSFUL = 'DIGEST_DELETE_TABLE_DATA_SUCCESSFUL';
export const DELETE_TABLE_DATA_SUCCESSFUL = 'DELETE_TABLE_DATA_SUCCESSFUL';
export const DELETE_TABLE_DATA_ERROR = 'DELETE_TABLE_DATA_ERROR';

export const DELETE_GROUPPED_TABLE_DATA = 'DELETE_GROUPPED_TABLE_DATA';
export const DIGEST_DELETE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DIGEST_DELETE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const DELETE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DELETE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const DELETE_GROUPPED_TABLE_DATA_ERROR = 'DELETE_GROUPPED_TABLE_DATA_ERROR';

export const BATCH_DELETE_GROUPED_TABLE_DATA = 'BATCH_DELETE_GROUPED_TABLE_DATA';
export const BATCH_DIGEST_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL = 'BATCH_DIGEST_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL';
export const BATCH_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL = 'BATCH_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL';
export const BATCH_DELETE_GROUPED_TABLE_DATA_ERROR = 'BATCH_DELETE_GROUPED_TABLE_DATA_ERROR';

export const BATCH_DELETE_TABLE_DATA = 'BATCH_DELETE_TABLE_DATA';
export const BATCH_DIGEST_DELETE_TABLE_DATA_SUCCESSFUL = 'BATCH_DIGEST_DELETE_TABLE_DATA_SUCCESSFUL';
export const BATCH_DELETE_TABLE_DATA_SUCCESSFUL = 'BATCH_DELETE_TABLE_DATA_SUCCESSFUL';
export const BATCH_DELETE_TABLE_DATA_ERROR = 'BATCH_DELETE_TABLE_DATA_ERROR';

export const DELETE_PLANNER_BOOKING = 'DELETE_PLANNER_BOOKING';

export const UPDATE_TABLE_DATA = 'UPDATE_TABLE_DATA';
export const DIGEST_UPDATE_TABLE_DATA_SUCCESSFUL = 'DIGEST_UPDATE_TABLE_DATA_SUCCESSFUL';
export const UPDATE_TABLE_DATA_SUCCESSFUL = 'UPDATE_TABLE_DATA_SUCCESSFUL';
export const UPDATE_TABLE_DATA_ERROR = 'UPDATE_TABLE_DATA_ERROR';

export const UPDATE_GROUPPED_TABLE_DATA = 'UPDATE_GROUPPED_TABLE_DATA';
export const DIGEST_UPDATE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DIGEST_UPDATE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const UPDATE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'UPDATE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const UPDATE_GROUPPED_TABLE_DATA_ERROR = 'UPDATE_GROUPPED_TABLE_DATA_ERROR';

export const UPDATE_CELL_DATA_SUCCESSFUL = 'UPDATE_CELL_DATA_SUCCESSFUL';
export const UPDATE_CELL_DATA_ERROR = 'UPDATE_CELL_DATA_ERROR';

export const PATCH_TABLE_DATA = 'PATCH_TABLE_DATA';
export const DIGEST_PATCH_TABLE_DATA_SUCCESSFUL = 'DIGEST_PATCH_TABLE_DATA_SUCCESSFUL';
export const PATCH_TABLE_DATA_SUCCESSFUL = 'PATCH_TABLE_DATA_SUCCESSFUL';
export const PATCH_MULTIPLE_TABLE_DATA_SUCCESSFUL = 'PATCH_MULTIPLE_TABLE_DATA_SUCCESSFUL';
export const PATCH_TABLE_DATA_ERROR = 'PATCH_TABLE_DATA_ERROR';
export const PATCH_PLANNER_GROUPPED_TABLE_DATA_SUCCESSFUL = 'PATCH_PLANNER_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const BATCH_DIGEST_PATCH_TABLE_DATA_SUCCESSFUL = 'BATCH_DIGEST_PATCH_TABLE_DATA_SUCCESSFUL';
export const BATCH_PATCH_TABLE_DATA_ERROR = 'BATCH_PATCH_TABLE_DATA_ERROR';
export const BATCH_PATCH_TABLE_DATA_SUCCESSFUL = 'BATCH_PATCH_TABLE_DATA_SUCCESSFUL';

export const PATCH_GROUPPED_TABLE_DATA = 'PATCH_GROUPPED_TABLE_DATA';
export const DIGEST_PATCH_GROUPPED_TABLE_DATA_SUCCESSFUL = 'DIGEST_PATCH_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const PATCH_GROUPPED_TABLE_DATA_SUCCESSFUL = 'PATCH_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const PATCH_MULTIPLE_GROUPPED_TABLE_DATA_SUCCESSFUL = 'PATCH_MULTIPLE_GROUPPED_TABLE_DATA_SUCCESSFUL';
export const PATCH_GROUPPED_TABLE_DATA_ERROR = 'PATCH_GROUPPED_TABLE_DATA_ERROR';

export const BATCH_DIGEST_PATCH_PAGED_TABLE_DATA_SUCCESSFUL = 'BATCH_DIGEST_PATCH_PAGED_TABLE_DATA_SUCCESSFUL';
export const BATCH_PATCH_PAGED_TABLE_DATA_ERROR = 'BATCH_PATCH_PAGED_TABLE_DATA_ERROR';
export const BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL = 'BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL';

export const BATCH_PATCH_GROUPED_TABLE_DATA = 'BATCH_PATCH_GROUPED_TABLE_DATA';
export const BATCH_DIGEST_PATCH_GROUPED_TABLE_DATA_SUCCESSFUL = 'BATCH_DIGEST_PATCH_GROUPED_TABLE_DATA_SUCCESSFUL';
export const BATCH_PATCH_GROUPED_TABLE_DATA_SUCCESSFUL = 'BATCH_PATCH_GROUPED_TABLE_DATA_SUCCESSFUL';
export const BATCH_PATCH_GROUPED_TABLE_DATA_ERROR = 'BATCH_PATCH_GROUPED_TABLE_DATA_ERROR';

export const CLEAR_TABLE_DATA = 'CLEAR_TABLE_DATA';
export const PARK_TABLE_DATA = 'PARK_TABLE_DATA';
export const CLEAR_PAGED_DATA = 'CLEAR_PAGED_DATA';

export const LOAD_PAGED_RESULTS_DATA = 'LOAD_PAGED_RESULTS_DATA';
export const DIGEST_LOAD_PAGED_RESULTS_DATA_SUCCESSFUL = 'DIGEST_LOAD_PAGED_RESULTS_DATA_SUCCESSFUL';
export const LOAD_PAGED_RESULTS_DATA_SUCCESSFUL = 'LOAD_PAGED_RESULTS_DATE_SUCCESSFUL';
export const LOAD_PAGED_RESULTS_DATA_ERROR = 'LOAD_PAGED_RESULTS_DATA_ERROR';
export const CLEAR_CACHED_PAGING = 'CLEAR_CACHED_PAGING';

export const PAGED_DATA_PAGE_SIZE_CHANGED = 'PAGED_DATA_PAGE_SIZE_CHANGED';
export const LOAD_ADDITIONAL_COLUMN_DATA_SUCCESS = 'LOAD_ADDITIONAL_COLUMN_DATA_SUCCESS';

export const LOAD_VALUE = 'LOAD_VALUE';
export const LOAD_VALUE_SUCCESSFUL = 'LOAD_VALUE_SUCCESSFUL';
export const LOAD_VALUE_ERROR = 'LOAD_VALUE_ERROR';

export const CREATE_CLICK = 'CREATE_CLICK';
export const DO_CREATE_ENTRY = 'DO_CREATE_ENTRY';
export const CREATE_ENTRY_SUCCESSFUL = 'CREATE_ENTRY_SUCCESSFUL';
export const CREATE_ENTRY_ERROR = 'CREATE_ENTRY_ERROR';

export const UPDATE_ENTRY = 'UPDATE_ENTRY';
export const UPDATE_ENTRY_SUCCESSFUL = 'UPDATE_ENTRY_SUCCESSFUL';


export const DELETE_ENTRY = 'DELETE_ENTRY';
export const DELETE_ENTRY_SUCCESSFUL = 'DELETE_ENTRY_SUCCESSFUL';

export const PLANNER_CHILD_RESIZED = 'PLANNER_CHILD_RESIZED';

export const DIGEST_SELECT_WORKSPACE = 'DIGEST_SELECT_WORKSPACE';
export const SELECT_WORKSPACE = 'SELECT_WORKSPACE';
export const VALIDATE_SELECTED_WORKSPACE_ON_INIT_LOAD = 'VALIDATE_SELECTED_WORKSPACE_ON_INIT_LOAD';

export const SET_PLANNER_DATA_DIRTY = 'SET_PLANNER_DATA_DIRTY';

export const LOAD_WORKSPACE = 'LOAD_WORKSPACE';
export const LOAD_WORKSPACE_SUCCESSFUL = 'LOAD_WORKSPACE_SUCCESSFUL';
export const LOAD_WORKSPACES = 'LOAD_WORKSPACES';
export const LOAD_WORKSPACES_SUCCESSFUL = 'LOAD_WORKSPACES_SUCCESSFUL';

export const DIGEST_LOAD_WORKSPACES = 'DIGEST_LOAD_WORKSPACES';
export const DIGEST_LOAD_WORKSPACES_SUCCESSFUL = 'DIGEST_LOAD_WORKSPACES_SUCCESSFUL';
export const LOAD_WORKSPACES_SUCCESSFUL_SETUP = 'LOAD_WORKSPACES_SUCCESSFUL_SETUP';

export const LOAD_DEFAULT_WORKSPACE = 'LOAD_DEFAULT_WORKSPACE';

export const LOAD_COPY_WORKSPACE_TEMPLATE = 'LOAD_COPY_WORKSPACE_TEMPLATE';
export const LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL = 'LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL';

export const DELETE_WORKSPACE = 'DELETE_WORKSPACE';
export const DELETE_WORKSPACE_SUCCESS = 'DELETE_WORKSPACE_SUCCESS';

export const DIGEST_SET_CREATE_WORKSPACE_CHANGE = 'DIGEST_SET_CREATE_WORKSPACE_CHANGE';
export const SET_CREATE_WORKSPACE_CHANGE = 'SET_CREATE_WORKSPACE_CHANGE';

export const DIGEST_CREATE_WORKSPACE = 'DIGEST_CREATE_WORKSPACE';
export const DIGEST_CREATE_WORKSPACE_SUCCESSFUL = 'DIGEST_CREATE_WORKSPACE_SUCCESSFUL';

export const CREATE_WORKSPACE = 'CREATE_WORKSPACE';
export const DO_CREATE_WORKSPACE = 'DO_CREATE_WORKSPACE';
export const CREATE_WORKSPACE_SUCCESSFUL = 'CREATE_WORKSPACE_SUCCESSFUL';

export const RENAME_WORKSPACE = 'RENAME_WORKSPACE';
export const RENAME_WORKSPACE_SUCCESS = 'RENAME_WORKSPACE_SUCCESS';

export const REMOVE_CREATE_WORKSPACE_CHANGE = 'REMOVE_CREATE_WORKSPACE_CHANGE';

export const DIGEST_COPY_WORKSPACE = 'DIGEST_COPY_WORKSPACE';
export const DIGEST_COPY_WORKSPACE_SUCCESSFUL = 'DIGEST_COPY_WORKSPACE_SUCCESSFUL';

export const DIGEST_SAVE_AS_NEW_PLAN = 'DIGEST_SAVE_AS_NEW_PLAN';

export const MOVE_WORKSPACE = 'MOVE_WORKSPACE';
export const MOVE_WORKSPACE_SUCCESS = 'MOVE_WORKSPACE_SUCCESS';

export const DIGEST_WORKSPACE_STRUCTURE_CHANGE = 'DIGEST_WORKSPACE_STRUCTURE_CHANGE';
export const DELETE_WORKSPACE_FILTER_SETTINGS = 'DELETE_WORKSPACE_FILTER_SETTINGS';

export const SAVE_WORKSPACE_SETTINGS = 'SAVE_WORKSPACE_SETTINGS';
export const SAVE_WORKSPACE_SETTINGS_SUCCESSFUL = 'SAVE_WORKSPACE_SETTINGS_SUCCESSFUL';

export const STORE_WORKSPACE_VIEW_SETTINGS = 'STORE_WORKSPACE_VIEW_SETTINGS';
export const RESTORE_WORKSPACE_VIEW_SETTINGS = 'RESTORE_WORKSPACE_VIEW_SETTINGS';
export const CLEAR_STORED_WORKSPACE_VIEW_SETTINGS = 'CLEAR_STORED_WORKSPACE_VIEW_SETTINGS';

export const SAVE_WORKSPACE_IF_ANY_CHANGES = 'SAVE_WORKSPACE_IF_ANY_CHANGES';

export const MANAGE_WORKSPACES_SECTION = {
    ADD_WORKSPACE_EDIT_MODE: 'MANAGE_WORKSPACES_SECTION_ADD_WORKSPACE_EDIT_MODE',
    REMOVE_WORKSPACE_EDIT_MODE: 'MANAGE_WORKSPACES_SECTION_REMOVE_WORKSPACE_EDIT_MODE',
    SET_VISIBILITY: 'MANAGE_WORKSPACES_SECTION_SET_VISIBILITY'
};

export const ADD_MOST_RECENTLY_USED_WORKSPACE = 'ADD_MOST_RECENTLY_USED_WORKSPACE';
export const ADD_MOST_RECENTLY_USED_WORKSPACE_SUCCESSFUL = 'ADD_MOST_RECENTLY_USED_WORKSPACE_SUCCESSFUL';

export const GET_MOST_RECENTLY_USED_WORKSPACES = 'GET_MOST_RECENTLY_USED_WORKSPACES';
export const GET_MOST_RECENTLY_USED_WORKSPACES_SUCCESSFUL = 'GET_MOST_RECENTLY_USED_WORKSPACES_SUCCESSFUL';

export const DROP_MOST_RECENTLY_USED_WORKSPACE = 'DROP_MOST_RECENTLY_USED_WORKSPACE';
export const DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS = 'DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS';

export const ADD_FILTERS_MODEL = 'ADD_FILTERS_MODEL';
export const SET_FILTERS_MODEL = 'SET_FILTERS_MODEL';
export const ADD_TABLE_DATA_MODEL = 'ADD_TABLE_DATA_MODEL';
export const ADD_GROUPPED_TABLE_DATA_MODEL = 'ADD_GROUPPED_TABLE_DATA_MODEL';
export const ADD_PAGED_DATA_MODEL = 'ADD_PAGED_DATA_MODEL';
export const ADD_DETAILS_PANE_MODEL = 'ADD_DETAILS_PANE_MODEL';
export const ADD_PLANNER_DATA_MODEL = 'ADD_PLANNER_DATA_MODEL';

export const FILTERS_MODELS_LOADED_SUCCESS = 'FILTERS_MODELS_LOADED_SUCCESS';
export const TABLE_DATA_MODELS_LOADED_SUCCESS = 'TABLE_DATA_MODELS_LOADED_SUCCESS';
export const GROUPPED_TABLE_DATA_MODELS_LOADED_SUCCESS = 'GROUPPED_TABLE_DATA_MODELS_LOADED_SUCCESS';
export const PAGED_DATA_MODELS_LOADED_SUCCESS = 'PAGED_DATA_MODELS_LOADED_SUCCESS';
export const DETAILS_PANE_MODELS_LOADED_SUCCESS = 'DETAILS_PANE_MODELS_LOADED_SUCCESS';
export const PLANNER_DATA_MODELS_LOADED_SUCCESS = 'PLANNER_DATA_MODELS_LOADED_SUCCESS';

export const VIEW_SETTINGS_CHANGED = 'VIEW_SETTINGS_CHANGED';
export const DATE_RANGE_CHANGED = 'DATE_RANGE_CHANGED';
export const DATE_TOGGLE_OPTION_CHANGED = 'DATE_TOGGLE_OPTION_CHANGED';
export const FILTERS_SETTINGS_CHANGED = 'FILTERS_SETTINGS_CHANGED';
export const CLOSE_SORT_FLOATING_ACTION_BAR = 'CLOSE_SORT_FLOATING_ACTION_BAR';

export const HIDE_HISTORIC_RECORDS_CHANGED = 'HIDE_HISTORIC_RECORDS_CHANGED';
export const HIDE_FUTURE_RECORDS_CHANGED = 'HIDE_FUTURE_RECORDS_CHANGED';
export const HIDE_UNASSIGNED_ROWS_CHANGED = 'HIDE_UNASSIGNED_ROWS_CHANGED';
export const HIDE_ROLES_RECORDS_CHANGED = 'HIDE_ROLES_RECORDS_CHANGED';
export const HIDE_DRAFT_ROLES_RECORDS_CHANGED = 'HIDE_DRAFT_ROLES_RECORDS_CHANGED';
export const HIDE_REQUESTED_ROLES_RECORDS_CHANGED = 'HIDE_REQUESTED_ROLES_RECORDS_CHANGED';
export const HIDE_LIVE_BARS_CHANGED = 'HIDE_LIVE_BARS_CHANGED';
export const HIDE_WEEKENDS_CHANGED = 'HIDE_WEEKENDS_CHANGED';
export const HIDE_UNASSIGNED_ROLES_CHANGED = 'TOGGLE_HIDE_UNASSIGNED_ROLES_CHANGED';
export const HIDE_ROLES_BY_NAME_CHANGED = 'TOGGLE_HIDE_ROLES_BY_NAME_CHANGED';
export const HIDE_ROLES_BY_REQUIREMENTS_CHANGED = 'TOGGLE_HIDE_ROLES_BY_REQUIREMENTS_CHANGED';
export const HIDE_POTENTIAL_CONFLICTS_CHANGED = 'HIDE_POTENTIAL_CONFLICTS_CHANGED';
export const HIDE_JOB_TIMELINE_CHANGED = 'HIDE_JOB_TIMELINE_CHANGED';
export const HIDE_JOB_MILESTONES_CHANGED = 'HIDE_JOB_MILESTONES_CHANGED';
export const HIDE_INACTIVE_RESOURCES_CHANGED = 'HIDE_INACTIVE_RESOURCES_CHANGED';
export const HIDE_FIELDS_LABELS_ON_BARS_CHANGED = 'HIDE_FIELDS_LABELS_ON_BARS_CHANGED';
export const CLONE_COMMON_VIEW_HIDE_TOGGLES = 'CLONE_COMMON_VIEW_HIDE_TOGGLES';

export const CHANGE_COLOUR_SCHEME = 'CHANGE_COLOUR_SCHEME';

export const TABLE_COLUMN_RESIZED = 'TABLE_COLUMN_RESIZED';

export const PG_ROW_EXPAND_CHANGED = 'PG_ROW_EXPAND_CHANGED';
export const SELECT_EDITS = 'SELECT_EDITS';
export const SELECT_SINGLE_EDIT = 'SELECT_SINGLE_EDIT';
export const PERSIST_SELECTED_EDITS = 'PERSIST_SELECTED_EDITS';
export const EDIT_DATA = 'EDIT_DATA';
export const COLUMN_ORDER_CHANGED = 'COLUMN_ORDER_CHANGED';
export const UPDATE_TABLE_DATA_SELECTION = 'UPDATE_TABLE_DATA_SELECTION';
export const CHANGE_TABLE_DATA_SELECTION = 'CHANGE_TABLE_DATA_SELECTION';
export const PERSIST_TABLE_DATA_SELECTION = 'PERSIST_TABLE_DATA_SELECTION';

export const LOAD_TABLE_DATA_FIELD_INFOS = 'LOAD_TABLE_DATA_FIELD_INFOS';
export const LOAD_TABLE_DATA_FIELD_INFOS_SUCCESS = 'LOAD_TABLE_DATA_FIELD_INFOS_SUCCESS';

export const LOAD_TABLE_INFOS = 'LOAD_TABLE_INFOS';
export const LOAD_TABLE_INFOS_SUCCESS = 'LOAD_TABLE_INFOS_SUCCESS';
export const LOAD_TABLE_INFOS_ERROR = 'LOAD_TABLE_INFOS_ERROR';

export const LOAD_ENTITY_STRUCTURE = 'LOAD_ENTITY_STRUCTURE';
export const LOAD_ENTITY_STRUCTURE_SUCCESS = 'LOAD_ENTITY_STRUCTURE_SUCCESS';
export const LOAD_ENTITY_STRUCTURE_ERROR = 'LOAD_ENTITY_STRUCTURE_ERROR';

export const AUTOCOMPLETE_SEARCH = 'AUTOCOMPLETE_SEARCH';
export const AUTOCOMPLETE_INPUT = 'AUTOCOMPLETE_INPUT';
export const AUTOCOMPLETE_SEARCH_SUCCESS = 'AUTOCOMPLETE_SEARCH_SUCCESS';
export const AUTOCOMPLETE_RESOURCE_SEARCH_SUCCESS = 'AUTOCOMPLETE_RESOURCE_SEARCH_SUCCESS';
export const AUTOCOMPLETE_SEARCH_ERROR = 'AUTOCOMPLETE_SEARCH_ERROR';
export const AUTOCOMPLETE_DROPDOWN_VISIBILITY_CHANGE = 'AUTOCOMPLETE_DROPDOWN_VISIBILITY_CHANGE';
export const AUTOCOMPLETE_CLEAR_SUGGESTIONS = 'AUTOCOMPLETE_CLEAR_SUGGESTIONS';
export const AUTOCOMPLETE_CLEAR_AUTOCOMPLETE_FIELD = 'AUTOCOMPLETE_CLEAR_AUTOCOMPLETE_FIELD';
export const CLEAR_AUTOCOMPLETE_LOADED_STATE = 'CLEAR_AUTOCOMPLETE_LOADED_STATE';

export const SET_DETAILS_PANE_COLLAPSED = 'SET_DETAILS_PANE_COLLAPSED';
export const SET_DETAILS_PANE_SELECTED_TAB = 'SET_DETAILS_PANE_SELECTED_TAB';
export const SET_DETAILS_PANE_SELECTED_TAB_FOR_TABLE = 'SET_DETAILS_PANE_SELECTED_TAB_FOR_TABLE';
export const SET_DETAILS_PANE_HOVERED_TAB = 'SET_DETAILS_PANE_HOVERED_TAB';
export const DELETE_DETAILS_PANE_HOVERED_TAB = 'DELETE_DETAILS_PANE_HOVERED_TAB';
export const SET_DETAILS_PANE_TABS_ACTIVE = 'SET_DETAILS_PANE_TABS_ACTIVE';
export const SET_DETAILS_PANE_VISIBILITY = 'SET_DETAILS_PANE_VISIBILITY';
export const CG_CONTEXT_MENU_HIDE = 'CG_CONTEXT_MENU_HIDE';
export const CG_CONTEXT_MENU_SHOW = 'CG_CONTEXT_MENU_SHOW';
export const SET_SELECTED_TAB_IS_BATCHED = 'SET_SELECTED_TAB_IS_BATCHED';
export const SET_DETAILS_PANE_DISPLAY_TAB_DOT = 'SET_DETAILS_PANE_DISPLAY_TAB_DOT';
export const VALIDATE_DETAILS_PANE_TABS_ENABLED = 'VALIDATE_DETAILS_PANE_TABS_ENABLED';
export const SET_SHOW_SUB_TAB = 'SET_SHOW_SUB_TAB';

export const RL_SORT = 'RL_SORT';

export const SORT_PLANNER_DATA = 'SORT_PLANNER_DATA';
export const SORT_PLANNER_SUB_REC_DATA = 'SORT_PLANNER_SUB_REC_DATA';

export const TOOLTIP_SHOW = 'TOOLTIP_SHOW';
export const TOOLTIP_SHOW_REQUEST = 'TOOLTIP_SHOW_REQUEST';
export const TOOLTIP_SHOW_PERFORM = 'TOOLTIP_SHOW_PERFORM';

export const SET_TOOLTIP_CONTEXTUAL_MENU = 'SET_TOOLTIP_CONTEXTUAL_MENU';
export const BUILD_TOOLTIP_CONTEXTUAL_MENU = 'BUILD_TOOLTIP_CONTEXTUAL_MENU';
export const BUILD_TOOLTIP_CONTEXTUAL_MENU_FROM_EPIC = 'BUILD_TOOLTIP_CONTEXTUAL_MENU_FROM_EPIC';

export const TOOLTIP_HIDE = 'TOOLTIP_HIDE';

export const DELETE_BOOKING = 'DELETE_BOOKING';

export const PATCH_BOOKING = 'PATCH_BOOKING';

export const CREATE_BOOKING = 'CREATE_BOOKING';

export const SPLIT_BARS = 'SPLIT_BARS';
export const SPLIT_BARS_SUCCESS = 'SPLIT_BARS_SUCCESS';
export const SPLIT_BARS_ERROR = 'SPLIT_BARS_ERROR';

export const ADD_BAR_TO_CLIPBOARD = 'ADD_BAR_TO_CLIPBOARD';
export const LOAD_DATA_FOR_CLIPBOARD_ENTITY = 'LOAD_DATA_FOR_CLIPBOARD_ENTITY';
export const CLEAR_BAR_FROM_CLIPBOARD = 'CLEAR_BAR_FROM_CLIPBOARD';
export const ADD_CELL_VALUE_TO_CLIPBOARD = 'ADD_CELL_VALUE_TO_CLIPBOARD';
export const CLEAR_CELL_VALUE_FROM_CLIPBOARD = 'CLEAR_CELL_VALUE_FROM_CLIPBOARD';

export const BUILD_BAR_ADDITIONAL_DATA = 'BUILD_BAR_ADDITIONAL_DATA';
export const BUILD_BAR_COLOURS = 'BUILD_BAR_COLOURS';

export const PLANNER_SELECTION_CREATED = 'PLANNER_SELECTION_CREATED';
export const PLANNER_SELECTION_UPDATED = 'PLANNER_SELECTION_UPDATED';
export const CLEAR_PLANNER_SELECTION = 'CLEAR_PLANNER_SELECTION';
export const CLEAR_PLANNER_CHILD_ROW_SELECTION = 'CLEAR_PLANNER_CHILD_ROW_SELECTION';
export const CLEAR_PLANNER_SELECT_EDITS = 'CLEAR_PLANNER_SELECT_EDITS';

export const LOAD_DELETE_JOB_PROMPT_CONTEXT = 'LOAD_DELETE_JOB_PROMPT_CONTEXT';
export const LOAD_REMOVE_ROLE_PUBLICATION_PROMPT_CONTEXT = 'LOAD_REMOVE_ROLE_PUBLICATION_PROMPT_CONTEXT';

export const LOAD_DELETE_ROLE_GROUP_PROMPT_CONTEXT = 'LOAD_DELETE_ROLE_GROUP_PROMPT_CONTEXT';

export const ENTITY_WINDOW = {
    OPEN: 'ENTITY_WINDOW_OPEN',
    OPEN_REQUEST: 'ENTITY_WINDOW_OPEN_REQUEST',
    OPEN_FOR_MULTIPLE: 'ENTITY_WINDOW_OPEN_FOR_MULTIPLE',
    OPEN_FOR_MULTIPLE_REQUEST: 'ENTITY_WINDOW_OPEN_FOR_MULTIPLE_REQUEST',
    CLOSE: 'ENTITY_WINDOW_CLOSE',
    CLOSE_GLOBAL_CREATE_MODAL: 'CLOSE_GLOBAL_CREATE_MODAL',
    CLOSE_PREVIEW_PAGE: 'ENTITY_WINDOW_CLOSE_PREVIEW_PAGE',
    DISCARD_CHANGES: 'ENTITY_WINDOW_DISCARD_CHANGES',
    EDIT: 'ENTITY_WINDOW_EDIT',
    CREATE: 'ENTITY_WINDOW_CREATE',
    FIELD_CHANGED: 'ENTITY_WINDOW_FIELD_CHANGED',
    FIELD_CHANGED_CONTEXTUALLY: 'ENTITY_WINDOW_FIELD_CHANGED_CONTEXTUALLY',
    FIELD_CHANGED_FOR_ALL_ENTITIES: 'ENTITY_WINDOW_FIELD_CHANGED_FOR_ALL_ENTITIES',
    DISPLAY_VALUE_CHANGED: 'ENTITY_WINDOW_DISPLAY_VALUE_CHANGED',
    DISPLAY_VALUE_CHANGED_CONTEXTUALLY: 'ENTITY_WINDOW_DISPLAY_VALUE_CHANGED_CONTEXTUALLY',
    SUBMIT_INSERT: 'ENTITY_WINDOW_SUBMIT_INSERT',
    RESUBMIT_INSERT: 'ENTITY_WINDOW_RESUBMIT_INSERT',
    BATCH_SUBMIT_INSERT: 'ENTITY_WINDOW_BATCH_SUBMIT_INSERT',
    BATCH_SUBMIT_INSERT_REQUEST: 'ENTITY_WINDOW_BATCH_SUBMIT_INSERT_REQUEST',
    BATCH_RESUBMIT_INSERT: 'ENTITY_WINDOW_BATCH_RESUBMIT_INSERT',
    SUBMIT_INSERT_SUCCESSFUL: 'ENTITY_WINDOW_SUBMIT_INSERT_SUCCESSFUL',
    DIGEST_SUBMIT_INSERT_SUCCESSFUL: 'ENTITY_WINDOW_DIGEST_SUBMIT_INSERT_SUCCESSFUL',
    SUBMIT_DELETE: 'ENTITY_WINDOW_SUBMIT_DELETE',
    SUBMIT_DELETE_SUCCESSFUL: 'ENTITY_WINDOW_SUBMIT_DELETE_SUCCESSFUL',
    DIGEST_SUBMIT_DELETE_SUCCESSFUL: 'ENTITY_WINDOW_DIGEST_SUBMIT_DELETE_SUCCESSFUL',
    SUBMUT_BATCH_DELETE_ENTITIES: 'ENTITY_WINDOW_SUBMUT_BATCH_DELETE_ENTITIES',
    SUBMIT_UPDATE: 'ENTITY_WINDOW_SUBMIT_UPDATE',
    BATCH_SUBMIT_UPDATE: 'ENTITY_WINDOW_BATCH_SUBMIT_UPDATE',
    SUBMIT_UPDATE_SUCCESSFUL: 'ENTITY_WINDOW_SUBMIT_UPDATE_SUCCESSFUL',
    SUBMIT_UPDATE_ERROR: 'ENTITY_WINDOW_SUBMIT_UPDATE_ERROR',
    DIGEST_SUBMIT_UPDATE_SUCCESSFUL: 'ENTITY_WINDOW_DIGEST_SUBMIT_UPDATE_SUCCESSFUL',
    SUBMIT_FAIL: 'ENTITY_WINDOW_SUBMIT_FAIL',
    CONTEXTUAL_EDIT_START: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_START',
    CONTEXTUAL_EDIT_CANCEL: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_CANCEL',
    CONTEXTUAL_EDIT_APPLY: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_APPLY',
    CONTEXTUAL_EDIT_APPLY_SUBMIT: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_APPLY_SUBMIT',
    SET_BATCH_FIELDS: 'SET_BATCH_FIELDS',
    SUBMIT_INSERT_ROLEREQUEST_ATTEMPT: 'ENTITY_WINDOW_SUBMIT_INSERT_ROLEREQUEST_ATTEMPT',
    SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT: 'ENTITY_WINDOW_SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT',

    SUBMIT_INSERT_TABLE_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_INSERT_TABLE_DATA_SUCCESS',
    SUBMIT_INSERT_BATCH_TABLE_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_INSERT_BATCH_TABLE_DATA_SUCCESS',
    SUBMIT_INSERT_BATCH_TABLE_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_INSERT_BATCH_TABLE_DATA_ERROR',
    SUBMIT_INSERT_TABLE_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_INSERT_TABLE_DATA_ERROR',
    SUBMIT_INSERT_GROUPPED_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_INSERT_GROUPPED_DATA_SUCCESS',
    SUBMIT_INSERT_GROUPPED_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_INSERT_GROUPPED_DATA_ERROR',
    SUBMIT_INSERT_GROUPPED_DATA_BATCH_SUCCESS: 'ENTITY_WINDOW_SUBMIT_INSERT_GROUPPED_DATA_BATCH_SUCCESS',
    SUBMIT_INSERT_GROUPPED_DATA_BATCH_ERROR: 'ENTITY_WINDOW_SUBMIT_INSERT_GROUPPED_DATA_BATCH_ERROR',
    SUBMIT_UPDATE_PAGED_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_UPDATE_PAGED_DATA_SUCCESS',
    SUBMIT_BATCH_UPDATE_PAGED_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_BATCH_UPDATE_PAGED_DATA_SUCCESS',
    SUBMIT_BATCH_UPDATE_PAGED_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_BATCH_UPDATE_PAGED_DATA_ERROR',
    SUBMIT_UPDATE_PAGED_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_UPDATE_PAGED_DATA_ERROR',
    SUBMIT_UPDATE_TABLE_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_UPDATE_TABLE_DATA_SUCCESS',
    SUBMIT_UPDATE_TABLE_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_UPDATE_TABLE_DATA_ERROR',
    SUBMIT_UPDATE_BATCH_REQUEST: 'ENTITY_WINDOW_SUBMIT_UPDATE_BATCH_REQUEST',
    SUBMIT_UPDATE_BATCH_REQUEST_SUCCESS: 'ENTITY_WINDOW_SUBMIT_UPDATE_BATCH_REQUEST_SUCCESS',
    SUBMIT_UPDATE_BATCH_REQUEST_ERROR: 'ENTITY_WINDOW_SUBMIT_UPDATE_BATCH_REQUEST_ERROR',
    SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS',
    SUBMIT_UPDATE_GROUPPED_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_UPDATE_GROUPPED_DATA_ERROR',
    SUBMIT_DELETE_TABLE_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_DELETE_TABLE_DATA_SUCCESS',
    SUBMIT_DELETE_TABLE_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_DELETE_TABLE_DATA_ERROR',
    SUBMIT_DELETE_GROUPPED_TABLE_DATA_SUCCESS: 'ENTITY_WINDOW_SUBMIT_DELETE_GROUPPED_TABLE_DATA_SUCCESS',
    SUBMIT_DELETE_GROUPPED_TABLE_DATA_ERROR: 'ENTITY_WINDOW_SUBMIT_DELETE_GROUPPED_TABLE_DATA_ERROR',
    CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS',
    CONTEXTUAL_EDIT_PAGED_DATA_ERROR: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_PAGED_DATA_ERROR',
    CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS',
    CONTEXTUAL_EDIT_TABLE_DATA_ERROR: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_TABLE_DATA_ERROR',
    CONTEXTUAL_EDIT_GROUPPED_DATA_SUCCESS: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_GROUPPED_DATA_SUCCESS',
    CONTEXTUAL_EDIT_GROUPPED_DATA_ERROR: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_GROUPPED_DATA_ERROR',

    SET_FORM_ERROR_MESSAGES: 'ENTITY_WINDOW_SET_FORM_ERROR_MESSAGES',
    SET_FIELD_ERRORS: 'ENTITY_WINDOW_SET_FIELD_ERRORS',
    SET_FIELD_ERRORS_FOR_ALL_ENTITIES: 'ENTITY_WINDOW_SET_FIELD_ERRORS_FOR_ALL_ENTITIES',
    SET_FORM_ERROR: 'ENTITY_WINDOW_SET_FORM_ERROR',
    CONTEXTUAL_EDIT_APPLY_SUCCESS: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_APPLY_SUCCESS',
    CONTEXTUAL_EDIT_APPLY_ERROR: 'ENTITY_WINDOW_CONTEXTUAL_EDIT_APPLY_ERROR',

    UPDATE_ENTITY: 'ENTITY_WINDOW_UPDATE_ENTITY',
    UPDATE_MULTIPLE_ENTITIES: 'ENTITY_WINDOW_UPDATE_MULTIPLE_ENTITIES',
    UPDATE_ALL_ENTITIES: 'ENTITY_WINDOW_UPDATE_ALL_ENTITIES',
    DISCARD_BUDGET_CHANGES: 'DISCARD_BUDGET_CHANGES',

    DRIVEN_FIELD_ACTIONS: {
        SET_BOOKING_WORK_NONWORK_DAYS_EXPLANATION: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_BOOKING_WORK_NONWORK_DAYS_EXPLANATION',
        SET_BOOKING_STATUS_EXPLANATION: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_BOOKING_STATUS_EXPLANATION',
        UPDATE_BAR_DATE_RANGE_VALUE: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BAR_DATE_RANGE_VALUE',
        CLEAR_BOOKING_WORK_NONWORK_DAYS_EXPLANATION: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.CLEAR_BOOKING_WORK_NONWORK_DAYS_EXPLANATION',
        SET_TIME_ALLOCATION_READ_ONLY_EXPLANATION: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_TIME_ALLOCATION_READ_ONLY_EXPLANATION',
        SET_TIME_ALLOCATION_FTE_EXPLANATION: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_TIME_ALLOCATION_FTE_EXPLANATION',
        BAR_MULTIPLE_RESOURCES_CHANGED: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.BAR_MULTIPLE_RESOURCES_CHANGED',
        LOAD_BOOKING_MULTIPLE_RESOURCES_AVATARS: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.LOAD_BOOKING_MULTIPLE_RESOURCES_AVATARS',
        SET_BAR_RESOURCE_DESCRIPTION_EXPLANATION: 'ENTITY_WINDOW_SET_BAR_RESOURCE_DESCRIPTION_EXPLANATION',
        UPDATE_BAR_TIME_ALLOCATION_VALUES: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BAR_TIME_ALLOCATION_VALUES',
        FIELD_CHANGED: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.FIELD_CHANGED',
        FIELD_CHANGED_CONTEXTUALLY: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.FIELD_CHANGED_CONTEXTUALLY',
        UPDATE_BAR_CHARGE_RATE_VALUES: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BAR_CHARGE_RATE_VALUES',
        LOAD_RESOURCE_CHARGE_RATE_CURRENT_VALUE: 'LOAD_RESOURCE_CHARGE_RATE_CURRENT_VALUE',
        UPDATE_RESOURCE_GUIDS: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_RESOURCE_GUIDS',
        ROLE_CREATION_UPDATE_RESOURCE_GUIDS: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.ROLE_CREATION_UPDATE_RESOURCE_GUIDS',
        GET_ENTITY_AUDIT_DATA: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.GET_ENTITY_AUDIT_DATA',
        UPDATE_FIELD_TABLE_DATA: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_FIELD_TABLE_DATA',
        UPDATE_BUDGET_SECTION: 'UPDATE_BUDGET_SECTION',
        UPDATE_BUDGET_SECTION_FIELDS_VISIBILITY: 'UPDATE_BUDGET_SECTION_FIELDS_VISIBILITY',
        UPDATE_FIELD_ON_CONTEXTUAL_EDIT: 'UPDATE_FIELD_ON_CONTEXTUAL_EDIT',
        UPDATE_CRITERIA_BUDGET_SECTION_FIELDS_VISIBILITY: 'UPDATE_CRITERIA_BUDGET_SECTION_FIELDS_VISIBILITY',
        REFRESH_RESOURCE_DP: 'REFRESH_RESOURCE_DP',
        VALIDATE: 'VALIDATE_ACTIVE_ENTITY',
        UPDATE_OVERLAPPING_BOOKING: 'UPDATE_OVERLAPPING_BOOKING',
        GET_RESOURCE_TINY_AVATAR: 'GET_RESOURCE_TINY_AVATAR',
        VALIDATE_MULTIPLE_ENTITIES: 'VALIDATE_MULTIPLE_ENTITIES',
        SET_REJECT_REASONS_EXPLANATION: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_REJECT_REASONS_EXPLANATION',
        UPDATE_ROLE_RESOURCE_MESSAGE_WARNING: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_ROLE_RESOURCE_MESSAGE_WARNING',
        SET_SYSTEM_INFO: 'SET_SYSTEM_INFO',
        UPDATE_PAGE_PARAMS: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_PAGE_PARAMS',
        CLEAR_ROLEREQUEST_GROUP_FIELD_VALUE: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.CLEAR_ROLEREQUEST_GROUP_FIELD_VALUE',
        UPDATE_ENTITY_WINDOW_SECTION_MESSAGES: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_ENTITY_WINDOW_SECTION_MESSAGES',
        UPDATE_ROLE_ENTITY_WINDOW_SECTION_MESSAGES: 'ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_ROLE_ENTITY_WINDOW_SECTION_MESSAGES',
        UPDATE_ASSIGNEES_BUDGET_SECTION: 'UPDATE_ASSIGNEES_BUDGET_SECTION',
        CHANGE_ASSIGNEE_BUDGET_FIELD: 'CHANGE_ASSIGNEE_BUDGET_FIELD',
        VALIDATE_CRITERIA_BUDGET_SECTION: 'VALIDATE_CRITERIA_BUDGET_SECTION',
        VALIDATE_CRITERIA_ROLE_ASSIGNEE_FIELD: 'VALIDATE_CRITERIA_ROLE_ASSIGNEE_FIELD',
        CALCULATE_CRITERIA_ROLE_TOTALS: 'CALCULATE_CRITERIA_ROLE_TOTALS',
        RESET_CRITERIA_ROLE_TOTALS: 'RESET_CRITERIA_ROLE_TOTALS',
        UPDATE_CRITERIA_CHARGE_RATE_VALUE_EXPLANATION: 'UPDATE_CRITERIA_CHARGE_RATE_VALUE_EXPLANATION',
        UPDATE_JOB_MILESTONE_FIELD_VALUE: 'UPDATE_JOB_MILESTONE_FIELD_VALUE',
        UPDATE_JOB_TOTAL_HEALT_VALUE: 'UPDATE_JOB_TOTAL_HEALT_VALUE',
        VALIDATE_JOB_TOTAL_HEALT_VALUE: 'VALIDATE_JOB_TOTAL_HEALT_VALUE',
        VALIDATE_MULTIPLE_JOB_TOTAL_HEALT_VALUES: 'VALIDATE_MULTIPLE_JOB_TOTAL_HEALT_VALUES',
        GET_BOOKING_JOB_HOURS_OVERBUDGET: 'GET_BOOKING_JOB_HOURS_OVERBUDGET',
        UPDATE_BOOKING_JOB_HOURS_OVERBUDGET: 'UPDATE_BOOKING_JOB_HOURS_OVERBUDGET'
    },
    POPULATE_ASSIGNEE_CHANGED_BUDGET_DATA: 'POPULATE_ASSIGNEE_CHANGED_BUDGET_DATA',
    CHANGE_CRITERIA_ROLE_CHARGEMODE: 'CHANGE_CRITERIA_ROLE_CHARGEMODE',
    LOAD_FIELD_VALUES: 'ENTITY_WINDOW_LOAD_FIELD_VALUES',
    HIGHLIGHT_FIELD: 'ENTITY_WINDOW_HIGHLIGHT_FIELD',
    CLEAR_HIGHLIGHT_FIELD: 'ENTITY_WINDOW_CLEAR_HIGHLIGHT_FIELD',
    CLEAR_HIGHLIGHT_FIELD_REQUEST: 'ENTITY_WINDOW_CLEAR_HIGHLIGHT_FIELD_REQUEST',
    ADD_NEW_SECTION: 'ENTITY_WINDOW_ADD_NEW_SECTION',
    UPDATE_ENTITY_TEMPLATE: 'ENTITY_WINDOW_UPDATE_ENTITY_TEMPLATE',
    EXTEND_SECTION: 'EXTEND_SECTION',
    ADD_DYNAMIC_CONFIG: 'ENTITY_WINDOW_ADD_DYNAMIC_CONFIG',
    GET_DYNAMIC_CONFIG: 'ENTITY_WINDOW_GET_DYNAMIC_CONFIG',
    SET_ACTIVE_TAB: 'ENTITY_WINDOW_SET_ACTIVE_TAB',
    SET_ACTIVE_ENTITY: 'ENTITY_WINDOW_SET_ACTIVE_ENTITY',
    LAZY_LOAD_ACTIVE_ENTITY: 'ENTITY_WINDOW_LAZY_LOAD_ACTIVE_ENTITY',
    REMOVE_BATCH_ENTITY: 'ENTITY_WINDOW_REMOVE_BATCH_ENTITY',
    EXPAND_COLLAPSE_SECTION: 'ENTITY_WINDOW_EXAPAND_COLLAPSE_SECTION',
    VALIDATE: 'ENTITY_WINDOW_VALIDATE',
    HIDE_SECTION_CONTENT: 'ENTITY_WINDOW_HIDE_SECTION_CONTENT',
    SET_MESSAGES: 'ENTITY_WINDOW_SET_MESSAGES',
    UPDATE_MESSAGES: 'ENTITY_WINDOW_UPDATE_MESSAGES',
    SET_UI_DISABLED_STATUS: 'ENTITY_WINDOW_SET_UI_DISABLED_STATUS',
    REVERT_UI_CHANGES_FOR_ALL_ENTITIES: 'ENTITY_WINDOW_REVERT_UI_CHANGES_FOR_ALL_ENTITIES',
    CHANGE_READ_ONLY_FIELDS_VISIBILITY: 'ENTITY_WINDOW_CHANGE_READ_ONLY_FIELDS_VISIBILITY',

    ADD_NEW_ENTITY: 'ADD_NEW_ENTITY',
    ADD_NEW_ENTITY_REQUEST: 'ADD_NEW_ENTITY_REQUEST',
    DELETE_ENTITY: 'DELETE_ENTITY',
    DUPLICATE_ENTITY: 'DUPLICATE_ENTITY',
    DISCARD_BATCH_ENTITY_CHANGES: 'DISCARD_BATCH_ENTITY_CHANGES',
    UPDATE_ENTITY_FIELD: 'UPDATE_ENTITY_FIELD',
    ARCHIVE_CRITERIA_ROLE: 'ARCHIVE_CRITERIA_ROLE',
    UPDATE_BATCH_IDS: 'UPDATE_BATCH_IDS',
    WITHDRAW_ROLE_APPLICATION: 'WITHDRAW_ROLE_APPLICATION',
    WITHDRAW_ROLE_APPLICATION_SUBMIT: 'WITHDRAW_ROLE_APPLICATION_SUBMIT',
    SET_CUSTOM_SETTINGS: 'ENTITY_WINDOW_SET_CUSTOM_SETTINGS'
};

export const UPDATE_ROLEREQUEST_STATUS = 'UPDATE_ROLEREQUEST_STATUS';

export const CREATE_ROLE_GROUP = 'CREATE_ROLE_GROUP';
export const OPEN_CREATE_ROLE_GROUP = 'OPEN_CREATE_ROLE_GROUP';
export const EDIT_ROLE_GROUP = 'EDIT_ROLE_GROUP';

export const PUBLISH_ROLE = 'PUBLISH_ROLE';
export const PUBLISH_ROLE_ERROR = 'PUBLISH_ROLE_ERROR';

export const EDIT_ROLE_PUBLICATION = 'EDIT_ROLE_PUBLICATION';
export const EDIT_ROLE_PUBLICATION_ERROR = 'EDIT_ROLE_PUBLICATION_ERROR';

export const REMOVE_ROLE_PUBLICATION = 'REMOVE_ROLE_PUBLICATION';
export const REMOVE_ROLE_PUBLICATION_ERROR = 'REMOVE_ROLE_PUBLICATION_ERROR';

export const INSERT_ROLEREQUEST_BOOKING_REQUEST_ATTEMPT = 'INSERT_ROLEREQUEST_BOOKING_REQUEST_ATTEMPT';
export const ATTEMPT_ROLEREQUEST_BOOKING_REQUEST_PROGRESS_TO_REQUESTED = 'ATTEMPT_ROLEREQUEST_BOOKING_REQUEST_PROGRESS_TO_REQUESTED';

export const SUBMIT_INSERT_ROLEREQUEST_ATTEMPT = 'SUBMIT_INSERT_ROLEREQUEST_ATTEMPT';
export const SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT = 'SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT';

export const DIGEST_CHANGE_ROLE_ASSIGNEES_SUCCESS = 'DIGEST_CHANGE_ROLE_ASSIGNEES_SUCCESS';

export const OPEN_MANAGE_BUDGET_EW = 'OPEN_MANAGE_BUDGET_EW';

export const OPEN_PUBLISH_ROLE_EW = 'OPEN_PUBLISH_ROLE_EW';
export const OPEN_EDIT_ROLE_PUBLICATION_EW = 'OPEN_EDIT_ROLE_PUBLICATION_EW';

export const SET_CRITERIA_ROLE_BUDGET_ERRORS = 'SET_CRITERIA_ROLE_BUDGET_ERRORS';
export const SET_ASSIGNEE_FIELD_VALUE = 'SET_ASSIGNEE_FIELD_VALUE';
export const SET_ASSIGNEE_BUDGET_FIELD_ERRORS = 'SET_ASSIGNEE_BUDGET_FIELD_ERRORS';
export const CLEAR_ASSIGNEE_BUDGET_FIELD_ERRORS = 'CLEAR_ASSIGNEE_BUDGET_FIELD_ERRORS';

export const LOAD_ADMINSETTING_SUBNAVIGATION = 'LOAD_ADMINSETTING_SUBNAVIGATION';
export const LOAD_ADMINSETTING_SUBNAVIGATION_SUCCESSFUL = 'LOAD_ADMINSETTING_SUBNAVIGATION_SUCCESSFUL';

export const LOAD_ADMINSETTING_DATA = 'LOAD_ADMINSETTING_DATA';
export const LOAD_ADMINSETTING_DATA_SUCCESSFUL = 'LOAD_ADMINSETTING_DATA_SUCCESSFUL';

export const UPDATE_ADMINSETTING_DATA = 'UPDATE_ADMINSETTING_DATA';
export const UPDATE_ADMINSETTING_DATA_SUCCESSFUL = 'UPDATE_ADMINSETTING_DATA_SUCCESSFUL';

export const CHANGE_STATUS = 'CHANGE_STATUS';
export const UPDATE_APPLICATION_NAVIGATION = 'UPDATE_APPLICATION_NAVIGATION';

export const PATCH_PAGED_DATA = 'PATCH_PAGED_DATA';
export const DIGEST_PATCH_PAGED_DATA_SUCCESSFUL = 'DIGEST_PATCH_PAGED_DATA_SUCCESSFUL';
export const PATCH_PAGED_DATA_SUCCESSFUL = 'PATCH_PAGED_DATA_SUCCESSFUL';
export const PATCH_MULTIPLE_PAGED_DATA_SUCCESSFUL = 'PATCH_MULTIPLE_PAGED_DATA_SUCCESSFUL';
export const PATCH_PAGED_DATA_ERROR = 'PATCH_PAGED_DATA_ERROR';
export const PATCH_PAGED_DATA_SILENT = 'PATCH_PAGED_DATA_SILENT';

export const TOGGLE_ADMIN_SL_SORT_ORDER = 'TOGGLE_ADMIN_SL_SORT_ORDER';
export const DELETE_SECTION = 'DELETE_SECTION';
export const RENAME_SECTION = 'RENAME_SECTION';
export const DUPLICATE_SECTION = 'DUPLICATE_SECTION';
export const CREATE_SECTION = 'CREATE_SECTION';
export const NO_ACTION = 'NO_ACTION';

export const CREATE_ADMINMODULE_SECTIONITEM = 'CREATE_ADMINMODULE_SECTION';
export const CREATE_ADMINMODULE_SECTIONITEM_SUCCESSFUL = 'CREATE_ADMINMODULE_SECTIONITEM_SUCCESSFUL';

export const PLANNER_TOGGLE_EXPAND_COLLAPSE_ALL = 'PLANNER_TOGGLE_EXPAND_COLLAPSE_ALL';

export const PLANNER_ROWS_DENSITY_CHANGED = 'PLANNER_ROWS_DENSITY_CHANGED';
export const BAR_FIELDS_CHANGED = 'BAR_FIELDS_CHANGED';
export const PLANNER_PAGE_FIELDS_CHANGED_SUCCESS = 'PLANNER_PAGE_FIELDS_CHANGED_SUCCESS';

export const TABLE_VIEW_PAGE_FIELDS_CHANGED_SUCCESS = 'TABLE_VIEW_PAGE_FIELDS_CHANGED_SUCCESS';
export const ADD_TABLE_VIEW_FIELD_OPTIONS_MODEL = 'ADD_TABLE_VIEW_FIELD_OPTIONS_MODEL';

export const RECORDS_LIST_SUB_REC_FIELDS_CHANGED = 'RECORDS_LIST_SUB_REC_FIELDS_CHANGED';
export const RECORDS_LIST_MASTER_REC_FIELDS_CHANGED = 'RECORDS_LIST_MASTER_REC_FIELDS_CHANGED';

export const ADD_PLANNER_FIELD_OPTIONS_MODEL = 'ADD_PLANNER_FIELD_OPTIONS_MODEL';
export const SET_PLANNER_FIELD_OPTIONS_PROPS = 'SET_PLANNER_FIELD_OPTIONS_PROPS';
export const LOAD_LINKED_FIELDS = 'LOAD_LINKED_FIELDS';
export const ADD_DATE_SENSITIVE_MASTER_REC = 'ADD_DATE_SENSITIVE_MASTER_REC';
export const ADD_DATE_SENSITIVE_SUB_REC = 'ADD_DATE_SENSITIVE_SUB_REC';
export const ADD_DATE_SENSITIVE_BAR_GROUPS = 'ADD_DATE_SENSITIVE_BAR_GROUPS';

export const API_SUFFIX = {
    SUCCESS: '_SUCCESS',
    IN_PROGRESS: '_IN_PROGRESS',
    FAILURE: '_FAILURE'
};

/**
 * ACTION TYPES FOR DIARIES
 */
export const DIARY_ACTIONS = {
    FETCH_DAY_TYPE: 'FETCH_DAY_TYPE',
    FETCH_DAY_TYPE_FOR_WORKPATTERN: 'FETCH_DAY_TYPE_FOR_WORKPATTERN',
    FETCH_WORK_PATTERN: 'FETCH_WORK_PATTERN',
    FETCH_DAY_TYPE_CONTENT: 'FETCH_DAY_TYPE_CONTENT',
    FETCH_WORK_PATTERN_CONTENT: 'FETCH_WORK_PATTERN_CONTENT',
    FETCH_DAYS: 'FETCH_DAYS',
    FETCH_DAYS_FOR_WORKPATTERN: 'FETCH_DAYS_FOR_WORKPATTERN',
    UPDATE_DAY_TYPE_CONTENT: 'UPDATE_DAY_TYPE_CONTENT',
    BATCH_UPDATE_DAY_TYPE_CONTENT: 'BATCH_UPDATE_DAY_TYPE_CONTENT',
    UPDATE_WORK_PATTERN_CONTENT: 'UPDATE_WORK_PATTERN_CONTENT',
    DAY_TYPE_IN_USE: 'DAY_TYPE_IN_USE',
    WORK_PATTERN_IN_USE: 'WORK_PATTERN_IN_USE',
    DIARY_IN_USE: 'DIARY_IN_USE',
    CLEAR_DIARY_IN_USE: 'CLEAR_DIARY_IN_USE',
    CLEAR_IN_USE_WARNING: 'CLEAR_IN_USE_WARNING',
    ADD_NEW_DAY_TYPE_ROW: 'ADD_NEW_DAY_TYPE_ROW',
    REMOVE_NEW_DAY_TYPE_ROW: 'REMOVE_NEW_DAY_TYPE_ROW',
    FETCH_DIARY_CALENDAR: 'FETCH_DIARY_CALENDAR',
    FETCH_DIARY_CALENDAR_CONTENT: 'FETCH_DIARY_CALENDAR_CONTENT',
    UPDATE_DIARY_CALENDAR: 'UPDATE_DIARY_CALENDAR',
    ADD_NEW_CUSTOM_DAYS_ROW: 'ADD_NEW_CUSTOM_DAYS_ROW',
    REMOVE_NEW_CUSTOM_DAY_ROW: 'REMOVE_NEW_CUSTOM_DAY_ROW',
    ADD_NEW_CUSTOM_PERIOD_ROW: 'ADD_NEW_CUSTOM_PERIOD_ROW',
    REMOVE_NEW_CUSTOM_PERIOD_ROW: 'REMOVE_NEW_CUSTOM_PERIOD_ROW',
    UPDATE_CUSTOM_DAY_OPERATION_TYPE: 'UPDATE_CUSTOM_DAY_OPERATION_TYPE',
    UPDATE_CUSTOM_PERIOD_OPERATION_TYPE: 'UPDATE_CUSTOM_PERIOD_OPERATION_TYPE',
    UPDATE_DIARY_CALENDAR_CONTENT: 'UPDATE_DIARY_CALENDAR_CONTENT',
    CHANGE_CALENDAR_YEAR: 'CHANGE_CALENDAR_YEAR',
    FETCH_DIARY_NAME: 'FETCH_DIARY_NAME',
    FETCH_WORK_PATTERN_NAME: 'FETCH_WORK_PATTERN_NAME',
    UPDATE_WORK_PATTERN_SELECTION_ITEM: 'UPDATE_WORK_PATTERN_SELECTION_ITEM',
    UPDATE_WORK_PATTERN_SELECTION_ITEM_ERROR_STATUS: 'UPDATE_WORK_PATTERN_SELECTION_ITEM_ERROR_STATUS',
    FETCH_DAY_TYPE_SELECTIONLIST_DATA: 'FETCH_DAY_TYPE_SELECTIONLIST_DATA',
    FETCH_DAY_TYPE_DATA_BY_ID: 'FETCH_DAY_TYPE_DATA_BY_ID',
    SET_ACTIVE_DIARY: 'SET_ACTIVE_DIARY',
    UPDATE_STANDARD_WORK_PATTERN: 'UPDATE_STANDARD_WORK_PATTERN',
    UPDATE_DIARY_ITEM_CONTENT: 'UPDATE_DIARY_ITEM_CONTENT',
    UPDATE_DIARY_INDIVIDUAL_STATE: 'UPDATE_DIARY_INDIVIDUAL_STATE',
    UPDATE_DIARY_ERROR_STATUS: 'UPDATE_DIARY_ERROR_STATUS',
    WIPE_DIARY_DATA: 'WIPE_DIARY_DATA',
    RESET_NEWLY_ADDED_SECTION_NAME: 'RESET_NEWLY_ADDED_SECTION_NAME'
};

export const DAY_TYPES_CONTEXT_MENU = {
    CREATE_DAY_TYPE: 'CREATE_DAY_TYPE',
    RENAME_DAY_TYPE: 'RENAME_DAY_TYPE',
    DELETE_DAY_TYPE: 'DELTE_DAY_TYPE',
    DUPLICATE_DAY_TYPE: 'DUPLICATE_DAY_TYPE',
    CREATE_DAY_TYPE_USING_HOTKEY: 'CREATE_DAY_TYPE_USING_HOTKEY',
    DELTE_WORK_PATTERN: 'DELTE_WORK_PATTERN',
    CREATE_WORK_PATTERN: 'CREATE_WORK_PATTERN',
    CREATE_WORK_PATTERN_USING_HOTKEY: 'CREATE_WORK_PATTERN_USING_HOTKEY'
};

export const DIARY_CONTEXT_MENU = {
    CREATE_DIARY: 'CREATE_DIARY',
    DUPLICATE_DIARY: 'DUPLICATE_DIARY',
    DUPLICATE_DIARY_USING_API: 'DUPLICATE_DIARY_USING_API',
    DELETE_DIARY: 'DELETE_DIARY',
    CREATE_DIARY_WITH_HOT_KEY: 'CREATE_DIARY_WITH_HOT_KEY'
};

export const LOAD_COLOUR_SCHEME_FIELDS = 'LOAD_COLOUR_SCHEME_FIELDS';
export const CLEAR_COLOUR_SCHEME_RULES = 'CLEAR_COLOUR_SCHEME_RULES';
export const UPDATE_COLOR_SCHEME_RULES = 'UPDATE_COLOR_SCHEME_RULES';
export const UPDATE_ACTIVE_COLOR_SCHEME_NAME = 'UPDATE_ACTIVE_COLOR_SCHEME_NAME';
export const RESET_COLOR_SCHEME = 'RESET_COLOR_SCHEME';

export const GET_ALL_COLOUR_SCHEMES = 'GET_ALL_COLOUR_SCHEMES';
export const GET_BY_ID_COLOUR_SCHEMES = 'GET_BY_ID_COLOUR_SCHEMES';
export const SAVE_COLOR_SCHEME = 'SAVE_COLOR_SCHEME';

export const COLOUR_SCHEME_ACTIONS = {
    CREATE_COLOUR_SCHEME_SECTION: 'CREATE_COLOUR_SCHEME_SECTION',
    CREATE_COLOUR_SCHEME_SECTION_USING_HOTKEY: 'CREATE_COLOUR_SCHEME_SECTION_USING_HOTKEY',
    DELETE_COLOUR_SCHEME_SECTION: 'DELETE_COLOUR_SCHEME_SECTION',
    DUPLICATE_COLOUR_SCHEME_SECTION: 'DUPLICATE_COLOUR_SCHEME_SECTION',
    DUPLICATE_COLOUR_SCHEME_SECTION_USING_API: 'DUPLICATE_COLOUR_SCHEME_SECTION_USING_API',
    UPDATE_COLOUR_SCHEME_TITLE: 'UPDATE_COLOUR_SCHEME_TITLE',
    UPDATE_COLOUR_SCHEME_TITLE_ERROR: 'UPDATE_COLOUR_SCHEME_TITLE_ERROR',
    UPDATE_COLOUR_SCHEME_ACTIVE_TAB: 'UPDATE_COLOUR_SCHEME_ACTIVE_TAB'
};

export const COST_REVENUE_ACTIONS = {
    FETCH_CHARGE_CODE: 'FETCH_CHARGE_CODE',
    ADD_CHARGE_CODE_ROW: 'ADD_CHARGE_CODE_ROW',
    UPDATE_CHARGE_CODE: 'UPDATE_CHARGE_CODE',
    DELETE_CHARGE_CODE_ROW: 'DELETE_CHARGE_CODE_ROW',
    FETCH_CHARGE_RATES: 'FETCH_CHARGE_RATES',
    FETCH_CHARGE_RATE_CONTENT: 'FETCH_CHARGE_RATE_CONTENT',
    FETCH_CHARGE_RATE_TITLE: 'FETCH_CHARGE_RATE_TITLE',
    UPDATE_CHARGE_RATE_CONTENT: 'UPDATE_CHARGE_RATE_CONTENT',
    FETCH_ACTIVE_CURRENCY: 'FETCH_ACTIVE_CURRENCY',
    UPDATE_CHARGE_RATE_CONTENT_SELECTED_ITEM: 'UPDATE_CHARGE_RATE_CONTENT_SELECTED_ITEM',
    UPDATE_CHARGE_RATE_TITLE_NAME: 'UPDATE_CHARGE_RATE_TITLE_NAME',
    FETCH_CHARGE_RATES_SELECTIONLIST_DATA: 'FETCH_CHARGE_RATES_SELECTIONLIST_DATA',
    FETCH_CHARGE_RATES_RESOURCE_DATA: 'FETCH_CHARGE_RATES_RESOURCE_DATA',
    FETCH_CHARGE_CODES_JOB_COUNT: 'FETCH_CHARGE_CODES_JOB_COUNT',
    RESET_CHARGE_RATE_UPDATE_CALL_STATUS: 'RESET_CHARGE_RATE_UPDATE_CALL_STATUS'
};

export const CHARGE_RATE_CONTEXT_MENU = {
    CREATE_CHARGE_RATE: 'CREATE_CHARGE_RATE',
    RENAME_CHARGE_RATE: 'RENAME_CHARGE_RATE',
    DELETE_CHARGE_RATE: 'DELETE_CHARGE_RATE',
    DUPLICATE_CHARGE_RATE: 'DUPLICATE_CHARGE_RATE',
    DUPLICATE_CHARGE_RATE_USING_API: 'DUPLICATE_CHARGE_RATE_USING_API',
    CREATE_CHARGE_RATE_USING_HOTKEY: 'CREATE_CHARGE_RATE_USING_HOTKEY'
};

export const ENTITY_CONFIG_ACTIONS = {
    FETCH_ENTITY_CONFIGURATION: 'FETCH_ENTITY_CONFIGURATION',
    FETCH_ENTITY_CONFIGURATION_CONTENT: 'FETCH_ENTITY_CONFIGURATION_CONTENT',
    FETCH_ENTITY_CONFIGURATION_FIELDS: 'FETCH_ENTITY_CONFIGURATION_FIELDS',
    UPDATE_ENTITY_CONFIGURATION_CONTENT: 'UPDATE_ENTITY_CONFIGURATION_CONTENT',

    FETCH_BOOKING: 'FETCH_BOOKING',
    FETCH_BOOKING_CONTENT: 'FETCH_BOOKING_CONTENT',
    FETCH_BOOKING_FIELDS: 'FETCH_BOOKING_FIELDS',
    UPDATE_BOOKING_CONTENT: 'UPDATE_BOOKING_CONTENT',

    FETCH_CLIENT: 'FETCH_CLIENT',
    FETCH_CLIENT_CONTENT: 'FETCH_CLIENT_CONTENT',
    FETCH_CLIENT_FIELDS: 'FETCH_CLIENT_FIELDS',
    UPDATE_CLIENT_CONTENT: 'UPDATE_CLIENT_CONTENT',

    FETCH_JOB: 'FETCH_JOB',
    FETCH_JOB_CONTENT: 'FETCH_JOB_CONTENT',
    FETCH_JOB_FIELDS: 'FETCH_JOB_FIELDS',
    UPDATE_JOB_CONTENT: 'UPDATE_JOB_CONTENT',

    FETCH_RESOURCE: 'FETCH_RESOURCE',
    FETCH_RESOURCE_CONTENT: 'FETCH_RESOURCE_CONTENT',
    FETCH_RESOURCE_FIELDS: 'FETCH_RESOURCE_FIELDS',
    UPDATE_RESOURCE_CONTENT: 'UPDATE_RESOURCE_CONTENT',

    FETCH_ROLE_REQUEST: 'FETCH_ROLE_REQUEST',
    FETCH_ROLE_REQUEST_CONTENT: 'FETCH_ROLE_REQUEST_CONTENT',
    FETCH_ROLE_REQUEST_FIELDS: 'FETCH_ROLE_REQUEST_FIELDS',
    UPDATE_ROLE_REQUEST_CONTENT: 'UPDATE_ROLE_REQUEST_CONTENT',

    FETCH_ROLE_REQUEST_GROUP: 'FETCH_ROLE_REQUEST_GROUP',
    FETCH_ROLE_REQUEST_GROUP_CONTENT: 'FETCH_ROLE_REQUEST_GROUP_CONTENT',
    FETCH_ROLE_REQUEST_GROUP_FIELDS: 'FETCH_ROLE_REQUEST_GROUP_FIELDS',
    UPDATE_ROLE_REQUEST_GROUP_CONTENT: 'UPDATE_ROLE_REQUEST_GROUP_CONTENT',

    FETCH_DEPARTMENT: 'FETCH_DEPARTMENT',
    FETCH_DEPARTMENT_CONTENT: 'FETCH_DEPARTMENT_CONTENT',
    FETCH_DEPARTMENT_FIELDS: 'FETCH_DEPARTMENT_FIELDS',
    UPDATE_DEPARTMENT_CONTENT: 'UPDATE_DEPARTMENT_CONTENT',

    FETCH_DIVISION: 'FETCH_DIVISION',
    FETCH_DIVISION_CONTENT: 'FETCH_DIVISION_CONTENT',
    FETCH_DIVISION_FIELDS: 'FETCH_DIVISION_FIELDS',
    UPDATE_DIVISION_CONTENT: 'UPDATE_DIVISION_CONTENT'
};

export const FIELD_LOOKUP_VALUES_ACTIONS = {
    FETCH_FIELD_LOOKUP_VALUES: 'FETCH_FIELD_LOOKUP_VALUES',
    FETCH_FIELD_LOOKUP_VALUES_CONTENT: 'FETCH_FIELD_LOOKUP_VALUES_CONTENT',
    UPDATE_FIELD_LOOKUP_VALUES_CONTENT: 'UPDATE_FIELD_LOOKUP_VALUES_CONTENT',
    UPDATE_FIELD_LOOKUP_VALUES: 'UPDATE_FIELD_LOOKUP_VALUES',
    BATCH_UPDATE_FIELD_LOOKUP_VALUES: 'BATCH_UPDATE_FIELD_LOOKUP_VALUES',
    FETCH_FIELD_ENTITY_LOOKUP_VALUES: 'FETCH_FIELD_ENTITY_LOOKUP_VALUES',
    CREATE_FIELD_LOOKUP_VALUE: 'CREATE_FIELD_LOOKUP_VALUE',
    CREATE_FIELD_LOOKUP_VALUE_USING_HOTKEY: 'CREATE_FIELD_LOOKUP_VALUE_USING_HOTKEY',
    DELETE_FIELD_LOOKUP_VALUE: 'DELETE_FIELD_LOOKUP_VALUE',
    UPDATE_VALUES_TITLE_ERROR: 'UPDATE_VALUES_TITLE_ERROR',
    UPDATE_VALUES_TITLE: 'UPDATE_VALUES_TITLE',
    FIELD_LOOKUP_VALUES_IN_USE: 'FIELD_LOOKUP_VALUES_IN_USE',
    FIELD_LOOKUP_VALUES_CLEAR_IN_USE_WARNING: 'FIELD_LOOKUP_VALUES_CLEAR_IN_USE_WARNING',
    CHANGE_FIELD_LOOKUP_VALUE_SORTING: 'CHANGE_FIELD_LOOKUP_VALUE_SORTING',
    RESET_UPDATE_CALL_FLAG: 'RESET_UPDATE_CALL_FLAG'
};

export const DATA_GRID_DENSITY_CHANGE = 'DATA_GRID_DENSITY_CHANGE';
export const DATA_GRID_COLUMNS_CHANGE = 'DATA_GRID_COLUMNS_CHANGE';
export const SET_DATA_GRID_COLUMNS = 'SET_DATA_GRID_COLUMNS';
export const DATA_GRID_COLUMNS_CHANGE_SUCCESS = 'DATA_GRID_COLUMNS_CHANGE_SUCCESS';

export const DATA_GRID_SORT_CHANGED = 'DATA_GRID_SORT_CHANGED';
export const DATA_GRID_PAGE_CHANGE = 'DATA_GRID_PAGE_CHANGE';
export const DATA_GRID_LOAD_DATA = 'DATA_GRID_LOAD_DATA';
export const DATA_GRID_PAGE_CHANGE_SUCCESS = 'DATA_GRID_PAGE_CHANGE_SUCCESS';
export const DATA_GRID_PAGE_CLOSE_DETAILS_PANE = 'DATA_GRID_PAGE_CLOSE_DETAILS_PANE';

export const OPEN_PLANNER_ASSIGNEE_DETAILS_PANE = 'OPEN_PLANNER_ASSIGNEE_DETAILS_PANE';

export const ROLE_INBOX_PAGE_DIGEST_DATAGRID_LOAD_DATA = 'ROLE_INBOX_PAGE_DIGEST_DATAGRID_LOAD_DATA';

export const MARKETPLACE_PAGE_ROLE_APPPLY = 'MARKETPLACE_PAGE_ROLE_APPLY';

export const OPEN_MANAGE_ROLE_TEMPLATES_MODAL = 'OPEN_MANAGE_ROLE_TEMPLATES_MODAL';
export const OPEN_MANAGE_ROLE_TEMPLATES_MODAL_ERROR = 'OPEN_MANAGE_ROLE_TEMPLATES_MODAL_ERROR';
export const DELETE_ROLE_TEMPLATE = 'DELETE_ROLE_TEMPLATE';
export const RENAME_ROLE_TEMPLATE = 'RENAME_ROLE_TEMPLATE';
export const INSERT_ROLE_TEMPLATE = 'INSERT_ROLE_TEMPLATE';
export const INSERT_ROLE_TEMPLATE_RETRY = 'INSERT_ROLE_TEMPLATE_RETRY';
export const INSERT_ROLE_TEMPLATE_ERROR = 'INSERT_ROLE_TEMPLATE_ERROR';
export const DELETE_ROLE_TEMPLATE_ERROR = 'DELETE_ROLE_TEMPLATE_ERROR';
export const RENAME_ROLE_TEMPLATE_ERROR = 'RENAME_ROLE_TEMPLATE_ERROR';

export const FILTERS_ACTIONS = {
    FILTER_APPLY: 'APPLY_FILTER',
    FILTER_OPERATOR_CHANGE: 'FILTER_OPERATOR_CHANGE',
    FILTER_VALUE_CHANGE: 'FILTER_VALUE_CHANGE',
    APPEND_FILTER_VALUE_CHANGE: 'APPEND_FILTER_VALUE_CHANGE',
    COMBINED_FILTER_VALUE_CHANGE: 'COMBINED_FILTER_VALUE_CHANGE',
    FILTER_VALUES_LOAD: 'FILTER_VALUES_LOAD',
    FILTER_VALUES_LOAD_SUCCESS: 'FILTER_VALUES_LOAD_SUCCESS',
    FILTER_CLEAR: 'FILTER_CLEAR',
    FILTER_CLOSE: 'FILTER_CLOSE',
    STORE_SKILL_FILTER_SELECTION: 'STORE_SKILL_FILTER_SELECTION',
    MULTI_FILTER_SEARCH_INPUT: 'MULTI_FILTER_SEARCH_INPUT',
    TOGGLE_FILTER_PANE: 'TOGGLE_FILTER_PANE',
    PLANNER_PAGE_FILTERS_VIEW_CHANGED: 'PLANNER_PAGE_FILTERS_VIEW_CHANGED',
    FILTERS_VIEW_CHANGED: 'FILTERS_VIEW_CHANGED',
    ADD_ADVANCED_FILTER: 'ADD_ADVANCED_FILTER',
    REMOVE_ADVANCED_FILTER: 'REMOVE_ADVANCED_FILTER',
    REORDER_ADVANCED_FILTERS: 'REORDER_ADVANCED_FILTERS',
    ADVANCED_FILTERS_APPLY: 'ADVANCED_FILTERS_APPLY',
    FILTER_FIELD_CHANGED: 'FILTER_FIELD_CHANGED',
    FILTER_LOGICAL_OPERATOR_CHANGED: 'FILTER_LOGICAL_OPERATOR_CHANGED',
    ADD_FILTERS: 'ADD_FILTERS',
    CLEAR_ALL_FILTERS: 'CLEAR_ALL_FILTERS',
    BASE_FILTER_SET: 'SET_BASE_FILTER',
    RESET_BASE_FILTER: 'RESET_BASE_FILTER',
    REFRESH_FILTERED_DATA: 'REFRESH_FILTERED_DATA',
    UPDATE_FILTER_SHORTCUT_VISIBILITY: 'UPDATE_FILTER_SHORTCUT_VISIBILITY',
    SHOW_ALL_FILTER_SHORTCUTS: 'SHOW_ALL_FILTER_SHORTCUTS',
    HANDLE_TABLE_VIEW_PAGE_DATA_FILTERS: 'HANDLE_TABLE_VIEW_PAGE_DATA_FILTERS',
    ADD_TABLE_VIEW_PAGE_FILTERS_MODEL: 'ADD_TABLE_VIEW_PAGE_FILTERS_MODEL',
    ADD_MULTIPLE_VIEWS_FILTERS_MODEL: 'ADD_MULTIPLE_VIEWS_FILTERS_MODEL',
    POPULATE_SKILLS: 'POPULATE_SKILLS',
    EXTERNAL_FILTER_APPLY: 'EXTERNAL_FILTER_APPLY',
    CLEAR_EXTERNAL_FILTER: 'CLEAR_EXTERNAL_FILTER',
    UNLOAD_MULTIVALUES_FILTER_LOADED_OPTIONS: 'UNLOAD_MULTIVALUES_FILTER_LOADED_OPTIONS',
    DISCARD_CURRENT_CHANGES: 'DISCARD_CURRENT_CHANGES',
    SET_HAS_HIDDEN_FILTERS: 'SET_HAS_HIDDEN_FILTERS',
    SET_FILTERS_STATE: 'SET_FILTERS_STATE',
    RESET_FILTER_TO_SAVED_PLAN: 'RESET_FILTER_TO_SAVED_PLAN',
    DISABLE_RESET_FILTER_BUTTON: 'DISABLE_RESET_FILTER_BUTTON'
};

export const CURRENCY_ACTIONS = {
    FETCH_CURRENCY: 'FETCH_CURRENCY',
    UPDATE_ACTIVE_CURRENCY: 'UPDATE_ACTIVE_CURRENCY'
};

export const COMPANY_INFORMATION_ACTIONS = {
    FETCH_COMPANY_INFORMATION: 'FETCH_COMPANY_INFORMATION',
    FETCH_COMPANY_INFORMATION_ADMIN: 'FETCH_COMPANY_INFORMATION_ADMIN',
    UPDATE_COMPANY_INFORMATION: 'UPDATE_COMPANY_INFORMATION',
    FETCH_UPDATED_COMPANY_INFORMATION: 'FETCH_UPDATED_COMPANY_INFORMATION'
};

export const UPDATE_SUBNAV_AREAS = 'UPDATE_SUBNAV_AREAS';

export const FIELD_PROPERTY_ACTIONS = {
    FETCH_FIELD_PROPERTIES: 'FETCH_FIELD_PROPERTIES',
    FETCH_FIELD_PROPERTY_CONTENT: 'FETCH_FIELD_PROPERTY_CONTENT',
    FETCH_FIELD_PROPERTY_CONTENT_DUMMY: 'FETCH_FIELD_PROPERTY_CONTENT_DUMMY',
    // FETCH_FIELD_DETAILS : 'FETCH_FIELD_DETAILS',
    UPDATE_FIELD_PROPERTIES: 'UPDATE_FIELD_PROPERTIES',
    BATCH_UPDATE_FIELD_PROPERTIES: 'BATCH_UPDATE_FIELD_PROPERTIES',
    UPDATE_FIELD_PROPERTY_SELECTION_ITEM: 'UPDATE_FIELD_PROPERTY_SELECTION_ITEM',
    ADD_CUSTOM_FIELD_PROPERTY: 'ADD_CUSTOM_FIELD_PROPERTY',
    SET_EXPANDED_KEYS: 'SET_EXPANDED_KEYS',
    REMOVE_FIELD: 'REMOVE_FIELD',
    CANCEL_REMOVE_FIELD: 'CANCEL_REMOVE_FIELD',
    LOAD_TABLE_DATA_ADMIN_SETTING: 'LOAD_TABLE_DATA_ADMIN_SETTING',
    LOAD_TABLE_ACCESS_DATA: 'LOAD_TABLE_ACCESS_DATA',
    FETCH_ACTIVE_CURRENCY_FIELD_PROPERTIES: 'FETCH_ACTIVE_CURRENCY_FIELD_PROPERTIES',
    SET_ACTIVE_TAB: 'SET_ACTIVE_TAB',
    UPDATE_ENTITY_AND_FIELD_PROPERTIES: 'UPDATE_ENTITY_AND_FIELD_PROPERTIES',
    FETCH_FIELD_DEFAULT_LOOKUP_VALUES: 'FETCH_FIELD_DEFAULT_LOOKUP_VALUES',
    FETCH_RESOURCE_BOOLEAN_FIELDS: 'FETCH_RESOURCE_BOOLEAN_FIELDS'
};

export const ADMINSETTING_APPLY_FILTERS_SELECTION = 'ADMINSETTING_APPLY_FILTERS_SELECTION';
export const ADMINSETTING_AUTOCOMPLETE_SEARCH = 'ADMINSETTING_AUTOCOMPLETE_SEARCH';
export const ADMINSETTING_AUTOCOMPLETE_INPUT = 'ADMINSETTING_AUTOCOMPLETE_INPUT';
export const ADMINSETTING_AUTOCOMPLETE_SEARCH_SUCCESS = 'ADMINSETTING_AUTOCOMPLETE_SEARCH_SUCCESS';
export const ADMINSETTING_AUTOCOMPLETE_SEARCH_ERROR = 'ADMINSETTING_AUTOCOMPLETE_SEARCH_ERROR';
export const ADMINSETTING_SET_FORM_ERROR = 'ADMINSETTING_SET_FORM_ERROR';
export const ADMINSETTING_AUTOCOMPLETE_DROPDOWN_VISIBILITY_CHANGE = 'ADMINSETTING_AUTOCOMPLETE_DROPDOWN_VISIBILITY_CHANGE';
export const ADMINSETTING_AUTOCOMPLETE_CLEAR_SUGGESTIONS = 'ADMINSETTING_AUTOCOMPLETE_CLEAR_SUGGESTIONS';

export const SKILL_STRUCTURE_ACTIONS = {
    LOAD: {
        //TODO: Remove "LOAD_" from the beginning of the fields
        LOAD_SKILL_STRUCTURE: 'LOAD_SKILL_STRUCTURE',
        LOAD_SKILL_STRUCTURE_SUCCESS: 'LOAD_SKILL_STRUCTURE_SUCCESS',
        LOAD_SKILL_SECTIONS: 'LOAD_SKILL_SECTIONS',
        LOAD_SKILL_SECTION: 'LOAD_SKILL_SECTION',
        LOAD_SKILL_SECTIONS_SUCCESS: 'LOAD_SKILL_SECTIONS_SUCCESS',
        LOAD_SKILL_SECTION_SUCCESS: 'LOAD_SKILL_SECTION_SUCCESS',
        LOAD_SKILLS: 'LOAD_SKILLS',
        LOAD_SKILLS_SUCCESS: 'LOAD_SKILLS_SUCCESS',
        LOAD_SKILL: 'LOAD_SKILL',
        LOAD_SKILL_SUCCESS: 'LOAD_SKILL_SUCCESS',
        LOAD_ALL_SKILLS: 'LOAD_ALL_SKILLS',
        LOAD_ALL_SKILLS_SUCCESS: 'LOAD_ALL_SKILLS_SUCCESS'
    },
    EDIT: {
        SKILL_SECTION: 'EDIT_SKILL_SECTION',
        SKILL: 'EDIT_SKILL'
    },
    CREATE: {
        SKILL_SECTION: 'CREATE_SKILL_SECTION',
        SKILL: 'CREATE_SKILL'
    },
    REMOVE: {
        SKILL: 'REMOVE_SKILL',
        SKILL_SECTION: 'REMOVE_SKILL_SECTION',
        SKILL_DELETE: 'REMOVE_SKILL_DELETE'
    },
    DELETE: {
        SKILL: 'DELETE_SKILL',
        SKILL_SUCCESS: 'DELETE_SKILL_SUCCESS',
        SKILL_SECTION: 'DELETE_SKILL_SECTION',
        SKILL_SECTION_SUCCESS: 'DELETE_SKILL_SECTION_SUCCESS'

    },
    UPDATE: {
        SKILL_SECTION: 'UPDATE_SKILL_SECTION',
        SKILL_SECTION_SUCCESS: 'UPDATE_SKILL_SECTION_SUCCESS',
        SKILL: 'UPDATE_SKILL',
        SKILL_SUCCESS: 'UPDATE_SKILL_SUCCESS'

    },
    INSERT: {
        SKILL_SECTION: 'INSERT_SKILL_SECTION',
        SKILL_SECTION_SUCCESS: 'INSERT_SKILL_SECTION_SUCCESS',
        DUPLICATE_SKILL_SECTION: 'DUPLICATE_SKILL_SECTION',
        SKILL: 'INSERT_SKILL',
        SKILL_SUCCESS: 'INSERT_SKILL_SUCCESS'
    },
    UI: {
        SELECT_SKILL_SECTION: 'SELECT_SKILL_SECTION',
        SKILL_SECTIONS_SET_SORT: 'SKILL_SECTIONS_SET_SORT',
        SKILL_LIST_SET_SORT: 'SKILL_LIST_SET_SORT',
        DISCARD_CHANGES: 'DISCARD_CHANGES',
        TRIGGER_SKILL_SECTION_RENAME: 'TRIGGER_SKILL_SECTION_RENAME',
        SKILL_SECTION_UPDATED: 'SKILL_SECTION_UPDATED',
        INSERT_SKILL_SECTION: 'UI_INSERT_SKILL_SECTION',
        UPDATE_SKILL_SECTION: 'UI_UPDATE_SKILL_SECTION',
        SKILLS_SAVE_CHANGES: 'SKILLS_SAVE_CHANGES',
        GO_TO_SECTION: 'GO_TO_SECTION',
        SKILL_SECTION_REMOVE_CHANGE: 'SKILL_SECTION_REMOVE_CHANGE',
        HANDLE_SKILL_EXPAND: 'HANDLE_SKILL_EXPAND',
        COLLAPSE_ALL_SKILLS: 'COLLAPSE_ALL_SKILLS',
        PROMP_SKILLS_SAVE_CHANGES: 'PROMP_SKILLS_SAVE_CHANGES',
        REMOVE_DELETES: 'REMOVE_DELETES'
    }
};

export const PLANNER_COMMAND_BAR = {
    SET_SECTION_VISIBILITY: 'PLANNER_COMMAND_BAR_SET_SECTION_VISIBILITY'
};

export const TABLE_VIEW_COMMAND_BAR = {
    SET_SECTION_VISIBILITY: 'TABLE_VIEW_COMMAND_BAR_SET_SECTION_VISIBILITY'
};

export const JOBS_COMMAND_BAR = {
    SET_SECTION_VISIBILITY: 'JOBS_COMMAND_BAR_SET_SECTION_VISIBILITY'
};

export const COMMAND_BAR = {
    SET_SECTION_VISIBILITY: 'COMMAND_BAR_SET_SECTION_VISIBILITY',
    UPDATE_ACTION_ELEMENT_LABEL: 'UPDATE_ACTION_ELEMENT_LABEL'
};

export const COMMAND_BAR_PLANS_SECTION = {
    POPULATE: 'COMMAND_BAR_POPULATE_PLANS_SECTION',
    SELECT_PLAN: 'COMMAND_BAR_SELECT_PLAN',
    REPOPULATE: 'COMMAND_BAR_PLANS_SECTION_REPOPULATE'
};

export const ENTITY_LOOKUP_WINDOWS = {
    SET_MANAGE_ENTITY_LOOKUP_WINDOW_VISIBILITY: 'SET_MANAGE_ENTITY_LOOKUP_WINDOW_VISIBILITY'
};

export const REQUEST_FAILED = 'REQUEST_FAILED';
export const CLEAR_REQUEST_ERROR = 'CLEAR_REQUEST_ERROR';

export const SET_ACTIVE_ADMIN_PAGE = 'SET_ACTIVE_ADMIN_PAGE';

export const RESOURCE_SKILLS = {
    POPULATE: {
        SKILLS: 'RESOURCE_SKILLS_POPULATE_SKILLS',
        SKILL_PREFERENCES: 'POPULATE_SKILL_PREFERENCES',
        AUTHORIZE_SKILLS: 'RESOURCE_SKILLS_POPULATE_AUTHORIZE_SKILLS'
    },
    LOAD: {
        SKILLS: 'RESOURCE_SKILLS_LOAD_SKILLS',
        SKILL_PREFERENCES: 'LOAD_SKILL_PREFERENCES',
        AUTHORIZE_SKILLS: 'RESOURCE_SKILLS_LOAD_AUTHORIZE_SKILLS'
    },
    UI: {
        INIT: 'RESOURCE_SKILLS_POPULATE_SINGLE_SKILL_FORM', //todo
        PRE_FIELD_CHANGE: 'PRE_RESOURCE_SKILLS_UI_SKILL_FIELD_CHANGE',
        FIELD_CHANGE: 'RESOURCE_SKILLS_UI_SKILL_FIELD_CHANGE',
        MARKED_FOR_DELETION: 'RESOURCE_SKILLS_MARKED_UI_SKILL_FOR_DELETION',
        ADD_SKILLS: 'RESOURCE_SKILLS_ADD_UI_SKILLS',
        DISCARD_DELETE: 'RESOURCE_SKILLS_DISCARD_UI_SKILL_DELETE'
    },
    DELETE: 'DELETE_RESOURCE_SKILL',
    UPDATE: 'UPDATE_RESOURCE_SKILL'
};

export const SKILLS_CHANGES = {
    ADD_SKILLS: 'SKILL_CHANGES_ADD_SKILLS',
    INIT: 'SKILLS_CHANGES_INIT',
    FIELD_CHANGE: 'SKILLS_CHANGES_FIELD_CHANGE',
    DELETE: 'SKILLS_CHANGES_DELETE_SKILL',
    UPDATE: 'SKILLS_CHANGES_UPDATE_SKILL',
    REMOVE_SKILL: 'SKILLS_CHANGES_REMOVE_SKILL',
    DISCARD_DELETE: 'SKILLS_CHANGES_DISCARD_DELETE',
    DISCARD_CHANGES: 'SKILLS_CHANGES_DISCARD_CHANGES',
    SET_SKILL_FIELDS_ERRORS: 'RESOURCE_SKILLS_CHANGES_SET_FIELD_ERRORS'
};

export const ROLE_GROUP_LIST_PAGE_ACTIONS = {
    LOAD: {
        DATA: 'ROLE_GROUP_LIST_PAGE_LOAD_DATA'
    },
    DELETE: 'ROLE_GROUP_LIST_PAGE_DELETE_ROLEGROUP'
};

export const ROLE_GROUP_DETAILS_PAGE_ACTIONS = {
    LOAD: {
        DATA: 'ROLE_GROUP_DETAILS_PAGE_LOAD_DATA',
        REQUEST_STATUS: 'ROLE_GROUP_DETAILS_PAGE_LOAD_REQUEST_STATUS',
        SET_LOADED_RESPONSE: 'ROLE_GROUP_DETAILS_PAGE_SET_LOADED_RESPONSE',
        CLEAN_LOADED_RESPONSE: 'ROLE_GROUP_DETAILS_PAGE_CLEAN_LOADED_RESPONSE',
        REQUEST_ASSIGNEES: 'ROLE_GROUP_DETAILS_PAGE_LOAD_REQUEST_ASSIGNEES',
        POPULATE_ROLES: 'POPULATE_ROLES'
    },
    UPDATE: 'ROLE_GROUP_DETAILS_PAGE_UPDATE_ROLEGROUP',
    SUBMIT_UPDATE: 'ROLE_GROUP_DETAILS_PAGE_SUBMIT_UPDATE_ROLEGROUP',
    DELETE: 'ROLE_GROUP_DETAILS_PAGE_DELETE_ROLEGROUP',
    HANDLE_DELETE: 'ROLE_GROUP_DETAILS_PAGE_HANDLE_DELETE_ROLEGROUP',
    OPEN_FOR_DIFFERENT_ROLE_GROUP: 'ROLE_GROUP_DETAILS_OPEN_FOR_DIFFERENT_ROLE_GROUP',
    REFRESH_ROLE_GROUP_TOTALS: 'ROLE_GROUP_DETAILS_PAGE_REFRESH_ROLE_GROUP_TOTALS'
};

export const TALENT_PROFILE = {
    LOAD: {
        DATA: 'TALENT_PROFILE_LOAD_DATA',
        CONFIG: 'TALENT_PROFILE_LOAD_CONFIG',
        ENTRY_DATA: 'TALENT_PROFILE_LOAD_ENTRY_DATA',
        ENTRY_DATA_POPULATE: 'TALENT_PROFILE_ENTRY_DATA_POPULATE',
        LINKED_DATA: 'TALENT_PROFILE_LOAD_LINKED_DATA',
        ENTRY_EXTERNAL_DATA: 'TALENT_PROFILE_LOAD_ENTRY_EXTERNAL_DATA',
        AUDIT_SECTION_DATA: 'LOAD_TALENT_PROFILE_AUDIT_SECTION_DATA'
    },
    POPULATE: {
        CONFIG: 'TALENT_PROFILE_POPULATE_CONFIG',
        ENTRY_DATA: 'POPULATE_TALENT_PROFILE_ENTRY_DATA',
        LINKED_DATA: 'TALENT_PROFILE_POPULATE_LINKED_DATA',
        AUDIT: 'POPULATE_TALENT_PROFILE_SECTIONS_AUDIT_DATA',
        UPDATE_NAV_SECTION_LINKS: 'TALENT_PROFILE_UPDATE_NAV_SECTION_LINKS',
        REMOVE_NAV_SECTION_LINK: 'TALENT_PROFILE_REMOVE_NAV_SECTION_LINK',
        // Action to update skills in the resource entity after a resource is edited
        UPDATE_ENTITY_SKILLS_AFTER_RESOURCE_EDIT: 'UPDATE_LATEST_SKILLS_IN_RESOURCE_ENTITY'
    },
    EDIT: {
        START_EDIT_FIELD: 'TALENT_PROFILE_START_EDIT_FIELD',
        FIELD_CHANGED: 'TALENT_PROFILE_FIELD_CHANGED',
        FIELD_CHANGED_CONTEXTUALLY: 'TALENT_PROFILE_FIELD_CHANGED_CONTEXTUALLY',
        CANCEL_FIELD_EDIT: 'TALENT_PROFILE_CANCEL_FIELD_EDIT',
        CANCEL_MULTIPLE_FIELD_EDIT: 'CANCEL_MULTIPLE_FIELD_EDIT',
        START_EDIT_MULTIPLE_FIELD: 'START_EDIT_MULTIPLE_FIELD',
        RESOURCE_SKILLS: 'POPULATE_TALENT_PROFILE_RESOURCE_SKILLS',
        CONTEXTUAL_EDIT_APPLY: 'TALENT_PROFILE_CONTEXTUAL_EDIT_APPLY',
        CONTEXTUAL_EDIT_APPLY_SUBMIT: 'TALENT_PROFILE_CONTEXTUAL_EDIT_APPLY_SUBMIT',
        CONTEXTUAL_EDIT_APPLY_SECCESS: 'CONTEXTUAL_EDIT_APPLY_SECCESS',
        CONTEXTUAL_EDIT_APPLY_ERROR: 'CONTEXTUAL_EDIT_APPLY_ERROR',
        MULTIPLE_CONTEXTUAL_EDIT_APPLY: 'TALENT_PROFILE_MULTIPLE_CONTEXTUAL_EDIT_APPLY',
        MULTIPLE_CONTEXTUAL_EDIT_APPLY_SUBMIT: 'TALENT_PROFILE_MULTIPLE_CONTEXTUAL_EDIT_APPLY_SUBMIT',
        MULTIPLE_CONTEXTUAL_EDIT_APPLY_SUCCESS: 'MULTIPLE_CONTEXTUAL_EDIT_APPLY_SUCCESS',
        MULTIPLE_CONTEXTUAL_EDIT_APPLY_ERROR: 'MULTIPLE_CONTEXTUAL_EDIT_APPLY_ERROR'
    },
    UI: {
        SET_FIELD_ERRORS: 'SET_TALENT_PROFILE_FIELD_ERRORS',
        SET_MESSAGES: 'SET_TALENT_PROFILE_MESSAGES'
    },
    CLEAR_TALENT_PROFILE_AUDIT: 'CLEAR_TALENT_PROFILE_AUDIT',
    UPDATE_RECOMMENDATION_VIEWED: 'UPDATE_RECOMMENDATION_VIEWED'
};

export const DIARIES = {
    LOAD_ALL: 'LOAD_ALL_DIARIES',
    POPULATE: 'POPULATE_DIARIES'
};

export const USER_FNAS = {
    LOAD: 'LOAD_USER_FNAS',
    POPULATE: 'POPULATE_USER_FNAS',
    APPLY: 'APPLY_APP_USER_FNAS'
};

export const APPLICATION_USER = {
    LOAD: {
        DATA: 'LOAD_APPLICATION_USER_DATA',
        RESOURCE_DATA: 'LOAD_APPLICATION_USER_RESOURCE_DATA',
        CLAIMS: 'LOAD_APPLICATION_USER_CLAIMS',
        PERMISSIONS: 'LOAD_APPLICATION_USER_PERMISSIONS',
        SECURITY_PROFILE_ACCESS_LEVELS: 'SECURITY_PROFILE_ACCESS_LEVELS'
    },
    POPULATE: {
        CLAIMS: 'POPULATE_USER_CLAIMS',
        PERMISSIONS: 'POPULATE_USER_PERMISSIONS',
        DATA: 'POPULATE_USER_DATA',
        RESOURCE_DATA: 'POPULATE_USER_RESOURCE_DATA',
        SURROGATE_ID: 'POPULATE_USER_SURROGATE_ID',
        SECURITY_PROFILE_ACCESS_LEVEL_DATA: 'SECURITY_PROFILE_ACCESS_LEVEL_DATA'
    },
    HANDLES: {
        RESOURCE_DATA_LOADED: 'USER_RESOURCE_DATA_LOADED_HANDLE'
    },
    PERSIST: {
        FORCE_REHYDRATE_STATE: 'FORCE_REHYDRATE_STATE',
        REHYDRATE_STATE: 'REHYDRATE_STATE'
    }
};

export const USER_MANAGEMENT = {
    GENERATE_USERS_PAGINATION_KEY: 'GENERATE_USERS_PAGINATION_KEY',
    FETCH_USERMANAGEMENT_SUMMARY: 'FETCH_USERMANAGEMENT_SUMMARY',
    FETCH_USER_LICENSING_INFO: 'FETCH_USER_LICENSING_INFO',
    FETCH_USER_SECURITY_PROFILE_OPTIONS: 'FETCH_USER_SECURITY_PROFILE_OPTIONS',
    FETCH_MORE_USERS: 'FETCH_MORE_USERS',
    INIT_USER_MANAGEMENT_PAGE_DATA: 'INIT_USER_MANAGEMENT_PAGE_DATA',
    UPDATE_USERS: 'UPDATE_USERS',
    ADD_NEW_USER_ROW: 'ADD_NEW_USER_ROW',
    REMOVE_USER_ROW: 'REMOVE_USER_ROW',
    CANCEL_REMOVE_USER_ROW: 'CANCEL_REMOVE_USER_ROW',
    SEND_USER_ACTIVATION_MAIL: 'SEND_USER_ACTIVATION_MAIL',
    SEND_RESET_PASSPHRASE: 'SEND_RESET_PASSPHRASE',
    EDITED_USER_INFO: 'EDITED_USER_INFO',
    SET_SORT_ORDER: 'SET_SORT_ORDER',
    RESET_USER_UPDATE_FINISH_STATE: 'RESET_USER_UPDATE_FINISH_STATE',
    UPDATE_SELECTED_USER: 'UPDATE_SELECTED_USER',
    RESET_SELECTED_USER: 'RESET_SELECTED_USER',
    BULK_RESEND_EMAIL_ACTION: 'BULK_RESEND_EMAIL_ACTION',
    BULK_RESET_PASSPHRASE: 'BULK_RESET_PASSPHRASE',
    SEND_C_ME_SURVEY: 'SEND_C_ME_SURVEY',
    UPDATE_FORM_DATA: 'UPDATE_FORM_DATA',
    RESET_USER_FORM_DATA: 'RESET_USER_FORM_DATA'
};

export const DAY_TYPES_ACTIONS = {
    FETCH_DAY_TYPE_NAME: 'FETCH_DAY_TYPE_NAME',
    UPDATE_DAY_TYPE: 'UPDATE_DAY_TYPE',
    RESET_DAY_TYPE_CONTENT: 'RESET_DAY_TYPE_CONTENT'
};

export const SECURITY_PROFILE_ACTIONS = {
    FETCH_FUNCTIONAL_ACCESS_RULES: 'FETCH_FUNCTIONAL_ACCESS_RULES',
    FETCH_SECURITY_PROFILES: 'FETCH_SECURITY_PROFILES',
    TOGGLE_FUNCTIONAL_ACCESS_RULE_VALUE: 'TOGGLE_FUNCTIONAL_ACCESS_RULE_VALUE',
    UPDATE_ACTIVE_TAB: 'UPDATE_ACTIVE_TAB',
    FETCH_SECURITY_PROFILES_DETAILS: 'FETCH_SECURITY_PROFILES_DETAILS',
    UPDATE_SECURITY_PROFILES_STATE: 'UPDATE_SECURITY_PROFILES_STATE',
    UPDATE_SECURITY_PROFILES_ACTIVE_SELECTION: 'UPDATE_SECURITY_PROFILES_ACTIVE_SELECTION',
    UPDATE_SECURITY_PROFILES_ACTIVE_SELECTION_ITEM: 'UPDATE_SECURITY_PROFILES_ACTIVE_SELECTION_ITEM',
    SECURITY_PROFILE_CONTEXT_MENU: {
        CREATE_SECURITY_PROFILE: 'CREATE_SECURITY_PROFILE',
        DELETE_SECURITY_PROFILE: 'DELETE_SECURITY_PROFILE',
        CREATE_SECURITY_PROFILE_USING_HOTKEY: 'CREATE_SECURITY_PROFILE_USING_HOTKEY'
    },
    FETCH_ENTITIES: 'LOAD_ENTITIES_SP',
    FETCH_ACCESS_LEVELS: 'LOAD_ACCESS_LEVELS_SP',
    FETCH_ENTITY_CONDITIONS: 'LOAD_ENTITY_CONDITIONS_SP',
    UPDATE_PLANNING_DATA_FIELDS: 'UPDATE_PLANNING_DATA_FIELDS',
    UPDATE_ENTITYACCESS_PREDEFINED_CONDITION_VALUE: 'UPDATE_ENTITYACCESS_PREDEFINED_CONDITION_VALUE',
    UPDATE_SECURITY_PROFILE_ITEM: 'UPDATE_SECURITY_PROFILE_ITEM',
    UPDATE_SECURITY_PROFILE_ERROR_STATUS: 'UPDATE_SECURITY_PROFILE_ERROR_STATUS',
    FETCH_ENTITIES_WITH_DETAILS: 'FETCH_ENTITIES_WITH_DETAILS',
    SECURITY_PROFILE_IN_USE: 'SECURITY_PROFILE_IN_USE',
    CLEAR_SECURITY_PROFILE_WARNING: 'CLEAR_SECURITY_PROFILE_WARNING',
    CLEAR_ALL_SECURITY_PROFILE_WARNINGS: 'CLEAR_ALL_SECURITY_PROFILE_WARNINGS',
    RESET_SECURITY_PROFILE: 'RESET_SECURITY_PROFILE',
    RESET_DYNAMIC_SECURITY_PROFILE: 'RESET_DYNAMIC_SECURITY_PROFILE',
    RESET_SECURITY_PROFILE_UPDATE_FINISH_STATE: 'RESET_SECURITY_PROFILE_UPDATE_FINISH_STATE',
    SET_ACTIVE_TAB_FOR_SECURITY_PROFILE: 'SET_ACTIVE_TAB_FOR_SECURITY_PROFILE',
    UPDATE_FIELD_RESTRICTION: 'UPDATE_FIELD_RESTRICTION',
    FIELD_SECURITIES_SET_EXPANDED_KEYS: 'FIELD_SECURITIES_SET_EXPANDED_KEYS',

    DELETE_SECURITY_PROFILE: 'DELETE_SECURITY_PROFILE',
    CREATE_SECURITY_PROFILE: 'CREATE_SECURITY_PROFILE',
    SAVE_SECURITY_PROFILE_TAB_CHANGES: 'SAVE_SECURITY_PROFILE_TAB_CHANGES',
    DISCARD_PROFILE_TAB_CHANGES: 'DISCARD_SECURITY_PROFILE_TAB_CHANGES',
    SET_PROFILE_CRUD_OPERATION_IN_PROGRESS: 'SET_SECURITY_PROFILE_CRUD_OPERATION_IN_PROGRESS',
    UPDATE_ENTITYACCESS_CONDITION_TYPE: 'SECURITY_PROFILE_UPDATE_ENTITYACCESS_CONDITION_TYPE',

    CUSTOM_CONDITIONS: {
        UPDATE_CONDITION_FIELD_NAME: 'UPDATE_CUSTOM_CONDITION_FIELD_NAME',
        UPDATE_CONDITION_OPERATOR: 'UPDATE_CUSTOM_CONDITION_OPERATOR',
        UPDATE_CONDITION_VALUE_TYPE: 'UPDATE_CUSTOM_CONDITION_VALUE_TYPE',
        UPDATE_CONDITION_VALUE: 'UPDATE_CUSTOM_CONDITION_ROW_VALUE',
        UPDATE_CUSTOM_CONDITION_VALUE: 'UPDATE_CUSTOM_CONDITION_VALUE',
        ADD_NEW_CONDITIONS_LIST: 'CUSTOM_CONDITIONS_ADD_NEW_CONDITIONS_LIST',
        ADD_NEW_CONDITION_ROW: 'CUSTOM_CONDITIONS_ADD_NEW_CONDITION_ROW',
        DELETE_CONDITION_ROW: 'CUSTOM_CONDITIONS_DELETE_CONDITION_ROW',
        SET_COLLAPSED_STATE: 'SET_CUSTOM_CONDITIONS_AREA_COLLAPSED_STATE'
    },
    VALIDATE_JSON_CONDITION: 'VALIDATE_JSON_CONDITION',
    SET_JSON_CONDITION_VALIDATION_RESULT: 'SET_JSON_CONDITION_VALIDATION_RESULT',
    UPDATE_JSON_CONDITION_VALUE: 'UPDATE_JSON_CONDITION_VALUE',
    UPDATE_CUSTOM_JSON_CONDITION_VALUE: 'UPDATE_CUSTOM_JSON_CONDITION_VALUE'
};

export const SECURITY_PROFILES_AT = {
    LOAD: {
        FNA_RULES: 'SECURITY_PROFILES_LOAD_FNA_RULES',
        FNA: 'SECURITY_PROFILES_LOAD_FNA',
        PROFILES: 'SECURITY_PROFILES_LOAD_PROFILES',
        EA: 'SECURITY_PROFILES_LOAD_EA',
        ENTITIES: 'LOAD_ENTITIES',
        ACCESS_LEVELS: 'LOAD_ACCESS_LEVELS',
        RULES_CONDITIONS: 'LOAD_RULES_CONDITIONS',
        ENTITY_ACCESS_LEVELS: 'LOAD_ENTITY_ACCESS_LEVELS',
        ENTITY_RULE_CONDITIONS: 'LOAD_ENTITY_RULES_CONDITIONS'
    },
    LOAD_SUCCESS: {
        FNA_RULES: 'SECURITY_PROFILES_FNA_RULES_LOAD_SUCCESS',
        FNA: 'SECURITY_PROFILES_FNA_LOAD_SUCCESS',
        PROFILES: 'SECURITY_PROFILES_PROFILES_LOAD_SUCCESS',
        EA: 'SECURITY_PROFILES_EA_LOAD_SUCCESS',
        ENTITIES: 'LOAD_ENTITIES_SUCCESS',
        ACCESS_LEVELS: 'LOAD_ACCESS_LEVELS_SUCCESS',
        RULES_CONDITIONS: 'LOAD_RULES_CONDITIONS_SUCCESS',
        ENTITY_ACCESS_LEVELS: 'LOAD_ENTITY_ACCESS_LEVELS_SUCCESS',
        ENTITY_RULE_CONDITIONS: 'LOAD_ENTITY_RULE_CONDITIONS_SUCCESS'
    },
    CREATE: {
        PROFILE: 'CREATE_SECURITY_PROFILE_DEPRECATED',
        ENTITY_ACCESS: 'CREATE_ENTITY_ACCESS'
    },
    EDIT: {
        PROFILE: 'EDIT_SECURITY_PROFILE',
        ENTITY_ACCESS: 'EDIT_ENTITY_ACCESS'
    },
    REMOVE: {
        ENTITY_ACCESS: 'REMOVE_ENTITY_ACCESS',
        ENTITY_ACCESS_DELETE: 'REMOVE_ENTITY_ACCESS_DELETE'
    },
    UPDATE: {
        ENTITY_ACCESS: 'UPDATE_ENTITY_ACCESS',
        FUNCTIONAL_ACCESSES: 'UPDATE_FUNCTIONAL_ACCESSES',
        PROFILE: 'UPDATE_PROFILE'
    },
    UPDATE_SUCCESS: {
        ENTITY_ACCESS: 'UPDATE_ENTITY_ACCESS_SUCCESS',
        FUNCTIONAL_ACCESSES: 'UPDATE_FUNCTIONAL_ACCESSES_SUCCESS',
        PROFILE: 'UPDATE_PROFILE_SUCCESS'
    },
    INSERT: {
        PROFILE: 'INSERT_SECURITY_PROFILE',
        ENTITY_ACCESS: 'INSERT_ENTITY_ACCESS',
        FUNCTIONAL_ACCESSES: 'INSERT_FUNCTIONAL_ACCESSES'
    },
    INSERT_SUCCESS: {
        PROFILE: 'INSERT_SECURITY_PROFILE_SUCCESS',
        ENTITY_ACCESS: 'INSERT_ENTITY_ACCESS_SUCCESS',
        FUNCTIONAL_ACCESSES: 'INSERT_FUNCTIONAL_ACCESSES_SUCCESS'
    },
    DELETE: {
        PROFILE: 'DELETE_SECURITY_PROFILE_DEPRECATED',
        ENTITY_ACCESSES: 'DELETE_ENTITY_ACCESSES'
    },
    DELETE_SUCCESS: {
        PROFILE: 'DELETE_SECURITY_PROFILE_DEPRECATED_SUCCESS',
        ENTITY_ACCESSES: 'DELETE_ENTITY_ACCESSES_SUCCESS'
    },
    UI: {
        SELECT_SECURITY_PROFILE: 'SELECT_SECURITY_PROFILE',
        SECURITY_PROFILES_SET_SORT: 'SECURITY_PROFILES_SET_SORT',
        GET_ENTITY_RULE_CONDITIONS: 'GET_ENTITY_RULE_CONDITIONS',
        DISCARD_SP_CHANGES: 'DISCARD_SP_CHANGES',
        DISCARD_SP_FNA_CHANGES: 'DISCARD_SP_FNA_CHANGES',
        DISCARD_SP_EA_CHANGES: 'DISCARD_SP_EA_CHANGES',
        SAVE_CHANGES: 'SECURITY_PROFILE_SAVE_CHANGES',
        CREATE_EA_CUSTOM_CONDITION_LIST: 'CREATE_EA_CUSTOM_CONDITION_LIST',
        TRIGGER_SECURITY_PROFILE_RENAME: 'TRIGGER_SECURITY_PROFILE_RENAME',
        REMOVE_PROFILE_CHANGES: 'REMOVE_PROFILE_CHANGES',
        SECURITY_PROFILE_UPDATED: 'SECURITY_PROFILE_UPDATED',
        GO_TO_PROFILE: 'GO_TO_PROFILE'
    }
};

export const CONFLICTS_ACTIONS = {
    FETCH_CONFLICTS: 'FETCH_CONFLICTS',
    UPDATE_ACTIVE_CONFLICTS: 'UPDATE_ACTIVE_CONFLICTS'
};

export const REPORT_SETTINGS_DATA_ACTION = {
    FETCH_REPORT_SETTINGS_DATA: 'FETCH_REPORT_SETTINGS_DATA',
    UPDATE_REPORT_SETTINGS_DATA: 'UPDATE_REPORT_SETTINGS_DATA',
    DATASET_REFRESH: 'DATASET_REFRESH',
    FETCH_DATASET_REFRESH_HISTORY: 'FETCH_DATASET_REFRESH_HISTORY',
    POPULATE_DATASET_REFRESH_HISTORY: 'POPULATE_DATASET_REFRESH_HISTORY'
};

export const WORKFLOWS_SETTINGS = {
    LOAD_ROLE_TYPE_WORKFLOW_SETTINGS: 'LOAD_ROLE_TYPE_WORKFLOW_SETTINGS',
    LOAD_ROLE_TYPE_WORKFLOW_SETTINGS_SUCCESSFULLY: 'LOAD_ROLE_TYPE_WORKFLOW_SETTINGS_SUCCESSFULLY',
    SAVE_ROLE_TYPE_WORKFLOW_SETTINGS: 'SAVE_ROLE_TYPE_WORKFLOW_SETTINGS',
    SAVE_ROLE_TYPE_WORKFLOW_SETTINGS_SUCCESSFULLY: 'SAVE_ROLE_TYPE_WORKFLOW_SETTINGS_SUCCESSFULLY',
    LOAD_ACTORS_CONDITIONS: 'LOAD_WORKFLOW_ACTORS_CONDITIONS',
    LOAD_ACTORS_CONDITIONS_SUCCESSFULLY: 'LOAD_WORKFLOW_ACTORS_CONDITIONS_SUCCESSFULLY',
    LOAD_APPROVER_TYPES: 'LOAD_WORKFLOW_APPROVER_TYPES',
    LOAD_APPROVER_TYPES_SUCCESSFULLY: 'LOAD_WORKFLOW_APPROVER_TYPES_SUCCESSFULLY',
    LOAD_ASSIGNER_TYPES: 'LOAD_WORKFLOW_ASSIGNER_TYPES',
    LOAD_ASSIGNER_TYPES_SUCCESSFULLY: 'LOAD_WORKFLOW_ASSIGNER_TYPES_SUCCESSFULLY',
    LOAD_REQUESTER_TYPES: 'LOAD_WORKFLOW_REQUESTER_TYPES',
    LOAD_REQUESTER_TYPES_SUCCESSFULLY: 'LOAD_WORKFLOW_REQUESTER_TYPES_SUCCESSFULLY',
    FORM: {
        UPDATE_FIELD_VALUE: 'ROLE_WORKFLOWS_FORM_CHANGE_FIELD',
        DISCARD_CHANGES: 'ROLE_WORKFLOWS_FORM_DISCARD_CHANGES'
    },
    SAVE: 'SAVE_WORKFLOW_SETTINGS'
};

export const ENTITY_IMPORT = {
    UPLOAD_ENTITY: 'UPLOAD_ENTITY',
    SET_CURRENT_ENTITY: 'SET_CURRENT_ENTITY',
    ADD_VALID_DATA_ENTRY: 'ADD_VALID_DATA_ENTRY',
    REMOVE_STATE_DATA_ENTRY: 'REMOVE_STATE_DATA_ENTRY',
    DOWNLOAD_ENTITY_TEMPLATE: 'DOWNLOAD_ENTITY_TEMPLATE',
    RESET_IMPORT_SUCCESSFUL_TOSTER: 'RESET_IMPORT_SUCCESSFUL_TOSTER',
    VALIDATE_ENTITY_DATA: 'VALIDATE_ENTITY_DATA'
};

export const CONDITION_LIST_AT = {
    ADD_NEW_CONDITION_LIST: 'ADD_NEW_CONDITION_LIST',
    ADD_NEW_CONDITION_ROW: 'ADD_NEW_CONDITION_ROW',
    DELETE_CONDITION_ROW: 'DELETE_CONDITION_ROW',
    EDIT_CONDITION_ROW: 'EDIT_CONDITION_ROW',
    EDIT_CONDITION_ROW_VALUE: 'EDIT_CONDITION_ROW_VALUE'
};

export const HOT_KEYS_HELP_WINDOW = {
    SET_VISIBILITY: 'SET_HOT_KEYS_HELP_WINDOW_VISIBILITY',
    SET_SECTIONS: 'SET_HOT_KEYS_HELP_WINDOW_SECTIONS'
};

export const SKILLS_CONFIGURATION_ACTIONS = {
    FETCH_SKILLS_CONFGIGURATION_SECTIONS: 'FETCH_SKILLS_CONFGIGURATION_SECTIONS',
    FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID: 'FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID',
    UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS: 'UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS',
    EDIT_SKILL_CATEGORY_DETAILS: 'EDIT_SKILL_CATEGORY_DETAILS',
    DELETE_SKILLS_CONFIGURATION: 'DELETE_SKILLS_CONFIGURATION',
    CREATE_SKILLS_CONFIGURATION_SECTION: 'CREATE_SKILLS_CONFIGURATION_SECTION',
    DUPLICATE_CONFIGURATION_SECTION: 'DUPLICATE_CONFIGURATION_SECTION',
    DUPLICATE_CONFIGURATION_SECTION_USING_API: 'DUPLICATE_CONFIGURATION_SECTION_USING_API',
    SET_SKILLS_TABLE_ROW_SORT_ORDER: 'SET_SKILLS_TABLE_ROW_SORT_ORDER',
    ADD_NEW_SKILL_ROW: 'ADD_NEW_SKILL_ROW',
    REMOVE_SKILLS_ROW: 'REMOVE_SKILLS_ROW',
    CANCEL_REMOVE_SKILL_ROW: 'CANCEL_REMOVE_SKILL_ROW',
    EDITED_SKILL_INFO: 'EDITED_SKILL_INFO',
    UPDATE_SKILL_TITLE_NAME: 'UPDATE_SKILL_TITLE_NAME',
    ON_EXPAND_KEY_SET: 'ON_EXPAND_KEY_SET',
    SET_TYPE_FILTERS: 'SET_TYPE_FILTERS',
    CLEAR_TYPE_FILTERS: 'CLEAR_TYPE_FILTERS',
    RESET_SKILLS_UPDATE_FINISH_STATE: 'RESET_SKILLS_UPDATE_FINISH_STATE',
    CREATE_SKILLS_CONFIGURATION_SECTION_USING_HOTKEY: 'CREATE_SKILLS_CONFIGURATION_SECTION_USING_HOTKEY',
    UPDATE_ACTIVE_COMPONENT_TAB_FOR_SKILL_TYPES: 'UPDATE_ACTIVE_COMPONENT_TAB_FOR_SKILL_TYPES',
    SET_ACTIVE_TAB_FOR_SKILL_TYPE: 'SET_ACTIVE_TAB_FOR_SKILL_TYPE',
    UPDATE_LEVEL_TYPE: 'UPDATE_LEVEL_TYPE',
    UPDATE_USE_LEVELS_FROM: 'UPDATE_USE_LEVELS_FROM',
    UPDATE_USE_LEVELS_FROM_USING_API: 'UPDATE_USE_LEVELS_FROM_USING_API',
    UPDATE_LEVEL_NAME: 'UPDATE_LEVEL_NAME',
    ADD_LEVELS: 'ADD_LEVELS',
    UPDATE_LEVELS: 'UPDATE_LEVELS',
    LOAD_TABLE_DATA_ADMIN_SETTING: 'LOAD_TABLE_DATA_ADMIN_SETTING',
    REMOVE_SKILL_FIELD: 'REMOVE_SKILL_FIELD',
    CANCEL_REMOVE_SKILL_FIELD: 'CANCEL_REMOVE_SKILL_FIELD',
    ADD_SKILL_FIELD: 'ADD_SKILL_FIELD',
    SKILL_FIELDS_EXPANDED_KEYS: 'SKILL_FIELDS_EXPANDED_KEYS',
    LOAD_SKILL_TABLE_ACCESS_DATA: 'LOAD_SKILL_TABLE_ACCESS_DATA',
    FETCH_ACTIVE_CURRENCY_SKILL: 'FETCH_ACTIVE_CURRENCY_SKILL',
    UPDATE_SKILL_FIELD: 'UPDATE_SKILL_FIELD',
    FETCH_ALL_ENTITIES: 'FETCH_ALL_ENTITIES',
    FETCH_ALL_ENTITIYTPES: 'FETCH_ALL_ENTITYTYPES',
    FETCH_ALL_CATEGORIES: 'FETCH_ALL_CATEGORIES',
    FETCH_ALL_SUBCATEGORIES: 'FETCH_ALL_SUBCATEGORIES',
    SKILL_FIELDS_REORDER: 'SKILL_FIELDS_REORDER',
    SKILL_TYPE_FIELD_ALREADY_EXIST: 'SKILL_TYPE_FIELD_ALREADY_EXIST',
    SKILL_TYPE_CLEAR_FIELD_ALREADY_EXIST_WARNING: 'SKILL_TYPE_CLEAR_FIELD_ALREADY_EXIST_WARNING',
    FETCH_ALL_DIVISIONS: 'FETCH_ALL_DIVISIONS',
    FETCH_ALL_DEPARTMENTS: 'FETCH_ALL_DEPARTMENTS'
};

export const IMPORT_LIBRARY_SKILLS = {
    FETCH_LIBRARY_SECTIONS: 'FETCH_LIBRARY_SECTIONS',
    FETCH_LIBRARY_SKILLS: 'FETCH_LIBRARY_SKILLS',
    UPDATE_SELECTED_SKILLS: 'UPDATE_SELECTED_SKILLS',
    IMPORT_LIBRARY_SKILLS: 'IMPORT_LIBRARY_SKILLS',
    IMPORT_LIBRARY_STATUS: 'IMPORT_LIBRARY_STATUS',
    SELECT_SKILL: 'SELECT_IMPORT_LIBRARY_SKILL',
    SEARCH_SKILL: 'SEARCH_IMPORT_LIBRARY_SKILL',
    CLEAR_LIBRARY_DATA: 'CLEAR_LIBRARY_DATA'
};

export const EDIT_RESOURCE_SKILLS_WINDOW = {
    OPEN: 'EDIT_RESOURCE_SKILLS_WINDOW_OPEN',
    CLOSE: 'EDIT_RESOURCE_SKILLS_WINDOW_CLOSE',
    CHANGE_TAB: 'EDIT_RESOURCE_SKILLS_CHANGE_TAB',
    BUILD_SECTIONS: 'EDIT_RESOURCE_SKILLS_WINDOW_BUILD_SECTIONS',
    COLLAPSE_SECTION: 'EDIT_RESOURCE_SKILLS_WINDOW_COLLAPSE_SECTION',
    ADD: {
        SKILLS: 'EDIT_RESOURCE_SKILLS_WINDOW_ADD_SKILLS'
    },
    REMOVE: {
        SKILL: 'EDIT_RESOURCE_SKILLS_WINDOW_REMOVE_SKILL'
    },
    UPDATE: {
        SKILLS: 'EDIT_RESOURCE_SKILLS_WINDOW_UPDATE_SKILLS',
        SKILLS_SUCCESS: 'EDIT_RESOURCE_SKILLS_WINDOW_UPDATE_SKILLS_SUCCESS'
    },
    AUTOCOMPLETE: {
        SEARCH_SKILLS: 'EDIT_RESOURCE_SKILLS_WINDOW_AUTOCOMPLETE_SEARCH_SKILLS',
        SEARCH_SKILLS_SUCCESS: 'EDIT_RESOURCE_SKILLS_WINDOW_AUTOCOMPLETE_SEARCH_SKILLS_SUCCESS',
        SEARCH_SKILLS_ERROR: 'EDIT_RESOURCE_SKILLS_WINDOW_AUTOCOMPLETE_SEARCH_SKILLS_ERROR',
        REMOVE_SKILLS: 'EDIT_RESOURCE_SKILLS_WINDOW_AUTOCOMPLETE_REMOVE_SKILLS'
    },
    UI: {
        DISCARD_CHANGES: 'EDIT_RESOURCE_SKILLS_WINDOW_DISCARD_CHANGES',
        DISCARD_DELETE: 'EDIT_RESOURCE_SKILLS_WINDOW_DISCARD_DELETE'
    }
};

export const RECOMMENDATION_SKILLS = {
    LOAD_RECOMMENDATION_SKILLS: 'LOAD_RECOMMENDATION_SKILLS',
    ACCEPT_RECOMMENDATION_SKILLS: 'ACCEPT_RECOMMENDATION_SKILLS',
    IGNORE_RECOMMENDATION_SKILLS: 'IGNORE_RECOMMENDATION_SKILLS',
    DISCARD_IGNORE_RECOMMENDATION_SKILLS: 'DISCARD_IGNORE_RECOMMENDATION_SKILLS'
};

export const RESOURCE_SKILLS_APPROVALS = {
    LOAD_APPROVALS_PENDING: 'LOAD_APPROVALS_PENDING',
    LOAD_APPROVALS_HISTORY: 'LOAD_APPROVALS_HISTORY'
};

export const UPDATE_AVATAR_WINDOW = {
    SET_VISIBILITY: 'SET_UPDATE_AVATAR_WINDOW_VISIBILITY',
    UPLOAD_AVATAR: 'UPDATE_AVATAR_WINDOW_UPLOAD_AVATAR',
    UPLOAD_AVATAR_SUCCESS: 'UPDATE_AVATAR_WINDOW_UPLOAD_AVATAR_SUCCESS',
    REMOVE_AVATAR: 'UPDATE_AVATAR_WINDOW_REMOVE_AVATAR',
    REMOVE_AVATAR_SUCCESS: 'UPDATE_AVATAR_WINDOW_REMOVE_AVATAR_SUCCESS'
};

export const AVATARS = {
    LOAD: 'LOAD_AVATAR',
    LOAD_MANY: 'LOAD_MANY_AVATARS',
    LOAD_SUCCESS: 'LOAD_AVATAR_SUCCESS',
    LOAD_AVATARS_SUCCESS: 'LOAD_AVATARS_SUCCESS',
    LOAD_AVATARS_BATCH_SUCCESS: 'LOAD_AVATARS_BATCH_SUCCESS',
    SET_CONFIG: 'SET_AVATAR_CONFIG',
    REMOVE: 'REMOVE_AVATAR'
};

export const FIELD_VALUE_EXPLANATIONS = {
    SET_EXPLANATIONS: 'SET_FIELD_VALUE_EXPLANATIONS',
    CLEAR_EXPLANATIONS: 'CLEAR_FIELD_VALUE_EXPLANATIONS',
    UPDATE_REPEAT_BOOKING_EXPLANATION: 'UPDATE_REPEAT_BOOKING_EXPLANATION'
};

export const FIELD_VALUE_MESSAGES = {
    SET_MESSAGES: 'SET_FIELD_VALUE_MESSAGES',
    CLEAR_MESSAGES: 'CLEAR_FIELD_VALUE_MESSAGES'
};

export const DELETE_JOB_PROMPT = 'DELETE_JOB_PROMPT';

export const DELETE_ROLE_PROMPT = 'DELETE_ROLE_PROMPT';

export const APPLICATION_CURRENCY = {
    LOAD: 'LOAD_APPLICATION_CURRENCY'
};

export const APPLICATION_HELP = {
    OPEN_KEYBOARD_SHORTCUTS_HELP: 'APPLICATION_HELP_OPEN_KEYBOARD_SHORTCUTS_HELP',
    OPEN_PLANNER_LEGEND: 'APPLICATION_HELP_OPEN_PLANNER_LEGEND'
};
export const COLOUR_SCHEME = {
    LOAD: 'LOAD_COLOUR_SCHEME',
    LOAD_SUCCESS: 'LOAD_COLOUR_SCHEME_SUCCESS',
    LOAD_ERROR: 'LOAD_COLOUR_SCHEME_ERROR',
    LOAD_CUSTOM_SCHEME: 'LOAD_CUSTOM_SCHEME',
    LOAD_CUSTOM_SCHEME_SUCCESS: 'LOAD_CUSTOM_SCHEME_SUCCESS'
};

export const COLOUR_SCHEME_LEGEND = {
    SET_VISIBILITY: 'SET_COLOUR_SCHEME_LEGEND_VISIBLITY',
    TOGGLE_VISIBILITY: 'TOGGLE_COLOUR_SCHEME_LEGEND_VISIBLITY'
};

export const CONFLICT_SETTINGS = {
    LOAD: 'LOAD_CONFLICT_SETTINGS',
    LOAD_SUCCESS: 'LOAD_CONFLICT_SETTINGS_SUCCESS',
    LOAD_ERROR: 'LOAD_CONFLICT_SETTINGS_ERROR'
};

export const CONTEXT_MENU_ACTIONS = {
    DELETE: 'DELETE',
    DUPLICATE: 'DUPLICATE'
};

export const TOASTER_ACTIONS = {
    SET_SUCCESS_TOASTER: 'SET_SUCCESS_TOASTER',
    SET_ERROR_TOASTER: 'SET_ERROR_TOASTER',
    RESET_TOASTER_STATUS: 'RESET_TOASTER_STATUS',
    SET_WARNING_TOASTER: 'SET_WARNING_TOASTER',
    SET_TOASTER_CONFIG: 'SET_TOASTER_CONFIG',
    SHOW_TOASTER_MESSAGE: 'SHOW_TOASTER_MESSAGE'
};

export const PROMPT_ACTIONS = {
    DISPATCH_OR_PROMPT: 'DISPATCH_OR_PROMPT',
    SET_MODAL_PROMPT: 'SET_MODAL_PROMPT',
    CLEAR_MODAL_PROMPT: 'CLEAR_MODAL_PROMPT',
    UPDATE_MODAL_PROMPT_CONTEXT: 'UPDATE_MODAL_PROMPT_CONTEXT'
};

export const LOAD_ROLE_PUBLICATION_CONTEXT_ERROR = 'LOAD_ROLE_PUBLICATION_COTEXT_ERROR';

export const COMMENTS = {
    LOAD_PAGED: 'LOAD_PAGED_COMMENTS',
    DIGEST_RELOAD: 'DIGEST_RELOAD_COMMENTS',
    RELOAD_PAGED: 'RELOAD_PAGED_COMMENTS',
    UPDATE_PAGED_DATA: 'UPDATE_PAGED_COMMENTS_DATA',
    CREATE: 'CREATE_COMMENT',
    CREATE_SUCCESS: 'CREATE_COMMENT_SUCCESS',
    CREATE_ERROR: 'CREATE_COMMENT_ERROR',
    EDIT_START: 'EDIT_COMMENT_START',
    EDIT_CANCEL: 'EDIT_COMMENT_CANCEL',
    EDIT_SUBMIT: 'EDIT_COMMENT_SUBMIT',
    EDIT_SUCCESS: 'EDIT_COMMENT_SUCCESS',
    EDIT_ERROR: 'EDIT_COMMENT_ERROR',
    EDIT_INSERT: 'EDIT_COMMENT_INSERT',
    UPDATE: 'UPDATE_COMMENT',
    DELETE: 'DELETE_COMMENT',
    DELETE_SUCCESS: 'DELETE_COMMENT_SUCCESS',
    DISCARD_CHANGES: 'DISCARD_COMMENTS_CHANGES'
};

export const AUDIT = {
    LOAD: 'LOAD_AUDIT',
    LOAD_SUCCESS: 'LOAD_AUDIT_SUCCESS',
    LOAD_ERROR: 'LOAD_AUDIT_ERROR',
    COLLAPSE_ENTRY: 'COLLAPSE_AUDIT_ENTRY',
    SORT_CHANGED: 'AUDIT_SORT_CHANGED'
};

export const ATTACHMENTS = {
    LOAD: 'LOAD_ATTACHMENTS',
    LOAD_SUCCESS: 'LOAD_ATTACHMENTS_SUCCESS',
    LOAD_ERROR: 'LOAD_ATTACHMENTS_ERROR',
    LOAD_CONFIG: 'LOAD_ATTACHMENTS_CONFIG',
    INSERT: 'INSERT_ATTACHMENT',
    DIGEST_INSERT: 'DIGEST_INSERT_ATTACHMENT',
    INSERT_SUCCESS: 'INSERT_ATTACHMENT_SUCCESS',
    INSERT_ERROR: 'INSERT_ATTACHMENT_ERROR',
    GET_ATTACHMENT_DATA: 'GET_ATTACHMENT_DATA',
    GET_ATTACHMENT_DATA_SUCCESS: 'GET_ATTACHMENT_DATA_SUCCESS',
    GET_ATTACHMENT_DATA_ERROR: 'GET_ATTACHMENT_DATA_ERROR',
    DELETE: 'DELETE_ATTACHMENT',
    DELETE_SUCCESS: 'DELETE_ATTACHMENT_SUCCESS',
    DELETE_ERROR: 'DELETE_ATTACHMENT_ERROR',
    SET_MESSAGES: 'SET_ATTACHMENT_MESSAGES',
    UI_ADD: 'ADD_ATTACHMENT_TO_UI',
    UI_REMOVE: 'REMOVE_ATTACHMENT_FROM_UI',
    DISCARD_CHANGES: 'DISCARD_ATTACHMENTS_CHANGES',
    DISCARD_MULTIPLE_ENTITIES_CHANGES: 'DISCARD_MULTIPLE_ENTITIES_ATTACHMENTS_CHANGES',
    CHANGE_TYPE: 'CHANGE_TYPE',
    CHANGE_ATTACHMENT_TYPE: 'CHANGE_ATTACHMENT_TYPE'
};

export const MULTI_VALUE_INPUT_ACTIONS = {
    ADD_MULTI_SELECT_VALUE: 'ADD_MULTI_SELECT_VALUE',
    REMOVE_MULTI_SELECT_VALUE: 'REMOVE_MULTI_SELECT_VALUE',
    RESET_MULTI_SELECT_VALUE: 'RESET_MULTI_SELECT_VALUE'
};

export const LICENSING_INFO_ACTIONS = {
    FETCH_LICENSING_INFO: 'FETCH_LICENSING_INFO',
    FETCH_ALL_LICENSING_INFO: 'FETCH_ALL_LICENSING_INFO'
};

export const ADMIN_ROUTER_ACTIONS = {
    SET_ROUTER_INFO: 'SET_ROUTER_INFO',
    RESET_ROUTER_INFO: 'RESET_ROUTER_INFO'
};

export const SET_USER_SELECTED_LANGUAGE = 'SET_USER_SELECTED_LANGUAGE';
export const POPULATE_TRANSLATION_MESSAGES = 'POPULATE_TRANSLATION_MESSAGES';
export const DELAYED_POPULATE_EW_TRANSLATION_MESSAGES = 'DELAYED_POPULATE_EW_TRANSLATION_MESSAGES';
export const POPULATE_EW_TRANSLATION_MESSAGES = 'POPULATE_EW_TRANSLATION_MESSAGES';
export const RELOAD_APP_TRANSLATION = 'RELOAD_APP_TRANSLATION';

export const SET_CONFIG = 'SET_CONFIG';
export const ADD_TO_CONFIG = 'ADD_TO_CONFIG';
export const UPDATE_CONFIG_TEST = 'UPDATE_CONFIG_TEST';

export const INIT_TRANSLATION = 'INIT_TRANSLATION';

export const BATCH_CRUD_ERROR = 'BATCH_CRUD_ERROR';
export const BATCH_CRUD_ERROR_PROMPT = 'BATCH_CRUD_ERROR_PROMPT';
export const SINGLE_CRUD_ERROR_PROMPT = 'SINGLE_CRUD_ERROR_PROMPT';

export const ROLL_FORWARD_SINGLE_CREATE_ERROR = 'ROLL_FORWARD_SINGLE_CREATE_ERROR';
export const ROLL_FORWARD_BATCH_CREATE_ERROR = 'ROLL_FORWARD_BATCH_CREATE_ERROR';

export const SELECTION_BAR = {
    SET_VISIBILITY: 'SET_SELECTION_BAR_VISIBILITY',
    CLOSE: 'CLOSE_SELECTION_BAR'
};

export const BATCHING_INTERCEPT = 'BATCHING_INTERCEPT';
export const LOAD_USER_ENTITY_ACCESS = 'LOAD_USER_ENTITY_ACCESS';
export const HANDLE_LOADED_USER_ENTITY_ACCESS = 'HANDLE_LOADED_USER_ENTITY_ACCESS';
export const LOAD_USER_ENTITY_ACCESS_SUCCESS = 'LOAD_USER_ENTITY_ACCESS_SUCCESS';
export const CLEAR_USER_ENTITY_ACCESS = 'CLEAR_USER_ENTITY_ACCESS';
export const SET_LOADING_USER_ENTITY_ACCESS_DATA = 'SET_LOADING_USER_ENTITY_ACCESS_DATA';

export const LOAD_WORKFLOW_ENTITY_ACCESS_SUCCESS = 'LOAD_WORKFLOW_ENTITY_ACCESS_SUCCESS';
export const ADD_WORKFLOW_ACCESSES_TO_ENTITY = 'ADD_WORKFLOW_ACCESSES_TO_ENTITY';
export const REMOVE_WORKFLOW_ACCESS_FROM_ENTITY = 'REMOVE_WORKFLOW_ACCESS_FROM_ENTITY';
export const ERASE_ENTITY_FROM_WORKFLOW_ACCESSES = 'ERASE_ENTITY_FROM_WORKFLOW_ACCESSES';

export const ROLE_GROUP_DP = {
    ROLE_GROUP_LOAD_ROLES: 'ROLE_GROUP_LOAD_ROLES',
    ROLE_GROUP_BUILD_ROLES: 'ROLE_GROUP_BUILD_ROLES',
    ROLE_GROUP_POPULATE_ROLES: 'ROLE_GROUP_POPULATE_ROLES'
};

export const LOAD_ROLE_GROUP_DETAILS = 'LOAD_ROLE_GROUP_DETAILS';
export const REFRESH_ROLE_GROUP = 'REFRESH_ROLE_GROUP';

export const JOB_PAGE_ROLE_GROUP_LIST_DP_ACTIONS = {
    POPULATE: {
        ROLE_GROUP: 'ROLE_GROUP_POPULATE'
    },
    LOAD: {
        ROLE_GROUP: 'ROLE_GROUP_LOAD_BY_ID'
    }
};

export const ROLE_GROUP_CREATION_MODAL = {
    OPEN: 'ROLE_GROUP_CREATION_MODAL_OPEN',
    OPEN_REQUEST: 'ROLE_GROUP_CREATION_MODAL_OPEN_REQUEST',
    CLOSE: 'ROLE_GROUP_CREATION_MODAL_CLOSE'
};

export const DIGEST_SAVE_ROLE_AS_TEMPLATE_SUCCESS = 'DIGEST_SAVE_ROLE_AS_TEMPLATE_SUCCESS';
export const DIGEST_SAVE_ROLE_AS_TEMPLATE_ERROR = 'DIGEST_SAVE_ROLE_AS_TEMPLATE_ERROR';
export const SAVE_AS_TEMPLATE_CREATION_MODAL = {
    OPEN: 'SAVE_AS_TEMPLATE_MODAL_OPEN',
    CLOSE: 'SAVE_AS_TEMPLATE_MODAL_CLOSE'
};

export const VIEW_ROLE_GROUP_LIST_PAGE_REQUEST = 'VIEW_ROLE_GROUP_LIST_PAGE_REQUEST';

export const SET_ROLEGROUP_LIST_PAGE_IDS = 'SET_ROLEGROUP_LIST_PAGE_IDS';

// Load page role request statuses
export const LOAD_PLANNER_ROLEREQUEST_STATUSES = 'LOAD_PLANNER_ROLEREQUEST_STATUSES';
export const LOAD_PLANNER_ROLEREQUEST_STATUSES_SUCCESS = 'LOAD_PLANNER_ROLEREQUEST_STATUSES_SUCCESS';
export const LOAD_ROLEREQUEST_STATUSES = 'LOAD_ROLEREQUEST_STATUSES';

export const BATCH_UPDATE_PLANNER_ROLEREQUEST_RESOURCE = 'BATCH_UPDATE_PLANNER_ROLEREQUEST_RESOURCE';
export const RELOAD_GROUPED_TABLE_DATA_PLANNER_MASTER_ROWS = 'RELOAD_GROUPED_TABLE_DATA_PLANNER_MASTER_ROWS';

export const UPDATE_PLANNER_ASSIGNEES = 'UPDATE_PLANNER_ASSIGNEES';

export const ROLE_INBOX_DUPLICATE_ROLEREQUEST = 'ROLE_INBOX_DUPLICATE_ROLEREQUEST';
export const ROLE_INBOX_ASSIGN_RESOURCE_TO_CRITERIA_ROLEREQUEST = 'ROLE_INBOX_ASSIGN_RESOURCE_TO_CRITERIA_ROLEREQUEST';
export const ROLE_INBOX_LOAD_REQUEST_ASSIGNEES = 'ROLE_INBOX_LOAD_REQUEST_ASSIGNEES';
export const POPULATE_ROLES_INFO = 'POPULATE_ROLES_INFO';
export const LOAD_ROLE_REQUEST_ASSIGNEES_INFO = 'LOAD_ROLE_REQUEST_ASSIGNEES_INFO';
export const PLANNER_ASSIGN_RESOURCE_TO_CRITERIA_ROLEREQUEST = 'PLANNER_ASSIGN_RESOURCE_TO_CRITERIA_ROLEREQUEST';
export const PROCESS_LOADED_ROLE_ASSIGNEES = 'PROCESS_LOADED_ROLE_ASSIGNEES';

export const MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION = 'MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION';
export const REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION = 'REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION';
export const MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION_SUCCESS = 'MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION_SUCCESS';
export const REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION_SUCCESS = 'REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION_SUCCESS';

export const MANAGE_PENDING_ROLEREQUEST_TIME_ALLOCATION_FAILED = 'MANAGE_PENDING_ROLEREQUEST_TIME_ALLOCATION_FAILED';

export const CREATE_DUPLICATE_ROLEREQUEST = 'CREATE_DUPLICATE_ROLEREQUEST';
export const ROLE_TRANSITION_DIALOG = {
    OPEN_DIALOG_REQUEST: 'ROLE_TRANSITION_DIALOG_OPEN_REQUEST',
    OPEN_DIALOG: 'ROLE_TRANSITION_DIALOG_OPEN',
    CLOSE_DIALOG: 'ROLE_TRANSITION_DIALOG_CLOSE',
    LOAD_DATA: 'ROLE_TRANSITION_DIALOG_LOAD_DATA',
    SUCCESS: 'ROLE_TRANSITION_SUCCESS',
    PERFORM: 'PERFORM_ROLE_TRANSITION',
    ERROR: 'ROLE_TRANSITION_ERROR',
    FAILED: 'ROLE_TRANSITION_FAILED',
    REJECT_REQUEST: 'reject_request',
    LOAD_PREDEFINED_REJECT_REASONS: 'LOAD_PREDEFINED_REJECT_REASONS',
    SELECT_REJECT_REASON: 'SELECT_REJECT_REASON',
    LOAD_ASSIGNEES_FOR_TRANSITION: 'LOAD_ASSIGNEES_FOR_TRANSITION'
};

export const GET_REQUESTED_ROLE_DATA = 'GET_REQUESTED_ROLE_DATA';

export const SET_REQUESTED_ROLE_DATA = 'SET_REQUESTED_ROLE_DATA';

export const CRITERIA_ACTIONS = {
    ADD_CRITERIA_FIELD: 'ADD_CRITERIA_FIELD',
    REMOVE_CRITERIA_FIELD: 'REMOVE_CRITERIA_FIELD',
    SET_CRITERIA_FIELDS: 'SET_CRITERIA_FIELDS',
    SET_CRITERIA_SKILLS: 'SET_CRITERIA_SKILLS',
    POPULATE_CRITERIAS_FOR_ROLES: 'POPULATE_CRITERIAS_FOR_ROLES',
    DISCARD_CRITERIA_CHANGES: 'DISCARD_CRITERIA_CHANGES',
    SET_CRITERIA_FIELDS_VALUE: 'SET_CRITERIA_FIELDS_VALUE',
    SET_CRITERIA_SKILLS_VALUE: 'SET_CRITERIA_SKILLS_VALUE',
    LOAD_CRITERIA_VALUES: 'LOAD_CRITERIA_VALUES',
    LOAD_CRITERIA_VALUES_FOR_MULTIPLE_ROLES: 'LOAD_CRITERIA_VALUES_FOR_MULTIPLE_ROLES',
    POPULATE_CRITERIA_VALUES: 'POPULATE_CRITERIA_VALUES',
    POPULATE_CRITERIA_VALUES_FOR_MULTIPLE_ROLES: 'POPULATE_CRITERIA_VALUES_FOR_MULTIPLE_ROLES',
    DUPLICATE_CRITERIA: 'DUPLICATE_CRITERIA',
    CREATE_ROLE_REQUIREMENTS_STATE: 'CREATE_ROLE_REQUIREMENTS_STATE',
    SET_CRITERIA_FIELDS_ERROR: 'SET_CRITERIA_FIELDS_ERROR',
    EXPAND_ROLE_CRITERIAS: 'EXPAND_ROLE_CRITERIAS',
    LOAD_CRITERIA_VALUES_FOR_GRID: 'LOAD_CRITERIA_VALUES_FOR_GRID',
    AUTOCOMPLETE_SKILLS: 'CRITERIA_ACTIONS_AUTOCOMPLETE_SKILLS',
    AUTOCOMPLETE_SKILLS_SUCCESS: 'CRITERIA_ACTIONS_AUTOCOMPLETE_SKILLS_SUCCESS',
    AUTOCOMPLETE_SKILLS_ERROR: 'CRITERIA_ACTIONS_AUTOCOMPLETE_SKILLS_ERROR',
    AUTOCOMPLETE_SKILLS_CLEAR: 'CRITERIA_ACTIONS_AUTOCOMPLETE_SKILLS_CLEAR',
    POPULATE_CRITERIAS_FOR_ROLES_TEMPLATE: 'POPULATE_CRITERIAS_FOR_ROLES_TEMPLATE'
};

export const CRITERIA_ROLE_ASSIGNMENT_ACTIONS = {
    POPULATE_ASSIGNEES_FOR_ALL: 'POPULATE_ASSIGNEES_FOR_ALL',
    POPULATE_ASSIGNEES_FOR_ROLE: 'REFRESH_ASSIGNEES_FOR_ROLE',
    SET_ACTIVE_ASSIGNEE: 'SET_ACTIVE_ASSIGNEE'
};

export const SUGGESTED_RESOURCES_ACTIONS = {
    LOAD_SUGGESTED_RESOURCE_LIST: 'LOAD_SUGGESTED_RESOURCE_LIST',
    CLEAR_SUGGESTED_RESOURCES: 'CLEAR_SUGGESTED_RESOURCES',
    BATCH_CLEAR_ENTITIES_SUGGESTED_RESOURCES: 'BATCH_CLEAR_ENTITIES_SUGGESTED_RESOURCES',
    LOAD_SUGGESTED_RESOURCE_LIST_SUCCESS: 'LOAD_SUGGESTED_RESOURCE_LIST_SUCCESS',
    SET_SELECTED_SUGGESTED_RESOURCE_INFO: 'SET_SELECTED_SUGGESTED_RESOURCE_INFO',
    UPDATE_ENTITY_SUGGESTED_RESOURCE_VALUES: 'UPDATE_ENTITY_SUGGESTED_RESOURCE_VALUES',
    TOGGLE_SUGGESTIONS_MATCHING_MECHANISM: 'TOGGLE_SUGGESTIONS_MATCHING_MECHANISM'
};

export const ASSIGN_RESOURCE_TO_CRITERIA_ROLE = 'ASSIGN_RESOURCE_TO_CRITERIA_ROLE';
export const UNASSIGN_RESOURCE_FROM_CRITERIA_ROLE = 'UNASSIGN_RESOURCE_FROM_CRITERIA_ROLE';

export const ASSIGN_RESOURCE_TO_CRITERIA = 'ASSIGN_RESOURCE_TO_CRITERIA';
export const ASSIGN_RESOURCE_TO_CRITERIA_SUCCESSFUL = 'ASSIGN_RESOURCE_TO_CRITERIA_SUCCESSFUL';
export const PLANNER_RESOURCE_ASSIGNED_TO_ROLE = 'PLANNER_RESOURCE_ASSIGNED_TO_ROLE';

export const SHORTLIST_RESOURCE = 'SHORTLIST_RESOURCE';
export const SHORTLIST_RESOURCE_SUCCESSFUL = 'SHORTLIST_RESOURCE_SUCCESSFUL';
export const SHORTLIST_RESOURCE_FAILED = 'SHORTLIST_RESOURCE_FAILED';
export const UNSHORTLIST_RESOURCE = 'UNSHORTLIST_RESOURCE';
export const UNSHORTLIST_RESOURCE_SUCCESSFUL = 'UNSHORTLIST_RESOURCE_SUCCESSFUL';
export const UNSHORTLIST_RESOURCE_FAILED = 'UNSHORTLIST_RESOURCE_FAILED';

export const ROLEREQUEST_MOVE_TO = 'ROLEREQUEST_MOVE_TO';
export const ROLEREQUEST_MOVE_TO_CREATE = 'ROLEREQUEST_MOVE_TO_CREATE';

export const TIMESHEETS_ACTIONS = {
    ON_DATE_SELECTION: 'ON_DATE_SELECTION',
    ON_TIMESHEET_ENTRIES_UPDATE: 'ON_TIMESHEET_ENTRIES_UPDATE',
    FETCH_TIMESHEET: 'FETCH_TIMESHEET',
    SAVE_TIMESHEET: 'SAVE_TIMESHEET',
    DELETE_TIMESHEET: 'DELETE_TIMESHEET',
    LOAD_TIMESHEET_JOBS: 'LOAD_TIMESHEET_JOBS',
    SET_SELECTED_TIMESHEET_JOBS: 'SET_SELECTED_TIMESHEET_JOBS',
    SET_SORT_ORDER: 'SET_SORT_ORDER '
};

export const NOTIFICATION_SERVICE = {
    START_CONNECTION: 'START_CONNECTION',
    CONNECTION_STATUS: 'CONNECTION_STATUS',
    CLOSE_CONNECTION: 'CLOSE_CONNECTION'
};

export const NOTIFICATION_DATA_ACTIONS = {
    NOTIFICATIONS_LOAD_DATA: 'NOTIFICATIONS_LOAD_DATA',
    LOAD_NOTIFICATION_HISTORY: 'LOAD_NOTIFICATION_HISTORY',
    LOAD_REAL_TIME_NOTIFICATION_HISTORY_DATA: 'LOAD_REAL_TIME_NOTIFICATION_HISTORY_DATA',
    LOAD_NOTIFICATION_SETTING: 'LOAD_NOTIFICATION_SETTING',
    NEW_NOTIFICATION_MESSAGE: 'NewNotificationMessage',
    DELETE_NOTIFICATIONS: 'DELETE_NOTIFICATIONS',
    CHANGE_NOTIFICATION_READ_UNREAD_STATUS: 'CHANGE_NOTIFICATION_READ_UNREAD_STATUS',
    MARK_ALL_NOTIFICATION_READ: 'MARK_ALL_NOTIFICATION_READ',
    DELETE_ENTITY_FROM_NOTIFICATION_EW: 'DELETE_ENTITY_FROM_NOTIFICATION_EW',
    UPDATE_NOTIFICATIONS_ENTITIES_TABLE_DATA: 'UPDATE_NOTIFICATIONS_ENTITIES_TABLE_DATA',
    LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT: 'LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT',
    LOAD_FIRST_BOOKING_IN_THE_SERIES: 'LOAD_FIRST_BOOKING_IN_THE_SERIES',
    UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA: 'UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA',
    LOAD_RECOMMENDATION_NOTIFICATION: 'LOAD_RECOMMENDATION_NOTIFICATION'
};

export const NOTIFICATION_SETTINGS_ACTIONS = {
    NOTIFICATION_SETTINGS_LOAD_DATA: 'NOTIFICATION_SETTINGS_LOAD_DATA',
    EMAIL_FREQUENCY_LOAD_DATA: 'EMAIL_FREQUENCY_LOAD_DATA',
    NOTIFICATION_SETTINGS_UPDATE_FORM: 'NOTIFICATION_SETTINGS_UPDATE_FORM',
    NOTIFICATION_SETTINGS_RESET_FORM: 'NOTIFICATION_SETTINGS_RESET_FORM',
    NOTIFICATION_SETTINGS_REMOVE_HIGHLIGHT: 'NOTIFICATION_SETTINGS_REMOVE_HIGHLIGHT',
    NOTIFICATION_SETTINGS_SAVE_FORM_DATA: 'NOTIFICATION_SETTINGS_SAVE_FORM_DATA',
    GLOBAL_NOTIFICATION_UPDATE_SETTINGS: 'GLOBAL_NOTIFICATION_UPDATE_SETTINGS',
    UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA: 'UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA'
};

export const SET_ACTIONABLE_REQUESTED_ROLE_DATA = 'SET_ACTIONABLE_REQUESTED_ROLE_DATA';

export const UNSAVED_CHANGES_PROMPT_ACTION = 'UNSAVED_CHANGES_PROMPT_ACTION';

export const ROLL_FORWARD_DIALOG = {
    OPEN: 'ROLL_FORWARD_DIALOG_OPEN',
    OPEN_FOR_EDIT: 'ROLL_FORWARD_DIALOG_OPEN_FOR_EDIT',
    CLOSE: 'ROLL_FORWARD_DIALOG_CLOSE',
    UPDATE_ALL_ENTITIES: 'ROLL_FORWARD_DIALOG_UPDATE_ALL_ENTITIES',
    OPTION_FIELD_CHANGE: 'ROLL_FORWARD_OPTION_FIELD_CHANGE',
    OPTION_FIELD_ERROR: 'ROLL_FORWARD_OPTION_FIELD_ERROR',
    SET_CAROUSEL_ELEMENT: 'ROLL_FORWARD_SET_CAROUSEL_ELEMENT',
    REMOVE_CAROUSEL_ELEMENT: 'ROLL_FORWARD_REMOVE_CAROUSEL_ELEMENT',
    CREATE_BOOKING: 'ROLL_FORWARD_CREATE_BOOKING',
    CREATE_BOOKING_SUCCESS: 'ROLL_FORWARD_CREATE_BOOKING_SUCCESS',
    CREATE_BOOKING_POST_OPERATIONS: 'ROLL_FORWARD_CREATE_BOOKING_POST_OPERATIONS',
    UPDATE_DESTINATION_DATE_OPTION: 'UPDATE_DESTINATION_DATE_OPTION'
};

export const JOB_DUPLICATE_DIALOG_ACTIONS = {
    OPEN: 'JOB_DUPLICATE_DIALOG_OPEN',
    OPEN_FOR_EDIT: 'JOB_DUPLICATE_DIALOG_OPEN_FOR_EDIT',
    SET_INITIAL_DATA: 'SET_INITIAL_DATA',
    CLOSE: 'JOB_DUPLICATE_DIALOG_CLOSE',
    UPDATE_ALL_ENTITIES: 'JOB_DUPLICATE_DIALOG_UPDATE_ALL_ENTITIES',
    OPTION_FIELD_CHANGE: 'JOB_DUPLICATE_OPTION_FIELD_CHANGE',
    OPTION_FIELD_ERROR: 'JOB_DUPLICATE_OPTION_FIELD_ERROR',
    CREATE_BOOKING: 'JOB_DUPLICATE_CREATE_BOOKING',
    CREATE_BOOKING_POST_OPERATIONS: 'JOB_DUPLICATE_CREATE_BOOKING_POST_OPERATIONS',
    SINGLE_CREATE_ERROR: 'JOB_DUPLICATE_SINGLE_CREATE_ERROR',
    LOAD_ADDITIONAL_TABLE_DATA: 'LOAD_ADDITIONAL_TABLE_DATA',
    INITIATE: 'JOB_DUPLICATE_INITIATE_DUPLICATION',
    UPDATE_ENTITY: 'UPDATE_ENTITY',
    UPDATE_FORM_DATA: 'UPDATE_JOB_DUPLICATE_FORM_DATA',
    RANGE_CHANGE: 'RANGE_CHANGE',
    UPDATE_DESTINATION_DATE_OPTION: 'UPDATE_DESTINATION_DATE_OPTION'
};

export const REPEAT_BOOKING_DIALOG_ACTIONS = {
    OPEN: 'REPEAT_BOOKING_DIALOG_OPEN',
    EDIT_SERIES: 'REPEAT_BOOKING_DIALOG_EDIT_SERIES',
    CLOSE: 'REPEAT_BOOKING_DIALOG_CLOSE',
    OPTION_FIELD_ERROR: 'REPEAT_BOOKING_OPTION_FIELD_ERROR',
    UPDATE_ENTITY: 'REPEAT_BOOKING_OPTION_UPDATE_ENTITY',
    UPDATE_FORM_DATA: 'REPEAT_BOOKING_UPDATE_FORM_DATA',
    UPDATE_EDIT_TYPE: 'REPEAT_BOOKING_UPDATE_EDIT_TYPE',
    UPDATE_BOOKING_SERIES: 'REPEAT_BOOKING_UPDATE_BOOKING_SERIES',
    UPDATE_RESOURCE_SUGGESTIONS: 'REPEAT_BOOKING_UPDATE_RESOURCE_SUGGESTIONS',
    GET_ALL_RESOURCE_AVAILABILITY: 'REPEAT_BOOKING_GET_ALL_RESOURCE_AVAILABILITY',
    CREATE_REPEAT_BOOKING: 'REPEAT_BOOKING_CREATE_REPEAT_BOOKING',
    SAVE_REPEAT_BOOKING_SUCCESS: 'REPEAT_BOOKING_SAVE_REPEAT_BOOKING_SUCCESS',
    CREATE_REPEAT_BOOKING_SUCCESS: 'REPEAT_BOOKING_CREATE_REPEAT_BOOKING_SUCCESS',
    CREATE_REPEAT_BOOKING_FAILURE: 'REPEAT_BOOKING_CREATE_REPEAT_BOOKING_FAILURE',
    UPDATE_REPEAT_BOOKING: 'REPEAT_BOOKING_UPDATE_REPEAT_BOOKING',
    UPDATE_REPEAT_BOOKING_SUCCESS: 'REPEAT_BOOKING_UPDATE_REPEAT_BOOKING_SUCCESS',
    UPDATE_REPEAT_BOOKING_FAILURE: 'REPEAT_BOOKING_UPDATE_REPEAT_BOOKING_FAILURE',
    DELETE_REPEAT_BOOKING: 'REPEAT_BOOKING_DELETE_REPEAT_BOOKING',
    DELETE_REPEAT_BOOKING_SUCCESS: 'REPEAT_BOOKING_DELETE_REPEAT_BOOKING_SUCCESS',
    DELETE_REPEAT_BOOKING_FAILURE: 'REPEAT_BOOKING_DELETE_REPEAT_BOOKING_FAILURE',
    POPULATE_BOOKING_SERIES: 'REPEAT_BOOKING_POPULATE_BOOKING_SERIES',
    CANCEL_POPULATE_BOOKING_SERIES: 'REPEAT_BOOKING_CANCEL_POPULATE_BOOKING_SERIES',
    RESET: 'REPEAT_BOOKING_RESET',
    SHOW_WARNING_UPDATE_SERIES_MODAL: 'SHOW_WARNING_UPDATE_SERIES_MODAL',
    HIDE_WARNING_UPDATE_SERIES_MODAL: 'HIDE_WARNING_UPDATE_SERIES_MODAL',
    OPEN_CONFIRMATION_DIALOG: 'REPEAT_BOOKING_OPEN_CONFIRMATION_DIALOG',
    CHANGE_TO_NON_RECURRENT_BOOKING: 'CHANGE_TO_NON_RECURRENT_BOOKING'
};

export const EDUCATION_SECTION = {
    DIALOG: {
        OPEN: 'EDUCATION_SECTION_DIALOG_OPEN',
        CLOSE: 'EDUCATION_SECTION_DIALOG_CLOSE',
        BULK_UPDATE: 'BULK_UPDATE'
    },
    EDIT: {
        START_INLINE_EDIT: 'START_INLINE_EDIT',
        CANCEL_INLINE_EDIT: 'CANCEL_INLINE_EDIT',
        OPTION_FIELD_CHANGE: 'OPTION_FIELD_CHANGE',
        ADD_NEW_RECORD: 'ADD_NEW_RECORD',
        DELETE_RECORD: 'DELETE_RECORD',
        OPTION_FIELD_APPLY_PATCH_CHANGES: 'OPTION_FIELD_APPLY_PATCH_CHANGES'
    },
    LOAD: {
        LOAD_DATA: 'LOAD_DATA',
        LOAD_PATCHED_DATA: 'LOAD_PATCHED_DATA'
    }
};

export const EXPERIENCE_SECTION = {
    DIALOG: {
        OPEN: 'EXPERIENCE_SECTION_DIALOG_OPEN',
        CLOSE: 'EXPERIENCE_SECTION_DIALOG_CLOSE',
        BULK_UPDATE: 'BULK_UPDATE'
    },
    EDIT: {
        START_INLINE_EDIT: 'START_INLINE_EDIT',
        CANCEL_INLINE_EDIT: 'CANCEL_INLINE_EDIT',
        OPTION_FIELD_CHANGE: 'OPTION_FIELD_CHANGE',
        OPTION_FIELD_ERROR: 'OPTION_FIELD_ERROR',
        ADD_NEW_RECORD: 'ADD_NEW_RECORD',
        DELETE_RECORD: 'DELETE_RECORD',
        OPTION_FIELD_APPLY_PATCH_CHANGES: 'OPTION_FIELD_APPLY_PATCH_CHANGES'
    },
    LOAD: {
        LOAD_DATA: 'LOAD_DATA',
        LOAD_PATCHED_DATA: 'LOAD_PATCHED_DATA'
    }
};

export const ROLEREQUEST_RESUBMIT_UPDATE = 'ROLEREQUEST_RESUBMIT_UPDATE';
export const PEOPLE_FINDERS_DIALOG = {
    OPEN: 'PEOPLE_FINDERS_DIALOG_OPEN',
    CLOSE: 'PEOPLE_FINDERS_DIALOG_CLOSE',
    SELECT_DATE_RANGE: 'SELECT_DATE_RANGE'
};

export const JOB_FILTER_DIALOG_ACTIONS = {
    OPEN: 'JOB_FILTER_DIALOG_OPEN',
    CLOSE: 'JOB_FILTER_DIALOG_CLOSE'
};

export const MASS_DUPLICATE_JOBS_ACTIONS = {
    SUBMIT_FORM: 'MASS_DUPLICATE_JOBS_SUBMIT_FORM',
    SUBMIT_FORM_SUCCESS: 'MASS_DUPLICATE_JOBS_SUBMIT_FORM_SUCCESS',
    SUBMIT_FORM_FAILURE: 'MASS_DUPLICATE_JOBS_SUBMIT_FORM_FAILURE',
    CLEAR_FORM: 'MASS_DUPLICATE_JOBS_CLEAR_FORM',
    REFRESH_SUMMARY: 'MASS_DUPLICATE_JOBS_REFRESH_SUMMARY',
    REFRESH_SUMMARY_ERROR: 'MASS_DUPLICATE_JOBS_REFRESH_SUMMARY_ERROR',
    REFRESH_SUMMARY_LOADING: 'MASS_DUPLICATE_JOBS_REFRESH_SUMMARY_LOADING',
    OPEN_CONFIRMATION_DIALOG: 'MASS_DUPLICATE_JOBS_OPEN_CONFIRMATION_DIALOG',
    FILTER_CHANGED: 'MASS_DUPLICATE_FILTER_CHANGED',
    FETCH_SUMMARY_COUNT: 'MASS_DUPLICATE_JOBS_FETCH_SUMMARY_COUNT',
    FETCH_SUMMARY_TABLE_DATA: 'MASS_DUPLICATE_JOBS_FETCH_SUMMARY_TABLE_DATA',
    OPEN_SUMMARY_TABLE_DIALOG: 'MASS_DUPLICATE_OPEN_SUMMARY_TABLE_DIALOG'
};

export const OPERATION_LOG_DIALOG_ACTIONS = {
    INITIATE_OPEN: 'OPERATION_LOG_INITIATE_OPEN',
    OPEN: 'OPERATION_LOG_DIALOG_OPEN',
    CLOSE: 'OPERATION_LOG_DIALOG_CLOSE',
    CANCEL_TASK: 'CANCEL_OPERATION_LOG',
    EXPANDED_OPEN: 'OPERATION_LOG_EXPANDED_DIALOG_OPEN',
    EXPANDED_CLOSE: 'OPERATION_LOG_EXPANDED_DIALOG_CLOSE',
    EXPANDED_LOAD_DATA: 'OPERATION_LOG_EXPANDED_LOAD_DATA'
};

export const REFRESH_DETAILS_PANE = 'REFRESH_DETAILS_PANE';

export const REFRESH_PAGE_ROLE_ASSIGNEES = 'REFRESH_PAGE_ROLE_ASSIGNEES';

export const REFRESH_PAGE_ROLE_ASSIGNEES_ERROR = 'REFRESH_PAGE_ROLE_ASSIGNEES_ERROR';

export const DIGEST_PLANNER_DATA_LOADED = 'DIGEST_PLANNER_DATA_LOADED';

export const LOAD_ASSIGNEES_POTENTIAL_CONFLICTS = 'LOAD_ASSIGNEES_POTENTIAL_CONFLICTS';
export const POPULATE_ASSIGNEES_POTENTIAL_CONFLICTS = 'POPULATE_ASSIGNEES_POTENTIAL_CONFLICTS';
export const LOAD_ASSIGNEES_POTENTIAL_CONFLICTS_ERROR = 'LOAD_ASSIGNEES_POTENTIAL_CONFLICTS_ERROR';

export const CHANGES_COLLECTION_ACTIONS = {
    DO_INSERT: 'DO_INSERT',
    DO_CLEAR_ALL: 'DO_CLEAR_ALL',
    DO_REMOVE_INSERT: 'DO_REMOVE_INSERT',
    DO_REMOVE: 'DO_REMOVE',
    DO_REMOVE_DELETE: 'DO_REMOVE_DELETE',
    DO_UPDATE_INSERT: 'DO_UPDATE_INSERT',
    DO_UPDATE: 'DO_UPDATE',
    DO_PATCH_INSERT: 'DO_PATCH_INSERT'
};

export const ACCEPT_COOKIE_POLICY = 'ACCEPT_COOKIE_POLICY';
export const SETUP_COOKIE_CONSENT_BANNER = 'SETUP_COOKIE_CONSENT_BANNER';

export const EXPANDED_KEYS_ACTIONS = {
    TOGGLE_KEY: 'TOGGLE_KEY',
    POPULATE_STATE: 'POPULATE_STATE',
    ADD_KEY: 'ADD_KEY'
};

export const REDIRECT_TO_ROLEGROUP_DETAILS_PAGE = 'REDIRECT_TO_ROLEGROUP_DETAILS_PAGE';

export const DELETE_ROLE_GROUP_REQUEST = 'DELETE_ROLE_GROUP_REQUEST';

export const DELETE_ROLE_GROUP = 'DELETE_ROLE_GROUP';

export const CME_PROFILING_TYPES = {
    DIALOG: {
        OPEN: 'CME_PROFILING_DIALOG_OPEN',
        CLOSE: 'CME_PROFILING_DIALOG_CLOSE'
    }
};

export const SUMMARY_PAGE_ACTIONS = {
    GET_WIDGET: 'GET_WIDGET',
    GET_WIDGET_SUCCESS: 'GET_WIDGET_SUCCESS',
    GET_WIDGET_FAILURE: 'GET_WIDGET_FAILURE',
    GET_WIDGET_SETTINGS: 'GET_WIDGET_SETTINGS',
    UPDATE_WIDGET_SETTINGS: 'UPDATE_WIDGET_SETTINGS',
    WIDGET_SETTINGS_SUCCESS: 'WIDGET_SETTINGS_SUCCESS',
    WIDGET_SETTINGS_FAILURE: 'WIDGET_SETTINGS_FAILURE',
    TOGGLE_ARRANGE_WIDGET: 'TOGGLE_ARRANGE_WIDGET',
    PAGE_WIDGET_LIST: 'PAGE_WIDGET_LIST',
    PAGE_WIDGET_LIST_SUCCESS: 'PAGE_WIDGET_LIST_SUCCESS',
    PAGE_WIDGET_LIST_FAILURE: 'PAGE_WIDGET_LIST_FAILURE',
    UPDATE_SUMMARY_OPEN_ON_LOGIN: 'UPDATE_SUMMARY_OPEN_ON_LOGIN',
    UPDATE_WIDGET_ACTIVE_STATUS: 'UPDATE_WIDGET_ACTIVE_STATUS',
    SUMMARY_DURATION_CHANGED: 'SUMMARY_DURATION_CHANGED'
};

export const FEATURE_MANAGEMENT_ACTIONS = {
    GET_FEATURE_FLAGS: 'GET_FEATURE_FLAGS',
    GET_FEATURE_FLAGS_SUCCESS: 'GET_FEATURE_FLAGS_SUCCESS',
    GET_FEATURE_FLAGS_FAILURE: 'GET_FEATURE_FLAGS_FAILURE'
};

export const LOAD_INITIAL_WIDGET_SETTINGS = 'LOAD_INITIAL_WIDGET_SETTINGS';
export const POLL_FEATURE_FLAG = 'POLL_FEATURE_FLAG';

export const LIST_PAGE_ACTIONS = {
    UPDATE_LIST_VIEW: 'UPDATE_LIST_VIEW'
};
