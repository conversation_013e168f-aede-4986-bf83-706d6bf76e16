import { COMMAND_BAR_PLANS_SECTION, PLANNER_COMMAND_BAR, COMMAND_BAR }from './actionTypes';

export function plannerCommandBarSetSectionVisiblity(sectionKey, visible) {
    return {
        type: PLANNER_COMMAND_BAR.SET_SECTION_VISIBILITY,
        payload: {
            sectionKey,
            visible
        }
    };
}

export function commandBarSetSectionVisibility(alias, sectionKey, visible) {
    return {
        type: `${COMMAND_BAR.SET_SECTION_VISIBILITY}_${alias}`,
        payload: {
            sectionKey,
            visible
        }
    };
}

export function commandBarPopulatePlansSection(workspaces, listPageAndBulkUpdateFeatureFlag) {
    return {
        type: COMMAND_BAR_PLANS_SECTION.POPULATE,
        payload: {
            workspaces,
            listPageAndBulkUpdateFeatureFlag,
            selectedWorkspaceGuid: workspaces.selected
        }
    };
}
export function commandBarRepopulatePlansSection(workspaces, listPageAndBulkUpdateFeatureFlag) {
    return {
        type: COMMAND_BAR_PLANS_SECTION.REPOPULATE,
        payload: {
            workspaces,
            selectedWorkspaceGuid: workspaces.selected,
            listPageAndBulkUpdateFeatureFlag
        }
    };
}

export function commandBarSelectPlan(workspaces, selectedWorkspaceGuid) {
    return {
        type: COMMAND_BAR_PLANS_SECTION.SELECT_PLAN,
        payload: {
            workspaces,
            selectedWorkspaceGuid
        }
    };
}

export function commandBarUpdateActionElementLabel(alias, sectionKey, label) {
    return {
        type: `${COMMAND_BAR.UPDATE_ACTION_ELEMENT_LABEL}_${alias}`,
        payload: {
            sectionKey,
            label
        }
    };
}