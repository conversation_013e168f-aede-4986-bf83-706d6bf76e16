import { IMPORT_LIBRARY_SKILLS, API_SUFFIX } from '../actionTypes';

// NOTE: The Import Library uses skillsConfigurationReducer since it is part of skillsConfiguration

//#region Import Library Actions
export function fetchLibrarySections() {
    return { type:IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS };
}

export function fetchLibrarySectionsSuccess(data) {
    return {
        type:`${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS}${API_SUFFIX.SUCCESS}`,
        payload:data
    };
}

export function fetchLibrarySectionsFailure() {
    return { type:`${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS}${API_SUFFIX.FAILURE}` };
}

export function fetchLibrarySkills(categoryId, subCategoryId, checkSkills) {
    return {
        type:IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS,
        payload: {
            categoryId,
            subCategoryId,
            checkSkills
        }
    };
}

export function fetchLibrarySkillsSuccess(categoryId, subCategoryId, data, checkSkills) {
    return {
        type:`${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS}${API_SUFFIX.SUCCESS}`,
        payload: {
            categoryId,
            subCategoryId,
            data,
            checkSkills
        }
    };
}

export function fetchLibrarySkillsFailure(categoryId, subCategoryId) {
    return {
        type:`${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS}${API_SUFFIX.FAILURE}`,
        payload: {
            categoryId,
            subCategoryId,
            data:[]
        }
    };
}

export function updateSelectedSkills(selectedSkills) {
    return {
        type:IMPORT_LIBRARY_SKILLS.UPDATE_SELECTED_SKILLS,
        payload: { selectedSkills }
    };
}

export function importLibrarySkills() {
    return { type: IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_SKILLS };
}

export function getImportLibraryStatus() {
    return { type: IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_STATUS };
}

export function getImportLibraryStatusSuccess(status) {
    return { type: `${IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_STATUS}${API_SUFFIX.SUCCESS}`, payload:status };
}

export function getImportLibraryStatusFailure() {
    return { type:`${IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_STATUS}${API_SUFFIX.FAILURE}`, payload: false };
}

export function selectLibrarySkill(skill) {
    return { type: IMPORT_LIBRARY_SKILLS.SELECT_SKILL, payload:skill };
}

export function searchImportLibrarySkills(searchTerm) {
    return { type: IMPORT_LIBRARY_SKILLS.SEARCH_SKILL, payload:{ searchTerm } };
}

export function searchImportLibrarySkillsSuccess(skills) {
    return { type: `${IMPORT_LIBRARY_SKILLS.SEARCH_SKILL}${API_SUFFIX.SUCCESS}`, payload:skills };
}

export function searchImportLibrarySkillsFailure() {
    return { type:`${IMPORT_LIBRARY_SKILLS.SEARCH_SKILL}${API_SUFFIX.FAILURE}`, payload:[] };
}

export function clearLibraryData() {
    return { type:IMPORT_LIBRARY_SKILLS.CLEAR_LIBRARY_DATA };
}
//#endregion