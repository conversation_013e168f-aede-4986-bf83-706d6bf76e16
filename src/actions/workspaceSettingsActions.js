import { PLANNER_PAGE_ALIAS } from '../constants';
import { COMMAND_BAR_PROP_KEY_TOGGLE_VALUE } from '../constants/commandBarConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import * as actionTypes from './actionTypes';

export function plannerChildResized(workspaceGuid, primaryWidthRatio) {
    return {
        type: actionTypes.PLANNER_CHILD_RESIZED,
        payload: {
            workspaceGuid,
            primaryWidthRatio
        }
    };
}

export function viewSettingChanged(workspaceGuid, tableDataGuid, plannerDataGuid, newViewSetting) {
    return {
        type: actionTypes.VIEW_SETTINGS_CHANGED,
        payload: {
            workspaceGuid,
            tableDataGuid,
            plannerDataGuid,
            newViewSetting
        }
    };
}

export function tableViewViewSettingChanged(workspaceGuid, tableViewDataGuid, newViewSetting) {
    return {
        type: `${actionTypes.VIEW_SETTINGS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`,
        payload: {
            workspaceGuid,
            tableViewDataGuid,
            newViewSetting
        }
    };
}

export function dateRangeChanged(workspaceGuid, startDate, endDate, actor) {
    return {
        type: actionTypes.DATE_RANGE_CHANGED,
        payload: {
            workspaceGuid,
            startDate,
            endDate,
            actor
        }
    };
}

export function tableViewDateRangeChanged(workspaceGuid, startDate, endDate, actor) {
    return {
        type: `${actionTypes.DATE_RANGE_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`,
        payload: {
            workspaceGuid,
            startDate,
            endDate,
            actor
        }
    };
}

export function dateToggleOptionChanged(workspaceGuid, dateToggleOption, unit) {
    return {
        type: actionTypes.DATE_TOGGLE_OPTION_CHANGED,
        payload: {
            workspaceGuid,
            dateToggleOption,
            unit
        }
    };
}

export function tableViewDateToggleOptionChanged(workspaceGuid, dateToggleOption, unit) {
    return {
        type: `${actionTypes.DATE_TOGGLE_OPTION_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`,
        payload: {
            workspaceGuid,
            dateToggleOption,
            unit
        }
    };
}

export function recordsListSortChanged(workspaceGuid, table, field, alias = null) {
    const actionType = alias ? `${actionTypes.RL_SORT}_${alias}` : actionTypes.RL_SORT;

    return {
        type: actionType,
        payload: {
            workspaceGuid,
            sort: {
                table,
                field
            }
        }
    };
}

export function columnOrderChanged(workspaceGuid, startIndex, endIndex, alias = null) {
    const actionType = alias ? `${actionTypes.COLUMN_ORDER_CHANGED}_${alias}` : actionTypes.COLUMN_ORDER_CHANGED;

    return {
        type: actionType,
        payload: {
            workspaceGuid,
            startIndex,
            endIndex
        }
    };
}

export function resizeTableColumn(workspaceGuid, newWidth, columnIndex, alias = null) {
    const actionType = alias ? `${actionTypes.TABLE_COLUMN_RESIZED}_${alias}` : actionTypes.TABLE_COLUMN_RESIZED;

    return {
        type: actionType,
        payload: {
            workspaceGuid,
            newWidth,
            columnIndex
        }
    };
}

export function plannerRowsDensityChanged(workspaceGuid, density) {
    return {
        type: actionTypes.PLANNER_ROWS_DENSITY_CHANGED,
        payload: {
            workspaceGuid,
            density
        }
    };
}

export function barFieldsChanged(workspaceGuid, fields, tableName) {
    return {
        type: actionTypes.BAR_FIELDS_CHANGED,
        payload: {
            workspaceGuid,
            fields,
            tableName
        }
    };
}

export function recordListSubRecFieldsChanged(workspaceGuid, tableName, fields, alias = null) {
    const actionType = alias ? `${actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED}_${alias}` : actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED;

    return {
        type: actionType,
        payload: {
            workspaceGuid,
            tableName,
            fields
        }
    };
}

export function recordListMasterRecFieldsChanged(workspaceGuid, tableName, fields, alias = null) {
    const actionType = alias ? `${actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED}_${alias}` : actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED;

    return {
        type: actionType,
        payload: {
            workspaceGuid,
            tableName,
            fields
        }
    };
}

export function setPlannerFieldsOptionsProps(workspaceGuid, fields, props) {
    return {
        type: actionTypes.SET_PLANNER_FIELD_OPTIONS_PROPS,
        payload: {
            workspaceGuid,
            fields,
            props
        }
    };
}

export function plannerPageFieldsChangedSuccess(workspaceGuid) {
    return {
        type: actionTypes.PLANNER_PAGE_FIELDS_CHANGED_SUCCESS,
        payload: { workspaceGuid }
    };
}

export function tableViewPageFieldsChangedSuccess(workspaceGuid) {
    return {
        type: actionTypes.TABLE_VIEW_PAGE_FIELDS_CHANGED_SUCCESS,
        payload: { workspaceGuid }
    };
}

export function addPlannerFieldOptionsModel(workspaceGuid, fieldOptions) {
    return {
        type: actionTypes.ADD_PLANNER_FIELD_OPTIONS_MODEL,
        payload: { workspaceGuid, fieldOptions }
    };
}

export function addTableViewFieldOptionsModel(workspaceGuid, fieldOptions) {
    return {
        type: actionTypes.ADD_TABLE_VIEW_FIELD_OPTIONS_MODEL,
        payload: { workspaceGuid, fieldOptions }
    };
}


export function filterSettingsChanged(workspaceGuid, alias = PLANNER_PAGE_ALIAS) {
    return {
        type: `${actionTypes.FILTERS_SETTINGS_CHANGED}_${alias}`,
        payload: {
            workspaceGuid
        }
    };
}

export function hideHistoricRecordsChanged(workspaceGuid, hideHistoricRecords) {
    return {
        type: actionTypes.HIDE_HISTORIC_RECORDS_CHANGED,
        payload: {
            workspaceGuid,
            hideHistoricRecords
        }
    };
}

export function tableViewHideHistoricRecordsChanged(workspaceGuid, hideHistoricRecords) {
    return {
        type: `${actionTypes.HIDE_HISTORIC_RECORDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`,
        payload: {
            workspaceGuid,
            hideHistoricRecords
        }
    };
}

export function hideFutureRecordsChanged(workspaceGuid, hideFutureRecords) {
    return {
        type: actionTypes.HIDE_FUTURE_RECORDS_CHANGED,
        payload: {
            workspaceGuid,
            hideFutureRecords
        }
    };
}

export function hideUnassignedRowsChanged(workspaceGuid, hideUnassignedResourceRows) {
    return {
        type: actionTypes.HIDE_UNASSIGNED_ROWS_CHANGED,
        payload: {
            workspaceGuid,
            hideUnassignedResourceRows
        }
    };
}

export function tableViewHideFutureRecordsChanged(workspaceGuid, hideFutureRecords) {
    return {
        type:  `${actionTypes.HIDE_FUTURE_RECORDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`,
        payload: {
            workspaceGuid,
            hideFutureRecords
        }
    };
}

export const hideInactiveResourcesChanged = (workspaceGuid, hideInactiveResources) => {
    return {
        type: `${actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED}`,
        payload: {
            workspaceGuid,
            hideInactiveResources
        }
    };
};

export const tableViewHideInactiveResourcesChanged = (workspaceGuid, hideInactiveResources) => {
    return {
        type: `${actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`,
        payload: {
            workspaceGuid,
            hideInactiveResources
        }
    };
};

export function hideJobTimelineChanged(workspaceGuid, hideJobTimeline) {
    return {
        type: actionTypes.HIDE_JOB_TIMELINE_CHANGED,
        payload: {
            workspaceGuid,
            hideJobTimeline
        }
    };
}

export function hideJobMilestonesChanged(workspaceGuid, hideJobMilestones) {
    return {
        type: actionTypes.HIDE_JOB_MILESTONES_CHANGED,
        payload: {
            workspaceGuid,
            hideJobMilestones
        }
    };
}

export function hideRolesChanged(workspaceGuid, hideRolesRecords) {
    return {
        type: actionTypes.HIDE_ROLES_RECORDS_CHANGED,
        payload: {
            workspaceGuid,
            hideRolesRecords
        }
    };
}

export function hideFieldsLabelsOnBarsChanged(shouldHide, barsTableName, workspaceGuid) {
    return {
        type: actionTypes.HIDE_FIELDS_LABELS_ON_BARS_CHANGED,
        payload: {
            shouldHide,
            barsTableName,
            workspaceGuid
        }
    };
}

export function hideUnassignedRolesChanged(workspaceGuid, hideUnassignedRoles) {
    return {
        type: actionTypes.HIDE_UNASSIGNED_ROLES_CHANGED,
        payload: {
            workspaceGuid,
            toggleKey: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_UNASSIGNED_ROLES,
            toggleValue: hideUnassignedRoles
        }
    };
}

export function hideRolesByNameChanged(workspaceGuid, hideRolesByName) {
    return {
        type: actionTypes.HIDE_ROLES_BY_NAME_CHANGED,
        payload: {
            workspaceGuid,
            toggleKey: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_NAME,
            toggleValue: hideRolesByName
        }
    };
}

export function hideRolesByRequirementsChanged(workspaceGuid, hideRolesByRequirements) {
    return {
        type: actionTypes.HIDE_ROLES_BY_REQUIREMENTS_CHANGED,
        payload: {
            workspaceGuid,
            toggleKey: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_CRITERIA,
            toggleValue: hideRolesByRequirements
        }
    };
}

export function hideDraftRolesChanged(workspaceGuid, hideDraftRolesRecords) {
    return {
        type: actionTypes.HIDE_DRAFT_ROLES_RECORDS_CHANGED,
        payload: {
            workspaceGuid,
            toggleKey: 'hideDraftRolesRecords',
            toggleValue: hideDraftRolesRecords
        }
    };
}

export function hideRequestedRolesChanged(workspaceGuid, hideRequestedRolesRecords) {
    return {
        type: actionTypes.HIDE_REQUESTED_ROLES_RECORDS_CHANGED,
        payload: {
            workspaceGuid,
            toggleKey: 'hideRequestedRolesRecords',
            toggleValue: hideRequestedRolesRecords
        }
    };
}

export function hideLiveBarsChanged(workspaceGuid, hideLiveBars) {
    return {
        type: actionTypes.HIDE_LIVE_BARS_CHANGED,
        payload: {
            workspaceGuid,
            toggleKey: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_LIVE_BARS,
            toggleValue: hideLiveBars
        }
    };
}

export function hideWeekendsChanged(workspaceGuid, hideWeekends) {
    return {
        type: actionTypes.HIDE_WEEKENDS_CHANGED,
        payload: {
            workspaceGuid,
            hideWeekends
        }
    };
}

export function hidePotentialConflictsChanged(workspaceGuid, hidePotentialConflicts) {
    return {
        type: actionTypes.HIDE_POTENTIAL_CONFLICTS_CHANGED,
        payload: {
            hidePotentialConflicts,
            workspaceGuid
        }
    };
}

export function cloneCommonViewHideToggles(workspaceGuid, from, to) {
    return {
        type: actionTypes.CLONE_COMMON_VIEW_HIDE_TOGGLES,
        payload: {
            workspaceGuid,
            from,
            to
        }
    };
}

export function storeWorkspaceViewSettingsAction(workspaceGuid, targetView) {
    return {
        type: actionTypes.STORE_WORKSPACE_VIEW_SETTINGS,
        payload: {
            workspaceGuid,
            targetView
        }
    };
}

export function restoreWorkspaceViewSettingsAction(workspaceGuid, view) {
    return {
        type: actionTypes.RESTORE_WORKSPACE_VIEW_SETTINGS,
        payload: {
            workspaceGuid,
            view
        }
    };
}

export function clearStoredWorkspaceViewSettingsAction(workspaceGuid, views) {
    return {
        type: actionTypes.CLEAR_STORED_WORKSPACE_VIEW_SETTINGS,
        payload: {
            workspaceGuid,
            views
        }
    };
}

export function closeSortFloatingActionBar() {
    return {
        type: actionTypes.CLOSE_SORT_FLOATING_ACTION_BAR
    };
}