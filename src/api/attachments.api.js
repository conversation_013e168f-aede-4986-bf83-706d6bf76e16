import RxPhoenixHttp from './RxPhoenixHttp/RxPhoenixHttp';
import { simpleMapPipe, unescapeServerResponsePipe } from './pipes.api';
import { AttachmentsConfigUrl, AttachmentsByEntityId, AttachmentById } from '../constants/globalConsts';
import BaseApi from './base.api';

export default class AttachmentAPI extends BaseApi {
    constructor(pipe = simpleMapPipe) {
        super(new RxPhoenixHttp(), pipe);

        this.httpInsertFile = new RxPhoenixHttp({}, '');
    }

    getAttachment$(tableName, entityId, attachmentId, getFileContent = true) {
        return this.http.get(AttachmentById(tableName, entityId, attachmentId), `getFileContent=${getFileContent}`)
            .pipe(this.pipe);
    }

    getAttachments$(tableName, entityId) {
        return this.http.get(AttachmentsByEntityId(tableName, entityId)).pipe(this.pipe);
    }

    getAttachmentsConfig$(tableName) {
        return this.http.get(AttachmentsConfigUrl(tableName)).pipe(this.pipe);
    }

    insertAttachment$(tableName, entityId, data) {
        return this.httpInsertFile.post(AttachmentsByEntityId(tableName, entityId), data).pipe(unescapeServerResponsePipe, this.pipe);
    }

    deleteAttachment$(tableName, entityId, attachmentId) {
        return this.http.delete(AttachmentById(tableName, entityId, attachmentId), attachmentId).pipe(this.pipe);
    }

    /**
     * The method is used to change the type of an attachment.
     * @param {string} tableName Defines tableName to which the attachment gets saved.
     * @param {string} entityId Defines resourceId.
     * @param {string} attachmentId Defines id of the attachment.
     * @param {string} moveDocumentTo defined the attachment is moved to 'Documents' or 'Others'
     * @returns {boolean} Return boolean on successful moving of the attachment.
     */
    moveDocumentTo$(tableName, entityId, attachmentId, moveDocumentTo) {
        const url = `${AttachmentById(tableName, entityId, attachmentId)}/${moveDocumentTo}`;

        return this.http.patch(url, {}).pipe(this.pipe);
    }
}