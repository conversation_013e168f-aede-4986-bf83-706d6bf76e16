import RxPhoenixHttp from './RxPhoenixHttp/RxPhoenixHttp';
import { simpleMapPipe, simpleMapPipeWithoutRequestFailedDispatch, simpleMapPipeDirectResponse, simpleMapPipeResponseWithWarning, overrideForbiddenResponsePipe } from './pipes.api';
import {
    AdminSettingGetOptionURL, AdminSettingUpdateSettingValueUrl, SECTION_CREATE_URL, SECTION_DUPLICATE_URL, SECTION_RENAME_URL,
    SECTION_DELETE_URL, PRE_VALIDATE_IMPORT, GET_ENTITY_TEMPLATE, UPLOAD_ENTITY_IMPORT, DAY_TYPES_CREATE, DAY_TYPES_DELETE,
    WORK_PATTERN_CREATE, WORK_PATTERN_DELETE, ADMIN_SETTING_BASE_URL_API, DIARY_CALENDAR, CURRENCY_API_URL, CHAR<PERSON>_CODE_URL,
    CHARGE_RATE_URL, COLOURSCHEME_TABLEFIELD_URL, GET_ALL_COLOUR_SCHEME, GET_BY_ID_COLOUR_SCHEME, UPDATE_COLOUR_SCHEME, ENTITY_CONFIGURATION_URL, FIELD_LOOKUP_VALUES_URL, FIELD_PROPERTY_URL, FIELD_PROPERTY_BYID_URL,
    USER_MANAGEMENT, CONFLICTS_API_URL, USER_SECURITYPROFILE_OPTION_URL, ENTITY_IMPORT, COMPANY_INFORMATION_API_URL, SECURITY_PROFILES_URL, SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL,
    FUNCTIONAL_ACCESS_RULES_URL, SkillSectionsUrl, SkillCategoriesUrl, SkillEntityTypesUrl, AllSkillCategoriesUrl, AllSkillSubCategoriesUrl, SKILL_ENTITY_DETAILS_URL,
    GET_LICENSE_INFO, NamedTableAccessBatchUrl, PagingTableAccessUrl, DIARY_IN_USE, ACTIVE_DIARY_TYPE, ACTIVE_FIELD_TYPE, GET_REPORT_SETTINGS_DATA, UPDATE_REPORT_SETTINGS_DATA, FilteredTableAccessUrl, TABLE_NAMES,
    FetchSkillsUrl, DIVISION_API_URL, DEPARTMENT_API_URL, StandardTaxonomiesUrl
} from '../constants';
import { map } from 'rxjs/operators';
import { DATASET_REFRESH_HISTORY_URL, DATASET_REFRESH_URL, SEND_SERVICE_PASSWORD_RESET } from '../constants/globalConsts';
import { Observable } from 'rxjs';

const http = new RxPhoenixHttp();

const getAdminSettingSubNavigation$ = () => {
    const queryParams = `depthLevel=${3}`;

    return http.get(AdminSettingGetOptionURL, queryParams).pipe(simpleMapPipe);
};

const getAdminSettingData$ = (area, section) => {
    const queryParams = `settingOption=${area + '|' + section}&depthLevel=${4}`;

    return http.get(AdminSettingGetOptionURL, encodeURI(queryParams)).pipe(simpleMapPipe);
};

const getAdminSettingDataWithChildSection$ = (area, section, childSection) => {
    const queryParams = `settingOption=${area + '|' + section + '|' + childSection}&depthLevel=${4}`;

    return http.get(AdminSettingGetOptionURL, encodeURI(queryParams)).pipe(simpleMapPipe);
};

const getAllColourScheme$ = () => {
    return http.get(GET_ALL_COLOUR_SCHEME).pipe(simpleMapPipe);
};

const getByIdColourScheme$ = (guid) => {
    const getByIDColourSchemeUrl = `${GET_BY_ID_COLOUR_SCHEME}/${guid}`;

    return http.get(getByIDColourSchemeUrl).pipe(simpleMapPipe);
};

const updateColorSchemeData$ = (configuration) => {
    return http.put(UPDATE_COLOUR_SCHEME, '', configuration).pipe(simpleMapPipe);
};

const updateAdminSettingData$ = (configuration) => {
    return http.put(AdminSettingUpdateSettingValueUrl, '', configuration).pipe(simpleMapPipe);
};

const deleteSectionData$ = (sectionInfo = {}) => {
    const { menuDetails = {} } = sectionInfo;
    const query = `sectionId=${menuDetails.id}`;

    return http.delete(SECTION_DELETE_URL, query).pipe(simpleMapPipe);
};

const renameSectionData$ = (sectionInfo = {}) => {
    const { menuDetails = {}, name } = sectionInfo;
    const query = `sectionId=${menuDetails.id}&sectionName=${name}`;

    return http.put(SECTION_RENAME_URL, query).pipe(simpleMapPipe);
};

const duplicateSectionData$ = (sectionInfo = {}) => {
    const { menuDetails = {} } = sectionInfo;
    const query = `sectionId=${menuDetails.id}`;

    return http.post(SECTION_DUPLICATE_URL, {}, query).pipe(simpleMapPipe);
};

const createSectionData$ = (sectionInfo = {}) => {
    const queryParams = `sectionId=${sectionInfo.parentSectionId}&sectionName=${sectionInfo.sectionName}`;

    return http.post(SECTION_CREATE_URL, {}, queryParams).pipe(simpleMapPipe);
};

const fetchDiariesMetaData$ = (payload = {}) => {
    return http.get(`${ADMIN_SETTING_BASE_URL_API}/${payload}`).pipe(simpleMapPipe);
};

const fetchDiaryContentById$ = (payload = {}) => {
    const url = `${ADMIN_SETTING_BASE_URL_API}/${payload.diaryType}/${payload.Id}`;

    return http.get(url).pipe(simpleMapPipe);
};

const fetchDays$ = (payload = {}) => {
    return http.get(`${ADMIN_SETTING_BASE_URL_API}/${payload}`).pipe(simpleMapPipe);
};

const updateDiaryContent$ = (payload = {}) => {
    const url = `${ADMIN_SETTING_BASE_URL_API}/${payload.diaryType}`;

    return http.put(url, '', payload.contents).pipe(
        map(response => ([response, [DIARY_IN_USE, ACTIVE_DIARY_TYPE]])),
        simpleMapPipeResponseWithWarning
    );
};

const batchUpdateDayTypeContent$ = (payload = {}) => {
    const url = `${ADMIN_SETTING_BASE_URL_API}/${payload.diaryType}/BatchUpdate`;

    return http.put(url, '', payload.contents).pipe(
        map(response => ([response, [DIARY_IN_USE, ACTIVE_DIARY_TYPE]])),
        simpleMapPipeResponseWithWarning
    );
};

const createDayType$ = (apiPayload = {}) => {
    const url = `${DAY_TYPES_CREATE}`;

    return http.post(url, apiPayload, '').pipe(simpleMapPipe);
};

const deleteDayType$ = (dayTypeInfo = {}) => {
    const { menuDetails = {} } = dayTypeInfo;
    const query = '';
    const url = `${DAY_TYPES_DELETE}/${menuDetails.id}`;

    return http.delete(url, query);
};

const createWorkPattern$ = (apiPayload = {}) => {
    const url = `${WORK_PATTERN_CREATE}`;

    return http.post(url, apiPayload, '').pipe(simpleMapPipe);
};

const deleteWorkPattern$ = (dayTypeInfo = {}) => {
    const { menuDetails = {} } = dayTypeInfo;
    const query = '';
    const url = `${WORK_PATTERN_DELETE}/${menuDetails.id}`;

    return http.delete(url, query);
};

const fetchChargeRateContent$ = () => {
    return http.get(CHARGE_RATE_URL).pipe(simpleMapPipe);
};

const fetchChargeRateContentById$ = (payload = {}) => {
    const url = `${CHARGE_RATE_URL}/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const createChargeRate$ = (apiPayload = {}) => {
    return http.post(CHARGE_RATE_URL, apiPayload, '').pipe(simpleMapPipe);
};

const deleteChargeRate$ = (apiPayload = {}) => {
    const { menuDetails = {} } = apiPayload;
    const url = `${CHARGE_RATE_URL}/${menuDetails.guid}`;

    return http.delete(url, '');
};

const updateChargeRateContent$ = (payload = {}) => {
    return http.put(`${CHARGE_RATE_URL}/BatchUpdate`, '', payload).pipe(simpleMapPipe);
};

const fetchEntityConfigurationContent$ = () => {
    return http.get(ENTITY_CONFIGURATION_URL).pipe(simpleMapPipe);
};

const fetchEntityConfigurationContentById$ = (payload = {}) => {
    const url = `${ENTITY_CONFIGURATION_URL}/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const updateEntityConfigurationContent$ = (payload) => {
    return http.put(`${ENTITY_CONFIGURATION_URL}/BatchUpdate`, '', payload).pipe(simpleMapPipe);
};

const fetchFieldLookupValuesContent$ = () => {
    return http.get(FIELD_LOOKUP_VALUES_URL).pipe(simpleMapPipe);
};

const fetchFieldLookupValuesContentById$ = (payload = '') => {
    const url = `${FIELD_LOOKUP_VALUES_URL}/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const updateFieldLookupValuesContent$ = (payload = {}) => {
    return http.put(FIELD_LOOKUP_VALUES_URL, '', payload).pipe(simpleMapPipe);
};

const batchUpdateFieldLookupValues$ = (payload = {}) => {
    return http.put(`${FIELD_LOOKUP_VALUES_URL}/BatchUpdate`, '', payload).pipe(
        map(response => ([response, [ACTIVE_FIELD_TYPE]])),
        simpleMapPipeResponseWithWarning
    );
};

const getDiaryCalendar$ = () => {
    const URL = DIARY_CALENDAR;

    return http.get(URL).pipe(simpleMapPipe);
};

const updateDiaryCalendar$ = (diaryPayload) => {
    return http.put(DIARY_CALENDAR, '', diaryPayload);
};

const fetchChargeCodeContent$ = () => {
    return http.get(CHARGE_CODE_URL).pipe(simpleMapPipe);
};

const updateChargeCodeContent$ = (payload = {}) => {
    return http.put(CHARGE_CODE_URL, '', payload).pipe(simpleMapPipe);
};

const fetchCurrencies$ = () => {
    const url = `${CURRENCY_API_URL}`;

    return http.get(url, '');
};

const fetchActiveCurrency$ = () => {
    const url = `${CURRENCY_API_URL}/active`;

    return http.get(url);
};

const updateActiveCurrency$ = (id) => {
    const url = `${CURRENCY_API_URL}/${id}`;

    return http.put(url, '', '').pipe(simpleMapPipeDirectResponse);
};

const fetchFieldPropertiesContent$ = () => {
    return http.get(FIELD_PROPERTY_URL).pipe(simpleMapPipe);
};

const fetchSkillEntityDetailsContent$ = (payload = {}) => {
    const queryParams = `entityName=${payload}`;

    return http.get(SKILL_ENTITY_DETAILS_URL, queryParams).pipe(simpleMapPipe);
};

const fetchFieldPropertyContentByEntityId$ = (payload = {}) => {
    const url = `${FIELD_PROPERTY_BYID_URL}/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const fetchFieldDefaultLookupByEntityId$ = (payload = {}) => {
    const url = `${FIELD_PROPERTY_BYID_URL}/GetFilteredLookup/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const getResourceBooleanFields$ = (payload = {}) => {
    const url = `${FIELD_PROPERTY_BYID_URL}/GetBooleanFields/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const updateFieldProperties$ = (payload = {}) => {
    const url = `${FIELD_PROPERTY_BYID_URL}/BatchUpdate`;

    return http.put(url, '', payload).pipe(simpleMapPipe);
};

const batchUpdateFieldProperties$ = (payload = {}) => {
    const url = `${FIELD_PROPERTY_BYID_URL}/BatchUpdate`;

    return http.put(url, '', payload).pipe(simpleMapPipe);
};

const getColourSchemeFields$ = () => {
    return http.get(COLOURSCHEME_TABLEFIELD_URL).pipe(simpleMapPipe);
};

const fetchLicensingInfoByKey$ = (licenseKey) => {
    const url = `${GET_LICENSE_INFO}/${licenseKey}`;

    return http.get(url).pipe(simpleMapPipe);
};

const fetchLicensingInfo$ = () => {
    const url = `${GET_LICENSE_INFO}`;

    return http.get(url).pipe(simpleMapPipe);
};

const fetchUserSecurityProfileOptions$ = () => {
    const url = USER_SECURITYPROFILE_OPTION_URL;

    return http.get(url).pipe(simpleMapPipe);
};

const fetchUserRecords$ = (userMetaData) => {
    const url = PagingTableAccessUrl('resource');
    let pagedData = '';
    if (userMetaData.id) {
        pagedData = pagedData.concat(`id=${userMetaData.id}&`);
    }
    const queryParams = `${pagedData}from=${userMetaData.from}&count=${userMetaData.count}`;

    return http.get(url, queryParams).pipe(simpleMapPipe);
};

const updateUserInfo$ = (tableName, payload) => {
    return http.patch(NamedTableAccessBatchUrl(tableName), '', payload).pipe(simpleMapPipeWithoutRequestFailedDispatch);
};

const batchAddUser$ = (tableName, payload) => {
    return http.post(NamedTableAccessBatchUrl(tableName), payload, '').pipe(simpleMapPipeWithoutRequestFailedDispatch);
};

const batchDeleteUser$ = (tableName, payload) => {
    return http.deleteMultiple(NamedTableAccessBatchUrl(tableName), payload).pipe(simpleMapPipeWithoutRequestFailedDispatch);
};

const sendUserActivationMail$ = (userInfo) => {
    const url = `${USER_MANAGEMENT}/sendemailinvite`;
    const payload = [userInfo.userGuid]; //This is temporary for single user guid, in case of bulk edit support, userInfo.userGuid is supposed to be an array datatype directly to pass in URL

    return http.post(url, payload, '').pipe(simpleMapPipe);
};

const sendBulkUserActivationMail$ = (payload) => {
    const url = `${USER_MANAGEMENT}/sendemailinvite`;

    return http.post(url, payload, '').pipe(simpleMapPipe);
};

const sendResetPassphraseEmail$ = (userInfo) => {
    const url = `${USER_MANAGEMENT}/sendpasswordreset`;
    const payload = [userInfo.userGuid]; //This is temporary for single user guid, in case of bulk edit support, userInfo.userGuid is supposed to be an array datatype directly to pass in URL

    return http.post(url, payload, '').pipe(simpleMapPipe);
};

const sendBulkResetPassphraseEmail$ = (payload) => {
    const url = `${USER_MANAGEMENT}/sendpasswordreset`;

    return http.post(url, payload, '').pipe(simpleMapPipe);
};

const sendCMeSurveyRequest$ = (url, payload) => {
    return http.post(url, payload, '', true).pipe(simpleMapPipe);
};

const fetchConflicts$ = () =>{
    const url = `${CONFLICTS_API_URL}`;

    return http.get(url, '');
};

const updateActiveConflicts$ = (payload = {}) => {
    return http.put(CONFLICTS_API_URL, '', payload).pipe(simpleMapPipe);
};

const uploadEntity$ = (data) => {
    const httpWithHeaders = new RxPhoenixHttp({ headers: { 'Content-Type': 'multipart/form-data' } });
    const url = `${UPLOAD_ENTITY_IMPORT}`;

    return httpWithHeaders.post(url, data).pipe(simpleMapPipeDirectResponse);
};

const addValidEntityImportDataEntry$ = (entityType, allEntities) => {
    const url = `${ENTITY_IMPORT}`;
    const queryParams = `type=${entityType}`;

    return http.post(url, allEntities, queryParams).pipe(simpleMapPipe);
};

const validateEntityImportDataEntry$ = (entityType, allEntities) => {
    const url = `${PRE_VALIDATE_IMPORT}`;
    const queryParams = `type=${entityType}`;

    return http.post(url, allEntities, queryParams).pipe(simpleMapPipe);
};

const DownloadEntityTemplate$ = (entityType) => {
    const url = `${GET_ENTITY_TEMPLATE}`;
    const queryParams = `type=${entityType}`;

    return http.get(url, queryParams).pipe(simpleMapPipeDirectResponse);
};

const fetchCompanyInformation$ = () => {
    const url = `${COMPANY_INFORMATION_API_URL}/GetByIdCompany`;

    return http.get(url, '');
};

const fetchCompanyInformationPiped$ = () => {
    return fetchCompanyInformation$().pipe(simpleMapPipe);
};

const updateCompanyInformation$ = (payload = {}) => {
    const httpWithHeaders = new RxPhoenixHttp({ headers: { 'Content-Type': 'multipart/form-data' } });
    const url = `${COMPANY_INFORMATION_API_URL}/UpdateCompanyInfo`;

    return httpWithHeaders.post(url, payload, '').pipe(simpleMapPipe);
};

const fetchFunctionalAccessRules$ = () => {
    return http.get(FUNCTIONAL_ACCESS_RULES_URL, '').pipe(simpleMapPipe);
};

const fetchSecurityProfile$ = () => {
    return http.get(SECURITY_PROFILES_URL, '').pipe(simpleMapPipe);
};

const fetchSecurityProfileDetails$ = ({ securityProfileGuid, entityId }) => {
    const queryParams = `id=${securityProfileGuid}&entityId=${entityId}`;

    return http.get(SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL, queryParams).pipe(simpleMapPipe);
};

const createSecurityProfile$ = (payload) => {
    const body = JSON.stringify(payload);

    return http.post(SECURITY_PROFILES_URL, body, '').pipe(simpleMapPipe);
};

const deleteSecurityProfile$ = (id) => {
    const queryParams = `id=${id}`;

    return http.delete(SECURITY_PROFILES_URL, queryParams).pipe(simpleMapPipe);
};

const updateSecurityProfile$ = (payload) => {
    return http.post(SECURITY_PROFILE_ENTITY_DETAILS_ACCESS_URL, payload, '').pipe(simpleMapPipe);
};

/**
 *
 * @param {string} sort
 * @returns {Observable<import('../types/category.jsdoc').Category>}
 */
const fetchSkillsConfigurationSections$ = (sort = null) => {
    const query = `sort=${sort}`;

    return http.get(SkillCategoriesUrl, query).pipe(simpleMapPipe);
};

/**
 *
 * @param {string} sort
 * @returns {Observable<import('../types/skillEntityType.jsdoc').SkillEntityType>}
 */
const fetchSkillEntityTypes$ = (sort = null) => {
    const query = `sort=${sort}`;

    return http.get(SkillEntityTypesUrl, query).pipe(simpleMapPipe);
};

/**
 *
 * @param {string} searchTerm
 * @returns {Observable<import('../types/category.jsdoc').BaseCategory>}
 */
const fetchCategories$ = (searchTerm = '') => {
    const query = `searchTerm=${searchTerm}`;

    return http.get(AllSkillCategoriesUrl, query).pipe(simpleMapPipe);
};

/**
 * @returns {Observable<import('../types/department.jsdoc').Department>}
 */
const fetchDepartments$ = () => {
    return http.get(DEPARTMENT_API_URL).pipe(simpleMapPipe);
};

/**
 * @returns {Observable<import('../types/division.jsdoc').Division>}
 */
const fetchDivisions$ = () => {
    return http.get(DIVISION_API_URL).pipe(simpleMapPipe);
};

/**
 *
 * @param {string} searchTerm
 * @returns {Observable<import('../types/subcategory.jsdoc').SubCategory>}
 */
const fetchSubCategories$ = (searchTerm = '') => {
    const query = `searchTerm=${searchTerm}`;

    return http.get(AllSkillSubCategoriesUrl, query).pipe(simpleMapPipe);
};

/**
 * @param {string} payload
 * @param {string} sort
 * @returns {Observable<import('../types/skillTaxonomyCategory.jsdoc').SkillTaxonomyCategory>}
 */
const fetchSkillsConfigurationBySectionId$ = (payload = {}, sort = null) => {
    const url = `${SkillCategoriesUrl}/${payload}`;
    const query = `sort=${sort}`;

    return http.get(url, query).pipe(simpleMapPipe);
};

const fetchSkillNames$ = (skillName) => {
    const query = `skillname=${skillName}`;

    return http.post(FetchSkillsUrl, [], query);
};

const batchUpdateSkillsConfigurationSections$ = (payload = []) => {
    const url = `${SkillCategoriesUrl}/BatchUpdate`;

    return http.put(url, '', payload);
};

const fetchChargeRatesSelectionListData$ = () => {
    return http.get(CHARGE_RATE_URL).pipe(simpleMapPipe);
};

const fetchDayTypeSelectionListData$ = () => {
    return http.get(DAY_TYPES_CREATE).pipe(simpleMapPipe);
};

const fetchDayTypeDataById$ = (payload = {}) => {
    const url = `${DAY_TYPES_CREATE}/${payload}`;

    return http.get(url).pipe(simpleMapPipe);
};

const fetchReportSettingsData$ = () => {
    const url = `${GET_REPORT_SETTINGS_DATA}`;

    return http.get(url);
};

const updateReportSettingsData$ = (payload) => {
    return http.put(UPDATE_REPORT_SETTINGS_DATA, '', payload);
};

/**
 * Perform dataset refresh
 * @param {Observable<import('../types/datasetRefreshRequest.jsdoc').DatasetRefreshRequest>} [payload=null] Optional refresh request
 * @returns {string} Refresh Request Id or Error message
 */
const performDatasetRefresh$ = (payload = null) => {
    return http.post(DATASET_REFRESH_URL, '', payload);
};

/**
 * Get dataset refresh history data
 *
 * @returns {Observable<import('../types/refreshHistory.jsdoc').RefreshHistory>}
 */
const getDatasetRefreshHistory$ = () => {
    return http.get(DATASET_REFRESH_HISTORY_URL);
};

const getServiceAccountLicenseInfo$ = (licenseKey) => {
    const url = `${GET_LICENSE_INFO}/${licenseKey}`;

    return http.get(url).pipe(simpleMapPipe);
};

const setServiceAccountPassword$ = (userInfo) => {
    const url = SEND_SERVICE_PASSWORD_RESET;
    const payload = [userInfo.id];
    const queryParams = '';

    return http.post(url, payload, queryParams).pipe(overrideForbiddenResponsePipe);
};

/**
 * Get standard skill library
 *  @returns {Observable<import('../types/standardSkillTaxonomy.jsdoc').StandardSkillTaxonomy>}
 */
const fetchLibrarySections$ = () => {
    return http.get(StandardTaxonomiesUrl).pipe(simpleMapPipe);
};

/**
 * Search skill from standard skill library
 * @param {Number} categoryId category of skill
 * @param {Number} subCategoryId subCategory of skill
 *  @returns {Observable<import('../types/standardSkill.jsdoc').StandardSkill>}
 */
const fetchLibrarySkills$ = (categoryId, subCategoryId) => {
    const url = `${StandardTaxonomiesUrl}/${categoryId}/subcategories/${subCategoryId}/skills`;

    return http.get(url).pipe(simpleMapPipe);
};

/**
 * Search skill from standard skill library
 * @param {String} searchTerm skill to search
 *  @returns {Observable<import('../types/standardSkill.jsdoc').StandardSkill>}
 */
const searchImportLibrarySkill$ = (searchTerm) =>{
    const url = `${StandardTaxonomiesUrl}/skills`;
    const query = `searchTerm=${searchTerm}`;

    return http.get(url,query).pipe(simpleMapPipe);
};

export {
    fetchDiariesMetaData$,
    fetchDiaryContentById$,
    updateDiaryContent$,
    batchUpdateDayTypeContent$,
    getAdminSettingSubNavigation$,
    getAdminSettingData$,
    getAllColourScheme$,
    getByIdColourScheme$,
    updateAdminSettingData$,
    updateColorSchemeData$,
    deleteSectionData$,
    renameSectionData$,
    duplicateSectionData$,
    createSectionData$,
    deleteDayType$,
    createDayType$,
    createWorkPattern$,
    deleteWorkPattern$,
    fetchDays$,
    getAdminSettingDataWithChildSection$,
    fetchChargeRateContent$,
    fetchChargeRateContentById$,
    createChargeRate$,
    deleteChargeRate$,
    updateChargeRateContent$,
    fetchChargeCodeContent$,
    updateChargeCodeContent$,
    fetchEntityConfigurationContent$,
    fetchEntityConfigurationContentById$,
    updateEntityConfigurationContent$,
    fetchFieldLookupValuesContent$,
    fetchFieldLookupValuesContentById$,
    updateFieldLookupValuesContent$,
    batchUpdateFieldLookupValues$,
    getDiaryCalendar$,
    updateDiaryCalendar$,
    fetchCurrencies$,
    fetchActiveCurrency$,
    updateActiveCurrency$,
    fetchFieldPropertiesContent$,
    fetchFieldPropertyContentByEntityId$,
    fetchFieldDefaultLookupByEntityId$,
    getResourceBooleanFields$,
    updateFieldProperties$,
    batchUpdateFieldProperties$,
    getColourSchemeFields$,
    fetchUserSecurityProfileOptions$,
    fetchUserRecords$,
    fetchConflicts$,
    updateActiveConflicts$,
    updateUserInfo$,
    batchAddUser$,
    batchDeleteUser$,
    sendUserActivationMail$,
    sendResetPassphraseEmail$,
    sendBulkUserActivationMail$,
    sendBulkResetPassphraseEmail$,
    sendCMeSurveyRequest$,
    uploadEntity$,
    addValidEntityImportDataEntry$,
    validateEntityImportDataEntry$,
    DownloadEntityTemplate$,
    fetchCompanyInformation$,
    fetchCompanyInformationPiped$,
    updateCompanyInformation$,
    fetchFunctionalAccessRules$,
    fetchSecurityProfile$,
    fetchSecurityProfileDetails$,
    createSecurityProfile$,
    deleteSecurityProfile$,
    updateSecurityProfile$,
    fetchSkillsConfigurationSections$,
    fetchSkillsConfigurationBySectionId$,
    batchUpdateSkillsConfigurationSections$,
    fetchChargeRatesSelectionListData$,
    fetchDayTypeSelectionListData$,
    fetchDayTypeDataById$,
    fetchLicensingInfoByKey$,
    fetchSkillEntityDetailsContent$,
    fetchLicensingInfo$,
    fetchReportSettingsData$,
    updateReportSettingsData$,
    getServiceAccountLicenseInfo$,
    setServiceAccountPassword$,
    fetchSkillEntityTypes$,
    fetchCategories$,
    fetchSubCategories$,
    fetchSkillNames$,
    fetchDepartments$,
    fetchDivisions$,
    fetchLibrarySections$,
    fetchLibrarySkills$,
    performDatasetRefresh$,
    getDatasetRefreshHistory$,
    searchImportLibrarySkill$
};
