import RxPhoenixHttp from '../RxPhoenixHttp/RxPhoenixHttp';
import BaseApi from '../base.api';
import { simpleMapPipe } from '../pipes.api';
import { ResourceSkillsByIdUrl, ResourceSkillsRecommendationByIdUrl, ResourceSkillsApprovalsPendingByIdUrl, ResourceSkillsApprovalsHistoryByIdUrl, FilteredResourceSkillsUrl, SkillPreferenceUrl } from '../../constants';
import { getSkillExpiryFieldSelector } from '../../selectors/skillExpirySelector';
import { AuthorizedResourceSkills } from '../../constants/globalConsts';

const toServerSkill = (({ id, levelId, fields = [], skillPreference, isIgnored = false }) => {
    const skillExpiryFieldId = getSkillExpiryFieldSelector().baseSkillExpiryField.id;

    return {
        resourceskill_skill_guid: id,
        resourceskill_skilllevel_guid: levelId,
        // filter skill expiry field from fields
        fieldList: fields.filter(field => field.fieldId !== skillExpiryFieldId),
        resourceskill_skillpreference: skillPreference,
        // get skill expiry date from fields
        resourceskill_skillexpirydate: fields.find(field => field.fieldId === skillExpiryFieldId)?.fieldValue,
        resourceskill_isignored: isIgnored
    };
});

export default class ResourceSkillsApi extends BaseApi {
    constructor(pipe = simpleMapPipe) {
        super(new RxPhoenixHttp(), pipe);
    }

    getResourceSkills$(resourceId) {
        return this.http.get(ResourceSkillsByIdUrl(resourceId)).pipe(this.pipe);
    }

    getAutoCompleteSkills$(sectionId, searchTerm, maxResults, filterByResourcePermission) {
        let queryParams = this.getQueryParams({ searchTerm: encodeURIComponent(searchTerm), section: sectionId, maxResults, filterByResourcePermission });

        return this.http.post(FilteredResourceSkillsUrl, '', queryParams).pipe(this.pipe);
    }

    updateResourceSkills$(resourceId, skills) {
        return this.http.post(ResourceSkillsByIdUrl(resourceId), skills.map(toServerSkill)).pipe(this.pipe);
    }

    getSkillPreferences$() {
        return this.http.get(SkillPreferenceUrl).pipe(this.pipe);
    }

    getResourceSkillsRecommendations$(resourceId) {
        return this.http.get(ResourceSkillsRecommendationByIdUrl(resourceId)).pipe(this.pipe);
    }

    getResourceSkillsApprovalsPending$(resourceId) {
        return this.http.get(ResourceSkillsApprovalsPendingByIdUrl(resourceId)).pipe(this.pipe);
    }

    getResourceSkillsApprovalsHistory$(resourceId) {
        return this.http.get(ResourceSkillsApprovalsHistoryByIdUrl(resourceId)).pipe(this.pipe);
    }

    /**
     * The method is used get the authorized resource skills.
     * @returns {Observable<import('../../types/authorizeResourceSkillDTO.jsdoc').AuthorizeResourceSkillDTO[]>}
     */
    getAuthorizeResourceSkills$() {
        return this.http.get(AuthorizedResourceSkills).pipe(this.pipe);
    }
}
