import { connect } from 'react-redux';
import { IdentityServerAuthentication } from '../authentication/authentication';
import React from 'react';
import { Route, Switch, Redirect } from 'react-router-dom';
import { Layout, Spin, Tooltip } from 'antd';
import { withHotKeys } from '../lib/hotKeys/hotKeys';
import { Icon } from '../lib';
import styles from '../../styles/base.less';
import { ErrorPage } from './errorPages';
import {
  createDynImportedComponent,
  createDynImportedComponentCreator,
} from '../factories/dynamicImportComponentFactory';
import { getPageLoadFunc, buildPagesIndex } from '../pages';
import {
  getAllPages,
  getErrorPageGoToPage,
  getPageLinkSelector,
} from '../selectors/navigationSelectors';
import { hasFunctionalAccessGlobal } from '../selectors/applicationFnasSelectors';
import { browserHistory, getCurrentPage, getCurrentSubPage } from '../history';
import {
  navBarPageChange,
  navBarCollapse,
  PAGE_ACTIONS,
} from '../actions/navigateActions';
import {
  getHotKeyConfig,
  triggerHotKeyAction,
} from '../utils/hotKeys/globalHotKeysUtils';
import { globalHotKeysConfigByStrategy } from '../state/hotKeys/global/hotKeysConfig';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';
import { ADMIN_SETTINGS_ALIAS } from '../constants/adminSettingConsts';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { ERROR_PAGES } from '../pages/pages';
import Logout from '../pages/logout';
import store from '../store/configureStore';
import { SILENT_RENEW_PATH } from '../constants/authenticationConsts';
import { StaticMessagesContext } from '../contexts/staticMessagesContext';
import appInsights from '../utils/applicationInsightsService';
import { SUMMARY_PAGE_ALIAS } from '../constants/summaryPageConsts';
import { getWidgetSettingsSelector } from '../selectors/summaryPageSelector';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';
import { JOBS_PAGE_ALIAS } from '../constants';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';

const { Sider } = Layout;

const siderBreakpoint = {
  xs: '480px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  xxl: '1600px',
};

const CompanyInformation = React.lazy(() =>
  import('../components/companyInformation/companyInformation.connect')
);
const MaintenanceStatusBanner = React.lazy(() =>
  import('../feature/maintenanceStatusBanner/components/banner')
);
const ConnectedCookieConsentBanner = React.lazy(() =>
  import('../connectedComponents/connectedCookieConsentBanner').then(
    (module) => ({ default: module.ConnectedCookieConsentBanner })
  )
);

class MainLayout extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      collapsed: false,
    };
    appInsights.trackPageView();
    this.authentication = new IdentityServerAuthentication();
    this.historyHookUnlisten = () => null;
  }

  shouldLoadRoutes() {
    return this.props.shouldLoadRoutes && this.authentication.getToken();
  }

  shouldRenderNav(pages = []) {
    const currentPageName = getCurrentPage(pages);
    const currentSubPageName = getCurrentSubPage();
    const navigateToPage = currentSubPageName || currentPageName;
    const currentPage = pages.find((page) => navigateToPage === page.name);

    return currentPageName && currentPage.hasAccess && !currentPage.hideNavBar;
  }

  getDefaultPage(pages = []) {
    const defaultPage = pages.find(
      (page) =>
        page.visible && page.hasAccess && page.name !== SUMMARY_PAGE_ALIAS
    );
    const { isSummaryPageFeatureActive, openSummaryPageOnLogin } = this.props;

    if (isSummaryPageFeatureActive) {
      if (openSummaryPageOnLogin) {
        const summaryPage = pages.find(
          (page) => page.name === SUMMARY_PAGE_ALIAS
        );
        if (summaryPage && summaryPage.visible && summaryPage.hasAccess) {
          return summaryPage;
        }
      }
    }
    return defaultPage;
  }

  loadingTag(page) {
    return <h1>Loading {page}</h1>;
  }

  toggle() {
    this.setState({
      collapsed: !this.state.collapsed,
    });
  }

  navClickCallback(page) {
    return page;
    //TODO Implement when needed;
  }

  onGoToLoginPage() {
    return <Logout />;
  }

  buildRouteComponent(loadFunc) {
    const component = createDynImportedComponentCreator(loadFunc);

    return (props) =>
      component({ ...props, setContainerNode: this.setActiveRouteNode });
  }

  buildSubPagesRoutes(
    parentPage,
    parentPath,
    routeNotFoundComponent,
    routeNoAccessComponent
  ) {
    const pagesComponents = buildPagesIndex(parentPage.subPages);
    const subPages = parentPage.subPages.reduce((accumulator, subPage) => {
      const pageSubs = subPage.subPages
        ? this.buildSubPagesRoutes(subPage, `${parentPath}${subPage.path}`)
        : [];
      let render = this.buildRouteComponent(pagesComponents[subPage.name]);

      if (!parentPage.visible) {
        render = routeNotFoundComponent;
      } else if (!parentPage.hasAccess) {
        render = routeNoAccessComponent;
      }

      const parentPageRoute = (
        <Route
          key={subPage.name}
          path={`${parentPath}${subPage.path}`}
          render={render}
          exact={false}
        />
      );

      accumulator.push(...accumulator, parentPageRoute, ...pageSubs);

      return accumulator;
    }, []);

    return subPages;
  }

  renderNavigation(navigation) {
    if (navigation === null) return null;

    let nav = null;
    const { name, lazyLoad, loadFunc } = navigation.component;
    const { props } = navigation;
    const navProps = {
      navItemClick: this.navClickCallback,
      navCollapsed: this.props.collapsed,
      ...props,
    };

    if (lazyLoad) {
      nav = createDynImportedComponent(loadFunc, navProps);
    } else {
      let Component = window[name];
      nav = <Component {...navProps} />;
    }

    const collapseBtnAriaLabel = this.props.collapsed
      ? this.props.expandLeftNavigation
      : this.props.collapseLeftNavigation;

    const tooltipTitle = this.props.collapsed
      ? this.props.expandText
        ? this.props.expandText
        : 'Expand'
      : '';

    return (
      <Sider
        trigger={null}
        collapsible
        collapsed={this.props.collapsed}
        collapsedWidth={60}
        onCollapse={() => this.toggle()}
        defaultCollapsed={false}
        breakpoint={siderBreakpoint}
        width={175}
        className={styles.navContainer}
      >
        <React.Suspense fallback={<div />}>
          <CompanyInformation />
        </React.Suspense>

        {nav}
        <Tooltip placement="right" title={tooltipTitle}>
          <div
            aria-label={collapseBtnAriaLabel}
            role="button"
            tabIndex="0"
            aria-expanded={!this.props.collapsed}
            id="collapseBtn"
            className={`${styles.toggleBtn} ${
              this.props.collapsed ? 'toggleBtn--collapsed' : ''
            }`}
            onClick={() => this.props.onNavCollapse(!this.props.collapsed)}
          >
            <Icon type={this.props.collapsed ? 'right' : 'left'} />{' '}
            <span>
              {this.props.collapseText ? this.props.collapseText : 'Collapse'}
            </span>
          </div>
        </Tooltip>
      </Sider>
    );
  }

  renderRoutes() {
    const {
      pages,
      errorCodes,
      errorPageGoToPage,
      errorMessages,
      adminSettingCommon,
      listPageAndBulkUpdateFeatureFlag,
    } = this.props;
    let routes = [];
    const { adminSettingsLicenseInfo } = adminSettingCommon;
    const licensingConfigured =
      adminSettingsLicenseInfo &&
      Object.keys(adminSettingsLicenseInfo).length > 0;
    const errorCodeRoutes = errorMessages
      ? errorCodes.map((errorCode) => {
          return (
            <Route
              path={`/${errorCode}`}
              exact={true}
              key={errorCode}
              component={
                errorCode !== '401'
                  ? (props) => (
                      <ErrorPage
                        licensingConfigured={licensingConfigured}
                        errorCode={errorCode}
                        pages={pages}
                        goToPage={errorPageGoToPage}
                        staticMessages={errorMessages}
                        {...props}
                      />
                    )
                  : this.onGoToLoginPage
              }
            />
          );
        })
      : [];

    if (this.shouldLoadRoutes()) {
      let defaultPage = this.getDefaultPage(pages);

      const routeNotFoundComponent = () => {
        return errorMessages ? (
          <ErrorPage
            licensingConfigured={licensingConfigured}
            errorCode={404}
            pages={pages}
            goToPage={errorPageGoToPage}
            staticMessages={errorMessages}
          />
        ) : null;
      };
      const routeNoAccessComponent = (props) => {
        return errorMessages ? (
          <ErrorPage
            {...props}
            licensingConfigured={licensingConfigured}
            errorCode={403}
            pages={pages}
            goToPage={errorPageGoToPage}
            staticMessages={errorMessages}
          />
        ) : null;
      };

      const pagesRoutes = pages.reduce((accumulator, page) => {
        const loadFunc = getPageLoadFunc(page.name);
        let render = this.buildRouteComponent(loadFunc);

        if (listPageAndBulkUpdateFeatureFlag) {
          if (page.name === JOBS_PAGE_ALIAS) {
            return accumulator;
          }
        } else {
          if (page.name === LIST_PAGE_ALIAS) {
            return accumulator;
          }
        }

        if (!page.visible) render = routeNotFoundComponent;
        else if (!page.hasAccess) render = routeNoAccessComponent;

        const subPages = page.subPages
          ? this.buildSubPagesRoutes(
              page,
              page.path,
              routeNotFoundComponent,
              routeNoAccessComponent
            )
          : [];

        if (!page.path) {
          return accumulator;
        }

        accumulator.push(
          <Route
            key={page.name}
            path={page.path}
            render={render}
            exact={page.exact}
          />,
          ...subPages
        );

        return accumulator;
      }, []);

      routes = [
        <Route key="defaultRedirect" path="/" exact>
          <Redirect to={defaultPage.navigationLink} />
        </Route>,
        <Route key="silentRenewRedirect" path={SILENT_RENEW_PATH} exact />,
        <Route key="jobsToListsRedirect" path="/jobs">
          <Redirect to="/lists" />
        </Route>,
        ...pagesRoutes,
        ...errorCodeRoutes,
        <Route key="noRouteFound" component={routeNotFoundComponent} />,
      ];
    }

    return <Switch>{routes}</Switch>;
  }

  componentDidMount() {
    const { onNavChange, openPage } = this.props;

    this.historyHookUnlisten = browserHistory.listen((location, action) => {
      const currentPage = getCurrentPage();
      const currentSubPage = getCurrentSubPage();
      const oldPage = this.props.page;
      const oldSubPage = this.props.subPage;
      const currentUrl = `${location.pathname}${location.search}`;
      const oldUrl = this.props.pageUrl;
      const isErrorPageLink = Object.keys(ERROR_PAGES || {}).some(
        (page) => (ERROR_PAGES[page] || {}).navigationLink == currentUrl
      );

      // keep old behaviour for admin page to dispatch navBarPageChange every time we do In page navigation - otherwise sub navs is broken
      // Should be revised after admin sub pages follow the BasePage and deep linking mechanisms
      if (
        oldPage !== currentPage ||
        isErrorPageLink ||
        currentPage == ADMIN_SETTINGS_ALIAS ||
        oldSubPage !== currentSubPage
      ) {
        onNavChange(currentPage, currentSubPage);
      }

      // browser back and forward events
      const isBrowserNavigationEvent = action == 'POP';
      const isInPageNavigationFromBrowserNavEvent =
        isBrowserNavigationEvent &&
        oldPage === currentPage &&
        oldUrl !== currentUrl;
      const isPhxNavToProfilePage =
        action == 'PUSH' &&
        currentPage == PROFILE_PAGE_ALIAS &&
        oldUrl !== currentUrl;

      if (isInPageNavigationFromBrowserNavEvent || isPhxNavToProfilePage) {
        const pathProps = {
          ...location,
          search: window.location.search,
        };
        const navigateToPage = currentSubPage || currentPage;
        openPage(navigateToPage, pathProps);
      }
    });
  }

  componentWillUnmount() {
    this.historyHookUnlisten();
  }

  render() {
    const {
      navigation,
      pages,
      hotKeysProps,
      userSelectedLanguage,
      bannerStaticMessages,
    } = this.props;
    const currentPage = getCurrentPage();
    let result = <Spin className="spin" spinning={true} size={'large'} />;

    if (pages.length > 0) {
      let nav = null;

      if (this.shouldRenderNav(pages)) {
        nav = this.renderNavigation(navigation);
      }

      result = (
        <>
          <Layout.Header className={styles.header}>
            <StaticMessagesContext.Provider value={bannerStaticMessages}>
              <React.Suspense fallback={<div />}>
                <MaintenanceStatusBanner />
              </React.Suspense>
            </StaticMessagesContext.Provider>
          </Layout.Header>
          <Layout
            style={{ height: '100%' }}
            className={`${currentPage} ${userSelectedLanguage}-lang`}
            {...hotKeysProps}
          >
            {nav}
            {this.renderRoutes()}
            <React.Suspense fallback={<div />}>
              <ConnectedCookieConsentBanner />
            </React.Suspense>
          </Layout>
        </>
      );
    }

    return result;
  }
}

const errorCodes = ['401', '403', '404', 'error'];

const mapDispatchToProps = (dispatch) => {
  return {
    openPage: (page, location) => {
      if (PAGE_ACTIONS.OPEN[page]) {
        dispatch(PAGE_ACTIONS.OPEN[page](location, page));
      }
    },
    onNavChange: (page, subPage) => {
      dispatch(navBarPageChange(page, subPage));
    },
    onNavCollapse: (collapsed) => {
      dispatch(navBarCollapse());
      window.localStorage.setItem('navBarCollapsed', collapsed);
    },
    triggerHotKey: (hotKey) => {
      const state = store.getState();
      const hotKeysConfig = globalHotKeysConfigByStrategy;
      const { functionalAccess } = getHotKeyConfig(hotKey) || {
        functionalAccess: null,
      };

      if (hasFunctionalAccessGlobal(state, functionalAccess)) {
        triggerHotKeyAction(state, dispatch, hotKey, hotKeysConfig);
      }
    },
  };
};

const mapStateToProps = (state) => {
  const {
    navigation,
    applicationSettings: { pages },
    adminSetting,
    companyInformation: companyInformationState = {},
  } = state;
  const { companyInformation = {} } = companyInformationState;
  const { adminSettingCommon } = adminSetting;
  const applicationPages = getAllPages(pages);
  const { collapseText, expandText, errorMessages } = getTranslationsSelector(
    state,
    {
      sectionName: 'pages',
      idsArray: ['collapseText', 'expandText', 'errorMessages'],
    }
  );
  const { collapseLeftNavigation, expandLeftNavigation } =
    getTranslationsSelector(state, {
      sectionName: 'common',
      idsArray: ['collapseLeftNavigation', 'expandLeftNavigation'],
    });
  const bannerStaticMessages = getTranslationsSelector(state, {
    sectionName: 'banners',
    idsArray: ['maintenanceStatusBanner'],
  });
  const { openOnLogin: openSummaryPageOnLogin } =
    getWidgetSettingsSelector(state); // Create a selector for this
  const isSummaryPageFeatureActive =
    getFeatureFlagSelector(SUMMARY_PAGE_ALIAS)(state);
  const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(
    FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE
  )(state);

  const getPageLink = getPageLinkSelector(state);

  let additionalProps = {};

  if (navigation.page == ADMIN_SETTINGS_ALIAS) {
    //For Admin Settings relying on constant rerendering for sub navigation
    additionalProps = {
      newObject: {},
    };
  }

  return {
    userSelectedLanguage: companyInformation.initialCurrentCulture,
    ...navigation,
    pages: applicationPages,
    shouldLoadRoutes: pages.loaded,
    errorCodes,
    errorPageGoToPage: getErrorPageGoToPage(pages),
    getHotKeyConfig,
    hotKeysConfig: globalHotKeysConfigByStrategy,
    collapseText,
    expandText,
    errorMessages,
    page: navigation.page,
    subPage: navigation.subPage,
    pageUrl: getPageLink(navigation.page),
    adminSettingCommon,
    expandLeftNavigation,
    collapseLeftNavigation,
    bannerStaticMessages,
    openSummaryPageOnLogin,
    isSummaryPageFeatureActive,
    listPageAndBulkUpdateFeatureFlag,
    ...additionalProps,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withHotKeys(MainLayout));
