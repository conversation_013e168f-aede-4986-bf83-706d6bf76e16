import { Layout } from 'antd';
import React from 'react';
import BaseLayout from './base-layout';
import { layoutElemPropType } from './layoutProps';
import { createComponent } from '../factories/componentFactory';
import styles from './styles/entityListLayout.less';

class EntityListLayout extends BaseLayout {
    constructor(props) {
        super(props);
    }

    getHeaderContent() {
        const { commandBar, filterPane, filterPaneHidden } = this.props;
        const bar = createComponent(commandBar.component, commandBar.props);
        const pane = createComponent(filterPane.component, filterPane.props);

        return (
            <React.Fragment key={'entityListLayoutHeader'}>
                <Layout.Header
                    style={commandBar.containerStyle}
                    hidden={this.componentHidden(commandBar)}>
                    {bar}
                </Layout.Header>
                <Layout.Header
                    style={filterPane.containerStyle}
                    className={filterPane.containerClassName}
                    hidden={filterPaneHidden}>
                    {pane}
                </Layout.Header>
            </React.Fragment>
        );
    }

    getMainContent() {
        const { dataGrid, detailsPane } = this.props;

        if (!dataGrid) {
            return '';
        }

        const grid = createComponent(dataGrid.component, dataGrid.props);
        const details = createComponent(detailsPane.component, detailsPane.props);

        return (
            <Layout key={'entityListLayoutMainContent'}>
                <Layout.Content
                    style={dataGrid.containerStyle}
                    hidden={this.componentHidden(dataGrid)}
                >
                    {grid}
                </Layout.Content>

                <Layout.Sider width={0} className={styles.layoutSider}>
                    {details}
                </Layout.Sider>
            </Layout>
        );
    }

    getFooterContent() {
        return null;
    }
}

EntityListLayout.propTypes = {
    commandBar: layoutElemPropType.isRequired,
    dataGrid: layoutElemPropType.isRequired,
    filterPane: layoutElemPropType.isRequired,
    detailsPane: layoutElemPropType.isRequired
};

export default EntityListLayout;
