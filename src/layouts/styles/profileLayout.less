
.talentProfilePage { 

    box-shadow: none !important;
    margin-left: 0px !important;

    .ant-layout-content {
        
        overflow: hidden;
/* Styles for new Talent Profile header */
        .tp-profile-header {
            padding: 0 1em 0 1em;
            background: @white-color; 
            z-index: 1;
            box-shadow: -4px 0px 2px 0px rgba(0, 0, 0, 0.2);
            /* height changed to auto to stack commandbar vertically */
            height: auto;
            #CommandBar {
                display: flex;
                /* Arrange flex items in a horizontal row and allow them to wrap to the next line if needed */
                flex-flow: wrap;
                align-items: center;
                button {
                    margin: 0;
                    margin-left: 1em;
                    &.view-other-resources {
                        margin-left: auto !important;
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }
/* Styles for old Talent Profile header */
        .tp-profile-header-legacy {
            padding: 0 1em 0 1em;
            background: @white-color; 
            z-index: 1;
            box-shadow: -4px 0px 2px 0px rgba(0, 0, 0, 0.2);
            #CommandBar {
                display: flex;
                align-items: center;
                button {
                    margin: 0;
                    margin-left: 1em;
                    &.view-other-resources {
                        margin-left: auto !important;
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }

        .tp-profile-content-container {
            overflow-y: scroll;
            height: calc(100% - 68px);
            // Override layout inside .main-section to display fields in a flex row
            .talent-profile-body .main-section .formFieldControlLayoutWrapper .formControlLayoutItem {
                display: flex;
            }

            .formFieldControlLayoutWrapper .formControlLayoutItem {
                display: block;
            } 
        }
    }

    .tp-slider {
        @width: 300px;
        flex: 0 0 @width !important;
        max-width: @width !important;
        min-width: @width !important;
        background-color: @secondary-color;
    }
}

.talentProfilePage {
    .profile-section{
        .workHistoryGrid {
            .ant-table-thead > tr > th,
            .ant-table-tbody > tr > td {
              width: 143px !important;
              min-width: 143px !important;
              max-width: 143px !important;
            }
            .ant-table-fixed-left {
                visibility: hidden;
                opacity: 0;
            }
        }
    }
}