import { connect } from 'react-redux';
import { CreatePlanContent } from '../../../components/prompts';
import { updateModalPromptContext } from '../../../actions/promptActions';
import { getClearAriaLabelButton, getStaticPromptMessagesSelector } from '../../../selectors/promptsSelectors';
import { WORKSPACE_DESCRIPTION, WORKSPACE_NAME_MAX_LENGTH } from '../../../constants/plannerConsts';
import { stringReplacePlaceholders } from '../../../utils/commonUtils';
import { getFieldInfoSelector } from '../../../selectors/tableStructureSelectors';
import { TABLE_NAMES } from '../../../constants';
import { getFieldMaxLength } from '../../../utils/fieldUtils';
import { getOrderedPrivateWorkspaces } from '../../../selectors/workspaceSelectors';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../../constants/globalConsts';

const mapStateToProps = (state, ownProps) => {
    const fieldInfo = getFieldInfoSelector(state)(TABLE_NAMES.WORKSPACE, WORKSPACE_DESCRIPTION) || {};
    const workspacesStructureMap = state.plannerPage.workspaces.workspacesStructure.map || {};
    const privatePlans = getOrderedPrivateWorkspaces(workspacesStructureMap) || [];
    const maxNameLength = getFieldMaxLength(fieldInfo, WORKSPACE_NAME_MAX_LENGTH);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    const placeholderValues = {
        maxNameLength
    };
    let translationsStrings;

    if (listPageAndBulkUpdateFeatureFlag) {
        translationsStrings = getStaticPromptMessagesSelector(state, 'createOrSaveAsNewWorkspacePrompt');
    } else {
        translationsStrings = getStaticPromptMessagesSelector(state, 'createOrSaveAsNewPlanPrompt');
    }
    let staticMessages = {};

    Object.keys(translationsStrings).forEach(key => {
        staticMessages = {
            ...staticMessages,
            [key]: stringReplacePlaceholders(translationsStrings[key], placeholderValues) || translationsStrings[key]
        };
    });
    const clearAriaLabel = getClearAriaLabelButton(state)(staticMessages.name);

    return {
        maxNameLength,
        ...staticMessages,
        privatePlans,
        clearAriaLabel
    };
};

const mapDispatchToProps = (dispatch, ownProps) => {
    const { pageAlias } = ownProps;

    return {
        updateModalContext: (changes) => dispatch(updateModalPromptContext(changes, pageAlias))
    };
};

const ConnectedCreatePlanContent = connect(
    mapStateToProps,
    mapDispatchToProps
)(CreatePlanContent);

export {
    ConnectedCreatePlanContent
};