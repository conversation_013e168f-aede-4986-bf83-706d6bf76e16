import React from 'react';
import { connect } from 'react-redux';
import { MakePlanPrivateContent } from '../../../components/prompts';
import { getWorkspaceStructure } from '../../../selectors/workspaceSelectors';
import { getPropOrDefault } from '../../../utils/promptUtils';
import { getStaticPromptMessagesSelector } from '../../../selectors/promptsSelectors';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../../constants/globalConsts';

const mapStateToProps = (state, ownProps) => {
    const {
        dispatchedAction: { payload }
    } = ownProps;
    const { workspaces } = state.plannerPage;
    const { workspace_description, workspace_accesstype } = getWorkspaceStructure(workspaces, payload.workspaceGuid);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
    const { planTypeMessageStart, makeString, privatePlanString, messageFirstPart, messageBold } = getStaticPromptMessagesSelector(state, listPageAndBulkUpdateFeatureFlag ? 'makeWorkspacePrivatePrompt' : 'makePlanPrivatePrompt');

    if (listPageAndBulkUpdateFeatureFlag) {
        return {
            makeString: getPropOrDefault(makeString, 'Make'),
            privatePlanString: getPropOrDefault(privatePlanString, 'a private workspace'),
            planTypeMessageStart: getPropOrDefault(planTypeMessageStart, 'This workspace is currently'),
            messageFirstPart: getPropOrDefault(messageFirstPart, 'If you make this workspace private,'),
            messageBold: getPropOrDefault(messageBold, 'it will no longer be available to others.'),
            details: {
                planInfo: {
                    name: workspace_description,
                    accessType: workspace_accesstype
                }
            }
        };
    } else {
        return {
            makeString: getPropOrDefault(makeString, 'Make'),
            privatePlanString: getPropOrDefault(privatePlanString, 'a private plan'),
            planTypeMessageStart: getPropOrDefault(planTypeMessageStart, 'This plan is currently'),
            messageFirstPart: getPropOrDefault(messageFirstPart, 'If you make this plan private,'),
            messageBold: getPropOrDefault(messageBold, 'it will no longer be available to others.'),
            details: {
                planInfo: {
                    name: workspace_description,
                    accessType: workspace_accesstype
                }
            }
        };
    }
}

const ConnectedMakePlanPrivateContent = connect(
    mapStateToProps
)(MakePlanPrivateContent);

export {
    ConnectedMakePlanPrivateContent
}