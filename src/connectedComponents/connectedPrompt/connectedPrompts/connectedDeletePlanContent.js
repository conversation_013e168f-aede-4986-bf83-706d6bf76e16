import React from 'react';
import { connect } from 'react-redux';
import { DeletePlanContent } from '../../../components/prompts';
import { getWorkspaceStructure } from '../../../selectors/workspaceSelectors';
import { getPropOrDefault } from '../../../utils/promptUtils'
import { getStaticPromptMessagesSelector } from '../../../selectors/promptsSelectors';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../../constants/globalConsts';

const mapStateToProps = (state, ownProps) => {
    const {
        dispatchedAction: { payload }
    } = ownProps;
    const workspaces = state.plannerPage.workspaces;
    const wsStructure = getWorkspaceStructure(workspaces, payload.workspaceGuid);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
    const {
        workspaceTypeMessage,
        publicDeleteMessageStart,
        publicDeleteMessageEnd,
        question,
        deleteString,
        planString } = getStaticPromptMessagesSelector(state, listPageAndBulkUpdateFeatureFlag ? 'deleteWorkspacePrompt' : 'deletePlanPrompt');


    if (listPageAndBulkUpdateFeatureFlag) {
        return {
            workspaceTypeMessage: getPropOrDefault(workspaceTypeMessage, 'This workspace is currently'),
            publicDeleteMessageStart: getPropOrDefault(publicDeleteMessageStart, 'If you delete this workspace'),
            publicDeleteMessageEnd: getPropOrDefault(publicDeleteMessageEnd, 'it will no longer be available to others'),
            question : getPropOrDefault(question, 'Do you wish to permanently delete this workspace?'),
            planString : getPropOrDefault(planString, 'workspace'),
            deleteString : getPropOrDefault(deleteString, 'Delete'),
            details: {
                planInfo: {
                    name: wsStructure.workspace_description,
                    accessType: wsStructure.workspace_accesstype
                }
            }
        };

    } else {
        return {
            workspaceTypeMessage: getPropOrDefault(workspaceTypeMessage, 'This plan is currently'),
            publicDeleteMessageStart: getPropOrDefault(publicDeleteMessageStart, 'If you delete this plan'),
            publicDeleteMessageEnd: getPropOrDefault(publicDeleteMessageEnd, 'it will no longer be available to others'),
            question : getPropOrDefault(question, 'Do you wish to permanently delete this plan?'),
            planString : getPropOrDefault(planString, 'plan'),
            deleteString : getPropOrDefault(deleteString, 'Delete'),
            details: {
                planInfo: {
                    name: wsStructure.workspace_description,
                    accessType: wsStructure.workspace_accesstype
                }
            }
        };
    }

}

const ConnectedDeletePlanContent = connect(
    mapStateToProps
)(DeletePlanContent);

export {
    ConnectedDeletePlanContent
}