import React from 'react';
import { connect } from 'react-redux';
import { SaveChangesContent } from '../../../components/prompts';
import { getWorkspaceStructure, workspaceBelongsToResource, getWorkspaceIsEditable, getIsPublicWorkspace } from '../../../selectors/workspaceSelectors';
import { digestSetCreateWorkspaceChange, digestSelectWorkspace, saveWorkspaceSettings } from '../../../actions/workspaceActions';
import { WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS } from '../../../constants';
import { clearModalPrompt } from '../../../actions/promptActions';
import { getSaveWorkspaceSettings } from '../../../utils/workspaceUtils';
import { omit } from '../../../utils/commonUtils';
import { getApplicationUserId } from '../../../selectors/applicationUserSelectors';
import { hasFunctionalAccessGlobal } from '../../../selectors/applicationFnasSelectors';
import { getPropOrDefault } from '../../../utils/promptUtils';
import { getStaticPromptMessagesSelector } from '../../../selectors/promptsSelectors';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../../constants/globalConsts';


const canSaveToPublic = (state, { payload } ) => {
    const { saveWorkspaceGuid } = payload;
    const workspace = getWorkspaceStructure(state.plannerPage.workspaces, saveWorkspaceGuid);
    const belongsToRes = workspaceBelongsToResource(workspace, getApplicationUserId(state));
    const hasEditRights = getWorkspaceIsEditable(workspace);
    const isPublic = getIsPublicWorkspace(workspace);
    let result = true;

    if (isPublic && !belongsToRes) result = hasEditRights || hasFunctionalAccessGlobal(state, "ManageAllPublicPlans");

    return result;
}

const mapStateToProps = (state, ownProps) => {
    const { dispatchedAction } = ownProps;
    const { saveWorkspaceGuid } = dispatchedAction.payload;
    const workspace = getWorkspaceStructure(state.plannerPage.workspaces, saveWorkspaceGuid);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    const { title, message, planSuffix,
        saveAsPrivatePlanButtonLabel, saveChangesToPublicButtonLabel
    } = getStaticPromptMessagesSelector(state, listPageAndBulkUpdateFeatureFlag ? 'saveWorkspaceChangesPrompt' : 'saveChangesPrompt');

    return {
        state,
        title: getPropOrDefault(title, 'Unsaved Changes'),
        message: getPropOrDefault(message, 'is a'),
        planSuffix: getPropOrDefault(planSuffix, 'plan'),
        saveAsPrivatePlanButtonLabel: getPropOrDefault(saveAsPrivatePlanButtonLabel, 'Save as a private'),
        saveChangesToPublicButtonLabel: getPropOrDefault(saveChangesToPublicButtonLabel, 'Save changes to public'),
        canSaveToPublic: canSaveToPublic(state, dispatchedAction),
        details: {
            planInfo: {
                name: workspace.workspace_description,
                accessLevel: workspace.workspace_accesstype,
            },
        },
    };
}

const mapDispatchToProps = (dispatch, ownProps) => {
    


    return {
        saveChanges: (state ) => {
            const { dispatchedAction, pageAlias } = ownProps;
            const { saveWorkspaceGuid, selectWorkspaceGuid } = dispatchedAction.payload;
            const workspaceSettingsData = {
                workspace_settings: JSON.stringify({
                    ...getSaveWorkspaceSettings(state.plannerPage, saveWorkspaceGuid, true),
                }),
            };

            dispatch(saveWorkspaceSettings(saveWorkspaceGuid, workspaceSettingsData));
            dispatch(dispatchedAction);
            dispatch(clearModalPrompt(pageAlias));
            dispatch(digestSelectWorkspace(selectWorkspaceGuid));
        },

        saveAsPrivatePlan: (state) => {
            const { dispatchedAction, pageAlias } = ownProps;
            const { saveWorkspaceGuid, selectWorkspaceGuid } = dispatchedAction.payload;
            const saveWorkspaceStructure = getWorkspaceStructure(state.plannerPage.workspaces, saveWorkspaceGuid);
            const doCreate = true;
            const selectCreatedWorkspace = false;

            dispatch(
                digestSetCreateWorkspaceChange(
                    saveWorkspaceStructure.workspace_description,
                    WORKSPACE_ACCESS_TYPES.PRIVATE,
                    WORKSPACE_EDIT_RIGHTS.EDIT,
                    saveWorkspaceGuid,
                    doCreate,
                    selectCreatedWorkspace
                )
            );
            dispatch(clearModalPrompt(pageAlias));
            dispatch(digestSelectWorkspace(selectWorkspaceGuid));
        }
    };
};
const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    const { state } = propsFromState;
    return {
        ...omit(propsFromState, ["state"]),
        saveChanges: propsFromState.canSaveToPublic ? () => propsFromDispatch.saveChanges(state) : null,
        saveAsPrivatePlan: () => propsFromDispatch.saveAsPrivatePlan(state),
    };
};

const ConnectedSaveChangesContent = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(SaveChangesContent);

export {
    ConnectedSaveChangesContent
}