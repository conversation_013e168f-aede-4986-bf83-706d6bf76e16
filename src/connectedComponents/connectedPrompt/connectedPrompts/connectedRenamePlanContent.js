import React from 'react';
import { connect } from 'react-redux';
import { RenamePlanContent } from '../../../components/prompts';
import { getWorkspaceStructure } from '../../../selectors/workspaceSelectors';
import { getPropOrDefault } from '../../../utils/promptUtils';
import { getStaticPromptMessagesSelector } from '../../../selectors/promptsSelectors';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../../constants/globalConsts';

const mapStateToProps = (state, ownProps) => {
    const { dispatchedAction : { payload } } = ownProps;
    const { workspaceData, workspaceGuid } = payload;
    const workspace = getWorkspaceStructure(state.plannerPage.workspaces, workspaceGuid);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
    const { title, message, oldNamePrefix, newNamePrefix } = getStaticPromptMessagesSelector(state, listPageAndBulkUpdateFeatureFlag ? 'renameWorkspacePrompt' : 'renamePlanPrompt');

    return {
        title: getPropOrDefault(title, 'Rename plan?'),
        message: getPropOrDefault(message, 'You have renamed the plan. Do you wish to save this change?'),
        oldNamePrefix: getPropOrDefault(oldNamePrefix, 'from'),
        newNamePrefix: getPropOrDefault(newNamePrefix, 'to'),
        details: {
            oldName: workspace.workspace_description,
            newName: workspaceData.workspace_description,
        },
    };
}

const ConnectedRenamePlanContent = connect(
    mapStateToProps
)(RenamePlanContent);

export {
    ConnectedRenamePlanContent
}