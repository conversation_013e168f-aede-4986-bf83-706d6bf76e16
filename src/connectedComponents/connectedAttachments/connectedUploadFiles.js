import { connect } from 'react-redux';
import { ATTACHMENT_LABELS } from '../../constants/attachmentsConsts';
import { isUploadButtonDisabled, getAttachmentsStaticMessagesSelector, getEntityAttachmentConfig, getUploadDocumentsWindowStaticMessagesSelector } from '../../selectors/attachmentsSelectors';
import { convertFileToObjectUrl, isTalentProfilePageTransformedAndIsResourceTable, validateFile } from '../../utils/attachments';
import { addAttachmentToUI, insertAttachment, insertAttachmentError } from '../../actions/attachmentsActions';
import { UploadFiles } from '../../lib/uploadFiles';
import { TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import { isWindowModal } from '../../utils/commonUtils';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

const createUploadFilesMapStateToProps = state => {
    const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state);
    const mapStateToPropsUploadFiles = (state, ownProps) => {
        const { moduleName, tableName, entityId } = ownProps;

        const { uploadButtonLabel } = getAttachmentsStaticMessagesSelector(state);

        const { maxFileSizeMB, allowedFileTypes } = getEntityAttachmentConfig({
            attachments: state.attachments,
            tableName,
            talentProfilePageTransformedEnabled
        });

        // Get the translated modal static message
        const {
            headerTitle = '',
            uploadBtnTitle = '',
            cancelBtnTitle = '',
            uploadAreaText = '',
            fileTypesString = ' ',
            documentType = '',
            expiryDate = '',
            maxFileSize = ''
        } = getUploadDocumentsWindowStaticMessagesSelector(state);

        let additionalProps = { uploadButtonType: 'secondary' };

        if (moduleName === TALENT_PROFILE_ALIAS) {
            additionalProps.className = 'upload-file-button';
        }

        return {
            ...additionalProps,
            headerProps: {
                title: headerTitle
            },
            footerProps: {
                uploadBtnTitle,
                cancelBtnTitle
            },
            uploadModeProps: {
                allowedFileTypes,
                uploadAreaText,
                documentType,
                expiryDate,
                uploadAreaHint: `${fileTypesString} ${allowedFileTypes.join(' ')}`,
                maxFileSize: `Max ${maxFileSizeMB} MB per file`
            },
            moduleName,
            tableName,
            entityId,
            allowedFileTypes,
            maxFileSizeMB,
            uploadButtonLabel: uploadButtonLabel || ATTACHMENT_LABELS.UPLOAD_BUTTON,
            uploadButtonDisabled: isUploadButtonDisabled({
                attachments: state.attachments,
                tableName,
                moduleName,
                entityId,
                talentProfilePageTransformedEnabled
            }),
            validateFile: (size, name) => validateFile(size, name, maxFileSizeMB, allowedFileTypes),
            talentProfilePageTransformedEnabled
        };
    };

    return mapStateToPropsUploadFiles;
};

const mapDispatchToPropsUploadFileModalWindow = (dispatch) => {
    return {
        onUpload: (alias, tableName, entityId, data, talentProfilePageTransformedEnabled) => {
            const uploadedData = isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName) ? data.file : data;

            // Create the attachment object
            let attachment = {
                url: convertFileToObjectUrl(uploadedData),
                uid: uploadedData.uid,
                name: uploadedData.name,
                size: uploadedData.size,
                lastModified: uploadedData.lastModified,
                lastModifiedDate: uploadedData.lastModifiedDate
            };

            const payload = { alias, tableName, entityId, attachment };

            // Include the documentType and expiryDate if talentProfilePageTransformed feature flag is enabled
            if (talentProfilePageTransformedEnabled) {
                payload.documentType = data.documentType;
                payload.expiryDate = data.expiryDate;
            }

            dispatch(addAttachmentToUI(payload));
        }
    };
};

export const mapDispatchToPropsUploadFile = (dispatch, { moduleName /*, ...ownProps */ }) => {
    return {
        onUpload: (alias, tableName, entityId, uploadData, talentProfilePageTransformedEnabled) => {
            const data = isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName) ? uploadData.file : uploadData;
            const { name, url } = data;
            let uploadFields = { alias, tableName, entityId, name, url, data };
            if (talentProfilePageTransformedEnabled) {
                uploadFields = { ...uploadFields, documentType: uploadData.documentType, expiryDate: uploadData.expiryDate };
            }
            dispatch(insertAttachment(uploadFields));
        },
        onUploadFail: (alias, tableName, entityId, { name, size, url }) => {
            dispatch(insertAttachmentError(
                alias,
                {
                    tableName,
                    entityId,
                    name,
                    url,
                    size
                }
            ));
        },
        ...(
            isWindowModal(moduleName)
                ? mapDispatchToPropsUploadFileModalWindow(dispatch)
                : {}
        )
    };
};

const ConnectedUploadFiles = connect(
    createUploadFilesMapStateToProps,
    mapDispatchToPropsUploadFile
)(UploadFiles);

export {
    ConnectedUploadFiles
};
