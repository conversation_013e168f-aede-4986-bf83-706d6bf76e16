import { connect } from 'react-redux';
import { AttachmentsSection } from '../../lib/attachments';
import { getAttachmentsSectionDescription } from '../../lib/attachments/sectionDescription';
import { formatLocalDate, getDisplayLongDateTimeFormat } from '../../utils/dateUtils';
import { getUIEntityAttachmentsData, getAttachmentsStaticMessagesSelector, isAttachmentDownloadDisabled, getEntityAttachmentLoadingState } from '../../selectors/attachmentsSelectors';
import { ENTITY_WINDOW_SECTION_KEYS } from '../../constants/entityWindowConsts';
import { TALENT_PROFILE_ALIAS, TALENT_PROFILE_SECTION_KEYS } from '../../constants/talentProfileConsts';
import { getProfileSectionActiveMessages } from '../../selectors/talentProfileSelectors';
import { deleteAttachment, getAttachmentData, moveAttachmentTo, removeAttachmentFromUI } from '../../actions/attachmentsActions';
import { isWindowModal } from '../../utils/commonUtils';
import { createGetActiveWindowSelector, createGetEntityWindowSectionActiveMessagesSelector } from '../../selectors/entityWindowSelectors';
import { DISPLAY_DATE_TIME_FORMATS, FEATURE_FLAGS } from '../../constants/globalConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';

const wrappedFormatDate = date => formatLocalDate(date, getDisplayLongDateTimeFormat(DISPLAY_DATE_TIME_FORMATS.FULL_YEAR, DISPLAY_DATE_TIME_FORMATS.SHORT_TIME));

const createAttachmentsMapStateToProps = state => {
    const getEntityWindowSectionActiveMessagesSelector = createGetEntityWindowSectionActiveMessagesSelector();
    const getActiveWindowSelector = createGetActiveWindowSelector();
    const mapStateToPropsAttachments = (state, ownProps) => {
        const { moduleName, tableName, entityId } = ownProps;

        let moduleDerivedProps = {};
        if (moduleName === TALENT_PROFILE_ALIAS) {
            moduleDerivedProps.messages = getProfileSectionActiveMessages(state)(TALENT_PROFILE_SECTION_KEYS.ATTACHMENTS);
            moduleDerivedProps.messageAreaClassName = 'attachments-message-area';
        } else {
            const { entityWindow } = state;
            const window = getActiveWindowSelector({
                entityWindow: entityWindow.window[moduleName]
            });

            moduleDerivedProps.messages = getEntityWindowSectionActiveMessagesSelector({
                entityWindow: window,
                sections: entityWindow.settings[moduleName].sections[tableName]
            })(ENTITY_WINDOW_SECTION_KEYS.ATTACHMENTS);

            moduleDerivedProps.sectionDescriptionClassName = 'attachmentsSectionDescription';
        }

        const loading = getEntityAttachmentLoadingState({
            attachments: state.attachments,
            moduleName,
            entityId
        });

        const attachmentStaticMessages = getAttachmentsStaticMessagesSelector(state);
        const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state);

        return {
            ...moduleDerivedProps,
            moduleName,
            tableName,
            entityId,
            description: getAttachmentsSectionDescription(moduleDerivedProps.sectionDescriptionClassName, attachmentStaticMessages),
            files: getUIEntityAttachmentsData({
                attachments: state.attachments,
                moduleName,
                entityId
            }),
            messages: moduleDerivedProps.messages,
            isDownloadDisabled: attachmentId => isAttachmentDownloadDisabled({
                attachments: state.attachments,
                moduleName,
                entityId,
                attachmentId
            }),
            formatDate: wrappedFormatDate,
            attachmentsLabels: attachmentStaticMessages,
            loading,
            talentProfilePageTransformedEnabled
        };
    };

    return mapStateToPropsAttachments;
};

const mapDispatchToPropsAttachmentsModalWindow = (dispatch) => {
    return {
        onDeleteAttachment: (alias, tableName, entityId, attachmentId, attachmentUid) => {
            dispatch(removeAttachmentFromUI(alias, tableName, entityId, attachmentId, attachmentUid));
        }
    };
};

export const mapDispatchToPropsAttachments = (dispatch, { moduleName /*, ...ownProps */ }) => {
    return {
        onDeleteAttachment: (alias, tableName, entityId, attachmentId) => {
            dispatch(deleteAttachment(alias, tableName, entityId, attachmentId));
        },
        onMoveAttachmentTo: (alias, tableName, entityId, attachmentId, moveDocumentTo) => {
            dispatch(moveAttachmentTo(alias, tableName, entityId, attachmentId, moveDocumentTo));
        },
        onDownloadAttachment: (tableName, entityId, attachmentId) => {
            dispatch(getAttachmentData(tableName, entityId, attachmentId, moduleName));
        },
        ...(
            isWindowModal(moduleName)
                ? mapDispatchToPropsAttachmentsModalWindow(dispatch)
                : {}
        )
    };
};

const ConnectedAttachmentsSection = connect(
    createAttachmentsMapStateToProps,
    mapDispatchToPropsAttachments
)(AttachmentsSection);

export {
    ConnectedAttachmentsSection
};
