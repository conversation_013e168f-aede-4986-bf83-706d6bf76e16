import { connect } from 'react-redux';
import FloatingBar from '../lib/floatingBar';
import { getFloatingActionBarLabelSelector, getIsCalcFieldSortedSelector, getShouldShowFloatingActionBarSelector } from '../selectors/plannerPageSelectors';
import { closeSortFloatingActionBar, recordsListSortChanged } from '../actions/workspaceSettingsActions';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';
import { clearPlannerSelection, plannerVerScrollPosReset, sortPlannerData, sortPlannerSubRecData } from '../actions/plannerDataActions';
import { getSelectedWorkspaceGuid, getSelectedWorkspaceSettings } from '../selectors/workspaceSelectors';


const mapStateToProps = (state) => {
    const { plannerPage } = state;
    const workspaceGuid = getSelectedWorkspaceGuid(plannerPage.workspaces);
    const workspaceSettings = getSelectedWorkspaceSettings(plannerPage.workspaces);
    const commonStaticMessages = getTranslationsSelector(state, { sectionName: 'common' });
    const { floatingActionBarButtonLabel } = commonStaticMessages;
    const showFloatingActionBar = getShouldShowFloatingActionBarSelector(state);
    const floatingActionBarLabel = getFloatingActionBarLabelSelector(state);
    const selectedField = getIsCalcFieldSortedSelector(state);

    return {
        workspaceGuid,
        settings: workspaceSettings,
        floatingActionBarButtonLabel,
        showFloatingActionBar,
        floatingActionBarLabel,
        selectedField
    };
};

export const mapDispatchToProps = (dispatch) => {
    return {
        closeSortFloatingBar: () => {
            dispatch(closeSortFloatingActionBar());
        },
        onSortChanged: (
            workspaceGuid,
            workspaceSettings,
            sort,
            sortSubRecords
        ) => {
            dispatch(recordsListSortChanged(workspaceGuid, sort.table, sort.field));
            dispatch(clearPlannerSelection(workspaceSettings.plannerDataGuid));

            if (sortSubRecords) {
                dispatch(sortPlannerSubRecData(workspaceSettings));
            } else {
                dispatch(plannerVerScrollPosReset(workspaceSettings.plannerDataGuid));
                dispatch(sortPlannerData(workspaceSettings));
            }
        }
    };
};

const ConnectedFloatingBar = connect(
    mapStateToProps,
    mapDispatchToProps
)(FloatingBar);

export default ConnectedFloatingBar;