import { connect } from 'react-redux';
import { experienceSectionAddNewRecord, experienceSectionOpenDialog } from '../../actions/experienceSectionActions';
import { TABLE_NAMES } from '../../constants';
import { EXPERIENCE_SECTION_ALIAS } from '../../constants/experienceSectionConstants';
import { TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import TpExperienceSection from '../../lib/talentProfile/tpExperienceSection';
import {
    getExperienceFormDataSelector,
    getHasExperienceDataSelector,
    getHasExperienceEditPermissionSelector
} from '../../selectors/experienceSectionSelector';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import {
    getProfileExperienceSectionConfig,
    getResourceAuditSectionSelector,
    getTPResourceId,
    profileExperienceSectionVisible
} from '../../selectors/talentProfileSelectors';

const mapStateToProps = (state) => {
    const config = getProfileExperienceSectionConfig(state);
    const visible = profileExperienceSectionVisible(state);
    const editable = getHasExperienceEditPermissionSelector(state)(
        TALENT_PROFILE_ALIAS
    );
    const hasExperienceData = getHasExperienceDataSelector(state)(
        TALENT_PROFILE_ALIAS
    );
    const experienceTranslations = getTranslationsSelector(state, {
        sectionName: 'experienceSection'
    });
    const {
        addExperienceButtonLabel,
        editExperienceButtonLabel
    } = experienceTranslations;
    const { inlineEditGuids = [] } = getExperienceFormDataSelector(state)(TALENT_PROFILE_ALIAS);
    const resourceId = getTPResourceId(state);
    const experienceAuditInfo = getResourceAuditSectionSelector(state)(TABLE_NAMES.EXPERIENCE);

    return {
        id: config.SectionKey || '',
        config,
        visible,
        editable,
        hasExperienceData,
        buttonLabel: hasExperienceData ? editExperienceButtonLabel : addExperienceButtonLabel,
        resourceId,
        tableName: TABLE_NAMES.EXPERIENCE,
        disableAddEditButton: inlineEditGuids.length > 0,
        experienceAuditInfo,
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onEditClick: (alias) => (shouldAddNewRecord) => {
            const experienceAlias = `${EXPERIENCE_SECTION_ALIAS}_${alias}`;
            dispatch(experienceSectionOpenDialog(experienceAlias));
            if (shouldAddNewRecord) {
                dispatch(experienceSectionAddNewRecord(experienceAlias));
            }
        }
    };
};

const mergeProps = (mapStateToProps, mapDispatchToProps, ownProps) => {
    const alias = TALENT_PROFILE_ALIAS;

    return {
        ...mapStateToProps,
        onEditClick: mapDispatchToProps.onEditClick(alias),
        ...ownProps
    };
};

const ConnectedTpExperienceSection = connect(mapStateToProps, mapDispatchToProps, mergeProps)(
    TpExperienceSection
);

export { ConnectedTpExperienceSection };
