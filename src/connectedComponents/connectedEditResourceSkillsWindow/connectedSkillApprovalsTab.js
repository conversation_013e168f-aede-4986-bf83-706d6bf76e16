import { connect } from 'react-redux';
import ApprovalSkillsWindow from '../../lib/skillsComponents/editSkillsWindow/approvalSkillsWindow';
import { getSkillsStaticMessages } from '../../selectors/editSkillsWindowSelectors';
import { getTPResourceId, getManagerIdAndName } from '../../selectors/talentProfileSelectors';
import { getApplicationUserId } from '../../selectors/applicationUserSelectors.js';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';

const mapStateToProps = (state, ownProps) => {
    console.clear();
    console.log('%cConnectedSkillApprovalsTab: mapStateToProps', 'color: blue; font-weight: bold;', ownProps);

    const { pageAlias = '' } = ownProps;
    const resourceId = pageAlias === PROFILE_PAGE_ALIAS ? getTPResourceId(state) : getApplicationUserId(state);
    const staticMessages = getSkillsStaticMessages(state);
    const { managerName } = getManagerIdAndName(state);
    const approvalRequestsData = state.resourceSkills.approvalRequestsData || {
        pendingCount: 0,
        pendingRequests: [],
        historicRequests: []
    };

    return {
        resourceId,
        managerName,
        staticMessages,
        approvalRequestsData
    };
};

const mapDispatchToProps = (dispatch) => ({
    onResendRequest: (resourceId) => {
        // TODO: Implement the resend request logic
        console.log(`Resend request for resource ID: ${resourceId}`);
    }
});

const mergeProps = (stateProps, dispatchProps, ownProps) => {
    return {
        ...ownProps,
        ...stateProps,
        ...dispatchProps
    };
};

const ConnectedSkillApprovalsTab = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ApprovalSkillsWindow);

export { ConnectedSkillApprovalsTab };

