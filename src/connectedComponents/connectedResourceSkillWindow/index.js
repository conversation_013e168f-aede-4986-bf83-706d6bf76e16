import { connect } from 'react-redux';
import { SingleSkillWindowPopover } from '../../lib/skillsComponents/singleSkillWindow/singleSkillWindowPopover';
import { getTPResourceId } from '../../selectors/talentProfileSelectors';
import { deleteResourceSkill, updateResourceSkill, populateUISkillData, resourceSkillsDiscardChanges } from '../../actions/resourceSkillsActions';
import { ConnectedResourceSkillFieldsForm } from '../connectedSkillFieldsForm.js';
import { getResourceSkillsCanSaveSelector, getResourceSkillPreferenceSelector, getSkillPreferenceSelector } from '../../selectors/resourceSkillsSelectors';
import { getSkillsStaticMessages } from '../../selectors/editSkillsWindowSelectors';
import { uiSkillFieldChange } from '../../actions/resourceSkillsActions';
import { getSkillInfoSelector } from '../../selectors/skillStructureSelectors.js';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';
import { getResourceSkillApprovalPermission } from '../../selectors/talentProfileSelectors.js';

const mapStateToProps = (state) => {
    const entityId = getTPResourceId(state);
    const staticMessages = getSkillsStaticMessages(state);
    const getResourceSkillPreference = getResourceSkillPreferenceSelector(state);
    const getSkillInfo = getSkillInfoSelector(state);
    const skillApprovalFeatureFlagEnabled = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state);
    const resourceSkillApprovalPermission = getResourceSkillApprovalPermission(state);

    return {
        entityId,
        staticMessages,
        fieldsForm: ConnectedResourceSkillFieldsForm,
        updateSkillEnabled: getResourceSkillsCanSaveSelector(state)(entityId),
        skillPreferences: getSkillPreferenceSelector(state)(),
        getResourceSkillPreference,
        getSkillInfo,
        skillApprovalFeatureFlagEnabled,
        resourceSkillApprovalPermission
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        deleteSkill: (resourceId, skillId) => {
            dispatch(deleteResourceSkill(resourceId, skillId));
        },
        updateSkill: (resourceId, skillId) => {
            dispatch(updateResourceSkill(resourceId, skillId));
        },
        initSkillFieldsForm: (resourceId, skill) => { // move init logic
            dispatch(populateUISkillData(resourceId, skill));
        },
        onWindowClose: (resourceId) => {
            dispatch(resourceSkillsDiscardChanges(resourceId));
        },
        //Note The below is only for skillPreference field, due to it being outside ConnectedResourceSkillFieldsForm
        onSkillFieldChange: (entityId, skillId, fieldInfo, fieldValue, skillInfo, errors) => {
            dispatch(uiSkillFieldChange(entityId, skillId, fieldInfo, fieldValue, skillInfo, errors));
        }
    };
};

export const ConnectedResourceSkillWindowPopover = connect(
    mapStateToProps,
    mapDispatchToProps
)(SingleSkillWindowPopover);