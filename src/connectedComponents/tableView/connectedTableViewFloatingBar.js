import { connect } from 'react-redux';
import FloatingBar from '../../lib/floatingBar';
import { getSelectedWorkspaceGuid, getSelectedWorkspaceSettings } from '../../selectors/workspaceSelectors';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { getTableViewFloatingActionBarLabelSelector, getTableViewIsCalcFieldSortedSelector, getTableViewShouldShowFloatingActionBarSelector } from '../../selectors/tableViewSelectors';
import { closeSortFloatingActionBar, recordsListSortChanged } from '../../actions/workspaceSettingsActions';
import { clearTableViewSelection, sortTableViewData, sortTableViewSubRecData, tableViewVerScrollPosReset } from '../../actions/tableViewActions/actionCreators';
import { TABLE_VIEW_PAGE_ALIAS } from '../../constants/tableViewPageConsts';


const mapStateToProps = (state) => {
    const { tableViewPage } = state;
    const workspaceGuid = getSelectedWorkspaceGuid(tableViewPage.workspaces);
    const workspaceSettings = getSelectedWorkspaceSettings(tableViewPage.workspaces);
    const commonStaticMessages = getTranslationsSelector(state, { sectionName: 'common' });
    const { floatingActionBarButtonLabel } = commonStaticMessages;
    const showFloatingActionBar = getTableViewShouldShowFloatingActionBarSelector(state);
    const floatingActionBarLabel = getTableViewFloatingActionBarLabelSelector(state);
    const selectedField = getTableViewIsCalcFieldSortedSelector(state);

    return {
        workspaceGuid,
        settings: workspaceSettings,
        floatingActionBarButtonLabel,
        showFloatingActionBar,
        floatingActionBarLabel,
        selectedField
    };
};

export const mapDispatchToProps = (dispatch) => {
    return {
        closeSortFloatingBar: () => {
            dispatch(closeSortFloatingActionBar());
        },
        onSortChanged: (
            workspaceGuid,
            workspaceSettings,
            sort,
            sortSubRecords
        ) => {
            dispatch(
                recordsListSortChanged(workspaceGuid, sort.table, sort.field, TABLE_VIEW_PAGE_ALIAS)
            );
            dispatch(clearTableViewSelection(workspaceSettings.tableViewDataGuid));

            if (sortSubRecords) {
                dispatch(sortTableViewSubRecData(workspaceSettings));
            } else {
                dispatch(
                    tableViewVerScrollPosReset(workspaceSettings.tableViewDataGuid)
                );
                dispatch(sortTableViewData(workspaceSettings));
            }
        }
    };
};

const ConnectedTableViewFloatingBar = connect(
    mapStateToProps,
    mapDispatchToProps
)(FloatingBar);

export default ConnectedTableViewFloatingBar;