import { connect } from 'react-redux';
import { getProfileEducationSectionConfig, getResourceAuditSectionSelector, getTPResourceId, profileEducationSectionVisible } from '../../selectors/talentProfileSelectors';
import TpEducationSection from '../../lib/talentProfile/tpEducationSection';
import { getEducationFormDataSelector, getHasEducationDataSelector, getHasEducationEditPermissionSelector } from '../../selectors/educationSectionSelector';
import { TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { educationSectionAddNewRecord, educationSectionOpenDialog } from '../../actions/educationSectionActions';
import { TABLE_NAMES } from '../../constants';
import { EDUCATION_SECTION_ALIAS } from '../../constants/educationSectionConsts';

const mapStateToProps = (state) => {
    const config = getProfileEducationSectionConfig(state);
    const visible = profileEducationSectionVisible(state);
    const editable = getHasEducationEditPermissionSelector(state)(TALENT_PROFILE_ALIAS);
    const hasEducationData = getHasEducationDataSelector(state)(TALENT_PROFILE_ALIAS);
    const educationTranslations = getTranslationsSelector(state, { sectionName: 'educationSection' });
    const { addEducationButtonLabel, editEducationButtonLabel } = educationTranslations;
    const { inlineEditGuids = [] } = getEducationFormDataSelector(state)(TALENT_PROFILE_ALIAS);
    const resourceId = getTPResourceId(state);
    const educationAuditInfo = getResourceAuditSectionSelector(state)(TABLE_NAMES.EDUCATION);

    return {
        id: config.SectionKey || '',
        config,
        visible,
        editable,
        hasEducationData,
        buttonLabel: hasEducationData ? editEducationButtonLabel : addEducationButtonLabel,
        resourceId,
        tableName: TABLE_NAMES.EDUCATION,
        disableAddEditButton : inlineEditGuids.length > 0,
        educationAuditInfo
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onEditClick: (alias) => (shouldAddNewRecord) => {
            const educationAlias = `${EDUCATION_SECTION_ALIAS}_${alias}`;
            dispatch(educationSectionOpenDialog(educationAlias));
            if (shouldAddNewRecord) {
                dispatch(educationSectionAddNewRecord(educationAlias));
            }
        }
    };
};

const mergeProps = (mapStateToProps, mapDispatchToProps, ownProps) => {
    const alias = TALENT_PROFILE_ALIAS;

    return {
        ...mapStateToProps,
        onEditClick: mapDispatchToProps.onEditClick(alias),
        ...ownProps
    };
};

const ConnectedTpEducationSection = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(TpEducationSection);

export { ConnectedTpEducationSection };