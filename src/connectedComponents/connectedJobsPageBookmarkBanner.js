import { connect } from "react-redux";
import MessageBanner from "../components/common-components/messageBanner";
import { getTranslationsSelector } from "../selectors/internationalizationSelectors";

const mapStateToProps = (state) => {
  const { jobsPageBookmarkBanner } = getTranslationsSelector(state, {
    sectionName: "banners",
    idsArray: ["jobsPageBookmarkBanner"],
  });
  const { description, dismissLabel } = jobsPageBookmarkBanner;

  return {
    description,
    dismissLabel
  };
};

const mapDispatchToProps = (dispatch) => {
  return {};
};

const ConnectedJobsPageBookmarkBanner = connect(
  mapStateToProps,
  mapDispatchToProps
)(MessageBanner);

export { ConnectedJobsPageBookmarkBanner };
