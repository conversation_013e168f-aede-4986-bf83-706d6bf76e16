import React from 'react';
import { connect } from 'react-redux';
import { getHasHiddenFilters, getPlannerPageFiltersMetaData } from './selectors';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { PLANNER_PAGE_ALIAS } from '../../constants';
import * as filterActions from '../../actions/filterActions';
import store from '../../store/configureStore';


const AdvancedFiltersMessageMemo = React.lazy(() => import('../../lib/filters/advancedFilterMessage').then((module) => ({ default: module.AdvancedFiltersMessageMemo })));

const mapStateToProps = (state) => {
    const hasHiddenFilters = getHasHiddenFilters(state, PLANNER_PAGE_ALIAS);
    const { hiddenFiltersMessage, hiddenFiltersBoldMessage } = getTranslationsSelector(state, { sectionName: 'filterPane', idsArray: ['hiddenFiltersMessage', 'hiddenFiltersBoldMessage']});

    return {
        hasHiddenFilters,
        hiddenFiltersMessage,
        hiddenFiltersBoldMessage
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onClose: () => {
            const state = store.getState();
            const { filtersGuid } = getPlannerPageFiltersMetaData(state);

            dispatch(filterActions.setHasHiddenFilters(filtersGuid, false));
        }
    };
};

const WrappedAdvancedFiltersMessageMemo = (props) => (
    <React.Suspense fallback={<div />}>
        <AdvancedFiltersMessageMemo {...props} />
    </React.Suspense>
);

export const ConnectedPlannerFilterMessage = connect(
    mapStateToProps,
    mapDispatchToProps
)(WrappedAdvancedFiltersMessageMemo);