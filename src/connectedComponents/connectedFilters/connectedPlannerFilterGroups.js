import React from 'react';
import { connect } from 'react-redux';
import { getPlannerPageFilterGroups, getPlannerPageFiltersMetaData } from './selectors';
import { PLANNERPAGE_FILTER_ALIAS } from '../../constants/plannerConsts';

const AdvancedFiltersGroupMemo = React.lazy(() => import('../../lib/filters/advancedFiltersGroup').then((module) => ({ default: module.AdvancedFiltersGroupMemo })));

const mapStateToProps = (state) => {
    const { filtersGuid, title } = getPlannerPageFiltersMetaData(state);

    return {
        filtersGuid,
        title,
        filterGroups: getPlannerPageFilterGroups(state),
        filtersAlias: PLANNERPAGE_FILTER_ALIAS
    };
};

const WrappedAdvancedFiltersGroupMemo = (props) => (
    <React.Suspense fallback={<div />}>
        <AdvancedFiltersGroupMemo {...props} />
    </React.Suspense>
);

export const ConnectedPlannerFilterGroups = connect(
    mapStateToProps
)(WrappedAdvancedFiltersGroupMemo);