import React from 'react';
import { connect } from 'react-redux';
import { getIsApplyButtonDisabledSelector, getIsClearButtonDisabledSelector, getsPlannerAdvancedFilterSectionDataUnchanged, getsPlannerAdvancedFilterSectionReset, getStaticMessagesSelector, getSavedWorkspaceSettingsSelector } from './selectors';
import { getCurrentPlannerPagedDataObject, getCurrentPlannerRoleGroupsDataObject, getCurrentViewHideUnassignedResourceRows } from '../../selectors/plannerPageSelectors';
import { populateStringTemplates } from '../../utils/translationUtils';
import { applyAdvancedFilters, onResetFilterButton, onResetFilterToSavedPlan } from '../../actions/filterActions';
import { PLANNERPAGE_FILTER_ALIAS, PLANNER_PAGE_ALIAS } from '../../constants/plannerConsts';
import { getIsCriteriaRole } from '../../utils/roleRequestsUtils';
import { getSelectedWorkspaceSettings } from '../../selectors/workspaceSelectors';
import { TABLE_NAMES } from '../../constants';

const FilterPaneActionsMemo = React.lazy(() => import('../../lib/filters/advancedFilterPaneActions').then((module) => ({ default: module.FilterPaneActionsMemo })));

const mapStateToProps = (state) => {
    const staticMessages = getStaticMessagesSelector(state);
    const resourceRowCount = getCurrentPlannerPagedDataObject(state[PLANNER_PAGE_ALIAS]).rowCount;
    const criteriaRoles = (getCurrentPlannerRoleGroupsDataObject(state.plannerPage).data || []).filter((role => getIsCriteriaRole(role)));
    const wsSettings = getSelectedWorkspaceSettings(state[PLANNER_PAGE_ALIAS].workspaces);
    const { masterRecTableName, workspace_guid } = wsSettings;
    const isUnassignedRowsHidden = getCurrentViewHideUnassignedResourceRows(wsSettings);
    const shouldIncrementRowCount = (masterRecTableName === TABLE_NAMES.RESOURCE) && !isUnassignedRowsHidden;

    let rowCount = resourceRowCount + criteriaRoles.length;
    shouldIncrementRowCount && rowCount++;

    const { numberResults } = populateStringTemplates(staticMessages, { rowCount });
    const disableApplyButton = getIsApplyButtonDisabledSelector(state);
    const disableClearButton = getIsClearButtonDisabledSelector(state);
    const disableDiscardChangesButton = getsPlannerAdvancedFilterSectionDataUnchanged(state);
    const disableResetButton = getsPlannerAdvancedFilterSectionReset(state)(workspace_guid);

    const filterState = getSavedWorkspaceSettingsSelector(wsSettings, state.plannerPage);

    return {
        guid: filterState.guid,
        staticMessages,
        numberResults,
        disableApplyButton,
        disableClearButton,
        disableDiscardChangesButton,
        filterState,
        disableResetButton
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onApplyFilters: (guid) => {
            dispatch(applyAdvancedFilters(PLANNERPAGE_FILTER_ALIAS, guid));
        },
        onResetToSavedPlan: (guid, data) => {
            dispatch(onResetFilterToSavedPlan(PLANNERPAGE_FILTER_ALIAS, guid, data));
            dispatch(applyAdvancedFilters(PLANNERPAGE_FILTER_ALIAS, guid));
            dispatch(onResetFilterButton(PLANNERPAGE_FILTER_ALIAS, guid));
        }
    };
};

const WrappedFilterPaneActionsMemo = (props) => (
    <React.Suspense fallback={<div />}>
        <FilterPaneActionsMemo {...props} />
    </React.Suspense>
);

export const ConnectedPlannerFilterActions = connect(
    mapStateToProps,
    mapDispatchToProps
)(WrappedFilterPaneActionsMemo);