import React from 'react';
import { connect } from 'react-redux';
import { getCurrentPageAliasSelector } from '../../selectors/navigationSelectors';
import { getVisibleFilterSectionsSelector } from '../../selectors/filterSectionsSelectors';
import { getPlannerPageFiltersMetaData } from './selectors';

const AdvancedFilterPaneSectionsMemo = React.lazy(() => import('../../lib/filters/advancedFilterSections').then((module) => ({ default: module.AdvancedFilterPaneSectionsMemo })));

const mapStateToProps = (state) => {
    const pageAlias = getCurrentPageAliasSelector(state);
    const { filtersGuid, title } = getPlannerPageFiltersMetaData(state);

    const sections = getVisibleFilterSectionsSelector(state, pageAlias, filtersGuid);

    return {
        filtersGuid,
        sections,
        filterTitle: title
    };
};

const WrappedAdvancedFilterPaneSectionsMemo = (props) => (
    <React.Suspense fallback={<div />}>
        <AdvancedFilterPaneSectionsMemo {...props} />
    </React.Suspense>
);

export const ConnectedPlannerFilterSections = connect(
    mapStateToProps
)(WrappedAdvancedFilterPaneSectionsMemo);
