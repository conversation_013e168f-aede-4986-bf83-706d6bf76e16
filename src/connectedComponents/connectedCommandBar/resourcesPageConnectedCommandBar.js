import { connect } from "react-redux";
import { omit } from "../../utils/commonUtils";
import BaseCommandBar from "../../lib/commandBar/baseCommandBar";
import { commandBarActionCreators, getItemDisabled } from "../../utils/commandBarUtils";
import { GoToDate } from "../../components/planner/dateBar/goToDate";
import {baseActionBarDropdownMenuConfig} from '../../state/commandBar/reusableCommandBar';
import { DateRangePicker } from "../../components/planner/dateBar/dateRangePicker";
import { commandBarSetSectionVisibility } from '../../actions/commandBarActions';
import { hasFunctionalAccessGlobal } from '../../selectors/applicationFnasSelectors';
import { getCommandBarConfigSelector, getStaticMessagesSelector } from '../../selectors/commandBarSelectors';
import { getJobsPageFilters, getJobsPageFilterPaneCollapsed } from '../../selectors/dataGridPageSelectors';
import { CountBadge } from '../../lib/';
import { getAppliedFiltersCount } from "../../utils/filtersUtils";
import { JOBS_PAGE_ACTIONS, JOBS_PAGE_ALIAS, JOBSPAGE_FILTER_ALIAS, JOBS_PAGE_COMMAND_BAR_ALIAS } from "../../constants/jobsPageConsts";
import { getEntityInfoSelector } from "../../selectors/entityStructureSelectors";
import { getAccessibleEntitiesIdsSelector } from '../../selectors/userEntityAccessSelectors';
import { getApplicationFNAs, hasFunctionalAccessLocal } from '../../selectors/applicationFnasSelectors';
import { getDataGridSelectedEntities } from "../../utils/dataGridUtils";
import RadioGroupElement from '../../lib/commandBar/radioGroupElement';
import { RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_COMMAND_BAR_ALIAS } from "../../constants/resourcesPageConsts";
import { getActiveListView } from "../../selectors/listPageSelectors";
import { getFeatureFlagSelector } from "../../selectors/featureManagementSelectors";
import { FEATURE_FLAGS } from "../../constants/globalConsts";

const mapStateToPropsCB = state => {
    const { selection, baseFilter, guid } = getJobsPageFilters(state);
    const config = getCommandBarConfigSelector(state, RESOURCES_PAGE_ALIAS);
    const appliedFiltersCount = getAppliedFiltersCount(selection);
    const tableName = state[RESOURCES_PAGE_ALIAS].tableName;
    
    const getState = () => state;
    const components = {
        GoToDate: GoToDate,
        DateRangePicker: DateRangePicker,
        RadioGroupElement: RadioGroupElement
    };

    const hasFunctionalAccessWrapped = (fnaName) => hasFunctionalAccessGlobal(state, fnaName);
    const badgeComponentsByActionKey = {
        [JOBS_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: {
            render: CountBadge,
            props: {
                count: appliedFiltersCount
            }
        }
    };
    const getEntityInfo = getEntityInfoSelector(state);
    const getAccessibleEntitiesIdsSelectorWrapped = getAccessibleEntitiesIdsSelector(state);
    const staticMessages = getStaticMessagesSelector(state, JOBS_PAGE_ALIAS);

    const fnas = getApplicationFNAs(state);
    const getUserHasFunctionalAccess = (functionAccess) => hasFunctionalAccessLocal(fnas, functionAccess);

    const { entityId } = state.entityWindow.window.jobsPageDetailsPane;
    const { ids } = getDataGridSelectedEntities(state[JOBS_PAGE_ALIAS]);
    const entitiesIds = entityId ? (ids.length === 0 ? [entityId] : ids) : ids;
    const activeListView = getActiveListView(state);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    return {
        actionProps: {
            components,
            badgeComponentsByActionKey,
            filterHidden: getJobsPageFilterPaneCollapsed(state),
            jobsFilterPaneContextGuid : guid,
            entitiesIds,
            baseFilterApplied: baseFilter.applied,
            selectedBaseFilter: baseFilter.selectedBaseFilter,
            filtersAlias: JOBSPAGE_FILTER_ALIAS,
            tableName,
            masterRecTableName: activeListView,
            listPageAndBulkUpdateFeatureFlag: listPageAndBulkUpdateFeatureFlag
        },
        pageName: RESOURCES_PAGE_ALIAS,
        config,
        getState,
        baseActionBarDropdownMenuConfig,
        hasFunctionalAccess: hasFunctionalAccessWrapped,
        getEntityInfo,
        staticMessages,
        getAccessibleEntitiesIds: getAccessibleEntitiesIdsSelectorWrapped,
        getUserHasFunctionalAccess,
        getPopupContainer: (trigger) => trigger.parentNode
    };
};

const mapDispatchToPropsCB = dispatch => {
    return {
        actionHandle: (state, context) => {
            if (commandBarActionCreators[context.actionType]) {
                commandBarActionCreators[context.actionType](state, dispatch, context);
            }
        },
        setSectionVisibility: (sectionKey, visible) => {
            dispatch(commandBarSetSectionVisibility(RESOURCES_PAGE_COMMAND_BAR_ALIAS, sectionKey, visible));
        }
    };
};

const mergePropsCB = (propsFromState, propsFromDispatch, ownProps) => {
    return {
        ...propsFromState,
        ...ownProps,
        ...omit(propsFromDispatch, ["getState", "actionHandle"]),
        onAction(context) {
            propsFromDispatch.actionHandle(propsFromState.getState(), context);
        },
        getItemDisabled(action, additionalProps) {
            return getItemDisabled(propsFromState.getState(), action, additionalProps, propsFromState.actionProps);
        }
    };
};

const ResourcesPageConnectedCommandBar = connect(
    mapStateToPropsCB,
    mapDispatchToPropsCB,
    mergePropsCB
)(BaseCommandBar);

export { ResourcesPageConnectedCommandBar };
