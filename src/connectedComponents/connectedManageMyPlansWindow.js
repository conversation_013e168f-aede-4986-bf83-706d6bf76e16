import { connect } from 'react-redux';
import { omit } from '../utils/commonUtils';
import { shouldSaveIfAnyChanges } from '../utils/workspaceUtils';
import ManagePlansWindow from '../lib/managePlansWindow';
import { getOrderedPrivateWorkspaces, getOrderedPublicWorkspaces, getWorkspacesForInsert,
    getDefaultWorkspaceStructure, getWorkspaceSettings, getSelectedWorkspaceGuid,
    workspaceBelongsToResource, getWorkspaceIsEditable, getSaveAsNewPlanWorkspaceSelector } from '../selectors/workspaceSelectors';
import { hasFunctionalAccessGlobal } from '../selectors/applicationFnasSelectors';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { setManageMyPlansWindowVisibility } from '../actions/managePlansSectionActions';
import * as workspaceActions from '../actions/workspaceActions';
import * as managePlansSectionActions from '../actions/managePlansSectionActions';
import { WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS, PLANNER_PAGE_ALIAS } from '../constants';
import { promptAction } from '../actions/promptActions';
import { getManageMyPlansWindowStaticMessagesSelector } from '../selectors/workspaceSelectors';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';

const applyManageRightsToPlans = (plans = [], resId, FNAValue) => {
    return (plans || []).map(plan => {
        const belongsToRes = workspaceBelongsToResource(plan, resId);
        const hasEditRights = getWorkspaceIsEditable(plan);

        const canManage = (belongsToRes ||
            (!belongsToRes && FNAValue) ||
            (!belongsToRes && !FNAValue && hasEditRights));

        return {
            canManage,
            belongsToRes,
            ...plan
        };
    });
};

const mapStateToProps = state => {
    let { selected } = state.plannerPage.workspaces;
    const { workspacesInEditMode, managePublicPlansFNAName } = state.plannerPage.manageMyPlans;
    const workspacesInEditModeGuids = Object.keys(workspacesInEditMode);
    const plansForInsertInEditMode = getWorkspacesForInsert(state.plannerPage.workspaces).filter(workspace => workspacesInEditModeGuids.includes(workspace.workspace_guid));
    const privatePlans = [...getOrderedPrivateWorkspaces(plansForInsertInEditMode), ...getOrderedPrivateWorkspaces(state.plannerPage.workspaces.workspacesStructure.map)];
    const defaultWorkspaceStructure = getDefaultWorkspaceStructure(state.plannerPage.workspaces);
    const publicPlans = applyManageRightsToPlans(
        [...getOrderedPublicWorkspaces(plansForInsertInEditMode), ...getOrderedPublicWorkspaces(state.plannerPage.workspaces.workspacesStructure.map)],
        getApplicationUserId(state),
        hasFunctionalAccessGlobal(state, managePublicPlansFNAName)
    );
    const visible = state.plannerPage.manageMyPlans.visible;
    const getState = () => state;
    const staticMessages = getManageMyPlansWindowStaticMessagesSelector(state);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    return {
        visible,
        staticMessages,
        privatePlansColumn: {
            type: WORKSPACE_ACCESS_TYPES.PRIVATE,
            title: listPageAndBulkUpdateFeatureFlag ? staticMessages.privateWorkspacesColumnTitle : staticMessages.privatePlansColumnTitle,
            activePlanGuid: selected,
            plans: privatePlans
        },
        publicPlansColumn: {
            type: WORKSPACE_ACCESS_TYPES.PUBLIC,
            title: listPageAndBulkUpdateFeatureFlag ? staticMessages.publicWorkspacesLabel : staticMessages.publicPlansLabel,
            plans: publicPlans,
            activePlanGuid: selected
        },
        workspacesInEditMode,
        defaultWorkspace: defaultWorkspaceStructure,
        getState,
        listPageAndBulkUpdateFeatureFlag
    };
};

const mapDispatchToProps = dispatch => {
    return {
        onClose: () => {
            dispatch(setManageMyPlansWindowVisibility(false));
        },
        promptActions: {
            promptDeletePlan: (workspaceGuid, getState) => {
                const dispatchAction = workspaceActions.deleteWorkspace(workspaceGuid);
                dispatch(promptAction(dispatchAction, PLANNER_PAGE_ALIAS));
            },
            promptRenamePlan: (workspaceGuid, newDescription, getState) => {
                const dispatchAction = workspaceActions.renameWorkspace(workspaceGuid, { 'workspace_description' : newDescription });
                dispatch(promptAction(dispatchAction, PLANNER_PAGE_ALIAS));
            },
            promptSelectPlan: (workspaceGuid, getState) => {
                const state = getState();
                const workspaces = state.plannerPage.workspaces;
                const currentWorkspaceGuid = getSelectedWorkspaceGuid(workspaces);

                dispatch(setManageMyPlansWindowVisibility(false));

                if (shouldSaveIfAnyChanges(workspaces, currentWorkspaceGuid)) {
                    const dispatchAction = workspaceActions.saveWorkspaceIfAnyChanges(currentWorkspaceGuid, workspaceGuid);
                    dispatch(promptAction(dispatchAction, PLANNER_PAGE_ALIAS));
                } else {
                    dispatch(workspaceActions.digestSelectWorkspace(workspaceGuid));
                }
            },
            promptMovePlan: (uuid, accessType, editRights, getState) => {
                const workspaceData = {
                    'workspace_accesstype': accessType,
                    'workspace_editrights': editRights
                };
                const dispatchAction = workspaceActions.moveWorkspace(uuid, workspaceData);
                dispatch(promptAction(dispatchAction, PLANNER_PAGE_ALIAS));
            }
        },
        sectionActions :{
            WsAddEditMode :(guid, type) => {
                dispatch(managePlansSectionActions.manageWorkspacesAddEditMode(guid, type));
            },
            WsRemoveEditMode:(guid) => {
                dispatch(managePlansSectionActions.manageWorkspacesRemoveEditMode(guid));
            }
        },
        actions :{
            renamePlan: (workspaceGuid,newDescription) => {
                dispatch(workspaceActions.renameWorkspace(workspaceGuid, { 'workspace_description' : newDescription }));
            },
            digestCreatePlan: (defaultWorkspaceGuid, privatePlans, newPlanLabel) => {
                const doCreate = false;
                dispatch(workspaceActions.digestCreateWorkspace(WORKSPACE_ACCESS_TYPES.PRIVATE, WORKSPACE_EDIT_RIGHTS.EDIT, defaultWorkspaceGuid, privatePlans, newPlanLabel, doCreate));
            },
            createPlan: (uuid, newDescription) => {
                const selectCreatedWorkspace = false;
                dispatch(workspaceActions.digestWorkspaceStructureChange(uuid, { 'workspace_description' : newDescription }));
                dispatch(workspaceActions.createWorkspace(uuid, selectCreatedWorkspace));
            },
            removeCreatePlanChange: (uuid) => {
                dispatch(workspaceActions.removeCreateWorkspaceChange(uuid));
            },
            copyPlan: (newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName, getState) => {
                const doCreate = true;
                const templateWorkspaceSettings = getWorkspaceSettings(getState().plannerPage.workspaces, newWorkspaceTemplateGuid);
                const newWorkspaceSettings = getSaveAsNewPlanWorkspaceSelector(getState().plannerPage.workspaces);
                const { workspaceColourthemeGuid = null, workspaceCustomColourTheme = [] } = newWorkspaceSettings;

                if (!templateWorkspaceSettings) {
                    const selectWorkspace = false;
                    dispatch(workspaceActions.loadCopyWorkspaceTemplate(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, selectWorkspace));
                } else {
                    dispatch(workspaceActions.digestCopyWorkspace(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, workspaceColourthemeGuid, workspaceCustomColourTheme));
                }
            },
            movePlan: (uuid, accessType, editRights) => {
                const workspaceData = {
                    'workspace_accesstype': accessType,
                    'workspace_editrights': editRights
                };
                dispatch(workspaceActions.moveWorkspace(uuid, workspaceData));
            }
        }
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    return {
        ...propsFromDispatch,
        ...ownProps,
        ...omit(propsFromState, ['getState']),
        promptActions: {
            promptDeletePlan: (workspaceGuid) =>
                propsFromDispatch.promptActions.promptDeletePlan(workspaceGuid, propsFromState.getState),
            promptRenamePlan: (workspaceGuid, newDescription) =>
                propsFromDispatch.promptActions.promptRenamePlan(workspaceGuid, newDescription, propsFromState.getState),
            promptSelectPlan: (workspaceGuid) => {
                propsFromDispatch.promptActions.promptSelectPlan(workspaceGuid, propsFromState.getState);
            },
            promptMovePlan: (workspaceGuid, accessType, editRights) => {
                propsFromDispatch.promptActions.promptMovePlan(workspaceGuid, accessType, editRights, propsFromState.getState);
            }
        },
        actions: {
            ...propsFromDispatch.actions,
            copyPlan: (newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName) => {
                propsFromDispatch.actions.copyPlan(newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName, propsFromState.getState);
            },
            digestCreatePlan: (defaultWorkspaceGuid) => {
                propsFromDispatch.actions.digestCreatePlan(defaultWorkspaceGuid, propsFromState.privatePlansColumn.plans, propsFromState.staticMessages.newPlanLabel);
            }
        }
    };
};

const ConnectedManageMyPlansWindow = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ManagePlansWindow);

export { ConnectedManageMyPlansWindow };
