import React from 'react';
import styles from './styles.less';
import { Progress, Tooltip } from 'antd';
import { connect } from 'react-redux';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { Icon } from '../../lib';
import { getCurrentPageAliasSelector } from '../../selectors/navigationSelectors';
import { getSelectedWorkspaceSettings } from '../../selectors/workspaceSelectors';
import { PLANNER_PAGE_ALIAS, TABLE_NAMES } from '../../constants';
import { TABLE_VIEW_PAGE_ALIAS } from '../../constants/tableViewPageConsts';
import { getEntityInfoSelector } from '../../selectors/entityStructureSelectors';
import { getEntityAlias } from '../../utils/entityStructureUtils';
import { populateStringTemplates } from '../../utils/translationUtils';
import { JOB_LONG_RUNNING_OPERATION, JOB_TOTAL_RAGHEALTH_GUID, RAG_FIELDS } from '../../constants/fieldConsts';
import { isFieldAccessHidden } from '../../utils/fieldControlUtils';
import withStatus from '../../lib/icon/withStatus';
import withClassNameMap from '../../lib/hocs/withClassNameMap';
import withHealthTooltip from '../../lib/tooltipContent/withHealthTooltip';
import { HEALTH_ICON_STATUS_CLASSNAME } from '../../lib/badges/healthBadge/healthBadgeClassNameMaps';
import { getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';

const LongRunningIndicator = (props) => {
    const message = props.tooltipMessage || "There's a long-running operation currently in progress for this job.";

    return (
        <Tooltip placement="bottom" overlayClassName={styles.longRunningTooltip} title={message}>
            <Progress className={styles.progressIndicator} {...props}/>
        </Tooltip>
    );
};

function JobAvatar({ getFieldInfo, data, size, className = '',noStatusJobIconClassName = '' }) {
    const tableName = TABLE_NAMES.JOB;
    let JobIcon = (<Icon type={tableName} className={noStatusJobIconClassName} />);
    const totalHealthFieldInfo = getFieldInfo(tableName, JOB_TOTAL_RAGHEALTH_GUID);

    if (isFieldAccessHidden(totalHealthFieldInfo)) {
        return JobIcon;
    }

    const totalHealthValue = data[JOB_TOTAL_RAGHEALTH_GUID];

    JobIcon = withStatus(Icon, size);
    JobIcon = withClassNameMap(
        JobIcon,
        HEALTH_ICON_STATUS_CLASSNAME,
        'status'
    );

    const healthFields = RAG_FIELDS.reduce((fields, healthField) => {
        if (data[healthField]) {
            const { alias } = getFieldInfo(tableName, healthField);

            fields.push({
                label: alias,
                value: data[healthField]
            });
        }

        return fields;
    }, []);

    if (healthFields.length) {
        JobIcon = withHealthTooltip(
            JobIcon,
            healthFields
        );
    }

    return <JobIcon className={`${styles.jobAvatar} ${className}`} type={tableName} status={totalHealthValue} />;
}

const JobPrefixComponent = (props) => {
    return props.percent !== null
        ? (<LongRunningIndicator {...props} type='circle' width={27} strokeWidth={10} strokeColor='green'/>)
        : <JobAvatar {...props} />;
};

const getTooltipMessage = (getEntityInfo, staticLabels, tableName) => {
    const entityInfo = getEntityInfo(tableName);
    const entityAlias = getEntityAlias(entityInfo, { singularForm: true, capitalized: true, fallbackValue: tableName });
    const { longRunningjobIndicatorTooltipMessage } = populateStringTemplates(staticLabels, { entityAlias });

    return longRunningjobIndicatorTooltipMessage;
};

const mapStateToProps = (state, ownProps) => {
    const longRunningPercentage = (ownProps.data || {})[JOB_LONG_RUNNING_OPERATION];
    const percent = longRunningPercentage != null ? Math.round(longRunningPercentage) : null;
    const staticLabels = getTranslationsSelector(state, { sectionName: 'longRunningTaskBanners' });
    const currentPageAlias = getCurrentPageAliasSelector(state);
    const getEntityInfo = getEntityInfoSelector(state);
    const getFieldInfo = getFieldInfoSelector(state);
    const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state);
    //Job icon modified with spacing between icon and job name in new TP version.
    const noStatusJobIconClassName = talentProfilePageTransformedEnabled ? 'job-icon-no-status-flag-enabled' : ''

    let tableName = TABLE_NAMES.JOB;

    if ([PLANNER_PAGE_ALIAS, TABLE_VIEW_PAGE_ALIAS].includes(currentPageAlias)) {
        tableName = getSelectedWorkspaceSettings(state[currentPageAlias].workspaces).masterRecTableName;
    }

    return {
        ...ownProps,
        percent,
        noStatusJobIconClassName,
        hidden: tableName !== TABLE_NAMES.JOB,
        tooltipMessage: getTooltipMessage(getEntityInfo, staticLabels, TABLE_NAMES.JOB),
        getFieldInfo
    };
};

const ConnectedJobPrefixComponent = connect(
    mapStateToProps
)(JobPrefixComponent);

export default ConnectedJobPrefixComponent;