import React from 'react';
import { connect } from 'react-redux';
import TalentProfileRoot from '../../lib/talentProfile/index';
import TalentProfileRootLegacy from '../../lib/talentProfile/indexLegacy';
import ConnectedTPResume from './connectedTPResume';
import ConnectedQuickRef from './connectedQuickRef';
import ConnectedProfileSections from './connectedProfileSections';
import { ConnectedTpSkillsSection } from './connectedTPSkillsSection';
import { getFormField } from '../connectedEntityWindow/tabs';
import { mapDataStateToProps } from './commonState';
import { mapFieldChangeDispatchToProps } from './commonDispatch';
import { setUpdateAvatarWindowVisibility } from '../../actions/updateAvatarWindowActions';
import { AVATAR_SIZES } from '../../constants/avatarConsts';
import { Form } from '@ant-design/compatible';
import {
    getAvatarAltValueSelector,
    getAvatarImageURL
} from '../../selectors/avatarSelectors';
import { hasCMeColours } from '../../utils/entityWindowUtils';
import { getLicenseValuesByKeySelector } from '../../selectors/commonSelectors';
import { ConnectedResourceSkillWindowPopover } from '../connectedResourceSkillWindow';
import { EDIT_FNAS_PER_TABLENAME } from '../../constants/tablesConsts';
import { TABLE_NAMES } from '../../constants';
import { getApplicationAccessSelector } from '../../selectors/userEntityAccessSelectors';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { ConnectedTpRecentWorkSection } from './connectedTpRecentWorkSection';
import { ConnectedTpEducationSection } from '../connectedEducation/connectedTpEducationSection';
import { ConnectedTpExperienceSection } from '../connectedExperience/connectedTpExperienceSection';
import { ConnectedTpCMeSection } from '../connectedCMe/connectedTpCMeSection';
import { RESOURCE_FIRSTNAME, RESOURCE_LASTNAME, JOB_END_DATE } from '../../constants/fieldConsts';
import ConnectedTPRecommendations from './connectedTPRecommendations';
import { FEATURE_FLAGS, LICENSE_KEYS_ADMIN_SETTINGS } from '../../constants/globalConsts.js';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors.js';
import { TALENT_PROFILE_SECTIONS } from '../../constants/talentProfileConsts.js';
import SectionToRender from './sectionToRender.js';
import {
    showRecommendationAlert,
    getTPEntity,
    getTPResourceId,
    profileCMeSectionVisible,
    getAvatarStaticMessages,
    profileRecentWorkSectionVisible
} from '../../selectors/talentProfileSelectors';
import { isFieldAccessHidden } from '../../utils/fieldControlUtils';
import { talentProfileWorkHistorySelector } from '../../selectors/workHistorySelector';
import { getFieldInfoSelector } from '../../selectors/tableStructureSelectors';

const mapPropsToFields = (props) => {
    const result = {};
    const { entity, tableName } = props;

    Object.keys(entity).forEach((key) => {
        result[key] = Form.createFormField({
            ...getFormField(
                props,
                props.getFieldInfo(tableName, key)
            )
        });
    });

    return result;
};

const RootWrapper = (props) => {
    const ProfileComponent = props.talentProfilePageTransformedEnabled
        ? TalentProfileRoot
        : TalentProfileRootLegacy;

    return (
        <ProfileComponent {...props}>
            {(externalProps) => {

                const { hasCMeData = false, showCMeSection, showRecentWork, showRecommendations, talentProfilePageTransformedEnabled } = props;

                // Common props shared
                const sharedProps = {
                    ...externalProps,
                    talentProfilePageTransformedEnabled
                };

                return {
                    recommendations: <ConnectedTPRecommendations showRecommendations={showRecommendations} />,
                    resume: <ConnectedTPResume {...externalProps} />,
                    quickRef: <ConnectedQuickRef {...externalProps} />,
                    sections: <ConnectedProfileSections {...externalProps} />,
                    employment: <SectionToRender sectionType={TALENT_PROFILE_SECTIONS.EMPLOYMENT} {...sharedProps} />,
                    documents: <SectionToRender sectionType={TALENT_PROFILE_SECTIONS.ATTACHMENTS} {...sharedProps} />,
                    additional: <SectionToRender sectionType={TALENT_PROFILE_SECTIONS.ADDITIONAL} {...sharedProps} />,
                    skillsSection: <ConnectedTpSkillsSection {...sharedProps} skillWindowPopover={ConnectedResourceSkillWindowPopover} />,
                    recentWorkSection: <ConnectedTpRecentWorkSection {...externalProps} showRecentWork={showRecentWork} />,
                    educationSection: <ConnectedTpEducationSection {...sharedProps} />,
                    experienceSection: <ConnectedTpExperienceSection {...sharedProps} />,
                    cMeSection: <ConnectedTpCMeSection {...externalProps} showCMeSection={showCMeSection} hasCMeData={hasCMeData} />
                };
            }}
        </ProfileComponent>
    );
};

const { licenseCmeEnabled } = LICENSE_KEYS_ADMIN_SETTINGS;

const mapStateToProps = (state) => {
    const { talentProfilePage, avatars } = state;
    const entity = getTPEntity(state);
    const { changeProfielPictureText = 'Change profile picture' } = getAvatarStaticMessages(state);
    const tableName = TABLE_NAMES.RESOURCE;
    const resourceId = getTPResourceId(state);
    const { [RESOURCE_FIRSTNAME]: firstName = '', [RESOURCE_LASTNAME]: lastName = '' } = entity || {};
    const resourseName = `${firstName} ${lastName}`;
    const avatarAltText = getAvatarAltValueSelector(state)(resourseName);
    const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state);

    const cMeVisible = profileCMeSectionVisible(state);
    const cMeEnabled = (getLicenseValuesByKeySelector(state)(licenseCmeEnabled) || {}).subscribedCount;
    const hasCMeData = hasCMeColours(entity);

    const isProfileSectionVisible = profileRecentWorkSectionVisible(state);
    const getFieldInfo = getFieldInfoSelector(state);
    const fieldInfo = getFieldInfo(TABLE_NAMES.JOB, JOB_END_DATE);
    const endDateVisible = !isFieldAccessHidden(fieldInfo);
    const talentProfileWorkHistory = talentProfileWorkHistorySelector(state);

    return {
        ...mapDataStateToProps(state),
        editingDisabled: !getApplicationAccessSelector(state)(tableName, (resourceId ? [resourceId] : []), ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName]),
        hasConfig: null !== talentProfilePage.config,
        autoComplete: talentProfilePage.autoComplete,
        mapPropsToFields,
        avatarProps: {
            size: AVATAR_SIZES.MEDIUM.value,
            src: getAvatarImageURL(avatars, resourceId, AVATAR_SIZES.MEDIUM.label),
            showOverlay: true,
            alt: avatarAltText,
            overlayProps: {
                icon: 'camera',
                text: changeProfielPictureText
            }
        },
        talentProfilePageTransformedEnabled,
        showRecommendations: showRecommendationAlert(state),
        cMeVisible,
        hasCMeData,
        cMeEnabled,
        showCMeSection: cMeVisible && hasCMeData && cMeEnabled,
        showRecentWork: isProfileSectionVisible && endDateVisible && talentProfileWorkHistory.length > 0
    };
};

const matDispatchToProps = (dispatch) => {
    return {
        ...mapFieldChangeDispatchToProps(dispatch),
        onAvatarClick: () => dispatch(setUpdateAvatarWindowVisibility(true))
    };
};

export default connect(mapStateToProps, matDispatchToProps)(RootWrapper);
