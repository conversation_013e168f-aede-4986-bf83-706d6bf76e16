import { JOB_BOOKINGS_CALC_FIELDS, JOB_BUDGET_CONSUMED, JOB_PROFITMARGIN } from '../../../constants/fieldConsts';

export const getFieldSpecificProps = (field, entity) => {
    let props = {};
    const MAX_PERCENTAGE = 100;

    switch (field.name) {
        case JOB_BUDGET_CONSUMED: {

            if (entity[JOB_BUDGET_CONSUMED] > MAX_PERCENTAGE) {
                props = { additionalClassName: 'overbudgetLabel' };
            }
            break;
        }
        case JOB_PROFITMARGIN: {
            const PROFITMARGIN_MIN_PERCENTAGE = 0;

            if (entity[JOB_PROFITMARGIN] < PROFITMARGIN_MIN_PERCENTAGE) {
                props = { additionalClassName: 'overbudgetLabel' };
            }
            break;
        }
        case JOB_BOOKINGS_CALC_FIELDS.TOTAL_HOURS_BOOKED: {
            if (entity[JOB_BOOKINGS_CALC_FIELDS.HOURS_PERCENTAGE_BUDGET] > MAX_PERCENTAGE) {
                props = { additionalClassName: 'overbudgetLabel' };
            }
            break;
        }
        case JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST: {
            if (entity[JOB_BUDGET_CONSUMED] > MAX_PERCENTAGE) {
                props = { additionalClassName: 'overbudgetLabel' };
            }
            break;
        }
        case JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_REVENUE: {

            if (entity[JOB_BOOKINGS_CALC_FIELDS.JOB_REVENUE_AS_PERCENTAGE_OF_TARGET] < MAX_PERCENTAGE) {
                props = { additionalClassName: 'overbudgetLabel' };
            }
            break;
        }
        default:

    }

    return props;
};