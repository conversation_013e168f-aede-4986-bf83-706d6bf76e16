import { connect } from 'react-redux';
import ImportLibrarySkills from '../../lib/importSkillDialog';
import { fetchLibrarySections, fetchLibrarySkills, updateSelectedSkills, importLibrarySkills, searchImportLibrarySkills, selectLibrarySkill, clearLibraryData } from '../../actions/adminSettings/importLibrarySkillsActions';
import { getInitialSkillCountSelector, getLibrarySectionsSelector } from '../../selectors/adminSettingSelectors/skillConfigurationSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';

export const mapStateToProps = (state) => {
    const librarySections = getLibrarySectionsSelector(state)();
    const initialSkillCount = getInitialSkillCountSelector(state);
    const isImportLibrarySearchEnabled = getFeatureFlagSelector(FEATURE_FLAGS.RETAIN_IMPORT_LIBRARY_SEARCH_SKILL)(state);

    return { librarySections, initialSkillCount, isImportLibrarySearchEnabled };
};

export const mapDispatchToProps = (dispatch, ownProps) => {
    const { closeModal } = ownProps;

    return {
        fetchLibrarySections : () => {
            dispatch(fetchLibrarySections());
        },
        fetchLibrarySkills : (categoryId, subCategoryId, checkSkills) => {
            dispatch(fetchLibrarySkills(categoryId, subCategoryId, checkSkills));
        },
        updateSelectedSkills: (selectedSkills) => {
            dispatch(updateSelectedSkills(selectedSkills));
        },
        importLibrarySkills: () => {
            dispatch(importLibrarySkills());
        },
        searchLibrarySkills:(searchTerm) => {
            dispatch(searchImportLibrarySkills(searchTerm));
        },
        selectLibrarySkill:(skillObj) =>{
            dispatch(selectLibrarySkill(skillObj));
        },
        clearImportLibraryData:() =>{
            dispatch(clearLibraryData());
            closeModal();
        }
    };
};

export const ConnectedImportLibrarySkills = connect(mapStateToProps,mapDispatchToProps)(ImportLibrarySkills);