import React from 'react';
import { WORKFLOW_ACTOR_SECTION_ITEM_TYPES } from '../../../../constants/workflowSettingsConsts';
import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';

import { Alert, Checkbox, Col, Row, Typography } from 'antd';
import DividerWithTitle from '../../../../lib/dividerWIthTitle';
import styles from './styles.less';
import MultiValueLinkedFieldControl from '../../../../lib/multiValueLinkedFieldControl';
import { TagItemList } from '../../../../lib/itemsList/tagItemsList';
import { TABLE_NAMES } from '../../../../constants';
import StatesTransitionsBadges from './statesTransitionsBadges';
import PropTypes from 'prop-types';
import { getNoResultsMessage, stringReplacePlaceholders } from '../../../../utils/commonUtils';
import { Icon } from '../../../../lib';
import ConnectedItemsListWithAvatars from '../../../../connectedComponents/connectedItemsListWithAvatars';
import Select from '../../../../lib/select';

const {
    TEXT,
    CHECKBOX,
    INFO_BANNER,
    DROPDOWN_SELECT,
    MULTI_VALUE_CONTROL,
    GROUPED_ACTIONS_TITLE,
    ACTOR_ACTION_STATES_BADGES,
    SINGLE_ROW_MULTIPLE_ITEMS_CONTROL
} = WORKFLOW_ACTOR_SECTION_ITEM_TYPES;


const getFormControl = (item, options) => {
    const { controlType, fieldName } = item;
    const { getFieldDecorator, getFieldInfo, } = options;

    const fieldInfo = getFieldInfo(fieldName);
    let control = null;

    let fieldDecoratorOptions = {
        rules: []
    };

    switch (controlType) {
        case CHECKBOX: {
            fieldDecoratorOptions = {
                ...fieldDecoratorOptions,
                valuePropName: 'checked'
            };

            control = (
                <Checkbox className={styles.checkBox} disabled={fieldInfo.readOnly}>{item.label}</Checkbox>
            );
            break;
        }
        case DROPDOWN_SELECT: {
            const { options = [] } = fieldInfo;

            control = (
                <Select
                    aria-label={item.label}
                    virtual={false}
                    getPopupContainer={trigger => trigger.parentNode}
                    items={options}
                    OptionContent={(item) => <>{item.description}</>}
                    optionValueKey="id"
                />
            );
            break;
        }
        case MULTI_VALUE_CONTROL: {
            const {
                getFieldInfo,
                onInputBlur,
                getTableAlias,
                onAutoCompleteDropdownVisibilityChange,
                onAutoCompleteInput,
                onAutoCompleteSearchSuggest,
                avatarConfig,
                suggestions,
                staticMessages = {},
                fieldsExplanations = {},
                uiFormData
            } = options;
            const { linkedTableName: table, lookupFilterLines, fieldName } = item;

            const wrappedOnInputBlur = () => {
                onInputBlur && onInputBlur(fieldName);
            };

            const fieldHasInvalidValue = (fieldInfo, value = []) => {
                const { tableName, maxLimitCount } = fieldInfo;

                return [TABLE_NAMES.RESOURCE, TABLE_NAMES.SECURITY_PROFILE].includes(tableName) && value.length >= maxLimitCount;
            };

            const getExplanation = (uiFieldValue = [], fieldInfo = {}) => {
                const { maxLimitCount } = fieldInfo;

                let message = fieldHasInvalidValue(fieldInfo, uiFieldValue) ? fieldsExplanations[fieldInfo.fieldName] : null
                message = stringReplacePlaceholders(message, { maxLimitCount });

                return message;
            };


            const fieldInfo = getFieldInfo(fieldName);

            const tableAlias = getTableAlias(table);
            const noResultsMessage = getNoResultsMessage(tableAlias, staticMessages);
            const placeholder = `${staticMessages.addAnotherPrefix || 'Add a'} ${tableAlias}`;

            const uiFieldValue = uiFormData[fieldName].value || [];
            const fieldSuggestions = suggestions[fieldName] || [];
            const selectedOptionsIds = uiFieldValue.map(itemValue => itemValue instanceof Object ? itemValue.id : itemValue);
            const unselectedSuggestions = (fieldSuggestions || []).filter(suggestion => !selectedOptionsIds.includes(suggestion.id));

            const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(fieldName);
            const itemListComponent = table === TABLE_NAMES.RESOURCE ? ConnectedItemsListWithAvatars : TagItemList;
            const fieldExplanationClassName = 'fieldExplanation';

            control = (
                <MultiValueLinkedFieldControl
                    id={`MultiValueLinkedField_${fieldName}`}
                    placeholder={placeholder}
                    options={unselectedSuggestions}
                    fieldTableName={table}
                    noResultsMessage={noResultsMessage}
                    fieldName={fieldName}
                    actualFieldName={fieldName}
                    tableName={table}
                    showLabel={false}
                    autoFocus={false}
                    selectOnBlur={false}
                    onAutoCompleteDropdownVisibilityChange={(...args) => {
                        onAutoCompleteDropdownVisibilityChange(...args);
                        wrappedOnInputBlur();
                    }}
                    onInput={(enteredText) => {
                        onAutoCompleteInput(table, fieldName, enteredText);
                    }}
                    onInputBlur={wrappedOnInputBlur}
                    getFieldInfo={getFieldInfoWrapped}
                    searchSuggests={(tableName, fieldInfo, value) => {
                        onAutoCompleteSearchSuggest(tableName, fieldInfo, value, lookupFilterLines, []);
                    }}
                    avatarConfig={avatarConfig}
                    fieldExplanation={getExplanation(uiFieldValue, fieldInfo)}
                    isFieldDisabled={fieldHasInvalidValue(fieldInfo, uiFieldValue)}
                    staticLabels={staticMessages}
                    itemListComponent={itemListComponent}
                    fieldExplanationClassName={fieldExplanationClassName}
                />
            );
            break;
        }
    }

    return getFieldDecorator(fieldName, fieldDecoratorOptions)(control);
};

const ActorItemInfoControl = ({ item }) => {
    let control = null;

    switch (item.controlType) {
        case TEXT: {
            control = (
                <Typography.Text className={`${styles.typographyTitle} ${item.className}`}>{item.text}</Typography.Text>
            );
            break;
        }
        case ACTOR_ACTION_STATES_BADGES: {
            control = (
                <StatesTransitionsBadges states={item.states} />
            );
            break;
        }
        case GROUPED_ACTIONS_TITLE: {
            control = (
                <DividerWithTitle title={item.text}></DividerWithTitle>
            );
            break;
        }
        case INFO_BANNER: {
            control = (
                <Alert type="info" showIcon message={item.text} icon={<Icon type="info-circle" />} style={{ width: 'auto', marginBottom: '10px' }} />
            );
            break;
        }
    }

    return control;
};

const CommonActorItemControlPropTypes = {
    item: PropTypes.object.isRequired,
    options: PropTypes.object.isRequired
};

const MultipleItemsActorRow = ({ item, options }) => {
    const { items } = item;

    const shouldShowItemControl = (item) => {
        const { showWhen } = item;
        let shouldShow = true;

        if (showWhen) {
            const { uiFormData = {}, getFieldInfo } = options;
            const { fieldName, targetValue, targetValueMapKey } = showWhen;
            const { options: targetFieldOptions = [] } = getFieldInfo(fieldName);

            const actualValueId = uiFormData[showWhen.fieldName].value;
            const actualValueMapped = (targetFieldOptions.find(option => option.id === actualValueId) || {})[targetValueMapKey];

            shouldShow = actualValueMapped == targetValue;
        }

        return shouldShow;
    };

    return (
        <Row className={styles.rowFlex}>
            {
                items
                    .filter(shouldShowItemControl)
                    .map((item, index) => (
                        <Col key={`${item.fieldName}_col_${index}`} span={item.columnSpan} className={item.className}>
                            <ActorItemControl item={item} options={options} />
                        </Col>
                    ))
            }
        </Row>
    );
};

MultipleItemsActorRow.propTypes = CommonActorItemControlPropTypes;

const SingleItemActorControl = ({ item, options }) => {

    const getFormItemControl = () => {
        const { uiFormData, getFieldInfo, staticMessages = {}, getTableAlias } = options;
        const { multiValueFieldErrorMessagePrefix = 'Select at least one' } = staticMessages;
        const { fieldName } = item;
        const { tableName } = getFieldInfo(item.fieldName);

        const { hasError } = uiFormData[fieldName] || {};
        const helpText = hasError ? `${multiValueFieldErrorMessagePrefix} ${getTableAlias(tableName)}` : null;
        const validationValue = hasError ? 'error' : '';

        return (
            <Form.Item help={helpText} validateStatus={validationValue}>
                {getFormControl(item, options)}
            </Form.Item>
        );
    };

    return item.fieldName
        ? getFormItemControl()
        : (<ActorItemInfoControl item={item} />);
};

SingleItemActorControl.propTypes = CommonActorItemControlPropTypes;

export const ActorItemControl = ({ item, options }) => {
    const { items = [], controlType } = item;

    return controlType === SINGLE_ROW_MULTIPLE_ITEMS_CONTROL && items.length
        ? (<MultipleItemsActorRow item={item} options={options} />)
        : (<SingleItemActorControl item={item} options={options} />);
};

ActorItemControl.propTypes = CommonActorItemControlPropTypes;