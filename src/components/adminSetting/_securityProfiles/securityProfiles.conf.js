import { COMMANDBAR_CONSTANTS } from '../../../constants/adminSettingConsts';
import messages from '../../common-components/helpers/application.i18n';

const { securityProfiles = {} } = messages;
const { warningMessages = {} } = securityProfiles;
const { title, message, buttonLabel } = warningMessages.deleteFailure;

export default {
    actionBarConf: {
        buttons: {
            primary: {
                label: 'Confirm Changes',
                action: undefined
            },
            secondary: {
                label: 'Cancel',
                action: undefined
            }
        },
        formErrorMessage: 'This form has errors.'
    },
    commandBarConf : [
        {
            type: COMMANDBAR_CONSTANTS.CONTEXTUAL_EDIT,
            config: undefined
        }
    ],
    isContextMenuOn: true,
    CONSTANTS : {
        EXPANDED_KEYS: 'expandedKeys'
    },
    warnings: {
        'delete_failure': {
            warningTitle: title,
            warningMessage: message,
            buttonLabel: buttonLabel
        }
    },
    iconTypesFunctionalAccess: {
        'Planner': 'plans-page',
        'Dashboards': 'dashboard',
        'Profile': 'user',
        'Reports': 'dashboard',
        'Job management': 'job',
        'Roles board': 'megaphone',
        'Table View': 'table'
    },
    updatedIconTypesFunctionalAccess: {
        'Summary': 'summary-security',
        'Profile': 'user-security',
        'Planner': 'planner-security',
        'Table View': 'tableView-security',
        'Job management': 'lists-security',
        'Reports': 'reports-security',
        'Roles': 'roles-security',
        'Roles board': 'megaphone',
        'Operation log': 'operationLog-security',
        'Administration settings': 'settings-security'
    }
};