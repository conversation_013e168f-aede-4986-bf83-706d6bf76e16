import { connect } from 'react-redux';
import FieldSecurityInfo from './fieldSecurityInfo';
import {
    updateFieldRestriction, updateExpandedKeysFieldSecurities,
    toggleFunctionalAccessRuleValue
} from '../../../../actions/adminSettings/securityProfileActions';
import { getEntityAccessCardsConfigSecuritySelector, getEntityEditFnaUIValueSelector } from '../../../../selectors/adminSettingSelectors/securityProfilesSelectors';

const mapSecurityStateToProps = (state, ownProps) => {
    const { adminSetting } = state;
    const { securityProfiles } = adminSetting;
    const {
        activeSecurityProfileName,
        activeSecurityProfileId,
        activeTabEntityId,
        entitiesById = {},
        fieldAccessLevels,
        fieldSecuritiesExpandedKeys,
        fieldSecurity
    } = securityProfiles;

    const editFnaUIValue = getEntityEditFnaUIValueSelector(state)(ownProps.entityName);
    const entityTableName = entitiesById[activeTabEntityId] || '';
    const cardsConfigSecurity = getEntityAccessCardsConfigSecuritySelector(state)(ownProps.entityName);

    return {
        fieldSecurity,
        activeSecurityProfileName,
        activeSecurityProfileId,
        fieldAccessLevels,
        fieldSecuritiesExpandedKeys,
        editFnaUIValue,
        entityTableName,
        cardsConfigSecurity
    };
};

const mapSecurityProfileDispatch = (dispatch) => {
    return {
        updateFNAValue: (value, id) => {
            dispatch(toggleFunctionalAccessRuleValue(value, id));
        },
        updateFieldRestriction: (payload) => {
            dispatch(updateFieldRestriction(payload));
        },
        updateExpandedKeysFieldSecurities: (securityProfileGuid, entityName, expandedKeys) => {
            dispatch(updateExpandedKeysFieldSecurities(securityProfileGuid, entityName, expandedKeys));
        }

    };
};

export default connect(mapSecurityStateToProps, mapSecurityProfileDispatch)(FieldSecurityInfo);
