import React from 'react';
import { Card } from 'antd';
import { FieldSecurityInfoHeader } from './FieldSecurityInfoHeader';
import { FieldSecurityInfoMainContent } from './FieldSecurityInfoMainContent';
import { FieldSecurityInfoFooter } from './FieldSecurityInfoFooter';
import adminBaseStyles from '../../../../../styles/admin-setting-base.less';
import { SECTION_CONSTANTS } from '../../../../constants/adminSettingConsts';
import EntityAccessCardContent from '../entityAccess/entityAccessCardContent';
import PropTypes from 'prop-types';

const FieldSecurityInfo = (props) => {
    const {
        entityName,
        activeSecurityProfileId,
        fieldAccessLevels,
        fieldSecurity,
        updateFieldRestriction,
        updateExpandedKeysFieldSecurities,
        fieldSecuritiesExpandedKeys,
        editFnaUIValue,
        entityTableName,
        messages,
        cardsConfigSecurity,
        entityId,
        updateFNAValue
    } = props;

    const commonCardContentProps = {
        entityId,
        entityName,
        messages,
        updateFNAValue
    };

    // Only get card configs that belong to functional access
    let cards = cardsConfigSecurity.filter(config => !config.isParentCard && !config.isEntityAccess)
        .map(config => {
            return (
                <EntityAccessCardContent key={`${entityName}_fn`} {...config} {...commonCardContentProps} />
            );
        });

    const toggleSections = () => {
        if (entityName === SECTION_CONSTANTS.ROLE_REQUEST) {
            return (
                <div className={adminBaseStyles.viewBudgetToggle}>
                    {cards}
                </div>
            );
        }
    };

    return (
        <React.Fragment>
            <div className="field-security-info">
                <div className={adminBaseStyles.contentNonGridArea}>
                    <Card className="card-layout" bordered={true}>
                        <FieldSecurityInfoHeader entityName={entityName} />
                        {toggleSections()}
                        <FieldSecurityInfoMainContent
                            activeSecurityProfileId={activeSecurityProfileId}
                            fieldAccessLevels={fieldAccessLevels}
                            fieldSecurities={fieldSecurity}
                            editFnaUIValue={editFnaUIValue}
                            entityName={entityName}
                            entityTableName={entityTableName}
                            updateFieldRestriction={updateFieldRestriction}
                            updateExpandedKeysFieldSecurities={updateExpandedKeysFieldSecurities}
                            fieldSecuritiesExpandedKeys={fieldSecuritiesExpandedKeys}
                        />
                        <FieldSecurityInfoFooter />
                    </Card>
                </div>
            </div>
        </React.Fragment>
    );
};


FieldSecurityInfo.propTypes = {
    entityName: PropTypes.string,
    activeSecurityProfileId: PropTypes.string,
    fieldAccessLevels: PropTypes.array,
    fieldSecurity: PropTypes.object,
    updateFieldRestriction : PropTypes.func,
    updateExpandedKeysFieldSecurities: PropTypes.func,
    fieldSecuritiesExpandedKeys: PropTypes.object,
    editFnaUIValue:PropTypes.object,
    entityTableName: PropTypes.string,
    messages:PropTypes.object,
    cardsConfigSecurity: PropTypes.array,
    entityId: PropTypes.string,
    updateFNAValue:PropTypes.func
};

export default FieldSecurityInfo;