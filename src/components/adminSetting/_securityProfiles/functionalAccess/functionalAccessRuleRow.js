import React from 'react';
import classnames from 'classnames';
import PropTypes from 'prop-types';
import { Col, Row, Switch, Typography } from 'antd';
import { Icon } from '../../../../lib';
import SECURITY_PROFILE_CONF from '../securityProfiles.conf';
import { PAGE_NAMES } from '../../../../constants';
const { iconTypesFunctionalAccess, updatedIconTypesFunctionalAccess } = SECURITY_PROFILE_CONF;

const Text = Typography.Text;
const Paragraph = Typography.Paragraph;

const FunctionalAccessRuleRow = (props) => {
    const { name, id, value, toggleAccessValue, isParentDisabled, iconType = '', messages = {}, canEdit, isParent, accessKey, listPageAndBulkUpdateFeatureFlag } = props;
    const rowClass = classnames({
        'row-disabled': isParentDisabled,
        'p-tb-half': true,
        'has-icon': iconType !== ''
    });
    const canEditRule = (isParent && canEdit) || (!isParent && canEdit === null);
    const { summaryPage = {} } = messages;
    const { explainSummaryPageTextSecurity } = summaryPage;
    const isSummaryRule = accessKey === PAGE_NAMES.SUMMARY;
    const summaryRule = isSummaryRule && <Paragraph className="explainSummaryPageTextSecurity">{explainSummaryPageTextSecurity}</Paragraph>;

    return (
        <Row className={rowClass}>
            <Col span={2}>
                <span className={`fa-card-icon ${listPageAndBulkUpdateFeatureFlag ? `newIcon ${updatedIconTypesFunctionalAccess[name]}` : ''}`}>{iconType !== '' && <Icon type={(listPageAndBulkUpdateFeatureFlag ? updatedIconTypesFunctionalAccess[name] : iconTypesFunctionalAccess[name]) || iconType} />}</span>
            </Col>
            <Col span={21}>
                <Text>{name}</Text>
                {summaryRule}
            </Col>
            <Col span={1} disabled>
                {
                    canEditRule && <>
                        <Switch
                            checked={value}
                            size="small"
                            onChange={(value) => toggleAccessValue(value, id, isParent)}
                            disabled={isParentDisabled}
                            aria-label={name}
                        >
                            {name}
                        </Switch>
                    </>
                }
            </Col>
        </Row>
    );
};

FunctionalAccessRuleRow.propTypes = {
    name: PropTypes.string,
    isParentDisabled: PropTypes.bool,
    isParent: PropTypes.bool,
    toggleAccessValue: PropTypes.func,
    id: PropTypes.string,
    value: PropTypes.bool,
    iconType: PropTypes.string,
    canEdit: PropTypes.bool,
    messages: PropTypes.object,
    accessKey: PropTypes.string,
    listPageAndBulkUpdateFeatureFlag: PropTypes.bool
};

const FunctionalAccessRuleRowMemo = React.memo(FunctionalAccessRuleRow);

const ParentFunctionalAccessRule = (props) => {
    return (
        <>
            <div className="fa-card-header">
                <FunctionalAccessRuleRowMemo {...props} isParent iconType={'settings'} />
            </div>
        </>
    );
};

const FunctionalAccessSubRules = (props) => {
    const { subRules, iconType, toggleAccessValue, isParentDisabled = false, messages = {} } = props;

    return (
        subRules.map((subRule) => {

            return <FunctionalAccessRuleRowMemo
                key={subRule.id}
                {...subRule}
                toggleAccessValue={toggleAccessValue}
                iconType={iconType}
                isParentDisabled={isParentDisabled}
                messages={messages}
            />;
        })
    );
};

export {
    ParentFunctionalAccessRule,
    FunctionalAccessSubRules
};