import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'react-intl';
import adminBaseStyles from '../../../../../styles/admin-setting-base.less';
import { FunctionalAccessSubRules, ParentFunctionalAccessRule } from './functionalAccessRuleRow';
import { PAGE_NAMES } from '../../../../constants';

const FunctionalAccessRuleContainer = (props) => {
    const { ruleInfo, toggleAccessValue, messages = {}, listPageAndBulkUpdateFeatureFlag } = props;
    const { id, name, canEdit, value, subRules = [], key } = ruleInfo;
    const showSubRules = value && subRules.length > 0;
    const isSummaryRule = key === PAGE_NAMES.SUMMARY;

    return (
        <>
            <ParentFunctionalAccessRule
                id={id} name={name} canEdit={canEdit} value={value} accessKey={key}
                toggleAccessValue={toggleAccessValue}
                messages={messages}
                listPageAndBulkUpdateFeatureFlag={listPageAndBulkUpdateFeatureFlag}
            />
            {
                showSubRules && <div className={isSummaryRule && 'summarySubRules'}>
                    <FunctionalAccessSubRules
                        subRules={subRules}
                        toggleAccessValue={toggleAccessValue}
                        isParentDisabled={!value}
                        messages={messages}
                    />
                </div>
            }
        </>
    );
};

// General tab content
class FunctionalAccessInfo extends PureComponent {
    render() {
        const { rulesConfig = [], messages = {}, toggleAccessValue, listPageAndBulkUpdateFeatureFlag } = this.props;

        return (
            <div className="functional-access-info">
                <div className={adminBaseStyles.contentNonGridArea}>
                    <h2><FormattedMessage id="functionalAccessHeading" /></h2>
                    <FormattedMessage id="functionalAccessSubHeading" />
                </div>
                <div className={adminBaseStyles.functionalAccessRule}>
                    {
                        rulesConfig.map((item) => (
                            <div className="fa-card-item" key={item.name}>
                                <FunctionalAccessRuleContainer
                                    messages={messages}
                                    ruleInfo={item}
                                    toggleAccessValue={toggleAccessValue}
                                    listPageAndBulkUpdateFeatureFlag={listPageAndBulkUpdateFeatureFlag}
                                />
                            </div>
                        ))
                    }
                </div>
            </div>
        );
    }
}

FunctionalAccessInfo.propTypes = {
    rulesConfig: PropTypes.array,
    messages: PropTypes.object,
    toggleAccessValue: PropTypes.func,
    listPageAndBulkUpdateFeatureFlag: PropTypes.bool
};

export default FunctionalAccessInfo;