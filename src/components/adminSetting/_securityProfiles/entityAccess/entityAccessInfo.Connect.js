import { connect } from 'react-redux';
import EntityAccessInfo from './entityAccessInfo';
import { toggleFunctionalAccessRuleValue } from '../../../../actions/adminSettings/securityProfileActions';
import { getFunctionalAccessSelector } from '../../../../selectors/applicationFnasSelectors';
import { getEntityAccessCardsConfigSelector,getEntityAccessCardsConfigSecuritySelector } from '../../../../selectors/adminSettingSelectors/securityProfilesSelectors';
import { WORKFLOW_SETTINGS_FNA } from '../../../../constants/tablesConsts';
import { updateSubnavAreas } from '../../../../actions/adminSettings/adminSettingActions';
import { browserHistory } from '../../../../history';
import { TABLE_NAMES } from '../../../../constants/globalConsts';

const mapSecurityStateToProps = (state, ownProps) => {
    const { entityName } = ownProps;
    const entityAccessByTableCardsConfig = getEntityAccessCardsConfigSelector(state)(entityName);
    const securityInfoCardsConfig = getEntityAccessCardsConfigSecuritySelector(state)(entityName);
    const hasWorkflowFna = getFunctionalAccessSelector(state)(WORKFLOW_SETTINGS_FNA);

    return {
        entityAccessByTableCardsConfig,
        hasWorkflowFna,
        securityInfoCardsConfig,
        isSkillEntity : entityName.toLowerCase() === TABLE_NAMES.SKILL
    };
};

const mapSecurityProfileDispatch = (dispatch) => {
    return {
        updateFNAValue: (value, id) => {
            dispatch(toggleFunctionalAccessRuleValue(value, id));
        },
        onWorkflowButtonClick: (data) => {
            dispatch(updateSubnavAreas(data));
            browserHistory.push('/admin-settings/workflows/roles-by-name');
        }
    };
};

export default connect(mapSecurityStateToProps, mapSecurityProfileDispatch)(EntityAccessInfo);