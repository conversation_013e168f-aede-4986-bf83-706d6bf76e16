import React from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'react-intl';
import { EntityAccessRuleRow } from './entityAccessRule';
import { Alert } from 'antd';
import styles from './styles.less';


/**
 * Get entity access card content for skill approval
 * @param {Object} props contains props defined in propTypes
 */
const SkillManagerApproval = (props) => {
    const { fnaValueProps, fnaName, updateFNAValue } = props;

    const { accessValue: fnaValue, ruleId: fnaRuleId } = fnaValueProps || {};
    const entityAccessRuleProps = {
        fnaRuleId,
        fnaName,
        fnaValue,
        showToggle: true,
        toggleValue: updateFNAValue
    };

    return (
        <>
            <EntityAccessRuleRow {...entityAccessRuleProps} />
            <Alert showIcon type="warning" message={<FormattedMessage id="managerApprovalAlert" />} className={styles.skillApprovalAlert} />
            <FormattedMessage id="managerApprovalSubHeading" />
        </>
    );

};

SkillManagerApproval.propTypes = {
    fnaValueProps: PropTypes.object,
    fnaName: PropTypes.string,
    updateFNAValue: PropTypes.func
};

export default SkillManagerApproval;