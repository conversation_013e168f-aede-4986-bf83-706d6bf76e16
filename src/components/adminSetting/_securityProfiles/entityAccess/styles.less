:local(.radioOptionContainer) {
    display: flex;
    justify-content: space-between;
    height: 30px;
}

:local(.predefinedConditionsSelect) {
    bottom: 5px;
    width: 360px !important;
}

:local(.collapsibleTitle) {
    font-size: @heading-5-size !important;
    margin-left: 20px;
    margin-top: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    span.anticon {
        padding: 0;
    }
}

:local(.iconCollapse) {
    margin-right: 5px;
    padding-bottom: 2px;
    font-size: 20px !important;
}

:local(.iconCollapseExpanded) {
    transform: rotate(180deg);
}

:local(.jsonWarningBanner) {
    width: 650px;
}

:local(.jsonTextArea) {
    width: 900px;
    margin-top: 15px !important;
    font-family: Courier;
}

:local(.apiPortalLink) {
    text-decoration: underline;
    margin-left: -10px;
}

:local(.jsonTextAreaForm) {
    margin-bottom: 0;
}

:local(.jsonConditionsArea) {
    padding-left: 15px;
}

:local(.ruleText) {
    font-weight: @medium-weight;
}

:local(.workflowCardBtnText):focus,
:local(.workflowCardBtnText):focus-visible {
    box-shadow: 0 0 0 1.5px @secondary-color !important;
}

:local(.entityAccessConditions) {
    margin: 0;
    padding: 0;
    border: none;

    legend {
        margin: 0;
        border: none;
        margin-bottom: 8px;
        color: @dark-color;
        font-size: @font-size-base;
    }
}

:local(.skillApprovalAlert){
    // Need to override due to side effect of (.planning-data-access .oQv9e4LBa6FuwNoSMf10 .ant-card .ant-card .ant-card-body > div)
    margin: 10px 0px ! important;
    padding: 8px 15px !important;
}