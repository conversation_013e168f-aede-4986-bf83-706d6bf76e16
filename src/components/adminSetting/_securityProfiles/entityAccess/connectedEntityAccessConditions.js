
import { connect } from 'react-redux';
import {
    setCustomConditionsAreaCollapsed, setJsonConditionValidationResult, updateConditionType, updateEntityAccessPredefinedConditionValue,
    updateJsonConditionValue, validateJsonCondition, setCustomConditionValue, setJsonCustomConditionValue
} from '../../../../actions/adminSettings/securityProfileActions';
import { TABLE_NAMES } from '../../../../constants';
import { ENTITY_ACCESS_CONDITION_TYPES, FUNCTIONAL_ACCESS_LEVEL_TYPES } from '../../../../constants/adminSettingConsts';
import { getApiPortalUrl } from '../../../../history';
import { getCustomConditionsStaticLabelsSelector } from '../../../../selectors/adminSettingSelectors/customConditionsSelectors';
import {
    getEntityAccessJsonCustomConditionSelector, getEntityAccessConditionTypeSelector, getEntityAccessPredefinedConditionsSelector,
    getEntityAccessPredefinedConditionValueSelector, getEntityAccessTableNameSelector, getCustomConditionsAreaCollapsedStateSelector,
    getEntityAccessCustomConditionSelector
} from '../../../../selectors/adminSettingSelectors/securityProfilesSelectors';
import EntityAccessConditions from './entityAccessConditions';

const INHERIT_READ_ACCESSES_TABLES = [TABLE_NAMES.BOOKING, TABLE_NAMES.ROLEREQUEST];

const getConditionTypesConfig = (staticLabels, accessType, entityTableName, isSkillEntity) => {
    const {
        commonPredefinedConditionsLabel,
        inheritReadPredefinedConditionsLabel,
        commonCustomConditionLabel,
        inheritReadCustomConditionLabel,
        jsonConditionLabel,
        inheritReadJsonConditionLabel,
        allSkillsLabel,
        onlyTheseSkillsLabel,
        onlyRelatedSkillsLabel
    } = staticLabels || {};

    const useInheritReadAccessLabel = INHERIT_READ_ACCESSES_TABLES.includes(entityTableName) && accessType === FUNCTIONAL_ACCESS_LEVEL_TYPES.READ;

    const predefinedOptionLabel = isSkillEntity ? allSkillsLabel :
        useInheritReadAccessLabel ? inheritReadPredefinedConditionsLabel : commonPredefinedConditionsLabel;
    const customOptionLabel = isSkillEntity ? onlyTheseSkillsLabel :
        useInheritReadAccessLabel ? inheritReadCustomConditionLabel : commonCustomConditionLabel;
    const jsonOptionLabel = isSkillEntity ? onlyRelatedSkillsLabel :
        useInheritReadAccessLabel ? inheritReadJsonConditionLabel : jsonConditionLabel;

    return [
        { value: ENTITY_ACCESS_CONDITION_TYPES.PREDEFINED, textLabel: predefinedOptionLabel },
        { value: ENTITY_ACCESS_CONDITION_TYPES.CUSTOM, textLabel: customOptionLabel },
        { value: ENTITY_ACCESS_CONDITION_TYPES.JSON, textLabel: jsonOptionLabel }
    ];
};

/**
 * Hide the entity access radio buttons
 *
 * @param {string} accessType access type of the fna
 * @param {string} entityTableName table name the fna belongs to
 * @param {boolean} hideEntityAccessConditions override it to be hidden
 * @returns {boolean} returns true or false based on condition
 */
const getShouldHideConditionTypesRadios = (accessType, entityTableName, hideEntityAccessConditions) =>
    hideEntityAccessConditions || (accessType === FUNCTIONAL_ACCESS_LEVEL_TYPES.READ && [TABLE_NAMES.ROLEREQUESTGROUP, TABLE_NAMES.CLIENT].includes(entityTableName));

const getShouldShowPredefinedConditions = (selectedConditionType, radioOptionConditionType, accessType, predefinedConditions = []) => {
    return selectedConditionType === ENTITY_ACCESS_CONDITION_TYPES.PREDEFINED
        && radioOptionConditionType === ENTITY_ACCESS_CONDITION_TYPES.PREDEFINED
        && !(accessType === FUNCTIONAL_ACCESS_LEVEL_TYPES.READ && predefinedConditions.length <= 1);
};

const mapStateToProps = (state, ownProps) => {
    const { entityId, accessLevelId, accessType, isSkillEntity, hideEntityAccessConditions } = ownProps;
    const predefinedConditionId = getEntityAccessPredefinedConditionValueSelector(state)(accessLevelId);
    const predefinedConditions = getEntityAccessPredefinedConditionsSelector(state)(entityId, accessLevelId);
    const entityTableName = getEntityAccessTableNameSelector(state);
    const conditionType = getEntityAccessConditionTypeSelector(state)(accessLevelId);
    const jsonCustomCondition = getEntityAccessJsonCustomConditionSelector(state)(accessLevelId);
    const customCondition = getEntityAccessCustomConditionSelector(state)(accessLevelId);

    const staticLabels = getCustomConditionsStaticLabelsSelector(state);
    const customConditionsAreaCollapsed = getCustomConditionsAreaCollapsedStateSelector(state)(accessLevelId, entityId);

    return {
        jsonCustomCondition,
        customCondition,
        conditionType,
        conditionTypesConfig: getConditionTypesConfig(staticLabels, accessType, entityTableName, isSkillEntity),
        predefinedConditionId,
        predefinedConditions,
        hideConditionTypesRadios: getShouldHideConditionTypesRadios(accessType, entityTableName, hideEntityAccessConditions),
        showCustomConditions: conditionType === ENTITY_ACCESS_CONDITION_TYPES.CUSTOM,
        getShouldShowPredefinedConditions: (radioOptionConditionType) => getShouldShowPredefinedConditions(conditionType, radioOptionConditionType, accessType, predefinedConditions),
        showJSONConditionArea: conditionType === ENTITY_ACCESS_CONDITION_TYPES.JSON,
        staticLabels,
        apiPortalUrl: getApiPortalUrl(),
        customConditionsAreaCollapsed,
        isSkillEntity
    };
};

const mapDispatchToProps = (dispatch, ownProps) => {
    return {
        updatePredefinedConditionValue: (newConditionId) => {
            dispatch(updateEntityAccessPredefinedConditionValue(newConditionId, ownProps.accessLevelId));
        },
        onConditionTypeChange: (conditionType) => {
            dispatch(updateConditionType(conditionType, ownProps.accessLevelId));
        },
        validateJsonCondition: (payload) => {
            dispatch(validateJsonCondition(payload, ownProps.entityId, ownProps.accessLevelId));
        },
        setJsonConditionValidationResult: (hasError) => {
            dispatch(setJsonConditionValidationResult(hasError, ownProps.accessLevelId));
        },
        updateJsonConditionValue: (value) => {
            dispatch(updateJsonConditionValue(value, ownProps.accessLevelId));
        },
        setCustomConditionsAreaCollapsed: (isCollapsed) => {
            dispatch(setCustomConditionsAreaCollapsed(isCollapsed, ownProps.accessLevelId, ownProps.entityId));
        },
        setCustomConditionValue: (value) => {
            dispatch(setCustomConditionValue(value, ownProps.accessLevelId));
        },
        setJsonConditionValue: (value) => {
            dispatch(setJsonCustomConditionValue(value, ownProps.accessLevelId));
        }
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(EntityAccessConditions);
