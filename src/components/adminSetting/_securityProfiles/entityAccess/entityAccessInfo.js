import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';


import { Card, Button } from 'antd';
import { FormattedMessage } from 'react-intl';
import adminBaseStyles from '../../../../../styles/admin-setting-base.less';
import { adminSettingConsts } from '../../../../constants';
import PropTypes from 'prop-types';
import EntityAccessCardContent from './entityAccessCardContent';
import styles from './styles.less';
import { getLabelDescription } from '../../../../constants';
import { FNAS_KEY } from '../../../../constants/adminSettingConsts';
import SkillManagerApproval from './skillManagerApproval';

const { SECTION_CONSTANTS } = adminSettingConsts;

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        md: { span: 4 },
        sm: { span: 4 }
    },
    wrapperCol: {
        xs: { span: 24 },
        md: { span: 6 },
        sm: { span: 6 }
    }
};

const StyledCardWrapper = (props) => (
    <Card className="card-layout" bordered={true}>
        {props.children}
    </Card>
);

StyledCardWrapper.propTypes = {
    children: PropTypes.node.isRequired
};

class EntityAccessInfo extends Component {
    constructor(props) {
        super(props);
    }

    getMainContent() {
        const { entityAccessByTableCardsConfig, securityInfoCardsConfig, entityName, entityId, messages, updateFNAValue, isSkillEntity, hasWorkflowFna, onWorkflowButtonClick } = this.props;

        const commonCardContentProps = {
            entityId,
            entityName,
            messages,
            updateFNAValue,
            isSkillEntity
        };

        // Cards that contain CRUD operations
        const entityAccessByTableCardContent = entityAccessByTableCardsConfig.filter(config => !config.isParentCard)
            .map(config => {
                return (
                    <StyledCardWrapper key={`${entityName}_${config.accessType}`}>
                        <EntityAccessCardContent {...config} {...commonCardContentProps} />
                    </StyledCardWrapper>
                );
            });

        if (entityName === SECTION_CONSTANTS.ROLE_REQUEST) {
            // Note: Add WORKFLOW_TAB_LINK to ROLEREQUEST config dynamically, because doesn't have config defined in FNAS_PER_TABLENAME_SECURITY_INFO
            securityInfoCardsConfig.push({
                isParentCard: false,
                hasWorkflowFna, onWorkflowButtonClick,
                isEntityAccess:true,
                hideEntityAccessConditions: true,
                key:FNAS_KEY.WORKFLOW_TAB_LINK
            });
        }

        // extra cards that need to be part of entityAccess
        const securityInfoCardsContent = securityInfoCardsConfig.filter(config => !config.isParentCard && config.isEntityAccess)
            .map(config => {
                return (
                    <StyledCardWrapper key={`${entityName}_${config.accessType}`}>
                        {getSecurityInfoCardsContent({ ...config, ...this.props })}
                    </StyledCardWrapper>
                );
            });

        // read card
        const parentCardContentConfig = entityAccessByTableCardsConfig.find(config => config.isParentCard) || {};
        const entityAccessCards = [...entityAccessByTableCardContent, ...securityInfoCardsContent];

        return (
            <StyledCardWrapper key={`${entityName}_mainCard`}>
                <EntityAccessCardContent {...parentCardContentConfig} {...commonCardContentProps} />
                {entityAccessCards}
            </StyledCardWrapper>
        );
    }

    render() {
        const { entityName, isSkillEntity } = this.props;
        const entity = getLabelDescription(entityName);
        const entityAccessSubHeading = isSkillEntity ? 'skillEntitySubHeading' : 'entityAccessSubHeading';

        return (
            <>
                <div className={adminBaseStyles.entityNameContainer}>
                    <h2>{entity}</h2>
                    <FormattedMessage id={entityAccessSubHeading} values={{ entityName: entity.toLowerCase() }} />
                    {entity !== SECTION_CONSTANTS.ROLE_REQUEST && <FormattedMessage id="entityAccessSubHeadingRemaining" />}
                </div>
                <div className="planning-data-access">
                    <Form {...formItemLayout}>
                        <div className={adminBaseStyles.contentNonGridArea}>
                            {this.getMainContent()}
                        </div>
                    </Form>
                </div>
            </>
        );
    }
}

/**
 * Get entity access card content based on key
 * @param {Object} props contains props defined in propTypes
 */
const getSecurityInfoCardsContent = (props) =>{
    const { onWorkflowButtonClick, hasWorkflowFna, key } = props;
    switch (key) {
        case FNAS_KEY.WORKFLOW_TAB_LINK: {
            return <WorkflowTabLink key={key} onWorkflowButtonClick={onWorkflowButtonClick} showButton={hasWorkflowFna} />;
        }
        case FNAS_KEY.MANAGER_APPROVAL: {
            return <SkillManagerApproval {...props} />;
        }
        default:
            return;
    }
};

const WorkflowTabLink = (props) => {
    const onBtnClick = () => {
        const { onWorkflowButtonClick } = props;
        onWorkflowButtonClick(['Workflows']);
    };

    return (
        <>
            <span className={styles.ruleText}><FormattedMessage id="workflowCardSubHeading" /></span>
            <p><FormattedMessage id="workflowCardMessage" /></p>
            {
                props.showButton
                    ? <Button className={styles.workflowCardBtnText} type="secondary" onClick={onBtnClick}>{<FormattedMessage id="workflowCardBtnText" />}</Button>
                    : <FormattedMessage id="workflowTurnedOff" />
            }
        </>
    );
};

WorkflowTabLink.propTypes = {
    onWorkflowButtonClick: PropTypes.func,
    showButton: PropTypes.bool
};

EntityAccessInfo.propTypes = {
    entityAccessByTableCardsConfig: PropTypes.array.isRequired,
    securityInfoCardsConfig:PropTypes.array.isRequired,
    entityName: PropTypes.string,
    entityId: PropTypes.string,
    messages: PropTypes.object,
    updateFNAValue: PropTypes.func.isRequired,
    isSkillEntity: PropTypes.bool,
    hasWorkflowFna: PropTypes.bool,
    onWorkflowButtonClick:PropTypes.func
};

export default EntityAccessInfo;