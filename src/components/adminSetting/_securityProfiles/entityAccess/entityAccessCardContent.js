import React from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'react-intl';
import { EntityAccessRuleRow } from './entityAccessRule';
import ConnectedEntityAccessConditions from './connectedEntityAccessConditions';

const SubHeaderText = ({
    entityName,
    accessType = '',
    isReadAccessType
}) => {
    const accessTypeLowerCase = accessType.toLowerCase();
    const accessLevelNameMsgId = accessTypeLowerCase + 'Entity';
    const readRuleConditionMsgId = 'readEntity' + entityName.replace(' ', '');
    const lineBreak = <br />;

    return (
        <FormattedMessage
            id={isReadAccessType ? readRuleConditionMsgId : accessLevelNameMsgId}
            values={{
                entityName: isReadAccessType ? entityName : entityName.toLowerCase(),
                lineBreak: lineBreak
            }}
        />
    );
};

SubHeaderText.propTypes = {
    entityName: PropTypes.string,
    accessType: PropTypes.string,
    isReadAccessType: PropTypes.bool
};

/**
 * Get entity access card content
 * @param {Object} props contains props defined in propTypes
 */
const EntityAccessCardContent = (props) => {
    const {
        entityName,
        accessType,
        messages = {},
        fnaValueProps,
        fnaName,
        updateFNAValue,
        entityId,
        accessLevelId,
        isReadAccessType,
        isSkillEntity,
        hideEntityAccessConditions = false
    } = props;

    const { accessValue: fnaValue, ruleId: fnaRuleId } = fnaValueProps || {};
    const entityAccessRuleProps = {
        fnaRuleId,
        fnaName: isSkillEntity ? messages.customConditions.addSkillHeader : fnaName,
        fnaValue,
        messages,
        showToggle: !isReadAccessType,
        toggleValue: updateFNAValue
    };

    return accessType ? (
        <div>
            <EntityAccessRuleRow {...entityAccessRuleProps} />

            <SubHeaderText accessType={accessType} entityName={entityName} isReadAccessType={isReadAccessType} />
            <div>
                {
                    true === fnaValue && (
                        <ConnectedEntityAccessConditions entityId={entityId} entityName={entityName} accessLevelId={accessLevelId} accessType={accessType} messages={messages}
                            isSkillEntity={isSkillEntity} hideEntityAccessConditions={hideEntityAccessConditions}/>
                    )
                }
            </div>
        </div>
    )
        : null;
};

EntityAccessCardContent.propTypes = {
    entityName: PropTypes.string,
    accessType: PropTypes.string,
    messages: PropTypes.object,
    fnaValueProps: PropTypes.object,
    name: PropTypes.string,
    updateFNAValue: PropTypes.func,
    entityId: PropTypes.string,
    accessLevelId: PropTypes.string,
    fnaName:PropTypes.string,
    isReadAccessType:PropTypes.bool,
    isSkillEntity:PropTypes.bool,
    hideEntityAccessConditions:PropTypes.bool
};

export default EntityAccessCardContent;