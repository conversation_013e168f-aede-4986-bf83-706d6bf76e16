.p-tb-half {
  padding: 0.5em 0;
}

.row-disabled {
  opacity: .7;
}

.switch-label{
  padding-left: 1em;
}

.entity-access-form {
  margin-top: 1%;

  .ant-legacy-form-item {
    margin-bottom: 0;
  }
}

.condition-row-container {
  display: flex;

  .condition-row {
    margin-right: @gutter-width;
  }
}

.ant-btn-primary:not(:disabled):hover {
  color: @white-color;
  border-color: @primary-color;
  background-color: @primary-btn-hover-bg;
}

.security-profile-content {
  display: flex;
  flex-direction: column;
  overflow-x: auto;

  .card-container {
    margin: 0 20px;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;

    .ant-tabs.ant-tabs-top {
      height: inherit;
    }

    .ant-tabs-content {
      height: 100%;

      .ant-tabs-tabpane {
        height: 100vh;
        max-height: calc(100vh - 260px);
      }
    }

    .ant-tabs-nav {
      border-top-left-radius: 0.5rem;
      border-top-right-radius: 0.5rem;
    }

    .ant-tabs-content-holder {
      margin-top: -1rem;
    }
  }

  .ant-tabs-nav-scroll {
    margin-left: 10px;
  }

  .ant-tabs-bar {
    background: @white-color;
    border-radius: 8px 8px 0 0;
    .ant-tabs-nav-scroll {
      margin-left: 10px;
    }
  }

  .ant-table-tbody > tr > td {
    padding: unset;
    padding-left: 20px;
    &.ant-table-column-sort {
      background: rgba(0, 0, 0, 0.01);
    }
  }
  
  .ant-table-tbody .ant-table-expanded-row .ant-table-cell {
    padding-right: 20px;
    padding-left: 70px;
  }
  .row-color-odd {
    background-color: unset;
  }

  .action-bar {
    position: fixed !important;
    width: calc(100% - 674px) !important;
  }
  .ant-select-dropdown {
    .ant-select-item-option-content {
      white-space: break-spaces;
    }
  }
}

.functional-access-info, .planning-data-access {
  margin: 0;
}

.no-tab-view{
  .functional-access-info, .planning-data-access {
    height: calc(100vh - 160px);
    overflow: auto;
  }
}

.functional-access-info {
  height: 100%;
  display: flex;
  flex-direction: column;

  .fa-card-item{
    padding: 8px 16px 16px 16px;
    border-bottom: 1px solid #B8BEC1;
    .ant-row {
      padding: 0;
    }
    .fa-card-header{
      font-weight: bold;
      line-height: 35px;
      .ant-typography {
        font-size: 14px;
        font-weight: @medium-weight;
        color: @secondary-btn-color;
      }
    }
    .fa-card-icon{
      height: 32px;
      width: 32px;
      background: @primary-color;
      color: @white-color;
      font-size: 1.2em;
      text-align: center;
      border-radius: 1em;
      margin-right: 8px;
      float: left;
      margin-top: 3px;
      visibility: hidden;

      .anticon {
        vertical-align: 0;
      }
      &.newIcon {
        border-radius: 0.5rem;
        float: none;
        padding: 0.4375rem;
        display: flex;
      }
      &.summary-security, &.user-security {
        background: linear-gradient(180deg, #FA5B5B 0%, #F68888 100%);;
      }
      &.planner-security {
        background: linear-gradient(170deg, #3E2F75 -16.16%, #A492DC 113.34%);
      }
      &.tableView-security {
        background: linear-gradient(180deg, #22A8A0 0%, #6DEAE2 100%);
      }
      &.lists-security {
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.00) 24.17%, rgba(255, 255, 255, 0.20) 81.5%);
        background-color: @primary-color;
      }
      &.reports-security {
        background: linear-gradient(180deg, #96D049 0%, #AED373 100%);
      }
      &.roles-security, &.megaphone {
        background: linear-gradient(180deg, #DC6782 0%, #E382A5 100%);
      }
      &.operationLog-security, &.settings-security {
        background: linear-gradient(180deg, #758EAD 0%, #99B4C8 100%);
      }
    }

    .has-icon .fa-card-icon {
      visibility: visible;
    }

    .is-child-access{
      margin-left: 35px;
    }
  }

}

.planning-data-access {
  .rule-condition {
    margin-bottom: 0;
    .ant-select {
      width: 100%
    }
  }

  .card-layout {
    border-radius: @card-border-radius;
    margin: 1.4rem 0 0 0;
  }

  .ant-card {
    border: 1px solid @light-grey-color-3;
  }

  .ant-card-body {
    padding: 1em;
  }

}

.field-security-info {
  padding-bottom: 10px;
}

.security-option {
  display: block;
  .field-restriction-description-container {
    margin-bottom: 8px;
    p {
      margin-bottom: 0;
    }
  }
}

.security-option > span {
  display: inline-flex;
}

.security-options-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.security-options {
  border-left-style: outset;
  border-left-width: 3px;
  padding-left: 10px;
}

.content-form-label-header {
  margin-bottom: 10px;
}

.field-restriction-description-container {
  display: inline-block;
}

.field-restriction-description {
  display: inline-block;
}

.field-security-info-notification {
  background-color: #eceaea;
  border-style: solid;
  border-radius: 5px;
  border-width: 1px;
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 7px 10px;
}

.field-security-info-notification-content {
  margin-left:15px;
  margin-top: 3px;
  margin-bottom: 3px;
}

.securityProfilesLayout {
  .ant-spin-nested-loading {
    height: 100%;
    
    .ant-spin-container {
      height: inherit;

      .adminSettingsContent {
        height: inherit;
      }
    }
  }
}

.ant-radio-wrapper .ant-radio-checked .ant-radio-inner::after {
  transform: scale(0.500);
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: @secondary-background;
}

.functional-access-info .fa-card-item .fa-card-header .ant-typography.explainSummaryPageTextSecurity {
  line-height: normal;
  font-weight: 400;
  margin: 10px 0 20px;
  white-space: pre-line;
  color: @main-text-color;
}

.summarySubRules {
  margin-left: 16px;
}