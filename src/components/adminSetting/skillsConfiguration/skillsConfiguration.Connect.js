import { connect } from 'react-redux';
import SkillsConfiguration from './skillsConfiguration';
import { fetchSkillsConfigurationSections, fetchSkillsConfigurationBySectionId, setSkillsTableRowSortOrder, addNewSkillInTable, removeSkillFromTable,
    cancelRemoveSkillFromTable, onChangedSkillConfigurationForm,
    updateBulkSkillsConfigurationSections, updateSkillTitleName,
    updateSkillsExpandKey, resetSkillsUpdateFinishState,
    updateActiveComponentTabForSkillTypes,
    updateLevelType,updateUseLevelsFrom,
    updateLevelName,addLevels,
    updateLevels,updateUseLevelsFromUsingAPI,
    skillsLoadTableData, fetchSkillTableAccessData,
    fetchSkillActiveCurrency,fetchSkillTypeEntities,
    setActiveTabForSkillType,
    fetchSkillEntityTypes,
    fetchCategories,
    fetchSubCategories,
    setTypeFilters,clearTypeFilters,
    clearSkillTypeWarningMessage, updateCurrentPageRouterState, resetCurrentPageRouterState,
    updateCategoryDetails,
    fetchDivisions,
    fetchDepartments } from '../../../actions/adminSettings/adminSettingActions';
import { toggleAdminSettingsSortOrder } from '../../../actions/persistDataActions';
import {} from '../../../actions/persistDataActions';
import { injectIntl } from 'react-intl';
import { getLoadedLicensesSelector } from '../../../selectors/commonSelectors';
import { getSkills } from '../../../selectors/skillStructureSelectors';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { getImportLibraryStatus, updateSelectedSkills } from '../../../actions/adminSettings/importLibrarySkillsActions';
import { getImportLibraryStatusSelector, getCurrentSkillCountSelector, getSectionSkillCountSelector } from '../../../selectors/adminSettingSelectors/skillConfigurationSelectors';
import { FEATURE_FLAGS } from '../../../constants/globalConsts';

const mapStateToProps = (state) => {
    const { adminSetting, persistData } = state;
    const { skillsConfiguration, adminSettingRouter } = adminSetting;
    const { sortOrderStatus } = persistData.adminSettings.selectionList;
    const licensesMap = getLoadedLicensesSelector(state);
    const getAllSkills = getSkills(state);
    const isRetainImportLibraryEnabled = getFeatureFlagSelector(FEATURE_FLAGS.RETAIN_IMPORT_LIBRARY)(state); // Remove feature flag once it is fully fledged
    const importInProgress = getImportLibraryStatusSelector(state);
    const currentSkillCount = getCurrentSkillCountSelector(state)();
    const getSkillCountByCategoryId = getSectionSkillCountSelector(state);

    return {
        skillsConfiguration : skillsConfiguration,
        sortOrderStatus:sortOrderStatus.skills,
        skillOnExpandedKeys : skillsConfiguration.skillOnExpandedKeys,
        isUpdateAPICallFinishState : skillsConfiguration.isUpdateAPICallFinishState,
        hasSectionLevelError : skillsConfiguration.hasSectionLevelError,
        isNewListItemAdded : skillsConfiguration.isNewListItemAdded,
        newlyAddedSectionName: skillsConfiguration.newlyAddedSectionName,
        newlyAddedSectionId: skillsConfiguration.newlyAddedSectionId,
        formattingFields:skillsConfiguration.formattingFields,
        levelsErrorStatus:skillsConfiguration.levelsErrorStatus,
        levelsGridModified:skillsConfiguration.levelsGridModified,
        loading:skillsConfiguration.loading,
        loadingSections:skillsConfiguration.loadingSections,
        activeCurrentTab:skillsConfiguration.activeCurrentTab,
        hasWarning:skillsConfiguration.hasWarning,
        updateFailed: skillsConfiguration.updateFailed,
        isAddedItemDeleted: skillsConfiguration.isAddedItemDeleted,
        adminSettingRouter,
        licensesMap,
        getAllSkills,
        isRetainImportLibraryEnabled,
        importInProgress,
        currentSkillCount,
        getSkillCountByCategoryId
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        fetchInitialData: () => {
            dispatch(fetchSkillsConfigurationSections({ isUpdateAPICallFinishState:false }));
            dispatch(skillsLoadTableData({ tableName: 'fieldtype', selection: null }, 'fieldProperties'));
            dispatch(fetchSkillTableAccessData('fieldformat'));
            dispatch(fetchSkillActiveCurrency());
            dispatch(fetchSkillTypeEntities('resourceskill'));
            dispatch(fetchSkillEntityTypes());
            dispatch(fetchSubCategories());
            dispatch(fetchDivisions());
            dispatch(fetchDepartments());
            dispatch(getImportLibraryStatus());
        },
        fetchSkillsConfigurationSections : ()=>{
            const payload = { isUpdateAPICallFinishState:false };
            dispatch(fetchSkillsConfigurationSections(payload));
        },
        fetchSkillsConfigurationBySectionId : (id)=>{
            dispatch(fetchSkillsConfigurationBySectionId(id));
        },
        toggleSortOrderStatus: (payload) => {
            dispatch(toggleAdminSettingsSortOrder(payload));
        },
        addNewSkillInTable: (payload) => {
            dispatch(addNewSkillInTable(payload));
        },
        removeSkillFromTable: (payload) => {
            dispatch(removeSkillFromTable(payload));
        },
        cancelRemoveSkillFromTable: (payload) => {
            dispatch(cancelRemoveSkillFromTable(payload));
        },
        setSkillsTableRowSortOrder: (metaData) => {
            dispatch(setSkillsTableRowSortOrder(metaData));
        },
        onChangedSkillConfigurationForm: (metaData) => {
            dispatch(onChangedSkillConfigurationForm(metaData));
        },
        updateBulkSkillsConfigurationSections : (payload)=>{
            dispatch(updateBulkSkillsConfigurationSections(payload));
        },
        updateSkillTitleName : (metaData)=>{
            dispatch(updateSkillTitleName(metaData));
        },
        updateSkillsExpandKey: (metaData)=>{
            dispatch(updateSkillsExpandKey(metaData));
        },
        setTypeFilters: (metaData) =>{
            dispatch(setTypeFilters(metaData));
        },
        clearTypeFilters: () =>{
            dispatch(clearTypeFilters());
        },
        resetSkillsUpdateFinishState:(metaData) => {
            dispatch(resetSkillsUpdateFinishState(metaData));
        },
        updateActiveComponentTabForSkillTypes:(payload) => {
            dispatch(updateActiveComponentTabForSkillTypes(payload));
        },
        updateLevelType:(payload) => {
            dispatch(updateLevelType(payload));
        },
        updateUseLevelsFrom:(payload) => {
            dispatch(updateUseLevelsFrom(payload));
        },
        updateUseLevelsFromUsingAPI:(payload) => {
            dispatch(updateUseLevelsFromUsingAPI(payload));
        },
        updateLevelName:(payload) => {
            dispatch(updateLevelName(payload));
        },
        addLevels:(payload) => {
            dispatch(addLevels(payload));
        },
        updateLevels:(payload) => {
            dispatch(updateLevels(payload));
        },
        loadTableData: (payload, alias) => {
            dispatch(skillsLoadTableData(payload, alias));
        },
        fetchSkillTableAccessData: (data) => {
            dispatch(fetchSkillTableAccessData(data));
        },
        fetchSkillActiveCurrency: (data) => {
            dispatch(fetchSkillActiveCurrency(data));
        },
        fetchSkillTypeEntities:(data) =>{
            dispatch(fetchSkillTypeEntities(data));
        },
        setActiveTabForSkillType:(data) =>{
            dispatch(setActiveTabForSkillType(data));
        },
        clearSkillTypeWarningMessage: () => {
            dispatch(clearSkillTypeWarningMessage());
        },
        updateCurrentPageRouterState:(fakeId,activeItem,queryParams) => {
            dispatch(updateCurrentPageRouterState(fakeId,activeItem,queryParams));
        },
        resetCurrentPageRouterState:() => {
            dispatch(resetCurrentPageRouterState());
        },
        fetchSkillEntityTypes:() => {
            dispatch(fetchSkillEntityTypes());
        },
        fetchSubCategories:() =>{
            dispatch(fetchSubCategories());
        },
        updateCategoryDetails:(data) =>{
            dispatch(updateCategoryDetails(data));
        },
        clearSelectedSkills:() =>{
            dispatch(updateSelectedSkills([]));
        }
    };
};

export default injectIntl(connect(mapStateToProps, mapDispatchToProps)(SkillsConfiguration));