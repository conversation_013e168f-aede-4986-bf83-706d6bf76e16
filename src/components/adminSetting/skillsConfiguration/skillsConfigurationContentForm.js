import React from 'react';
import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';


import { List, Button, Modal, Tag, Tooltip } from 'antd';
import PropTypes from 'prop-types';
import { bindAll, isEmpty, cloneDeep } from 'lodash';
import queryString from 'query-string';
import { IntlCollatorCompare, pushHistoryWithState, splitHyphenFirst } from '../../common-components/helpers';
import { messageService } from '../../common-components/helpers';
import { adminSettingConsts, LOCALE_EN } from '../../../constants';
import { SKILLS_CONFIGURATION_CONSTANTS, ROUTE_ADMIN_PAGE_CONSTANTS } from '../../../constants/adminSettingConsts';
import { AdminCommandBar } from '../adminCommandBar/adminCommandBar';
import adminBaseStyles from '../../../../styles/admin-setting-base.less';
import SkillsConfigurationContentList from './skillsConfigurationContent/skillsConfigurationContentList';
import { RowComponent } from '../../common-components/collapsibleGridOperations/collapsibleGridOperations';
import SkillsConfigurationSummary from './skillsConfigurationContent/SkillsConfigurationSummary';
import './skillsConfiguration.less';
import { SKILLS_CONFIGURATION_CONF } from './skillsConfigurationConf';
import { ConfirmationModal, LicenseInfo } from '../../common-components';
import { navigationService } from '../../common-components/helpers';
import { FormattedMessage } from 'react-intl';
import FormNotifier from '../../common-components/form-notifier';
import ComponentTab from '../../common-components/tab-structure/componentTabs';
import SkillTypeLevelsContent from './skillTypeLevelsContent/SkillTypeLevelsContent';
import SkillFields from './skillFields/skillFields.Connect';
import { ProvideWarningElement, ContactUs } from '../userManagement/navigationComponent/navigateComponent';
import { KEY_CODES, LICENSE_KEYS_ADMIN_SETTINGS } from '../../../constants/globalConsts';
import { getFormattedSearch } from '../../../utils/pageParamsUtils';
import CategorySettingsCard from './categorySettingsCard/categorySettingsCard';
import { ConnectedImportLibrarySkills } from '../../../connectedComponents/connectedImportLibrarySkills';

const { QUERY_PARAMS, DEFAULT_PARAM } = ROUTE_ADMIN_PAGE_CONSTANTS;
const { TAB } = QUERY_PARAMS;

const { licenseKeySkills, licenseKeySkillLevels, licenseKeySkillFields, licenseRetainLibrary } = LICENSE_KEYS_ADMIN_SETTINGS;

const siderWidthValue = 230;

const { COMMANDBAR_CONSTANTS } = adminSettingConsts;
const { FETCH_SKILLS_CONFGIGURATION_SECTIONS, FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID, UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS,
    REMOVE_SKILLS_ROW, CANCEL_REMOVE_SKILLS_ROW, EDITED_SKILL_INFO, SET_SKILLS_TABLE_ROW_SORT_ORDER, ON_EXPAND_KEY_SET, titleFieldName, editTitleName,
    SKILLS, SKILL_LEVELS, SKILL_FIELDS, SKILLS_AND_LEVELS, LEVELS_AND_FIELDS, SKILLS_AND_FIELDS, SKILLS_LEVELS_AND_FIELDS, POPUP_HEADING, POPUP_WARNING, DELETE_ITEMS_LABEL,
    KEEP_ITEMS_LABEL, DELETE_CONST, DELETE_MODIFY_CONST, KEEP_CONST, BUTTON_LABEL, FETCH_SKILLS_ENTITY_TYPES, SET_TYPE_FILTERS, EDIT_SKILL_CATEGORY_DETAILS,
    CLEAR_TYPE_FILTERS } = SKILLS_CONFIGURATION_CONSTANTS;

const ALL_FILTER_ID = 'ALL-ID';
const allTypeFilterValue = { id: ALL_FILTER_ID, value: ['All'] };
class SkillsConfigurationContentForm extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            commandBarConf: SKILLS_CONFIGURATION_CONF.commandBarConf,
            activeSelectionItem: null,
            nextActiveItem: null,
            editTitleName: false,
            actionBarConf: SKILLS_CONFIGURATION_CONF.actionBarConf,
            displayDeleteModal: false,
            displayDeleteAndModifyModal: false,
            displaySkillTypeDeleteModal: false,
            needToNavigate: undefined,
            deleteCount: 0,
            modifyCount: 0,
            handleDeleteCallback: () => { },
            displayLicenceWarningMessage: true,
            displayLicenceErrorMessage: true,
            /** @type {import('../../../types/category.jsdoc').Category[]} */
            skillFilters: [],
            showImportModal: false
        };
        this.actionBarCallback = () => { };
        this.childRef = React.createRef();
        bindAll(this, ['renderWarningMessage', 'clearWarningMessage', 'resetActionBarStatus', 'handleDeleteSkillTypeConfirmation', 'handleCancelDeleteSkillType', 'handleDeleteFromSelectionList', 'tabChange', 'getSkillsComponent', 'getLevelsComponent', 'getFieldsComponent', 'getTabConfig', 'selectedItem', 'toggleSortOrderStatus', 'callbackDispatchHandler', 'getCommandBarConfig',
            'handleTitleEnter', 'handleAddSkill', 'changeTitleInput', 'setStateFields', 'handleSubmit', 'getActionBarConfig',
            'createRequestMetaData', 'resetContent', 'getSkillTitleInputErrorMessage', 'setLatestSkillSectionTitleName', 'getSkillFields', 'getAllActiveSkillSectionDetail', 'renderImportModal',
            'handleCancelConfirmation', 'handleSaveConfirmation', 'confirmSave', 'getUserConfirmation', 'showConfirmationModal', 'setFormFieldsTouched', 'renderFirstOrDefaultItem', 'getTabDetails', 'getCurrentTabQueryParams', 'updateRouteAndSelectedItem', 'checkIsSkillTypeFieldsLabelsDupilcate']);
    }

    componentDidMount() {
        const { actionBarConf } = this.state;
        const { buttons } = actionBarConf;
        const { messages = {} } = this.props;
        const { actionBarFormErrorMessage } = messages;
        actionBarConf.formErrorMessage = actionBarFormErrorMessage;
        buttons.primary.label = <FormattedMessage id="actionBarSaveButtonLabel" />;
        buttons.secondary.label = <FormattedMessage id="actionBarCancelButtonLabel" />;
        buttons.secondary.action = this.resetContent;
        this.setState({
            actionBarConf: { ...actionBarConf }
        });
    }

    getActionBarConfig() {
        return this.state.actionBarConf;
    }

    componentDidUpdate(prevProps, prevState) {
        const { hasSectionLevelError,
            isNewListItemAdded,
            levelsErrorStatus,
            levelsGridModified,
            newlyAddedSectionId } = this.props;
        const { hasSectionLevelError: prevHasSectionLevelError,
            isNewListItemAdded: prevIsNewListItemAdded,
            levelsErrorStatus: prevLevelsErrorStatus,
            levelsGridModified: prevLevelsGridModified,
            newlyAddedSectionId: prevNewlyAddedSectionId } = prevProps;
        const { activeSelectionItem: prevActiveSelectionItem } = prevState;
        const { activeSelectionItem } = this.state;
        const { skillsConfiguration = {}, match = {}, adminSettingRouter } = this.props;
        const { skillsConfigurationSections = [], skillsConfigurationSectionDetails = {} } = skillsConfiguration;
        const { skillsConfiguration: prevSkillsConfiguration = {} } = prevProps;
        const { skillsConfigurationSections: prevSkillsConfigurationSections = [] } = prevSkillsConfiguration;

        //sends message to action bar when there are changes in skills and levels
        if (prevActiveSelectionItem !== activeSelectionItem ||
            prevHasSectionLevelError != hasSectionLevelError ||
            prevIsNewListItemAdded !== isNewListItemAdded ||
            prevLevelsErrorStatus !== levelsErrorStatus ||
            prevLevelsGridModified !== levelsGridModified) {
            const gridStatus = isNewListItemAdded || levelsGridModified;
            const errorStatus = hasSectionLevelError || levelsErrorStatus;
            messageService.sendMessage({ id: 'skillsConfigurationForm', isGridModified: gridStatus, errorStatus: errorStatus });
        }

        if (skillsConfigurationSections && skillsConfigurationSections.length) {
            //When no params id found, Set default active item and navigate accordingly
            if (!match.params[DEFAULT_PARAM]) {
                this.renderFirstOrDefaultItem();
            } else {
                let compareFakeId = splitHyphenFirst(match.params[DEFAULT_PARAM]) !== splitHyphenFirst(adminSettingRouter.fakeId);
                let surrogateId = match.params[DEFAULT_PARAM].split('-')[0];
                let surrogateSearchSuccess = false;
                // newFakeId
                if (compareFakeId) {
                    const activeItem = skillsConfigurationSections.find((item) => skillsConfigurationSectionDetails[item].fakeId && skillsConfigurationSectionDetails[item].fakeId.split('-')[0] === surrogateId);
                    // isNewFakeId found set to it
                    if (activeItem) {
                        surrogateSearchSuccess = true;
                        const queryParams = this.getCurrentTabQueryParams(activeItem);
                        this.renderFirstOrDefaultItem(activeItem, queryParams);
                    } else {
                        this.renderFirstOrDefaultItem();
                    }
                }
                // Any other way if active Item is altered or list items are added/removed
                if (!surrogateSearchSuccess && (activeSelectionItem !== prevActiveSelectionItem || skillsConfigurationSections.length !== prevSkillsConfigurationSections.length)) {
                    const queryParams = this.getCurrentTabQueryParams(activeSelectionItem);
                    if (newlyAddedSectionId && prevNewlyAddedSectionId != newlyAddedSectionId) {
                        const activeItem = skillsConfigurationSections.filter(item => skillsConfigurationSectionDetails[item].id === newlyAddedSectionId)[0];
                        this.setState({ activeSelectionItem: activeItem });
                        this.renderFirstOrDefaultItem(activeItem, queryParams, true);
                    } else {
                        this.renderFirstOrDefaultItem(activeSelectionItem, queryParams);
                    }
                }
                // //Before SAVE: When selection item clicked again, NavLink of selectionList change the path without queryParams so it has handled
                if (match.params[DEFAULT_PARAM] && splitHyphenFirst(match.params[DEFAULT_PARAM]) === splitHyphenFirst(adminSettingRouter.fakeId) && this.props.location.search !== adminSettingRouter.queryParams) {
                    const fakeId = match.params[DEFAULT_PARAM].split('-')[0];
                    const activeItem = skillsConfigurationSections.filter(item => skillsConfigurationSectionDetails[item].fakeId && skillsConfigurationSectionDetails[item].fakeId.split('-')[0] === fakeId)[0];
                    const isItemClickedAgain = activeItem && skillsConfigurationSectionDetails && skillsConfigurationSectionDetails[activeSelectionItem] && !isEmpty(adminSettingRouter.queryParams);
                    if (isItemClickedAgain) {
                        const queryParams = adminSettingRouter.queryParams;
                        this.props.setSelectedItem(skillsConfigurationSectionDetails[activeItem].name, skillsConfigurationSectionDetails[activeItem].fakeId, queryParams);
                    }
                }
            }
            //When newly added item gets deleted
            if (activeSelectionItem && !skillsConfigurationSectionDetails[activeSelectionItem]) {
                let activeItem = skillsConfigurationSections[0];
                let nextActiveItem = this.state.nextActiveItem;
                // In any case nextActiveItem post save is different & not null.
                // E.g scenario Handles when user rename newly added selection item.
                if (skillsConfigurationSectionDetails[this.state.nextActiveItem]) {
                    activeItem = this.state.nextActiveItem;
                    nextActiveItem = null;
                }
                this.setState({ activeSelectionItem: activeItem, nextActiveItem: nextActiveItem });
            }
        }
    }

    componentWillUnmount() {
        this.callbackDispatchHandler(CLEAR_TYPE_FILTERS);
        this.props.resetCurrentPageRouterState();
    }

    renderFirstOrDefaultItem(_activeItem, _queryParams, isNewItem = false) {
        const { skillsConfiguration = {}, messages = {} } = this.props;
        const { skillsConfigurationSections = [], skillsConfigurationSectionDetails = {} } = skillsConfiguration;
        const activeItem = _activeItem ? _activeItem : skillsConfigurationSections.sort(IntlCollatorCompare(LOCALE_EN))[0];
        const { skillTypeSkillsTabTitle } = messages;
        const queryParams = _queryParams ? _queryParams : `?${TAB}=${skillTypeSkillsTabTitle}`;
        if (activeItem && skillsConfigurationSectionDetails[activeItem] && skillsConfigurationSectionDetails[activeItem].fakeId) {
            this.updateRouteAndSelectedItem(skillsConfigurationSectionDetails[activeItem].fakeId, activeItem, queryParams, isNewItem);
        }
    }

    updateRouteAndSelectedItem(activeFakeId, activeItem, queryParams, isNewItem = false) {
        this.props.updateCurrentPageRouterState(activeFakeId, activeItem, queryParams);
        this.props.setSelectedItem(activeItem, activeFakeId, queryParams);
        this.selectedItem(activeItem, isNewItem);
    }

    resetActionBarStatus(callback = () => { }) {
        this.actionBarCallback = callback;
    }

    getConnectedContent() {
        return this.props.connectedComponents;
    }

    resetContent() {
        this.props.fetchSkillsConfigurationSections();
        this.props.loadTableData({ tableName: 'fieldtype', selection: null }, 'fieldProperties');
        this.props.fetchSkillTableAccessData('fieldformat');
        this.props.fetchSkillActiveCurrency();
        this.props.fetchSkillTypeEntities('resourceskill');
        this.props.fetchSkillEntityTypes();
        const { actionBarConf } = this.state;
        const { buttons } = actionBarConf;
        buttons.secondary.action = this.resetContent;
        this.setState({
            actionBarConf: { ...actionBarConf }
        });
        this.setState({
            activeSelectionItem: undefined
            //activeSelectionItemFakeId: undefined
        });
        this.props.updateCurrentPageRouterState('', '');
        this.actionBarCallback();
    }

    callbackDispatchHandler(type, data) {
        switch (type) {
            case FETCH_SKILLS_CONFGIGURATION_SECTIONS:
                this.props.fetchSkillsConfigurationSections();
                break;

            case FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID:
                this.props.fetchSkillsConfigurationBySectionId(data);
                break;

            case REMOVE_SKILLS_ROW:
                this.props.removeSkillFromTable(data);
                break;

            case CANCEL_REMOVE_SKILLS_ROW:
                this.props.cancelRemoveSkillFromTable(data);
                break;

            case EDITED_SKILL_INFO:
                this.props.onChangedSkillConfigurationForm(data);
                break;

            case SET_SKILLS_TABLE_ROW_SORT_ORDER:
                this.props.setSkillsTableRowSortOrder(data);
                break;
            case UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS:
                this.props.updateBulkSkillsConfigurationSections(data);
                break;
            case ON_EXPAND_KEY_SET:
                this.props.updateSkillsExpandKey(data);
                break;

            case FETCH_SKILLS_ENTITY_TYPES:
                this.props.fetchSkillEntityTypes();
                break;
            case SET_TYPE_FILTERS:
                this.props.setTypeFilters(data);
                break;
            case CLEAR_TYPE_FILTERS:
                this.props.clearTypeFilters();
                break;
            case EDIT_SKILL_CATEGORY_DETAILS:
                this.props.updateCategoryDetails(data);
                break;
        }
    }

    handleAddSkill(event) {
        if (event) {
            event.preventDefault();
        }
        const { activeSelectionItem } = this.state;
        // Add All Skill Type when ever adding new skill
        const { skillsConfiguration } = this.props;
        const { initialskillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, skillTypeFilters } = skillsConfiguration;
        const selectedItem = initialskillsConfigurationSectionDetails[activeSelectionItem].id;
        const filters = skillTypeFilters.find(x => x.id === selectedItem).value;
        if (!filters.includes(allTypeFilterValue.id)) {
            this.setSkillTypeFilters(selectedItem, allTypeFilterValue.id);
        }
        // When Adding skill use the updated category name, else initial category name
        const modifiedItem = updatedSkillsConfigurationSectionDetails[activeSelectionItem];
        const updatedSkillSectionName = (modifiedItem && modifiedItem.editTitleInfo && modifiedItem.editTitleInfo.name)
            ? modifiedItem.editTitleInfo.name : activeSelectionItem;

        const metaData = {
            skillSectionName: activeSelectionItem,
            updatedSkillSectionName
        };
        this.props.addNewSkillInTable(metaData);
    }

    getSelectionListProps = (props) => {
        const { match, location, skillsConfiguration, sortOrderStatus = false, licensesMap = {}, intl, currentSkillCount } = props;
        const { skillsConfigurationSections = [], skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = skillsConfiguration;
        const { state = {} } = location;
        const { areaName, sectionName } = cloneDeep(state);
        const { subscribedCount } = licensesMap[licenseKeySkills];

        return {
            toggleSortOrderStatus: this.toggleSortOrderStatus,
            listItems: skillsConfigurationSections,
            listItemDetails: skillsConfigurationSectionDetails,
            routerProps: { match, location },
            modifiedItems: updatedSkillsConfigurationSectionDetails,
            sortOrder: sortOrderStatus,
            activeItem: this.state.activeSelectionItem,
            selectedItem: this.selectedItem,
            currentSection: { area: areaName, section: sectionName, displayTitle: intl.formatMessage({ id: 'skillPageHeader' }) },
            contextMenuOn: true,
            showContextMenu: true,
            handleDelete: this.handleDeleteFromSelectionList,
            licenseCount: subscribedCount,
            licenseSelectionListCustomMsgId: 'selectionListSkillsCustomMsg',
            isLevel2: true,
            handleFilters: this.handleSkillsFilterByCategoryAndSubcategory,
            clearFilters: this.clearSkillFilters,
            showAddAnItemButton: true,
            currentCount: currentSkillCount,
            hasCreateItemRestriction: false,
            createBtnName: 'addSkillCategoryText'
        };
    }

    /**
     * Clear the skill filters
     * */
    clearSkillFilters = () => {
        this.setState({ skillFilters: [] });
    }

    /**
     * Filter skills by category and subcategory
     * @param {string} categoryName
     * @param {string} subCategoryName
     */
    handleSkillsFilterByCategoryAndSubcategory = (categoryName, subCategoryName) => {
        this.setState(prevState => {
            /** @type {import('../../../types/tempSkillTaxonomyCategory.jsdoc').TempSkillTaxonomyCategory[]} */
            const skillFilters = [...prevState.skillFilters];

            // Find the category
            const categoryIndex = skillFilters.findIndex(c => c.name === categoryName);

            if (categoryIndex === -1) {
                // Add new category with the subcategory
                skillFilters.push({
                    name: categoryName,
                    subCategories: [{ name: subCategoryName }]
                });
            } else {
                const category = skillFilters[categoryIndex];

                // Check if the subcategory exists
                const subCategoryIndex = category.subCategories.findIndex(s => s.name === subCategoryName);

                if (subCategoryIndex === -1) {
                    // Add new subcategory
                    category.subCategories.push({ name: subCategoryName });
                } else {
                    // Remove the subcategory
                    category.subCategories.splice(subCategoryIndex, 1);

                    // Remove the category if it has no subcategories left
                    if (category.subCategories.length === 0) {
                        skillFilters.splice(categoryIndex, 1);
                    }
                }
            }

            return { skillFilters };
        });
    };

    /**
     * Filter the skill configuration section by category and subcategory
     * @param {string} activeSelectionItem
     * @param {object} skillSectionDetail
     * @returns {{object} skillSectionDetail
     * */
    FilterSkillConfigurationSectionByCategoryAndSubcategory = (activeSelectionItem, skillSectionDetail) => {
        /**
        * @type {{
        *   skillFilters: import('../../../types/tempSkillTaxonomyCategory.jsdoc').TempSkillTaxonomyCategory[],
        * }}
         **/
        const { skillFilters } = this.state;
        if (skillFilters.length === 0 || !skillFilters.find(c => c.name === activeSelectionItem) || !skillSectionDetail || !skillSectionDetail.skills) {
            return skillSectionDetail;
        }
        const categoryDetails = skillSectionDetail;

        // Filter the subcategories
        const subCategories = skillFilters.find(c => c.name === activeSelectionItem).subCategories.map(s => s.name);

        // Filter the skills
        const filteredSkills = categoryDetails.skills.filter(s => s.subCategories.some(sub => subCategories.includes(sub)) || s.operation === 'ADD');

        // Update the category details
        return {
            ...categoryDetails,
            skills: filteredSkills
        };
    }

    selectedItem(selectedItem, isNewItem) {
        const { skillsConfiguration, form } = this.props;
        const { skillsConfigurationSectionDetails, loading } = skillsConfiguration;
        this.setState({ activeSelectionItem: skillsConfigurationSectionDetails[selectedItem].name });
        if (skillsConfigurationSectionDetails[selectedItem]) {
            if (!isNewItem) {
                skillsConfigurationSectionDetails[selectedItem].id && !skillsConfigurationSectionDetails[selectedItem].skills && !loading && this.props.fetchSkillsConfigurationBySectionId(skillsConfigurationSectionDetails[selectedItem].id);
            }
        }
        //Set skill title name field while trversing over skill sections
        this.setLatestSkillSectionTitleName(selectedItem, skillsConfiguration, form, isNewItem);
    }

    setLatestSkillSectionTitleName(activeSelectionItem, skillsConfiguration, form, isNewItem) {
        const { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = skillsConfiguration;
        const skillSectionDetail = updatedSkillsConfigurationSectionDetails && updatedSkillsConfigurationSectionDetails[activeSelectionItem] ?
            updatedSkillsConfigurationSectionDetails[activeSelectionItem] :
            skillsConfigurationSectionDetails && skillsConfigurationSectionDetails[activeSelectionItem];

        if (skillSectionDetail && skillSectionDetail.editTitleInfo) {
            const { isEditTitle, name, errorMessage } = skillSectionDetail.editTitleInfo;
            this.setState({ editTitleName: isEditTitle });

            if (isEditTitle && !isEmpty(errorMessage)) {
                form.setFields({ ['skillTitleName']: { touched: true, value: name, errors: [new Error(errorMessage)] } });
            } else {
                form.setFields({ ['skillTitleName']: { touched: true, value: name } });
            }
        } else {
            if (isNewItem) {
                this.setState({ editTitleName: false });
            } else if (skillSectionDetail) {
                form.setFields({ ['skillTitleName']: { touched: true, value: skillSectionDetail.name } });
                this.setState({ editTitleName: false });
            }
        }
        this.updateInitialSkillFilters(skillSectionDetail);

        return skillSectionDetail;
    }

    updateInitialSkillFilters(skillSectionDetail) {
        const { skillTypeFilters } = this.props.skillsConfiguration;
        const currentFilters = skillTypeFilters !== undefined ?
            skillTypeFilters.filter(x => x.id == skillSectionDetail.id) : [];
        if (currentFilters.length === 0) {
            const newFilters = { id: skillSectionDetail.id, value: [ALL_FILTER_ID] };
            this.callbackDispatchHandler(SET_TYPE_FILTERS, newFilters);
        }
    }

    toggleSortOrderStatus() {
        this.props.toggleSortOrderStatus('skills');
    }

    handleTitleEnter(event) {
        if (event.key == 'Enter') {
            this.setStateFields(editTitleName, false);
        }
    }

    setStateFields(fieldName, stateValue) {
        this.setState({
            [fieldName]: stateValue
        });
    }

    changeTitleInput(event) {
        const { form } = this.props;
        const inputValue = event.target.value;
        const errorMessage = this.getSkillTitleInputErrorMessage(inputValue);
        const hasError = errorMessage != '';

        const newFormFieldValue = {
            value: inputValue,
            touched: true,
            ...(hasError && { errors: [new Error(errorMessage)] })
        };

        form.setFields({ [titleFieldName]: newFormFieldValue });
        this.updateErrorStatusForSkilSection(hasError, inputValue, errorMessage);

        this.setFormFieldsTouched();

        if (event.keyCode === KEY_CODES.ENTER && !hasError) {
            this.setStateFields(titleFieldName, false);
        }
    }

    updateErrorStatusForSkilSection(errorStatus, value, errorMessage) {
        this.props.updateSkillTitleName({
            skillSectionName: this.state.activeSelectionItem,
            editTitleInfo: {
                isEditTitle: this.state.editTitleName,
                name: value,
                hasError: errorStatus,
                errorMessage
            }
        });
    }

    getSkillTitleInputErrorMessage(inputValue) {
        let { skillsConfiguration, messages = {} } = this.props;
        let { skillsConfigurationSections } = skillsConfiguration;

        const { skillCategoryRequired, uniqueCategoryNameRequired } = messages;

        const getIsInputDuplicateSkillTitle = () => skillsConfigurationSections.some(name => name.toLowerCase().trim() === inputValue.toLowerCase().trim()
            && this.state.activeSelectionItem !== inputValue);

        // newlyAddedRenameDuplicateValidation check is added, to fix issue "To trigger unique title validation on - Adding newly created item & renaming with same name. Bug 89952"
        const getIsNewlyAddedRenamedTitleDuplicate = () => {
            const { updatedSkillsConfigurationSectionDetails } = skillsConfiguration;
            const activeSelectionItem = this.state.activeSelectionItem;

            const updatedActiveItem = updatedSkillsConfigurationSectionDetails[activeSelectionItem] || {};
            const { editTitleInfo = {} } = updatedActiveItem;
            const { name = '' } = editTitleInfo;

            return name.toLowerCase() === inputValue.toLowerCase() && activeSelectionItem !== updatedActiveItem.name;
        };

        let errorMessage = '';

        if (inputValue.trim() === '' || inputValue === null) {
            errorMessage = skillCategoryRequired;
        } else if (getIsInputDuplicateSkillTitle() || getIsNewlyAddedRenamedTitleDuplicate()) {
            errorMessage = uniqueCategoryNameRequired;
        }

        return errorMessage;
    }

    getCommandBarConfig() {
        const { form, messages = {} } = this.props;
        const { getFieldDecorator } = form;
        const { common: commonMessages = {}, skillCommandBarFieldName } = messages;
        const { editLabel } = commonMessages;
        const showButtonContainer = false;

        const { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = this.props.skillsConfiguration;
        let fieldValueName = !isEmpty(skillsConfigurationSectionDetails) && skillsConfigurationSectionDetails[this.state.activeSelectionItem] && skillsConfigurationSectionDetails[this.state.activeSelectionItem].name;
        let initialName = skillsConfigurationSectionDetails[this.state.activeSelectionItem] && skillsConfigurationSectionDetails[this.state.activeSelectionItem].initialName; // change to initial name
        if (!isEmpty(updatedSkillsConfigurationSectionDetails[this.state.activeSelectionItem])) {
            fieldValueName = updatedSkillsConfigurationSectionDetails[this.state.activeSelectionItem].name;
        }

        const propsPassedName = {
            stateFieldName: editTitleName,
            value: fieldValueName,
            initialValue: initialName,
            editableMode: this.state.editTitleName,
            isDefault: skillsConfigurationSectionDetails[this.state.activeSelectionItem]?.sysMaintained,
            fieldName: titleFieldName,
            cssClassField: 'nameField',
            form: form,
            handleEnter: this.handleTitleEnter,
            changeInput: this.changeTitleInput,
            onBlur: () => {
                const titleError = form.getFieldsError([titleFieldName])[titleFieldName];

                !titleError && this.setStateFields(editTitleName, false);
            },
            setStateFields: this.setStateFields,
            setRequiredRule: false,
            editLabel,
            ariaLabel: skillCommandBarFieldName
        };
        let config = this.state.commandBarConf.map(conf => {
            switch (conf.type) {
                case COMMANDBAR_CONSTANTS.PAGE_TITLE:
                    return { type: conf.type, config: { ...conf.config, title: this.state.activeSelectionItem } };
                case COMMANDBAR_CONSTANTS.CONTEXTUAL_EDIT:
                    return {
                        type: conf.type, config: {
                            getFieldDecorator: getFieldDecorator,
                            showButtonContainer: showButtonContainer,
                            propsPassedName: propsPassedName
                        }
                    };
                case COMMANDBAR_CONSTANTS.ACTION_BUTTON:
                    return {
                        type: conf.type, config: {
                            label: <FormattedMessage id="addSkillButtonFromCommandBar" />,
                            onClickActionType: this.handleAddSkill,
                            icon: 'plus',
                            size: 'large',
                            className: 'addButtonContainer'
                        }
                    };
            }
        });

        return config;
    }

    getAllActiveSkillSectionDetail = (activeSelectionItem, skillsConfiguration) => {
        const { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = skillsConfiguration;

        return updatedSkillsConfigurationSectionDetails && updatedSkillsConfigurationSectionDetails[activeSelectionItem] &&
            updatedSkillsConfigurationSectionDetails[activeSelectionItem].skills ?
            updatedSkillsConfigurationSectionDetails[activeSelectionItem] :
            skillsConfigurationSectionDetails && skillsConfigurationSectionDetails[activeSelectionItem];
    }

    getLatestSkillSectionDetail = (activeSelectionItem, skillsConfiguration) => {
        const skillSectionDetail = this.getAllActiveSkillSectionDetail(activeSelectionItem, skillsConfiguration);
        const filterSkillSectionDetail = this.FilterSkillConfigurationSectionByCategoryAndSubcategory(activeSelectionItem, skillSectionDetail);

        return this.applyTypeFilters(filterSkillSectionDetail, skillsConfiguration);
    }

    applyTypeFilters(skillSectionDetail, skillsConfiguration) {
        if (!skillSectionDetail) return skillSectionDetail;

        const typeFilters = skillsConfiguration.skillTypeFilters.find(x => x.id === skillSectionDetail.id);
        const skills = skillSectionDetail.skills;
        if (typeFilters === undefined || typeFilters.value?.includes(ALL_FILTER_ID) || skills === undefined || skills?.length === 0) {
            return skillSectionDetail;
        }
        const newSkills = skills.filter(skill => typeFilters.value.includes(skill.entityTypeId));

        return { ...skillSectionDetail, skills: newSkills };
    }

    checkIsSkillTypeFieldsLabelsDupilcate(requestedPutData) {
        const { skillFields = [] } = requestedPutData?.[0] ?? {};

        const skillTypeFieldsLabels = skillFields.map(item => {
            const currentField = item && item.customFieldDetails;

            return currentField.label;
        });

        const isSkillTypeFieldsLabelsDupilcate = new Set(skillTypeFieldsLabels).size !== skillTypeFieldsLabels.length;

        return isSkillTypeFieldsLabelsDupilcate;
    }

    handleSubmit() {
        let {
            requestedPutData,
            deleteCount,
            modifyCount,
            deleteLevelCount,
            modifyLevelCount,
            deleteFieldCount,
            modifyFieldCount
        } = this.createRequestMetaData(this.props.skillsConfiguration.updatedSkillsConfigurationSectionDetails);
        this.setState({
            updatedData: requestedPutData,
            deleteCount: deleteCount,
            modifyCount: modifyCount,
            deleteLevelCount,
            modifyLevelCount,
            deleteFieldCount,
            modifyFieldCount
        },
        this.showConfirmationModal);

        const isSkillTypeFieldsLabelsDupilcate = this.checkIsSkillTypeFieldsLabelsDupilcate(requestedPutData);

        const { skillsConfiguration } = this.props;
        const { updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails } = skillsConfiguration;
        const { activeSelectionItem } = this.state;
        if (!(deleteCount || deleteLevelCount || deleteFieldCount)) {
            if (!isEmpty(requestedPutData)) {
                this.callbackDispatchHandler(UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS, requestedPutData);
                const itemDetails = updatedSkillsConfigurationSectionDetails[activeSelectionItem];
                if (itemDetails && (itemDetails.operation === 'DELETE')) {
                    this.setState({ activeSelectionItem: undefined });
                    //Code added to persist the active tab of active item after save
                    this.props.setActiveTabForSkillType({ selectionItem: undefined, activeTab: '0' });
                } else if (itemDetails && itemDetails.editTitleInfo && !isEmpty(itemDetails.editTitleInfo.name)) {
                    this.setState({ nextActiveItem: itemDetails.editTitleInfo.name });
                    //Code added to persist the active tab of active item after save
                    const initialItem = initialskillsConfigurationSectionDetails[activeSelectionItem];
                    this.props.setActiveTabForSkillType({ selectionItem: itemDetails.editTitleInfo.name, activeTab: initialItem.currentTab });
                }
            }
            if (!isSkillTypeFieldsLabelsDupilcate) {
                setTimeout(() => {
                    this.props.form.resetFields();
                    this.resetActionBarStatus();
                }, 0);
            }
            this.setStateFields(editTitleName, false);
        }
        this.actionBarCallback();
    }

    showConfirmationModal() {
        const { deleteCount, modifyCount, deleteLevelCount, modifyLevelCount, deleteFieldCount, modifyFieldCount } = this.state;
        if (deleteCount > 0 || deleteLevelCount > 0 || deleteFieldCount > 0) {
            if (modifyCount > 0 || modifyLevelCount > 0 || modifyFieldCount > 0) {
                this.setState({ displayDeleteAndModifyModal: true });
            } else {
                this.setState({ displayDeleteModal: true });
            }
        }
    }

    /**
     * Creates a SkillCategoryBindDto Object to be passed as payload to server
     * @param {object} updatedData
     * @returns {Observable<import('../../../types/skillCategoryBindDto.jsdoc').SkillCategoryBindDto>}
     * */
    createRequestMetaData(updatedData) {
        const requestedPutData = Object.keys(updatedData).map(section => {
            return {
                guid: (updatedData[section].operation == 'ADD') ? null : updatedData[section].id,
                name: (updatedData[section].editTitleInfo) ? updatedData[section].editTitleInfo.name : updatedData[section].name,
                operation: updatedData[section].operation,
                skills: this.getSkills(updatedData[section].skills, updatedData[section].operation),
                skillCategoryLevelName: updatedData[section].skillCategoryLevelName,
                skillLevels: this.getSkillLevels(updatedData[section].skillLevels, (updatedData[section].operation == 'ADD') ? null : updatedData[section].id),
                skillFields: this.getSkillFields(updatedData[section].skillFields, updatedData[section].operation),
                preExpiryNotification: updatedData[section].preExpiryNotification,
                watcher: updatedData[section].watcher,
                isExpiryDateMandatory: updatedData[section].isExpiryDateMandatory,
                isSkillExpiryEnabled: updatedData[section].isSkillExpiryEnabled
            };
        });

        const { deleteCount, modifyCount, deleteLevelCount, modifyLevelCount, deleteFieldCount, modifyFieldCount } = this.getPopupDetails(updatedData);

        return { requestedPutData, deleteCount, modifyCount, deleteLevelCount, modifyLevelCount, deleteFieldCount, modifyFieldCount };
    }

    getSkillFields(skillFields = [], skillTypeOperation) {
        const { formattingFields = {}, skillsConfiguration = {} } = this.props;
        const { activeSelectionItem } = this.state;
        const { initialskillsConfigurationSectionDetails } = skillsConfiguration;
        const skillTypeFieldsInitial = initialskillsConfigurationSectionDetails[activeSelectionItem] &&
            initialskillsConfigurationSectionDetails[activeSelectionItem].skillFields;
        let skillTypeFieldsFiltered = [];
        skillFields.map((item, index) => {
            const initialCurrentField = skillTypeFieldsInitial[index];
            const initialCurrentFieldGuid = initialCurrentField && initialCurrentField.customFieldDetails && initialCurrentField.customFieldDetails.guid;
            const currentField = item && item.customFieldDetails;
            //for field formatting
            if (currentField.fieldFormatGuid && !isEmpty(formattingFields)) {
                currentField.fieldFormat.guid = currentField.fieldFormatGuid;
                const fieldObj = formattingFields.find((field) => field.fieldformat_guid == currentField.fieldFormatGuid) || {};
                currentField.fieldFormat.name = fieldObj.fieldformat_name;
                delete currentField.fieldFormatGuid;
            }
            //check operation and perform respective manipulation
            if (item.operation === 'ADD') {
                item.guid = null;
                if (skillTypeOperation === 'ADD') item.skillCategoryGuid = null;
                const uniqueLabel = `${currentField.label.trim().replace(/\s+/g, '_')}`;
                currentField.name = uniqueLabel;
                skillTypeFieldsFiltered.push(item);
            } else if (item.operation === null || item.operation === 'UPDATE') {
                if ((initialCurrentFieldGuid !== currentField.guid) || currentField.modified || initialCurrentField.order !== item.order) {
                    item.operation = 'UPDATE';
                    currentField.operation = 'UPDATE';
                    skillTypeFieldsFiltered.push(item);
                }
            } else if (item.operation === 'DELETE') {
                skillTypeFieldsFiltered.push(item);
            }
            item.order = index + 1;
            delete currentField.modified;

            return item;
        });

        return skillTypeFieldsFiltered;
    }

    getUniqueFieldName(name, listItems = [], cnt = 1) {
        let defaultVal = name;
        while ((listItems).some((entry) => { return entry.customFieldDetails.name.toLowerCase() === defaultVal.toLowerCase(); })) {
            cnt++;
            defaultVal = `${name}${cnt}`;
        }

        return `${defaultVal}_${parseInt(Math.random() * 1000, 10)}`;
    }

    getSkillLevels(skillLevels = [], skillCategoryGuid) {
        const validDatasource = this.deleteInvalidItems(skillLevels);
        let { levels } = this.formatDatasource(validDatasource, skillCategoryGuid);

        return levels;
    }

    deleteInvalidItems(dataSource) {
        let newLevels = dataSource.filter((item) => (!item.error || item.error === null));
        let order = 1;
        let isItemDeleted = false;

        // Delete Unvisited new added rows
        newLevels = newLevels.filter((item) => !(item.newItem && !item.newItemVisited));
        newLevels = newLevels
            .filter(l => !(l.operation && l.operation.toLowerCase() === 'delete' && //to filter out records pulled using use levels from
                l.populatedUsingUseLevelsFrom))
            .map(function (itemX) {
                if (itemX.operation && itemX.operation.toLowerCase() === 'delete') {
                    if (!itemX.hideFromGrid) isItemDeleted = true;

                    return { ...itemX, newItem: false, order: -1 };
                } else {
                    if (isItemDeleted && !itemX.operation) {
                        return { ...itemX, newItem: false, order: order++, operation: 'Update' };
                    }

                    return { ...itemX, newItem: false, order: order++ };
                }
            });

        return newLevels;
    }

    formatDatasource(dataSource, skillCategoryGuid) {
        let delCount = 0;
        let levels = dataSource.reduce((values, level) => {
            const operation = level.operation && level.operation.toLowerCase();
            let row = {
                guid: null,
                operation: 'ADD',
                skillLevelNotes: level.skillLevelNotes,
                skillLevelDescription: level.skillLevelDescription,
                order: level.order,
                skillCategoryGuid: skillCategoryGuid
            };
            if (level.populatedUsingUseLevelsFrom) {
                values.push(row);

                return values;
            }
            switch (operation) {
                case 'add':
                    values.push(row);

                    return values;
                case 'delete':
                    delCount++;
                    row = {
                        ...row,
                        operation: 'DELETE',
                        guid: level.guid
                    };
                    values.push(row);

                    return values;
                default:
                    if (level.markForEdit || operation === 'update') {
                        row = {
                            ...row,
                            guid: level.guid,
                            operation: 'UPDATE'
                        };
                        values.push(row);

                        return values;
                    } else {
                        return values;
                    }
            }
        }, []);

        return { levels, delCount };
    }

    getPopupDetails(updatedData) {
        let deleteCount = 0;
        let modifyCount = 0;
        let deleteLevelCount = 0;
        let modifyLevelCount = 0;
        let deleteFieldCount = 0;
        let modifyFieldCount = 0;
        Object.keys(updatedData).map(section => {
            updatedData[section].skills && updatedData[section].skills.map(skill => {
                if (skill.operation === 'DELETE') {
                    deleteCount++;
                } else if (skill.operation === 'ADD' || skill.operation === 'UPDATE') {
                    modifyCount++;
                }
            });
            updatedData[section].skillLevels && updatedData[section].skillLevels.map(level => {
                if (level.operation && level.operation.toLowerCase() === 'delete' && !level.populatedUsingUseLevelsFrom) {
                    deleteLevelCount++;
                } else if (level.markForEdit || (level.operation && (level.operation.toLowerCase() === 'add' || level.operation.toLowerCase() === 'update'))) {
                    modifyLevelCount++;
                }
            });
            updatedData[section].skillFields && updatedData[section].skillFields.map(field => {
                if (field.operation && field.operation.toLowerCase() === 'delete') {
                    deleteFieldCount++;
                } else if (field.operation && (field.operation.toLowerCase() === 'add' || field.operation.toLowerCase() === 'update')) {
                    modifyFieldCount++;
                }
            });

            return section;
        });

        return { deleteCount, modifyCount, deleteLevelCount, modifyLevelCount, deleteFieldCount, modifyFieldCount };
    }

    getSkills(skills = [], skillTypeOperation) {
        return skills.filter(skill => skill.operation).map(skill => {
            return {
                guid: (skill.operation == 'ADD') ? null : skill.key,
                name: skill.name,
                tags: skill.tags,
                info: skill.info,
                entityTypeId: skill.entityTypeId,
                categories: skill.categories,
                subCategories: skill.subCategories,
                departments: skill.departments,
                divisions: skill.divisions,
                sectionId: skillTypeOperation === 'ADD' ? null : skill.sectionId,
                operation: skill.operation
            };
        });
    }

    handleSaveConfirmation(modalName) {
        const { needToNavigate, updatedData, activeSelectionItem } = this.state;
        const { skillsConfiguration } = this.props;
        const { updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails } = skillsConfiguration;
        this.setState({ [modalName]: false });
        if (!isEmpty(updatedData)) {
            this.callbackDispatchHandler(UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS, updatedData);
            const itemDetails = updatedSkillsConfigurationSectionDetails[activeSelectionItem];
            if (itemDetails && itemDetails.editTitleInfo && !isEmpty(itemDetails.editTitleInfo.name)) {
                this.setState({ activeSelectionItem: itemDetails.editTitleInfo.name });
            }

            //Code added to persist the active tab of active item after save
            const initialItem = initialskillsConfigurationSectionDetails[activeSelectionItem];
            if (itemDetails && (itemDetails.operation === 'DELETE')) {
                this.props.setActiveTabForSkillType({ selectionItem: undefined, activeTab: '0' });
            } else if (itemDetails && itemDetails.editTitleInfo && !isEmpty(itemDetails.editTitleInfo.name)) {
                this.props.setActiveTabForSkillType({ selectionItem: itemDetails.editTitleInfo.name, activeTab: initialItem.currentTab });
            }
        }
        this.props.form.resetFields();
        this.setStateFields(editTitleName, false);
        if (needToNavigate) {
            this.setState({ needToNavigate: undefined }, () => {
                this.props.history.push(needToNavigate.pathname, needToNavigate.state);
            });
        }
        this.actionBarCallback();
    }

    handleCancelConfirmation(modalName) {
        if (this.state.needToNavigate) {
            navigationService.cancelledNavigation();
            this.setState({ needToNavigate: undefined });
        }

        messageService.sendMessage({ submittedForm: false });
        messageService.sendMessage({ id: 'skillConfiguration', isGridModified: true });
        this.setState({ [modalName]: false });
    }

    getUserConfirmation(showModal = false, navigateTo = undefined) {
        this.setState({ needToNavigate: navigateTo });
    }

    confirmSave() {
        let { requestedPutData, deleteCount, modifyCount, deleteLevelCount, modifyLevelCount, deleteFieldCount, modifyFieldCount } = this.createRequestMetaData(this.props.skillsConfiguration.updatedSkillsConfigurationSectionDetails);
        this.setState({ updatedData: requestedPutData, deleteCount: deleteCount, modifyCount: modifyCount, deleteLevelCount, modifyLevelCount, deleteFieldCount, modifyFieldCount });

        return false;
    }

    renderModals() {
        const { skillsConfiguration, getSkillCountByCategoryId } = this.props;
        const { initialskillsConfigurationSectionDetails } = skillsConfiguration;
        const { displayDeleteModal, displayDeleteAndModifyModal, displaySkillTypeDeleteModal, deleteCount, deleteLevelCount, deleteFieldCount, itemTobeDeleted } = this.state;
        const totalCount = deleteCount + deleteLevelCount + deleteFieldCount;
        let deleteConfig = '';
        let totalDeleteCount = 0;
        let showWarningSubMsg = false;
        let heading = {};
        let warningMessage = {};
        let primaryButton = {};
        let secondaryButton = {};
        switch (totalCount) {
            case deleteCount: {
                deleteConfig = SKILLS;
                totalDeleteCount = deleteCount;
            }
                break;
            case deleteLevelCount: {
                deleteConfig = SKILL_LEVELS;
                totalDeleteCount = deleteLevelCount;
            }
                break;
            case deleteFieldCount: {
                deleteConfig = SKILL_FIELDS;
                totalDeleteCount = deleteFieldCount;
            }
                break;
            case (deleteLevelCount + deleteCount): {
                deleteConfig = SKILLS_AND_LEVELS;
                showWarningSubMsg = true;
            }
                break;
            case (deleteLevelCount + deleteFieldCount): {
                deleteConfig = LEVELS_AND_FIELDS;
                showWarningSubMsg = true;
            }
                break;
            case (deleteFieldCount + deleteCount): {
                deleteConfig = SKILLS_AND_FIELDS;
                showWarningSubMsg = true;
            }
                break;
            case (deleteLevelCount + deleteCount + deleteFieldCount): {
                deleteConfig = SKILLS_LEVELS_AND_FIELDS;
                showWarningSubMsg = true;
            }
                break;
        }

        if (displayDeleteModal) {
            heading = DELETE_CONST + deleteConfig + POPUP_HEADING;
            warningMessage = DELETE_CONST + deleteConfig + POPUP_WARNING;
            if (showWarningSubMsg) {
                primaryButton = DELETE_ITEMS_LABEL;
                secondaryButton = KEEP_ITEMS_LABEL;
            } else {
                primaryButton = DELETE_CONST + deleteConfig + BUTTON_LABEL;
                secondaryButton = KEEP_CONST + deleteConfig + BUTTON_LABEL;
            }

            return (
                <ConfirmationModal
                    visible
                    key="displayDeleteModal"
                    title={<FormattedMessage id={heading} />}
                    confirmationMessage={<FormattedMessage id="deleteSkillsPopupConfirmation" />}
                    handleSaveChanges={() => this.handleSaveConfirmation('displayDeleteModal')}
                    handleCancel={() => this.handleCancelConfirmation('displayDeleteModal')}
                    primaryButtonLabel={<FormattedMessage id={primaryButton} />}
                    secondaryButtonLabel={<FormattedMessage id={secondaryButton} />}
                >
                    <p><FormattedMessage id={warningMessage} values={{ number: <strong>{totalDeleteCount}</strong> }} /></p>
                    {showWarningSubMsg && <p><FormattedMessage id="deletePopupWarningMessage" /></p>}
                </ConfirmationModal>
            );
        }
        if (displayDeleteAndModifyModal) {
            heading = DELETE_MODIFY_CONST + deleteConfig + POPUP_HEADING;
            warningMessage = DELETE_MODIFY_CONST + deleteConfig + POPUP_WARNING;
            // Normal Modal

            return (
                <ConfirmationModal
                    visible
                    key="displayDeleteAndModifyModal"
                    title={<FormattedMessage id={heading} />}
                    confirmationMessage={<FormattedMessage id="deleteSkillsPopupConfirmation" />}
                    handleSaveChanges={() => this.handleSaveConfirmation('displayDeleteAndModifyModal')}
                    handleCancel={() => this.handleCancelConfirmation('displayDeleteAndModifyModal')}
                    primaryButtonLabel={<FormattedMessage id="actionBarSaveButtonLabel" />}
                    secondaryButtonLabel={<FormattedMessage id="cancelButtonLabel" />}
                >
                    <p><FormattedMessage id={warningMessage} values={{ number: <strong>{totalDeleteCount}</strong> }} /></p>
                    {showWarningSubMsg && <p><FormattedMessage id="deletePopupWarningMessage" /></p>}
                </ConfirmationModal>
            );
        }
        if (displaySkillTypeDeleteModal) {
            const itemDetails = initialskillsConfigurationSectionDetails[itemTobeDeleted];
            /* Note: In the previous version, we fetch the skill count in the initial API call,
            but in the newer version we are only able to get the count once we fetch the details of category
            rather than removing the whole code references, we use skill count from skillStructure as a fallback.
            In future if there would be a case where these things are not in sync, we need to re-visit the below logic */
            const skillCountToDelete = itemDetails ? (itemDetails.skillCount ?? getSkillCountByCategoryId(itemDetails.id)) : 0;

            return (
                <ConfirmationModal
                    visible
                    key="displaySkillTypeDeleteModal"
                    title={<FormattedMessage id="skillCategoryDeletePopupHeading" />}
                    confirmationMessage={<FormattedMessage id="deleteSkillsPopupConfirmation" />}
                    handleSaveChanges={this.handleDeleteSkillTypeConfirmation}
                    handleCancel={this.handleCancelDeleteSkillType}
                    primaryButtonLabel={<FormattedMessage id="deleteSkillCategoryButtonLabel" />}
                    secondaryButtonLabel={<FormattedMessage id="keepSkillCategory" />}
                >
                    <p><FormattedMessage id="skillCategoryDeletePopupWarningMessage" values={{ number: skillCountToDelete }} /></p>
                </ConfirmationModal>
            );
        }
    }

    handleDeleteFromSelectionList(callback, itemTobeDeleted) {
        this.setState({ handleDeleteCallback: callback, displaySkillTypeDeleteModal: true, itemTobeDeleted: itemTobeDeleted });
    }

    handleCancelDeleteSkillType() {
        this.setState({ displaySkillTypeDeleteModal: false });
    }

    handleDeleteSkillTypeConfirmation() {
        const { handleDeleteCallback = () => { } } = this.state;
        handleDeleteCallback();
        this.setState({ displaySkillTypeDeleteModal: false });
    }

    setFormFieldsTouched(val) {
        this.props.form.setFields({ 'form-notifier': { touched: true, value: val || 1 } });
    }

    getCurrentTabQueryParams(activeItem) {
        const { skillsConfiguration = {}, messages = {}, adminSettingRouter, location } = this.props;
        const formattedSearch = getFormattedSearch(location);
        const { initialskillsConfigurationSectionDetails = {} } = skillsConfiguration;
        const { skillTypeSkillsTabTitle } = messages;
        let queryParams = `?${TAB}=${skillTypeSkillsTabTitle}`;
        //When direct location is asked by user and has search tab query params
        if (adminSettingRouter.queryParams == '' && !isEmpty(formattedSearch)) {
            const queryParamKeys = queryString.parse(formattedSearch);
            if (queryParamKeys && queryParamKeys[TAB]) {
                queryParams = `?${TAB}=${queryParamKeys[TAB]}`;
            }
        }

        //When user navigates back to the visited selection item
        //TODO - Redux should provide queryParams based on selection item
        else if (initialskillsConfigurationSectionDetails && initialskillsConfigurationSectionDetails[activeItem]) {
            const tabDetails = this.getTabDetails();
            const currentTabKey = initialskillsConfigurationSectionDetails[activeItem].currentTab;
            const tabDefaultName = tabDetails[currentTabKey] && tabDetails[currentTabKey].tabDefaultName;
            if (tabDefaultName) {
                queryParams = `?${TAB}=${tabDefaultName}`;
            }
        }

        return queryParams;
    }

    getTabDetails() {
        const { messages = {} } = this.props;
        const { skillTypeSkillsTabTitle, skillTypeLevelsTabTitle, fieldsTabTitle } = messages;

        return [{
            tabName: <FormattedMessage id="skillTypeSkillsTabTitle" />,
            tabComponent: this.getSkillsComponent(),
            tabDefaultName: skillTypeSkillsTabTitle
        }, {
            tabName: <FormattedMessage id="skillTypeLevelsTabTitle" />,
            tabComponent: this.getLevelsComponent(),
            tabDefaultName: skillTypeLevelsTabTitle
        }, {
            tabName: <FormattedMessage id="fieldsTabTitle" />,
            tabComponent: this.getFieldsComponent(),
            tabDefaultName: fieldsTabTitle
        }];
    }

    getTabConfig() {
        const { skillsConfiguration } = this.props;
        const { initialskillsConfigurationSectionDetails = {} } = skillsConfiguration;
        const { activeSelectionItem } = this.state;
        if (!activeSelectionItem || !initialskillsConfigurationSectionDetails[activeSelectionItem]) return;
        const currentTab = this.getCurrentTab(initialskillsConfigurationSectionDetails[activeSelectionItem]);
        const tabConfig = {
            tabDetails: this.getTabDetails(),
            defaultActive: currentTab
        };

        return tabConfig;
    }

    getCurrentTab(activeSelectionData = {}) {
        const { currentTab } = activeSelectionData;

        return currentTab;
    }

    tabChange(currentTab) {
        const { activeSelectionItem } = this.state;
        const payload = {
            activeSelectionItem, currentTab
        };
        const { updateActiveComponentTabForSkillTypes, match, skillsConfiguration, updateCurrentPageRouterState } = this.props;
        updateActiveComponentTabForSkillTypes(payload);
        const tabDetails = this.getTabDetails();
        const currentTabName = tabDetails[currentTab] && tabDetails[currentTab].tabDefaultName;
        const queryParams = `?${TAB}=${currentTabName}`;
        pushHistoryWithState(`${match.url}`, queryParams, this.props);
        const { skillsConfigurationSectionDetails = {} } = skillsConfiguration;
        if (skillsConfigurationSectionDetails && skillsConfigurationSectionDetails[activeSelectionItem]) {
            updateCurrentPageRouterState(skillsConfigurationSectionDetails[activeSelectionItem].fakeId, activeSelectionItem, queryParams);
        }
    }

    getSkillsComponent() {
        const { form, match, skillsConfiguration, messages = {}, licensesMap = {}, isRetainImportLibraryEnabled, importInProgress, currentSkillCount } = this.props;
        const { disabledSkillType } = messages;
        const { getFieldDecorator } = form;
        const components = {
            body: {
                row: RowComponent
            }
        };
        const { initialskillsConfigurationSectionDetails, skillsConfigurationSectionDetails,
            skillEntityTypes, skillTypeFilters } = skillsConfiguration;
        const { activeSelectionItem } = this.state;
        const getDetails = this.getLatestSkillSectionDetail(activeSelectionItem, skillsConfiguration);
        const currentFilter = (getDetails && skillTypeFilters.find(x => x.id === getDetails.id)) ?? allTypeFilterValue;
        //subscribed count & threshold value
        const { subscribedCount: licensedCount } = licensesMap[licenseKeySkills];

        //get active skills count, These does not filter based on skill Type
        const allActiveSkills = this.getAllActiveSkillSectionDetail(activeSelectionItem, skillsConfiguration);
        const activeCount = allActiveSkills && allActiveSkills.skills && allActiveSkills.skills.length;

        //disable add skill button
        const disableTypeFilters = activeCount && getDetails.skills.some(x => x.operation === 'ADD');
        const disableAddSkill = licensedCount > 0 ? (currentSkillCount && currentSkillCount >= licensedCount) : false;
        const typeList = (skillEntityTypes && skillEntityTypes.map(x => ({ id: x.id, value: x.description }))) ?? [];
        const typeFilterItemList = [allTypeFilterValue, ...typeList];

        //show/hide retain library button
        const { subscribedCount: hasRetainLibraryLicense } = licensesMap[licenseRetainLibrary];

        return (
            <>
                <div className={disableTypeFilters ? 'disabled-Tag' : ''}>
                    <Tooltip title={disableTypeFilters ? disabledSkillType : ''}>
                        {typeFilterItemList.map((tag) => (
                            <Tag.CheckableTag
                                key={tag.id}
                                checked={currentFilter.value.includes(tag.id)}
                                onChange={e => this.handleTypeChange(e, disableTypeFilters, getDetails.id, tag.id)}
                            >
                                {tag.value}
                            </Tag.CheckableTag>
                        ))}
                    </Tooltip>
                </div>
                {activeSelectionItem && skillsConfigurationSectionDetails[activeSelectionItem] && skillsConfigurationSectionDetails[activeSelectionItem].skills &&
                    <div>
                        <div className="skills-parent-container">
                            <div className="skills-summary-container">
                                <div className="skills--license-container">
                                    <SkillsConfigurationSummary activeSkillConfiguration={this.state.activeSelectionItem} match={match} />
                                </div>

                                {isRetainImportLibraryEnabled && hasRetainLibraryLicense && <div className="add-skill-button-container">
                                    <Tooltip placement="top" title={importInProgress ? <FormattedMessage id="importInProgressText" /> : ''}>
                                        <Button onClick={this.handleImportModalShow} disabled={importInProgress || disableAddSkill} id="addLibrarySkillButton" type="secondary" className="add-skill-button">
                                            <FormattedMessage id="addSkillLibrary" />
                                        </Button>
                                    </Tooltip>
                                </div>}

                                <div className="add-skill-button-container">
                                    <Button onClick={this.handleAddSkill} disabled={disableAddSkill} id="addSkillButton" type="primary" className="add-skill-button">
                                        <FormattedMessage id="addSkill" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                        <List>
                            <SkillsConfigurationContentList {...this.props}
                                form={form}
                                getFieldDecorator={getFieldDecorator}
                                components={components}
                                callbackDispatchHandler={this.callbackDispatchHandler}
                                skillSectionDetail={this.getLatestSkillSectionDetail(activeSelectionItem, skillsConfiguration)}
                                initialSkillSectionDetail={initialskillsConfigurationSectionDetails[activeSelectionItem]}
                                setFormFieldsTouched={this.setFormFieldsTouched}
                                handleAddSkill={this.handleAddSkill}
                                messages={messages}
                                disabledAddSkill={disableAddSkill}
                            />
                        </List>
                    </div>}
            </>
        );
    }

    handleTypeChange(e, disabled, entityId, tagId) {
        if (disabled) {
            e.preventDefault();

            return;
        }
        this.setSkillTypeFilters(entityId, tagId);
    }

    setSkillTypeFilters(id, value) {
        this.callbackDispatchHandler(SET_TYPE_FILTERS, ({ id, value }));
    }


    getLevelsComponent() {
        const { form, skillsConfiguration, updateLevelType, updateUseLevelsFrom,
            updateLevelName, addLevels,
            updateLevels, updateUseLevelsFromUsingAPI, messages = {}, licensesMap = {} } = this.props;
        const { initialskillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = skillsConfiguration;
        const { activeSelectionItem } = this.state;
        const activeItem = updatedSkillsConfigurationSectionDetails[activeSelectionItem] ?
            updatedSkillsConfigurationSectionDetails[activeSelectionItem] :
            initialskillsConfigurationSectionDetails[activeSelectionItem];
        const licenseSkillsLevelsKeyValues = licensesMap[licenseKeySkillLevels];

        return (
            <SkillTypeLevelsContent
                form={form}
                activeItem={activeItem}
                activeSelectionItem={activeSelectionItem}
                updateLevelType={updateLevelType}
                updateUseLevelsFrom={updateUseLevelsFrom}
                updateUseLevelsFromUsingAPI={updateUseLevelsFromUsingAPI}
                updateLevelName={updateLevelName}
                addLevels={addLevels}
                updateLevels={updateLevels}
                updatedSkillsConfigurationSectionDetails={updatedSkillsConfigurationSectionDetails}
                initialskillsConfigurationSectionDetails={initialskillsConfigurationSectionDetails}
                messages={messages} licenseSkillsLevelsKeyValues={licenseSkillsLevelsKeyValues} />
        );
    }

    getFieldsComponent() {
        const { skillsConfiguration, form, messages = {}, licensesMap = {} } = this.props;
        const { initialskillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = skillsConfiguration;
        const { activeSelectionItem } = this.state;
        const activeItem = updatedSkillsConfigurationSectionDetails[activeSelectionItem] ?
            updatedSkillsConfigurationSectionDetails[activeSelectionItem] :
            initialskillsConfigurationSectionDetails[activeSelectionItem];
        const licenseSkillsFieldsKeyValues = licensesMap[licenseKeySkillFields];

        return (
            <SkillFields
                form={form}
                activeSelectionItem={activeSelectionItem}
                activeItem={activeItem}
                updatedSkillsConfigurationSectionDetails={updatedSkillsConfigurationSectionDetails}
                initialskillsConfigurationSectionDetails={initialskillsConfigurationSectionDetails}
                setFormFieldsTouched={this.setFormFieldsTouched}
                messages={messages}
                licenseSkillsFieldsKeyValues={licenseSkillsFieldsKeyValues}
            />
        );
    }

    clearWarningMessage() {
        this.props.clearSkillTypeWarningMessage();
        this.resetContent();
    }

    renderWarningMessage(hasWarning) {
        if (!hasWarning) {
            return null;
        }
        const Title = <FormattedMessage id="fieldAlreadyExistHeader" />;

        return (
            (<Modal
                title={Title}
                maskClosable={false}
                open={hasWarning}
                footer={[
                    <Button className="link-button" type="primary" key="cancel"
                        onClick={() => this.clearWarningMessage()}>
                        <FormattedMessage id="delete_failureButtonLabel" />
                    </Button>
                ]}
                onCancel={() => this.clearWarningMessage()}
            >
                <p><FormattedMessage id="fieldAlreadyExistDescription" /></p>
            </Modal>)
        );
    }

    //#region Import Library Skills
    handleImportModalShow = () => this.setState({ showImportModal: true })
    handleImportModalCancel = () => this.setState({ showImportModal: false });

    renderImportModal(showImportModal) {
        const { messages, clearSelectedSkills, licensesMap } = this.props;

        return (
            (<Modal
                title={<FormattedMessage id="skillLibraryHeader" />}
                maskClosable={false}
                destroyOnClose={true}
                open={showImportModal}
                centered={true}
                footer={null}
                onCancel={() => this.handleImportModalCancel()}
                width={'90%'}
                className="import-library-modal"
                afterClose={clearSelectedSkills}
            >
                <ConnectedImportLibrarySkills licenseProps={licensesMap[licenseKeySkills]} closeModal={this.handleImportModalCancel} messages={messages} />
            </Modal>)
        );
    }
    //#endregion

    render() {
        const { props } = this;
        const { form, skillsConfiguration, loadingSections, messages } = props;
        const { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = skillsConfiguration;
        const { activeSelectionItem, showImportModal } = this.state;
        let commandBarConfig = this.getCommandBarConfig();
        const tabConfig = this.getTabConfig();
        const mainLayoutElement = document.getElementsByClassName('ant-layout-content')[0] || {};
        const contentAreaWidth = `${mainLayoutElement.offsetWidth - siderWidthValue}px`;
        const categoryDetails = updatedSkillsConfigurationSectionDetails[activeSelectionItem] ?? skillsConfigurationSectionDetails[activeSelectionItem];

        const categorySettingCardProps = {
            form,
            categoryDetails,
            initialCategoryDetails: skillsConfigurationSectionDetails[activeSelectionItem],
            setFormFieldsTouched: this.setFormFieldsTouched,
            callbackDispatchHandler: this.callbackDispatchHandler,
            messages
        };

        return (
            <>
                {!loadingSections && activeSelectionItem && skillsConfigurationSectionDetails[activeSelectionItem] && //skillsConfigurationSectionDetails[activeSelectionItem].skills &&
                    <div className="skills-content-area" style={{ width: contentAreaWidth }}>
                        <Form >
                            {skillsConfigurationSectionDetails[activeSelectionItem].skills && <AdminCommandBar commandBarConfig={commandBarConfig} />}
                            <div className="skills-content-area">
                                {!loadingSections && categoryDetails && <CategorySettingsCard {...categorySettingCardProps} />}
                                <ComponentTab tabConfig={tabConfig} ref={this.tabRef} nofityParent={this.tabChange} {...this.props} />
                            </div>
                        </Form>
                    </div>
                }
                {(this.state.displayDeleteAndModifyModal || this.state.displayDeleteModal || this.state.displaySkillTypeDeleteModal) && this.renderModals()}
                <FormNotifier form={form} />
                {this.renderWarningMessage(this.props.hasWarning)}
                {this.renderImportModal(showImportModal)}
            </>
        );
    }
}

SkillsConfigurationContentForm.propTypes = {
    form: PropTypes.object,
    skillsConfiguration: PropTypes.object,
    match: PropTypes.object,
    sortOrderStatus: PropTypes.bool
};

SkillsConfigurationContentForm.defaultProps = {
    form: {},
    skillsConfiguration: {},
    match: {},
    sortOrderStatus: false,
    isUpdateAPICallFinishState: false
};

export default Form.create({
})(SkillsConfigurationContentForm);
