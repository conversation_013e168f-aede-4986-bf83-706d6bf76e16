import React from 'react';
import { connect } from 'react-redux';
import { getStaticMessagesSelector } from '../selectors';
import { getUserLicensingInfoServiceAccount } from '../reducers/actions';
import { SERVICEACCOUNT_SUBSCRIBEDCOUNT } from '../constants';
import { loadResourceServiceAccounts, loadServiceAccountSecurityProfile } from '../epics/actions';

const AdminPageBaseLayout = React.lazy(() => import('../../../../layouts/admin-page-base-layout'));

const mapStateToProps = (state) => {
    const staticMessages = getStaticMessagesSelector(state);

    return {
        staticMessages
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onInitialLoad: () => {
            dispatch(loadResourceServiceAccounts());
            dispatch(loadServiceAccountSecurityProfile());
            dispatch(getUserLicensingInfoServiceAccount(SERVICEACCOUNT_SUBSCRIBEDCOUNT));
        }
    };
};

const WrappedAdminPageBaseLayout = (props) => (
    <React.Suspense fallback={<div />}>
        <AdminPageBaseLayout {...props} />
    </React.Suspense>
);

export const ConnectedServiceAccountManagementLayout = connect(mapStateToProps, mapDispatchToProps)(WrappedAdminPageBaseLayout);