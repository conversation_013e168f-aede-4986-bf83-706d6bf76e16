import React, { useState, useCallback, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { throttle } from 'lodash';

import BookingByWeekCellTemplate from '../tableViewCells/bookingByWeekCellTemplate';
import EmptyCellTemplate from '../tableViewCells/emptyCellTemplate';
import StatusCellTemplate from '../tableViewCells/statusCellTemplate';
import { TABLE_VIEW_CELL_TYPES } from '../../../constants/tableViewPageConsts';
import { tableViewInvalidHoursPromptTrigger$ } from './tableViewInvalidHoursPromptTrigger';

const ReactGrid = React.lazy(() => import('@silevis/reactgrid')
    .then((module) => ({ default: module.ReactGrid }))
    .then(import('@silevis/reactgrid/styles.css')));

export const onCellsChanged = (changes, editCellHandler, setFocusLocation) => {
    changes.forEach((change) => {
        if (change.type === TABLE_VIEW_CELL_TYPES.BOOKING_BY_WEEK) {
            const { previousCell, newCell, rowId, columnId } = change;
            const oldValue = previousCell.text;
            const value = newCell.text;
            const metaData = newCell.metaData;

            if (oldValue || value && oldValue !== value) {
                setFocusLocation((prev) => ({ ...prev, editable: { rowId, columnId } }));
                editCellHandler(value, metaData);
            }
        }
    });
};

function TableGrid({
    rows,
    columns,
    disableVirtualScrolling,
    onCellContextMenu,
    onEditCell
}) {
    const [focusLocation, setFocusLocation] = useState({ current: {}, editable: {} });
    const throttledEditCell = useCallback(
        throttle(onEditCell, 200, { trailing: false }),
        []
    );
    const tableViewGridRef = useRef();

    useEffect(() => {
        const promptSubscription = tableViewInvalidHoursPromptTrigger$.subscribe((value) => {
            if (value) {
                setFocusLocation((prev) => ({
                    current: prev.editable,
                    editable: {}
                }));
            }

            (((tableViewGridRef.current || {}).state || {}).hiddenFocusElement || {}).focus();
        });

        return () => promptSubscription.unsubscribe();
    }, []);

    const updateFocusLocation = useCallback((cellLocation = {}) => {
        const { rowId = '', columnId = '' } = cellLocation;
        const { rowId: currentRowId, columnId: currentColumnId } = focusLocation.current;

        const isSameCell = rowId === currentRowId && columnId === currentColumnId;

        if (!isSameCell) {
            setFocusLocation((prev) => ({ ...prev, current: { rowId, columnId } }));
        }

        return true;
    }, [(focusLocation.current || {}).rowId, (focusLocation.current || {}).columnId]);

    return (
        <React.Suspense fallback={<div />}>
            <ReactGrid
                ref={tableViewGridRef}
                rows={rows}
                columns={columns}
                focusLocation={focusLocation.current}
                customCellTemplates={{
                    [TABLE_VIEW_CELL_TYPES.BOOKING_BY_WEEK]: new BookingByWeekCellTemplate({ onCellContextMenu: onCellContextMenu }),
                    [TABLE_VIEW_CELL_TYPES.STATUS]: new StatusCellTemplate(),
                    [TABLE_VIEW_CELL_TYPES.EMPTY]: new EmptyCellTemplate()
                }}
                disableVirtualScrolling={disableVirtualScrolling === true}
                onCellsChanged={(changes) => onCellsChanged(changes, throttledEditCell, setFocusLocation)}
                onFocusLocationChanging={updateFocusLocation}
            />
        </React.Suspense>
    );
}

TableGrid.propTypes = {
    rows: PropTypes.array,
    columns: PropTypes.array,
    disableVirtualScrolling: PropTypes.bool,
    onCellContextMenu: PropTypes.func,
    onEditCell: PropTypes.func
};

export default TableGrid;