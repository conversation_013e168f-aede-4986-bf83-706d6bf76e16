import React from 'react';
import PropTypes from 'prop-types'; 
import { Spin } from 'antd';
import { useState } from 'react';
import styles from './tableView.less';

const SplitPaneVerticalResizer = React.lazy(() =>
    import('../containers/splitPaneVerticalResizer').then((module) => ({ default: module.SplitPaneVerticalResizer })));
function TableViewLayoutContent({
    loading,
    componentChildren,
    filterPaneCollapsed,
    navigationCollapsed
}) {
    const resizeLimit = 0.5;
    const defaultWidthRatio = 0.25;

    const [primaryWidthRatio, setPrimaryWidthRatio] = useState(defaultWidthRatio);

    const shouldContainerResize = (oldProps, newProps) => (
        oldProps.filterPaneCollapsed !== newProps.filterPaneCollapsed
        || oldProps.navigationCollapsed !== newProps.navigationCollapsed
    );

    return (
        <div id={'tableViewComponent'} className={styles.container}>
            {
                loading ?
                    (
                        <Spin className="spin-light" size="large" />
                    ) : (
                        <React.Suspense fallback={<div />}>
                            <SplitPaneVerticalResizer
                                components={componentChildren}
                                defaultWidthRatio={defaultWidthRatio}
                                primaryWidthRatio={primaryWidthRatio}
                                onResizeEnd={setPrimaryWidthRatio}
                                filterPaneCollapsed={filterPaneCollapsed}
                                navigationCollapsed={navigationCollapsed}
                                shouldResize={shouldContainerResize}
                                maxSizeRatio={resizeLimit}
                            />
                        </React.Suspense>
                    )
            }
        </div>
    );
}

TableViewLayoutContent.propTypes = {
    loading: PropTypes.bool,
    filterPaneCollapsed: PropTypes.bool,
    navigationCollapsed: PropTypes.bool,
    componentChildren: PropTypes.array.isRequired,
    detailsPane: PropTypes.array.isRequired
};

export default TableViewLayoutContent;