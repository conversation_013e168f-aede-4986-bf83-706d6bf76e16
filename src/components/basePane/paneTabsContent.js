import React from 'react';
import PropTypes from 'prop-types';
import { Layout, Radio, Tooltip, Modal, Badge } from 'antd';
import ReactTooltip from 'react-tooltip';
import { Icon } from '../../lib/';
import styles from './PaneTabsContent.less';
import TooltipContextualMenu from '../../lib/tooltipContextualMenu/tooltipContextualMenu';
import { ROLE_AND_REQUEST_FEATURE_SWITCH } from '../../constants/globalConsts'
import { capitalizeFirstLetter } from '../../utils/commonUtils';
import { getLocalStorageItem } from '../../localStorage';
import { USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY } from '../../constants/localStorageConsts';

const hideInitialTootipDelay = 15000;

class PaneTabsContent extends React.Component {
    constructor(props) {
        super(props);
        this.hoveredTabKey = null;
        this.tabsRef = {};
        this.labelsRef = {};
        this.tabMouseLeaveHandler = this.tabMouseLeaveHandler.bind(this);
        this.tabMouseOverHandler = this.tabMouseOverHandler.bind(this);
        this.expandPaneHandler = this.expandPaneHandler.bind(this);
        this.collapsePaneHandler = this.collapsePaneHandler.bind(this);
        this.tabChangeHandler = this.tabChangeHandler.bind(this);
        this.handleStorage = this.handleStorage.bind(this);

        this.state = {
            tooltipVisible: true,
            showTooltip: !getLocalStorageItem(USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY)
        };

        this.onTabClick = this.onTabClick.bind(this);
    }

    tabChangeHandler(event){
        const tab = event.target.value;
        this.props.onTabChanged(this.props.detailsPaneGuid, tab);
    }

    expandPaneHandler(tabKey) {
        if (this.tabEnabled(tabKey)) {
            this.props.onPaneCollapse(false);
        }
    }

    collapsePaneHandler() {
        this.props.onPaneCollapse(true);
    }

    tabEnabled(tabKey){
        return tabKey && !this.props.tabs[tabKey].disabled;
    }

    shouldSetTabHovered(tabKey){
        return this.tabEnabled(tabKey) && this.hoveredTabKey;
    }

    tabMouseOverHandler(tabKey){
        this.hoveredTabKey = tabKey;
        this.setTooltipVisibility(false);

        if (this.shouldSetTabHovered(tabKey)){
            this.setTabHovered(tabKey)
        }
    }

    setTabHovered(tabKey){
        this.props.onTabHovered(this.props.detailsPaneGuid, tabKey);
    }

    tabMouseLeaveHandler(){
        this.hoveredTabKey = null;
        this.props.onTabUnhovered(this.props.detailsPaneGuid);
    }

    onTabClick(tabKey) {
        if (this.props.collapsed) {
            this.expandPaneHandler(tabKey);
        }else if (this.props.selectedTabKey === tabKey){
            this.collapsePaneHandler();
        }
    }

    onTabFocus(tabKey) {
        const ref = this.labelsRef[tabKey];

        if (ref && ref.current) {
            ReactTooltip.show(ref.current);
        }
    }

    onTabBlur(tabKey) {
        const ref = this.labelsRef[tabKey];

        if (ref && ref.current) {
            ReactTooltip.hide(ref.current);
        }
    }

    renderTabContent(tabKey) {
        const { isBatchedTab, suggestedResourceTabProps, detailsPaneGuid, isSingleEntitySelected, emptyStateConfig, emptyStateConfigKey } = this.props;
        const tabProps = { isBatchedTab, suggestedResourceTabProps, detailsPaneGuid, isSingleEntitySelected, emptyStateConfig, emptyStateConfigKey };

        return this.props.createDetailsContent(tabKey, tabProps);
    }

    renderModal() {
        let modal = null;

        if(this.props.showInModal){
            const tabKey = this.props.selectedTabKey;
            const tab = this.props.tabs[tabKey];
            modal = (
                <Modal
                    {...tab.modalProps}
                    open={this.props.showInModal}
                    onOk={this.props.onModalClose}
                    onCancel={this.props.onModalClose}
                >
                    {this.renderTabContent(this.props.selectedTabKey)}
                </Modal>
            );
        }

        return modal;
    }

    renderTabs() {
        const tabKeys = Object.keys(this.props.tabs);
        let tab = null;

        return tabKeys.map((key, index) => {
            tab = this.props.tabs[key];
            this.tabsRef[key] = React.createRef();
            this.labelsRef[key] = React.createRef();
            const tabName = capitalizeFirstLetter(tab.name);

            return (
                <div key={key + this.props.collapsed} id={key}
                    onClick={() => this.onTabClick(key)}
                    onMouseEnter={() => { this.tabMouseOverHandler(key) }}
                    onMouseLeave={this.tabMouseLeaveHandler}
                >
                    <div
                        tabIndex="0"
                        className={'dp-tab'}
                        data-tip
                        data-for={'DetailsPaneTabLabel' + index}
                        onFocus={() => this.onTabFocus(key)}
                        onBlur={() => this.onTabBlur(key)}
                        ref={this.labelsRef[key]}>

                        <Radio.Button className='dp-button' aria-label={tabName} ref={this.tabsRef[key]} value={key} disabled={tab.disabled}>
                            {!ROLE_AND_REQUEST_FEATURE_SWITCH ?
                                <Badge dot={tab.displayTabDot}>
                                    <Icon className='dp-icon' type={tab.icon} style={{ fontSize: '24px' }} />
                                </Badge> :
                                <Icon className='dp-icon' type={tab.icon} style={{ fontSize: '24px' }} />}
                        </Radio.Button>
                        <span className={'hover-tab'}></span>
                        <ReactTooltip
                            id={'DetailsPaneTabLabel' + index}
                            effect={'solid'}
                            place={'left'}>
                            <span>{tabName}</span>
                        </ReactTooltip>
                    </div>
                </div>
            );
        });
    }

    renderTabsMenu(){
        const { id, collapsed } = this.props;
        return  (
            <div
                className={'pane-tabs'}
                id={id}
                style={{
                    // display: 'flex',
                    // justifyContent: 'flex-end',
                    // marginTop: '180px'
                }}
            >
                <Radio.Group value={collapsed ? null  : this.props.selectedTabKey } buttonStyle='solid' onChange={this.tabChangeHandler}>
                    {this.renderTabs()}
                </Radio.Group>
            </div >
        );
    }

    getTooltipVisible() {
        return !!this.props.detailsPaneTooltip && this.props.visible && this.props.collapsed && this.state.tooltipVisible;
    }

    setTooltipVisibility(visible) {
        this.setState({
            tooltipVisible: visible
        });
    }

    handleStorage() {
        const shouldShowTooltip = !getLocalStorageItem(USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY);

        this.setState({ showTooltip: shouldShowTooltip });
    }

    componentDidMount() {
        if(this.getTooltipVisible()){
            this.hideTooltipTimeout = setTimeout(()=>{this.setTooltipVisibility(false)}, hideInitialTootipDelay);
        }

        window.addEventListener('storage', this.handleStorage);
    }

    componentWillUnmount() {
        clearTimeout(this.hideTooltipTimeout);
        this.setTooltipVisibility(true);

        window.removeEventListener('storage', this.handleStorage);
    }

    componentDidUpdate(prevProps) {
        const hiding = !this.props.visible && prevProps.visible;
        const showing = this.props.visible && !prevProps.visible && this.getTooltipVisible();

        if(hiding){
            clearTimeout(this.hideTooltipTimeout);
            this.setTooltipVisibility(true);
        }
        else if(showing) {
            this.hideTooltipTimeout = setTimeout(()=>{this.setTooltipVisibility(false)}, hideInitialTootipDelay);
        }
    }

    render() {
        const { detailsPaneTooltip, selectedTabKey } = this.props;
        const showTooltipText = this.state.showTooltip && !!detailsPaneTooltip;

        const content = (
            <Layout style={{ background: 'inherit', height: '100%' }}>
                <Layout.Sider width={52} style={{ background: 'inherit' }}>
                    {this.renderTabsMenu()}
                </Layout.Sider>
                <Layout.Content>
                    {this.renderModal()}
                    {this.renderTabContent(selectedTabKey)}
                </Layout.Content>
            </Layout>
        );

        if (showTooltipText) {
            return (
                <Tooltip
                    open={this.state.showTooltip}
                    placement={'left'}
                    overlayClassName={'details-pane-summary'}
                    title={<TooltipContextualMenu details={detailsPaneTooltip} showArrow={false} />}
                >
                    {content}
                </Tooltip>
            );
        }
    
        return content;
    }
}

PaneTabsContent.propTypes = {
    id: PropTypes.string,
    selectedTabKey: PropTypes.string.isRequired,
    tabs: PropTypes.object.isRequired,
    collapsed: PropTypes.bool.isRequired,
    onPaneCollapse: PropTypes.func.isRequired,
    onTabChanged: PropTypes.func.isRequired,
    detailsPaneGuid: PropTypes.string.isRequired
};

export {
    PaneTabsContent
}