import React from 'react';
import { TABLE_NAMES } from '../../constants';
import { DISPLAY_DATE_TIME_FORMATS } from '../../constants/globalConsts';
import { ContextualEditActions, ContextualEditControlWrapper } from '../../lib/contextualEditControl';
import GenericFormImplementation from '../../lib/genericFormImplementation';
import ReadonlyControl from '../../lib/readonlyControl';
import { getDisplayLongDateFormat, isBefore, parseLocalDate } from '../../utils/dateUtils';
import { formatFieldValue, isBlankFieldValue } from '../../utils/fieldControlUtils';
import styles from '../../lib/fieldControl/styles/fieldControlLayout.less';
import PropTypes from 'prop-types';
import { EDUCATION_FIELDS } from '../../constants/fieldConsts';
import { <PERSON><PERSON>, Divider } from 'antd';
import { Icon } from '../../lib';
import { EDUCATION_SECTION_ALIAS } from '../../constants/educationSectionConsts';
import { getIsFieldSetToHidden } from '../../utils/fieldUtils';

const getLookupFieldDescription = (fieldName, getLinkedData, uiData, getFieldInfo) => {
    let value = uiData[fieldName] && typeof(uiData[fieldName]) === 'object' ? uiData[fieldName].value : uiData[fieldName];

    if (value && typeof(value) === 'object') {
        return value.value;
    }

    const data = getLinkedData(
        getFieldInfo,
        fieldName,
        value,
        TABLE_NAMES.EDUCATION
    );

    return data.description;
};

const getCustomRules = (fieldName, messages = [], uiData) => {
    const rules = [];
    switch (fieldName) {
        case EDUCATION_FIELDS.EDUCATION_END: {
            rules.push({
                validator: (rule, value, callback) => {
                    const startDate = (uiData[EDUCATION_FIELDS.EDUCATION_START] || {}).value;
                    const endDate = value;
                    const error = [];
                    if (isBefore(endDate, parseLocalDate(startDate))) {
                        error.push(messages['endDateFieldError'] || '');
                    }

                    callback(error);
                }
            });
        }
    }

    return rules;
};

const EducationReadOnlyContent = (props) => {
    const { uiData, getLinkedData, getFieldInfo, getBlankValue } = props;

    const getFormatedDate = (fieldName, fieldInfo) => {

        return formatFieldValue(uiData[fieldName], fieldInfo, { dateFormat: getDisplayLongDateFormat(DISPLAY_DATE_TIME_FORMATS.NO_DAY) });
    };

    const getFieldValue = (fieldName) => {
        const fieldInfo = getFieldInfo(TABLE_NAMES.EDUCATION, fieldName);
        const { fieldtype } = fieldInfo;
        let value = '';
        let className = 'readonlyControl';

        switch (fieldtype) {
            case 'Date': {
                value = getFormatedDate(fieldName, fieldInfo);
                break;
            }
            case 'Lookup': {
                value = getLookupFieldDescription(fieldName, getLinkedData, uiData, getFieldInfo);
                break;
            }
            case 'Plain text': {
                value = uiData[fieldName];
                break;
            }
            default: {
                value = null;
            }
        }

        if (isBlankFieldValue(value)) {
            value = getBlankValue(fieldInfo);
            className = 'readonlyControl has-no-value';
        }

        if (getIsFieldSetToHidden(fieldName, uiData)) {
            value = <Icon type={'lock'} />;
        }

        return <ReadonlyControl displayValue={value} displayValueClassName={className}/>;
    };

    return (
        <div className="container">
            <div className="role-row">
                <div className="role-col">
                    <span className="strong">{getFieldValue(EDUCATION_FIELDS.EDUCATION_INSTITUTION)}</span>
                </div>
            </div>
            <div className="role-row">
                <div className="role-col">
                    {getFieldValue(EDUCATION_FIELDS.EDUCATION_FIELD)}
                </div>
                <span className="icon-bullet">&bull;</span>
                <div className="role-col">
                    {getFieldValue(EDUCATION_FIELDS.EDUCATION_DEGREE)}
                </div>
            </div>
            <div className="role-row">
                <div className="role-col">
                    {getFieldValue(EDUCATION_FIELDS.EDUCATION_START)}
                </div>
                <span className="icon-bullet">-</span>
                <div className="role-col">
                    {getFieldValue(EDUCATION_FIELDS.EDUCATION_END)}
                </div>
            </div>
            <div className="role-row">
                <div className="role-col">{getFieldValue(EDUCATION_FIELDS.EDUCATION_DETAILS)}</div>
            </div>
        </div>
    );
};

const InlineEditForm = (props) => {
    const { educationData, contextId, onContextualEditCancel, getFormHasChanges, getFormHasErrors } = props;

    const formItemLayout = {
        labelCol: {
            xs: { span: 6 },
            sm: { span: 8 }
        },
        wrapperCol: {
            xs: { span: 18 },
            sm: { span: 16 }
        },
        labelAlign: 'left',
        colon: true,
        layout: 'horizontal'
    };

    return (<div className="formInlineEdit">
        <GenericFormImplementation
            {...props}
            isBatch={false}
            contextId={props.contextId}
            uiData={educationData}
            sortedRecordsIds={[contextId]}
            formId={`genericForm_${contextId}`}
            getLinkedFieldValue={getLookupFieldDescription}
            formItemLayout={formItemLayout}
            getCustomRules={getCustomRules}
        />
        <ContextualEditActions
            styles={styles}
            extraSubmitParams={{
                form: `genericForm_${contextId}`,
                htmlType: 'submit'
            }}
            onApply={() => {
            }}
            onCancel={() => onContextualEditCancel(contextId)}
            submitDisabled={!getFormHasChanges([contextId]) || getFormHasErrors([contextId])}
        />
    </div>);
};

const EditEducationFormWrapper = (props) => {

    const { uiData, inlineEditGuids, onContextualEditStart, getLinkedData, getFieldInfo, getBlankValue, editable, data = [], talentProfilePageTransformedEnabled } = props;

    return Object.keys(uiData).filter(education => !education.startsWith('new_')).map((education, index) => {
        const educationData = data.find(record => record[EDUCATION_FIELDS.EDUCATION_GUID] === education) || {};
        const isLastItem = index === data.length - 1;
        if (inlineEditGuids.indexOf(education) !== -1) {
            return <InlineEditForm {...props} educationData={{ [education]: uiData[education] }} contextId={education}/>;
        } else {

            const educationReadOnlyContent = <EducationReadOnlyContent
                uiData={educationData}
                getLinkedData={getLinkedData}
                getFieldInfo={getFieldInfo}
                getBlankValue={getBlankValue} />;

            return (
                editable ?
                    <div>
                        <ContextualEditControlWrapper onContextualEditStart={() => onContextualEditStart(education)}>
                            {educationReadOnlyContent}
                        </ContextualEditControlWrapper>
                        {/* Added divider between details if not the last item and feature flag is enabled */}
                        {!isLastItem && talentProfilePageTransformedEnabled && <Divider className="divider" />}
                    </div>
                    :
                    <div>
                        {/* Added divider between details if not the last item and feature flag is enabled */}
                        {educationReadOnlyContent}{!isLastItem && talentProfilePageTransformedEnabled && <Divider className="divider" />}
                    </div>
            );
        }
    });
};

const BulkEditEducationFormWrapper = (props) => {
    const { uiData, deletedRecords = [], alias, staticLabels = {} } = props;
    const {addPrefix, deleteLabelText} = staticLabels;
    const formItemLayout = {
        labelCol: {
            xs: { span: 6 },
            sm: { span: 6 }
        },
        wrapperCol: {
            xs: { span: 18 },
            sm: { span: 18 }
        },
        labelAlign: 'left',
        colon: false,
        layout: 'horizontal'
    };

    const getAddDeleteComponent = ({ fieldName, recordId, showAdd }) => {
        const { uiData, onAddNewRecord, onDeleteRecord, mandatoryFields } = props;

        let shouldShowAddButton = showAdd;

        for (let i = 0; i < mandatoryFields.length; i++) {
            const field = mandatoryFields[i];
            const { errors, value } = uiData[recordId][field];

            if (!value || Array.isArray(errors)) {
                shouldShowAddButton = false;
                break;
            }
        }

        return (
            <div className="addDeleteController">
                <Button role='button' aria-label={deleteLabelText} tabIndex={1} className={'history-field-delete'} size={'default'} onClick={() => { onDeleteRecord(recordId); }}>
                    <Icon type="delete" />
                </Button>
                {
                    shouldShowAddButton && (
                        <Button role="button" aria-label={addPrefix} tabIndex={2} className={'history-field-add'} size={'default'} onClick={() => { onAddNewRecord(); }}>
                            <Icon type="plus" />
                        </Button>)
                }
            </div>
        );
    };

    const newKeys = Object.keys(uiData).filter(recordId => recordId.startsWith('new') && deletedRecords.indexOf(recordId) === -1);
    const oldKeys = Object.keys(uiData).filter(recordId => !recordId.startsWith('new') && deletedRecords.indexOf(recordId) === -1);
    const sortedRecordsIds = [...newKeys.sort().reverse(), ...oldKeys];

    return (
        <GenericFormImplementation
            {...props}
            uiData={uiData}
            sortedRecordsIds={sortedRecordsIds}
            formId={`genericForm_bulkEdit_${EDUCATION_SECTION_ALIAS}_${alias}`}
            isBatch={true}
            getLinkedFieldValue={getLookupFieldDescription}
            formItemLayout={formItemLayout}
            getAddDeleteComponent={getAddDeleteComponent}
            getCustomRules={getCustomRules}
        />
    );
};

const EducationSection = (props) => {

    const { isBulkEdit = false } = props;

    return (
        <div id={'education'} className="education">
            {
                isBulkEdit ? <BulkEditEducationFormWrapper {...props} /> : <EditEducationFormWrapper {...props} />
            }
        </div>
    );
};

EducationSection.propTypes = ({
    isBulkEdit: PropTypes.bool
});

BulkEditEducationFormWrapper.propTypes = ({
    uiData: PropTypes.object.isRequired,
    onAddNewRecord: PropTypes.func.isRequired,
    onDeleteRecord: PropTypes.func.isRequired,
    mandatoryFields: PropTypes.func.isRequired
});

EditEducationFormWrapper.propTypes = ({
    uiData: PropTypes.object.isRequired,
    inlineEditGuids: PropTypes.array.isRequired,
    onContextualEditStart: PropTypes.func.isRequired,
    getLinkedData: PropTypes.func.isRequired,
    getFieldInfo: PropTypes.func.isRequired,
    getBlankValue: PropTypes.func.isRequired,
    editable: PropTypes.bool.isRequired
});

InlineEditForm.propTypes = ({
    educationData: PropTypes.object.isRequired,
    contextId: PropTypes.string.isRequired,
    onContextualEditCancel: PropTypes.func.isRequired,
    getFormHasChanges: PropTypes.func.isRequired,
    getFormHasErrors: PropTypes.func.isRequired
});

EducationReadOnlyContent.propTypes = ({
    uiData: PropTypes.object.isRequired,
    getLinkedData: PropTypes.func.isRequired,
    getFieldInfo: PropTypes.func.isRequired,
    getBlankValue: PropTypes.func.isRequired
});

export {
    EducationSection,
    BulkEditEducationFormWrapper,
    EditEducationFormWrapper,
    InlineEditForm,
    EducationReadOnlyContent
};