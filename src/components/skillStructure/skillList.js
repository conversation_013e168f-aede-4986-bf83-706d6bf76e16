import React from 'react';
import PropTypes from 'prop-types';
import { Input, Button } from 'antd';
import { Icon } from '../../lib';
import { DataGrid } from '../../lib/dataGrid/index';
import IconDelete from './iconDelete';
import { DATA_GRID_IDS } from '../../constants/dataGridConsts';

class SkillList extends React.Component {
    constructor(props) {
        super(props);

        this.expandedRowRender = this.expandedRowRender.bind(this);
        this.onHeaderCell = this.onHeaderCell.bind(this);
        this.handleExpand = this.handleExpand.bind(this);
        this.handleChange = this.handleChange.bind(this);
        this.handleButtonDeleteClick = this.handleButtonDeleteClick.bind(this);
        this.handleRow = this.handleRow.bind(this);

        this.state = {
            searchText: '',
            searchedInSection: null,
            sortedInfo: null,
            pageNumber: 1,
            pageSize: 10
        };
    }

    componentDidUpdate(prevProps) {
        const { sortOption, filterOption } = this.props;
        let state = {};

        if (this.shouldUpdateSortedInfoInState()) {
            state = {
                sortedInfo: sortOption
            };
        }

        if (prevProps.sectionId !== this.props.sectionId) {
            state = {
                ...state,
                pageNumber: 1,
                pageSize: 10
            };
        }
    }

    shouldUpdateSortedInfoInState() {
        const { sortOption } = this.props;
        const stateSortOption = this.state.sortedInfo;

        return (
            (sortOption == null && stateSortOption != null) ||
            (sortOption != null && stateSortOption == null) ||
            (sortOption != null && stateSortOption.sectionId != null && sortOption.sectionId != stateSortOption.sectionId)
        );
    }

    handleSearch(selectedKeys, confirm) {
        confirm();
        this.setState({ searchText: selectedKeys[0] });
    }

    handleReset(clearFilters) {
        clearFilters();
        this.setState({ searchText: '', searchedInSection: null });
    }

    getFilteredValue(dataIndex) {
        const { filterOption } = this.props;
        var dataIndexFilterValue = '';

        if (filterOption != null) {
            if (filterOption.filters && filterOption.filters[dataIndex]) {
                dataIndexFilterValue = filterOption.filters[dataIndex];
            }
        }

        return dataIndexFilterValue;
    }

    buildColumnFilterProps(dataIndex) {
        return {
            filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
                <div style={{ padding: 8 }}>
                    <Input
                        ref={node => {
                            this.searchInput = node;
                        }}
                        placeholder={`Search ${dataIndex}`}
                        value={selectedKeys[0]}
                        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                        onPressEnter={() => this.handleSearch(selectedKeys, confirm)}
                        style={{ width: 188, marginBottom: 8, display: 'block' }}
                    />
                    <Button
                        type="primary"
                        onClick={() => this.handleSearch(selectedKeys, confirm)}
                        style={{ width: 90, marginRight: 8 }}
                    ><Icon type="search" /> Search</Button>
                    <Button onClick={() => this.handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                </div>
            ),
            filterIcon: filtered => (
                <Icon type="search" style={{ color: filtered ? '#1890ff' : undefined }} />
            ),
            onFilter: (value, record) =>
                record[dataIndex]
                    .toString()
                    .toLowerCase()
                    .includes(value.toLowerCase()),
            onFilterDropdownOpenChange: visible => {
                if (visible) {
                    setTimeout(() => this.searchInput.select());
                }
            },
            filteredValue: this.getFilteredValue(dataIndex)
        };

    }

    buildColumnSortProps(dataIndex) {
        let { sortedInfo } = this.state;
        sortedInfo = sortedInfo || {};

        return {
            sorter: (a, b) => a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
            sortOrder: sortedInfo.columnKey === dataIndex && sortedInfo.order
        };
    }

    createColumns(columns) {
        // const { onHeaderCell } = this;
        return columns.map(column => {
            const { dataIndex } = column;
            let columnFilterProps = null;
            let columnSortProps = null;
            let columnDeleteProps = null;

            if (dataIndex == 'name') {
                columnFilterProps = this.buildColumnFilterProps(dataIndex);
                columnSortProps = this.buildColumnSortProps(dataIndex);
            }

            if (dataIndex === 'tableOptions') {
                columnDeleteProps = this.buildTableOptionColumnProps(dataIndex);
            }

            return {
                ...column,
                ...columnFilterProps,
                ...columnSortProps,
                ...columnDeleteProps
                // onHeaderCell
            };
        });
    }

    buildTableOptionColumnProps(dataIndex) {
        return {
            render: (text, record, rowIndex) => {
                const { id, isDeleted } = record;

                return <>
                    {!isDeleted && (
                        <a className="deleteSkillButton" onClick={() => this.handleButtonDeleteClick(id, record) } >
                            <IconDelete />
                        </a>
                    )}
                </>;
            }
        };
    }

    handleButtonDeleteClick(id, record) {
        record.isDeleted = true;
        this.props.onButtonDeleteClick(id, record);
    }

    addHandlersToProps(gridProps) {
        const { onHeaderCell } = this;
        const newCols = gridProps.columns.map(column => {
            return { ...column, onHeaderCell };
        });

        return { ...gridProps, columns: newCols };
    }

    onHeaderCell(column) {
        const { sectionId } = this.props;

        return {
            onClick: () => this.props.onColumnSort(sectionId, column) // click header row
        };
    }

    handleChange(pagination, filters, sorter) {
        console.log('Various parameters', pagination, filters, sorter);
        const { sectionId } = this.props;
        this.props.applySort(sectionId, sorter);
        this.props.applyFilter(sectionId, filters);

        sorter = {
            ...sorter,
            sectionId
        };

        this.setState({
            sortedInfo: sorter
        });

        const { current, pageSize } = pagination;
        let pageNumber = this.state.pageNumber;
        let size = this.state.pageSize;

        if (current !== this.state.pageNumber) {
            pageNumber = current;
        }

        if (pageSize !== this.state.pageSize) {
            size = pageSize;
        }
        this.setState({
            sortedInfo: sorter,
            pageNumber,
            pageSize: size
        });
    }

    handleRow(record, index) {
        const { isDeleted, id } = record;
        const { cancelDeleteButtonLabel, skillMarkedForDeletionMessage } = this.props;

        return {
            skillMarkedForDeletionMessage,
            cancelDeleteButtonLabel,
            rowIndex: index,
            isDeleted,
            cancelDeletion: () => this.props.onButtonCancelDeleteClick(id, record),
            expandOnClick: () => !record.isDeleted && this.handleExpand(!record.isExpanded, record)
        };
    }

    expandedRowRender(record) {
        const { gridProps, getExpandedContentProps } = this.props;

        return gridProps.expandedRowRender(record, getExpandedContentProps(record));
    }

    handleExpand(setSkillExpanded, record) {
        this.props.handleSkillExpand(setSkillExpanded, record);
    }

    render() {
        const {
            data,
            gridProps
        } = this.props;

        const { expandedRowRender } = this;
        const { pageNumber, pageSize } = this.state;
        const expandedRowKeys = [];
        this.props.data.map((row, index) => row.isExpanded && expandedRowKeys.push(index));
        const rowCount = data.length;
        const pagination = {
            useTablePagination: true,
            pageSizesOptions: ['10', '20', '30'],
            pageSize,
            pageNumber,
            showSizeChanger: true,
            size: 'small',
            rowCount
        };

        const dataGridProps = {
            ...gridProps,
            ...pagination,
            columns: this.createColumns(gridProps.columns),
            expandedRowRender,
            dataSource: data,
            expandedRowKeys,
            onExpand: this.handleExpand,
            onChange: this.handleChange,
            onRow: this.handleRow,
            withoutCustomPagination: true,
            id: DATA_GRID_IDS.SKILLS_GRID_ID
        };

        return <DataGrid {...dataGridProps} />;
    }
}

SkillList.propTypes = {
    applySort: PropTypes.func.isRequired,
    applyFilter: PropTypes.func.isRequired
};

export default SkillList;