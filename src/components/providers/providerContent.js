import React from 'react';
import {IntlProvider} from 'react-intl';
import { LANGUAGES_TO_BE_SUPPORTED, ANTD5_LOCALES_MAP, DAYJS_LOCALES_MAP } from '../../constants/globalConsts';
import {bindAll} from 'lodash';
import {ConfigProvider} from 'antd';
import dayjs from 'dayjs';
import { loadIntlPolyfills } from '../../utils/intlPolyfills';

class IntlProviderContent extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            messageFromPromise:null,
            antDLocale: {},
            setAntDLanguage:{},
            applicationLanguage:''
        };
        bindAll['checkSupportedLang', 'setLanguageInApp', 'updateLocales'];
    }

    setLanguageInApp(){
        const {userSelectedLanguage, browserLanguage, defaultLanguage} = this.props;
        const checkSupportOfBrowserLang = this.checkSupportedLang();
        let languageToBeSet = defaultLanguage;
        //Using if else for code readabality.
        //check if userSelected language and default language are same
        if(userSelectedLanguage === defaultLanguage) {
            //check if we support the current browser langugage
            if(checkSupportOfBrowserLang){
                languageToBeSet = browserLanguage;
            }
            //if not supported default language is set
        }else{
            //condition if user selected language and default language are not same
            languageToBeSet = userSelectedLanguage;
        }
        return languageToBeSet;
    }

    updateLocales(appLanguage) {
        const getMessages = import(`../../i18n/messages/${appLanguage}`);
        getMessages.then(data => this.setState({ translationMessages: data.default, applicationLanguage: appLanguage }));

        const antDLocaleKey = ANTD5_LOCALES_MAP[appLanguage];
        const importAntDLocaleAsync = async (localeKey) => await import(`antd/lib/locale/${localeKey}.js`);
        importAntDLocaleAsync(antDLocaleKey).then(data => this.setState({ antDLocale: data.default }));

        const dayjsLocaleKey = DAYJS_LOCALES_MAP[appLanguage];
        const importDayjsLocaleAsync = async (dayjsLocaleKey) => await import(`dayjs/locale/${dayjsLocaleKey}.js`);
        importDayjsLocaleAsync(dayjsLocaleKey).then(() => dayjs.locale(dayjsLocaleKey));
    }

    componentDidMount() {
        const {userSelectedLanguage, defaultLanguage} = this.props;
        const locale = (navigator.language || 'en').split('-')[0];

        loadIntlPolyfills(locale);
        //need null check as api returns null in userselected language

        const applicationLanguage = userSelectedLanguage !== null ? this.setLanguageInApp() : defaultLanguage;

        this.updateLocales(applicationLanguage);
    }

    componentDidUpdate(prevProps) {
        const { userSelectedLanguage } = this.props;

        if (prevProps.userSelectedLanguage !== userSelectedLanguage) {
            this.updateLocales(userSelectedLanguage);
        }
    }

    checkSupportedLang() {
        const {browserLanguage} = this.props;
        let isSupported = false;
        LANGUAGES_TO_BE_SUPPORTED.map((index)=>{
            if(index.key === browserLanguage) {
                isSupported = true;
            }
        });
        return isSupported;
    }

    render() {
        const { children } = this.props;
        const { antDLocale, applicationLanguage, translationMessages } = this.state;
        //need null check as api returns null in userselected language

        if (!applicationLanguage || !translationMessages) {
            return null;
        }

        return (
            //Ant design provider and intl provider for internationalization
            //pass dynamic language based on selection of language in company info dropdown
            <>
                <ConfigProvider
                    theme={{
                        hashed: false,
                        token: {
                            fontFamily: 'Inter',
                            colorPrimary: '#018848'
                        },
                        components: {
                            Layout: {
                                headerHeight: 60
                            },
                            Typography: {
                                fontSizeHeading1: '1.857rem',
                                fontSizeHeading2: '1.429rem',
                                fontSizeHeading3: '1.286rem',
                                fontSizeHeading4: '1.143rem',
                                lineHeightHeading1: 1.23,
                                lineHeightHeading2: 1.35,
                                lineHeightHeading3: 1.35,
                                lineHeightHeading4: 1.4
                            },
                            DatePicker: {
                                cellHoverWithRangeBg: '#99CFB5',
                                cellRangeBorderColor: '#99CFB5'
                            }
                        }
                    }}
                    locale={antDLocale}
                >
                    <IntlProvider
                        locale={applicationLanguage}
                        messages={translationMessages}
                    >
                        {children}
                    </IntlProvider>
                </ConfigProvider>
            </>
        );
    }
}

export default IntlProviderContent;
