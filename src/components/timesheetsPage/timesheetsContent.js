import React from 'react';
import { bindAll } from 'lodash';
import ActionBar from '../../lib/actionBar/ActionBar';

const TimesheetsContentForm = React.lazy(() => import('./timesheetsContentForm'));

class TimesheetsContent extends React.Component {
    constructor(props) {
        super(props);
        bindAll(this, ['saveFormRef']);
    }

    saveFormRef(ref) {
        this.formRef = ref;
    }

    render() {
        return (

            <ActionBar
                formRef={this.formRef}
                formId="timesheet_form"
                loading={this.props.loading}
                messages={this.props.messages}
            >
                <React.Suspense fallback={<div />}>
                    <TimesheetsContentForm
                        wrappedComponentRef={this.saveFormRef}
                        {...this.props}
                    >
                    </TimesheetsContentForm>
                </React.Suspense>
            </ActionBar>
        );
    }
}

export default TimesheetsContent;