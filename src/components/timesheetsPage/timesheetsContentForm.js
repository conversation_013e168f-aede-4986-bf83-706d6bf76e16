import React from 'react';
import { bindAll } from 'lodash';
import TimesheetsDatePicker from './timesheetsDatePicker';
import './timesheets.less';
import { Form } from '@ant-design/compatible';

const TimesheetsDataGrid = React.lazy(() => import('./timesheetsDataGrid'));
// import '@ant-design/compatible/assets/index.css';


import actionBarConf from './timesheet.conf';
import FormNotifier from '../common-components/form-notifier';

class TimesheetForm extends React.Component {
    constructor(props) {
        super(props);
        this.childRef = React.createRef();
        this.state = {
            actionBarConf: actionBarConf
        };
        bindAll(this, ['resetContent', 'handleSubmit', 'getActionBarConfig']);
    }

    componentDidMount() {
        const { actionBarConf } = this.state;
        const { buttons } = actionBarConf;
        const { messages = {}, translatedValues } = this.props;
        const { submitButtonLabel, cancelButtonLabel } = translatedValues;
        const { actionBarFormErrorMessage } = messages;
        actionBarConf.formErrorMessage = actionBarFormErrorMessage;
        buttons.primary.label = submitButtonLabel;
        buttons.secondary.label = cancelButtonLabel;
        buttons.secondary.action = this.resetContent;
        this.setState({
            actionBarConf: { ...actionBarConf }
        });
    }

    getActionBarConfig() {
        return this.state.actionBarConf;
    }

    resetContent() {
        const { selectedDateRange, form, fetchTimesheet } = this.props;
        this.childRef.current.resetGrid();
        form.resetFields();
        fetchTimesheet(selectedDateRange);
    }

    handleSubmit(e) {
        e.preventDefault();
        const { form, submitTimesheet } = this.props;
        form.resetFields();
        form.validateFieldsAndScroll((errors) => {
            if (!errors) {
                this.childRef.current.resetGrid();
                submitTimesheet();
            }
        });
    }

    render() {
        const timesheetProps = {
            ...this.props,
            childRef: this.childRef
        };

        return (
            <div>
                <Form id="timesheet_form" onSubmit={this.handleSubmit}>
                    <div className="timesheets-content-form">
                        <TimesheetsDatePicker {...this.props} />
                        <React.Suspense fallback={<div />}>
                            <TimesheetsDataGrid {...timesheetProps} />
                        </React.Suspense>
                    </div>
                </Form>
                <FormNotifier form={this.props.form} />
            </div>
        );
    }
}

const TimesheetsContentForm = Form.create({ name: 'TimesheetForm' })(TimesheetForm);
export default TimesheetsContentForm;