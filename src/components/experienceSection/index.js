import React from 'react';
import { TABLE_NAMES } from '../../constants';
import { DISPLAY_DATE_TIME_FORMATS } from '../../constants/globalConsts';
import {
    ContextualEditActions,
    ContextualEditControlWrapper
} from '../../lib/contextualEditControl';
import GenericFormImplementation from '../../lib/genericFormImplementation';
import ReadonlyControl from '../../lib/readonlyControl';
import { getDisplayLongDateFormat,isBefore, parseToUtcDate } from '../../utils/dateUtils';
import {
    formatFieldValue,
    isBlankFieldValue
} from '../../utils/fieldControlUtils';
import styles from '../../lib/fieldControl/styles/fieldControlLayout.less';
import PropTypes from 'prop-types';
import { EXPERIENCE_FIELDS } from '../../constants/fieldConsts';
import { <PERSON><PERSON>, Divider } from 'antd';
import { Icon } from '../../lib';
import { EXPERIENCE_SECTION_ALIAS } from '../../constants/experienceSectionConstants';
import { getIsFieldSetToHidden } from '../../utils/fieldUtils';

const ExperienceReadOnlyContent = (props) => {
    const { uiData, getFieldInfo, getBlankValue } = props;

    const getFormatedDate = (fieldName, fieldInfo) => {
        return formatFieldValue(uiData[fieldName], fieldInfo, {
            dateFormat: getDisplayLongDateFormat(DISPLAY_DATE_TIME_FORMATS.NO_DAY)
        });
    };

    const getFieldValue = (fieldName) => {
        const fieldInfo = getFieldInfo(TABLE_NAMES.EXPERIENCE, fieldName);
        const { fieldtype } = fieldInfo;
        let value = '';
        let className = 'readonlyControl';

        switch (fieldtype) {
            case 'Date': {
                value = getFormatedDate(fieldName, fieldInfo);
                break;
            }
            case 'Plain text': {
                value = uiData[fieldName];
                break;
            }
            default: {
                value = null;
            }
        }

        if (isBlankFieldValue(value)) {
            value = getBlankValue(fieldInfo);
            className = 'readonlyControl has-no-value';
        }

        if (getIsFieldSetToHidden(fieldName, uiData)) {
            value = <Icon type={'lock'} />;
        }

        return (
            <ReadonlyControl displayValue={value} displayValueClassName={className} />
        );
    };

    return (
        <div className="container">
            <div className="role-row">
                <div className="role-col">
                    <span className="strong">
                        {getFieldValue(EXPERIENCE_FIELDS.EXPERIENCE_COMPANY)}
                    </span>
                </div>
            </div>
            <div className="role-row">
                <div className="role-col">
                    {getFieldValue(EXPERIENCE_FIELDS.EXPERIENCE_ROLE)}
                </div>
                <span className="icon-bullet">&bull;</span>
                <div className="role-col">
                    {getFieldValue(EXPERIENCE_FIELDS.EXPERIENCE_LOCATION)}
                </div>
            </div>
            <div className="role-row">
                <div className="role-col">
                    {getFieldValue(EXPERIENCE_FIELDS.EXPERIENCE_START)}
                </div>
                <span className="icon-bullet">-</span>
                <div className="role-col">
                    {getFieldValue(EXPERIENCE_FIELDS.EXPERIENCE_END)}
                </div>
            </div>
            <div className="role-row">
                <div className="role-col">
                    {getFieldValue(EXPERIENCE_FIELDS.EXPERIENCE_DETAILS)}
                </div>
            </div>
        </div>
    );
};

const InlineEditForm = (props) => {
    const {
        experienceData,
        contextId,
        onContextualEditCancel,
        getFormHasChanges,
        getFormHasErrors
    } = props;

    const formItemLayout = {
        labelCol: {
            xs: { span: 6 },
            sm: { span: 8 }
        },
        wrapperCol: {
            xs: { span: 18 },
            sm: { span: 16 }
        },
        labelAlign: 'left',
        colon: true,
        layout: 'horizontal'
    };

    const getCustomRules = (fieldName, messages = [], uiData) => {
        const rules = [];
        switch (fieldName) {
            case EXPERIENCE_FIELDS.EXPERIENCE_END: {
                rules.push({
                    validator: (rule, value, callback) => {
                        const startDate = (uiData[EXPERIENCE_FIELDS.EXPERIENCE_START] || {}).value;
                        const endDate = value;
                        const error = [];
                        if (isBefore(endDate, parseToUtcDate(startDate))) {
                            error.push(messages['endDateFieldError'] || '');
                        }

                        callback(error);
                    }
                });
            }
        }

        return rules;
    };

    return (
        <div className="formInlineEdit">
            <GenericFormImplementation
                {...props}
                formId={`genericForm_${props.contextId}`}
                isBatch={false}
                contextId={props.contextId}
                uiData={experienceData}
                sortedRecordsIds={[contextId]}
                formItemLayout={formItemLayout}
                getCustomRules={getCustomRules}
            />
            <ContextualEditActions
                styles={styles}
                extraSubmitParams={{
                    form: `genericForm_${contextId}`,
                    htmlType: 'submit'
                }}
                onApply={() => {}}
                onCancel={() => onContextualEditCancel(contextId)}
                submitDisabled={
                    !getFormHasChanges([contextId]) || getFormHasErrors([contextId])
                }
            />
        </div>
    );
};

const EditExperienceFormWrapper = (props) => {
    const {
        uiData,
        inlineEditGuids,
        onContextualEditStart,
        getFieldInfo,
        getBlankValue,
        editable,
        data = [],
        talentProfilePageTransformedEnabled
    } = props;

    return Object.keys(uiData)
        .filter((experience) => !experience.startsWith('new_'))
        .map((experience, index) => {
            const experienceData = data.find(record => record[EXPERIENCE_FIELDS.EXPERIENCE_GUID] === experience) || {};
            const isLastItem = index === data.length - 1;
            if (inlineEditGuids.indexOf(experience) !== -1) {
                return (
                    <InlineEditForm
                        {...props}
                        experienceData={{ [experience]: uiData[experience] }}
                        contextId={experience}
                    />
                );
            } else {
                const experienceReadOnlyContent = (
                    <ExperienceReadOnlyContent
                        uiData={experienceData}
                        getFieldInfo={getFieldInfo}
                        getBlankValue={getBlankValue}
                    />
                );

                return editable ? (
                    <div>
                        <ContextualEditControlWrapper
                            onContextualEditStart={() => onContextualEditStart(experience)}
                        >
                            {experienceReadOnlyContent}
                        </ContextualEditControlWrapper>
                        {/* Added divider between details if not the last item and feature flag is enabled */}
                        {!isLastItem && talentProfilePageTransformedEnabled && <Divider className="divider" />}
                    </div>

                ) : (
                    <div>{experienceReadOnlyContent}
                        {/* Added divider between details if not the last item and feature flag is enabled */}
                        {!isLastItem && talentProfilePageTransformedEnabled && <Divider className="divider" />}
                    </div>
                );
            }
        });
};

const BulkEditExperienceFormWrapper = (props) => {
    const { uiData, deletedRecords = [], alias, staticLabels = {} } = props;
    const {addPrefix, deleteLabelText} = staticLabels;

    const formItemLayout = {
        labelCol: {
            xs: { span: 6 },
            sm: { span: 6 }
        },
        wrapperCol: {
            xs: { span: 18 },
            sm: { span: 18 }
        },
        labelAlign: 'left',
        colon: false,
        layout: 'horizontal'
    };

    const getAddDeleteComponent = ({ fieldName, recordId, showAdd }) => {
        const { uiData, onAddNewRecord, onDeleteRecord, mandatoryFields = [] } = props;

        let shouldShowAddButton = showAdd;

        for (let i = 0; i < mandatoryFields.length; i++) {
            const field = mandatoryFields[i];
            const { errors, value } = uiData[recordId][field];

            if (!value || Array.isArray(errors)) {
                shouldShowAddButton = false;
                break;
            }
        }

        return (
            <div className="addDeleteController">
                <Button role='button' aria-label={deleteLabelText} tabIndex={1} className={'history-field-delete'} size={'default'} onClick={() => { onDeleteRecord(recordId); }}>
                    <Icon type="delete" />
                </Button>
                {
                    shouldShowAddButton && (
                        <Button role="button" aria-label={addPrefix} tabIndex={2} className={'history-field-add'} size={'default'} onClick={() => { onAddNewRecord(); }}>
                            <Icon type="plus" />
                        </Button>)
                }
            </div>
        );
    };

    const getCustomRules = (fieldName, messages = [], uiData) => {
        const rules = [];
        switch (fieldName) {
            case EXPERIENCE_FIELDS.EXPERIENCE_END: {
                rules.push({
                    validator: (rule, value, callback) => {
                        const startDate = (uiData[EXPERIENCE_FIELDS.EXPERIENCE_START] || {}).value;
                        const endDate = value;
                        const error = [];
                        if (isBefore(endDate, parseToUtcDate(startDate))) {
                            error.push(messages['endDateFieldError'] || '');
                        }

                        callback(error);
                    }
                });
            }
        }

        return rules;
    };

    const newKeys = Object.keys(uiData).filter(recordId => recordId.startsWith('new') && deletedRecords.indexOf(recordId) === -1);
    const oldKeys = Object.keys(uiData).filter(recordId => !recordId.startsWith('new') && deletedRecords.indexOf(recordId) === -1);
    const sortedRecordsIds = [...newKeys.sort().reverse(), ...oldKeys];

    return (
        <GenericFormImplementation
            {...props}
            uiData={uiData}
            sortedRecordsIds={sortedRecordsIds}
            formId={`genericForm_bulkEdit_${EXPERIENCE_SECTION_ALIAS}_${alias}`}
            isBatch={true}
            formItemLayout={formItemLayout}
            getAddDeleteComponent={getAddDeleteComponent}
            getCustomRules={getCustomRules}
        />
    );
};

const ExperienceSection = (props) => {
    const { isBulkEdit = false } = props;

    return (
        <div id={'experience'} className="experience">
            {isBulkEdit ? (
                <BulkEditExperienceFormWrapper {...props} />
            ) : (
                <EditExperienceFormWrapper {...props} />
            )}
        </div>
    );
};

ExperienceSection.propTypes = {
    isBulkEdit: PropTypes.bool
};

BulkEditExperienceFormWrapper.propTypes = {
    uiData: PropTypes.object.isRequired
};

EditExperienceFormWrapper.propTypes = {
    uiData: PropTypes.object.isRequired,
    inlineEditGuids: PropTypes.array.isRequired,
    onContextualEditStart: PropTypes.func.isRequired,
    getFieldInfo: PropTypes.func.isRequired,
    getBlankValue: PropTypes.func.isRequired,
    editable: PropTypes.bool.isRequired
};

InlineEditForm.propTypes = {
    experienceData: PropTypes.object.isRequired,
    contextId: PropTypes.string.isRequired,
    onContextualEditCancel: PropTypes.func.isRequired,
    getFormHasChanges: PropTypes.func.isRequired,
    getFormHasErrors: PropTypes.func.isRequired
};

ExperienceReadOnlyContent.propTypes = {
    uiData: PropTypes.object.isRequired,
    getFieldInfo: PropTypes.func.isRequired,
    getBlankValue: PropTypes.func.isRequired
};

export {
    ExperienceSection,
    BulkEditExperienceFormWrapper,
    EditExperienceFormWrapper,
    InlineEditForm,
    ExperienceReadOnlyContent
};
