import React from 'react';
import PropTypes from 'prop-types';
import { fromEvent, Subject } from 'rxjs';
import { PLANNER_VIEW_MODES } from '../../../constants';
import { takeUntil, finalize, filter } from 'rxjs/operators';
import Stepper from './stepper';
import styles from './styles/dateBar.less';
import { getViewModeFromDates, CUSTOM_OPTION_VALUE } from '../../../utils/dateToggleOptionsUtils';
import { formatFieldValue } from '../../../utils/fieldControlUtils';
import { isSameDay, addDays, dayValues, getDisplayLongDateFormat, getUtcFullDate } from '../../../utils/dateUtils';
import { getVisibleRanges } from '../../../utils/plannerDataUtils';
import { isMinorUnitDay } from '../../../utils/calendarGridUtils';
import { DISPLAY_DATE_TIME_FORMATS } from '../../../constants/globalConsts';
import { isEmptyObject } from '../../../utils/commonUtils';

class DateBarControls extends React.Component {
    constructor(props) {
        super(props);
        this.initialX = 0;
        this.scrollOffset = 0;
        this.widthChange = 0;
        this.startXOffset = 0;
        this.xOffsetMaxR = 0;
        this.xOffsetMaxL = 0;
        this.unsubscribe$ = new Subject();

        this.thumbSelector = null;
        this.currentDragSelector = null;

        this.scrollEnd = this.scrollEnd.bind(this);
        this.resizeEnd = this.resizeEnd.bind(this);
        this.dragSelectorMove = this.dragSelectorMove.bind(this);
        this.dragThumbResize = this.dragThumbResize.bind(this);
        this.setupResizeObservable = this.setupResizeObservable.bind(this);
        this.setupSlideObservable = this.setupSlideObservable.bind(this);
        this.resizerMouseOut = this.resizerMouseOut.bind(this);
        this.resizerMouseOver = this.resizerMouseOver.bind(this);
        this.setActiveHandle = this.setActiveHandle.bind(this);

        this.state = {
            leftHandleActive: false,
            rightHandleActive: false
        };
    }

    // react component methods

    shouldComponentUpdate(nextProps) {
        let update = this.scrollOffset != 0 || this.widthChange != 0;
        update = update || this.props.width !== nextProps.width;
        update = update || !isSameDay(nextProps.datePivot.date, this.props.datePivot.date);
        update = update || !isSameDay(nextProps.visibleRangeStart, this.props.visibleRangeStart) || !isSameDay(nextProps.visibleRangeEnd, this.props.visibleRangeEnd);
        update = update || !isSameDay(nextProps.startDate, this.props.startDate) || !isSameDay(nextProps.endDate, this.props.endDate);
        update = update || nextProps.viewMode.dateOption !== this.props.viewMode.dateOption;
        update = update || nextProps.hideWeekends !== this.props.hideWeekends;

        return update;
    }

    componentDidUpdate(prevProps) {
        if (prevProps.width !== this.props.width) {
            // this.props.snapComponents(true);
        }
    }

    componentWillUnmount() {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
    }

    componentDidMount() {
        this.setupScrollWheelObservable();
    }

    markActive(currentTarget) {
        if (!!currentTarget && !currentTarget.classList.contains("active")) {
            currentTarget.classList.add("active")
        }
        return currentTarget;
    }

    markInactive(currentTarget) {
        if (!!currentTarget && currentTarget.classList.contains("active")) {
            currentTarget.classList.remove("active")
        }

        return currentTarget;
    }

    // thumb bar dragging

    scrollStart(e) {
        this.thumbSelector = e.currentTarget;
        this.thumbSelector = this.markActive(this.thumbSelector);

        this.initialX = e.clientX;

        let dateThumbBorders = this.props.getDateThumbDomObject().getBoundingClientRect();
        let dateTrackBorders = this.props.getDateTrackDomObject().getBoundingClientRect();

        this.xOffsetMaxL = (dateThumbBorders.left - dateTrackBorders.left) * -1;
        this.xOffsetMaxR = Math.abs((dateThumbBorders.left + dateThumbBorders.width) - (dateTrackBorders.left + dateTrackBorders.width));
        const calendarDomObject = this.props.getCalendarDomObject();
        this.initialCalendarX = !isEmptyObject(calendarDomObject) ? calendarDomObject.viewBox.baseVal.left : undefined;
        this.props.beforeScroll();
    }

    isInsideDragRegion() {
        return this.scrollOffset > this.xOffsetMaxL && this.scrollOffset < this.xOffsetMaxR;
    }

    dragSelectorMove(e) {
        let newValue = e.clientX - this.initialX;
        this.scrollOffset = newValue > this.xOffsetMaxL ? newValue < this.xOffsetMaxR ? newValue : this.xOffsetMaxR : this.xOffsetMaxL;

        if (!this.isInsideDragRegion() && !this.props.isAutoscrollStarted()) {
            this.props.startAutoscroll(this.scrollOffset === this.xOffsetMaxL, this.scrollOffset, this.initialCalendarX, this.props.getDateThumbDomObject());
        } else if (this.isInsideDragRegion() && this.props.isAutoscrollStarted()) {
            this.props.endAutoscroll(this.scrollOffset);
        }

        if (!this.props.isAutoscrollStarted()) {
            this.props.scroll(false, this.scrollOffset, this.initialCalendarX)
        }
    }

    scrollEnd() {
        this.thumbSelector = this.markInactive(this.thumbSelector);
        this.thumbSelector = null;
        this.scrollOffset = 0;
        this.props.snapComponents();
        this.props.endAutoscroll(this.scrollOffset);
    }

    setupSlideObservable(e) {
        e.preventDefault();
        e.stopPropagation();

        this.scrollStart(e);
        this.props.onDragStart();

        const move$ = fromEvent(document, 'mousemove');
        const up$ = fromEvent(document, 'mouseup');

        move$
            .pipe(takeUntil(up$), finalize(this.scrollEnd))
            .subscribe(this.dragSelectorMove);
            // .subscribe((evt) => console.log(evt));
    }

    setActiveHandle(key, dragState) {
        const { leftHandle, rightHandle } = this.props.datebarOperations.resize;

        if (key === leftHandle && this.state.leftHandleActive !== dragState) {
            this.setState({ leftHandleActive: dragState });
        } else if (key === rightHandle && this.state.rightHandleActive !== dragState) {
            this.setState({ rightHandleActive: dragState });
        }
    }

    // thumb bar resizing functions

    resizeStart(e) {
        this.currentDragSelector = e.currentTarget;
        this.currentDragSelector = this.markActive(this.currentDragSelector);

        this.initialX = e.clientX;
        this.activeResizeHandle = !!e.currentTarget.id && e.currentTarget.id.includes('leftHandle') ? 'leftHandle' : 'rightHandle';

        let dateThumbBorders = this.props.getDateThumbDomObject().getBoundingClientRect();
        let dateTrackBorders = this.props.getDateTrackDomObject().getBoundingClientRect();

        this.xOffsetMaxL = (dateThumbBorders.left - dateTrackBorders.left) * -1;
        this.xOffsetMaxR = Math.abs((dateThumbBorders.left + dateThumbBorders.width) - (dateTrackBorders.left + dateTrackBorders.width));

        this.setActiveHandle(this.activeResizeHandle, true);
    }

    dragThumbResize(e) {

        const newValue = e.clientX - this.initialX;
        const thumbMinWidth = this.getThumbSetings().thumbWidth / 2;

        let { maxEnlargePxOffset, maxShrinkPxOffset } = this.props.getResizePxLimit(0);
        maxShrinkPxOffset = maxShrinkPxOffset > thumbMinWidth ? thumbMinWidth : maxShrinkPxOffset;
        this.widthChange = newValue > this.xOffsetMaxL ? newValue < this.xOffsetMaxR ? newValue : this.xOffsetMaxR : this.xOffsetMaxL;
        let { leftHandle, rightHandle } = this.props.datebarOperations.resize;

        switch (this.activeResizeHandle) {
            case leftHandle:
                if (this.widthChange < 0) {
                    this.widthChange = this.widthChange > -maxEnlargePxOffset ? this.widthChange : -maxEnlargePxOffset;
                } else if (this.widthChange > 0) {
                    this.widthChange = this.widthChange < maxShrinkPxOffset ? this.widthChange : maxShrinkPxOffset;
                }
                this.startXOffset = this.widthChange;
                break;
            case rightHandle:
                if (this.widthChange < 0) {
                    this.widthChange = this.widthChange > -maxShrinkPxOffset ? this.widthChange : -maxShrinkPxOffset;
                } else if (this.widthChange > 0) {
                    this.widthChange = this.widthChange < maxEnlargePxOffset ? this.widthChange : maxEnlargePxOffset;
                }
                break;
            default:
                console.error("Could not find active handle.")
        }

        this.props.resize(this.activeResizeHandle, this.widthChange);
    }

    resizeEnd() {
        this.setState({leftHandleActive: false, rightHandleActive: false});
        this.currentDragSelector = this.markInactive(this.currentDragSelector);
        this.currentDragSelector = null;
        this.startXOffset = 0;
        this.widthChange = 0;
        const { visibleRangeStart, visibleRangeEnd } =  this.props;
        const viewModeName = getViewModeFromDates(visibleRangeStart, visibleRangeEnd).name;
        this.props.changeDateToggleOption(CUSTOM_OPTION_VALUE, viewModeName);
        this.props.snapComponents();
    }

    setupResizeObservable(e) {
        e.preventDefault();
        e.stopPropagation();

        const { customRangeEnabled } = this.props;

        if (customRangeEnabled) {
            const ePersist = e;
            this.resizeStart(ePersist);

            const move$ = fromEvent(document, 'mousemove');
            const up$ = fromEvent(document, 'mouseup');

            move$
                .pipe(takeUntil(up$), finalize(this.resizeEnd))
                .subscribe(this.dragThumbResize);
        }
    }

    resizerMouseOver(e) {
        e.preventDefault();
        e.stopPropagation();

        const { customRangeEnabled } = this.props;

        if (customRangeEnabled) {
            const ePersist = e;
            this.currentDragSelector = ePersist.currentTarget;
            this.currentDragSelector = this.markActive(this.currentDragSelector);
        }
    }

    resizerMouseOut(e) {
        e.preventDefault();
        e.stopPropagation();

        const { customRangeEnabled } = this.props;

        if (customRangeEnabled) {
            this.currentDragSelector = this.markInactive(this.currentDragSelector);
            this.currentDragSelector = null;
        }
    }

    // settings getters

    getDateTrackDimensions() {
        return {
            height: this.props.height * 0.8,
            //width: this.props.width * 0.7, // altered for larger stepper area
            width: this.props.width - 310 // updated with a set pixel value of stepper
        };
    }

    getTrackSetings() {
        const { width, height } = this.getDateTrackDimensions();
        const padding = 10;

        return {
            trackX: padding,
            trackY: height / 2,
            trackWidth: width - padding
        };
    }

    getThumbSetings() {
        const { leftHandle } = this.props.datebarOperations.resize;
        const trackHeight = this.getDateTrackDimensions().height;
        const trackWidth = this.getTrackSetings().trackWidth;
        const thumbHeight = 24; // trackHeight * 0.66; // now being set to 24px, as per new designs
        const widthChange = this.activeResizeHandle === leftHandle ? -this.widthChange : this.widthChange;
        const thumbWidth = trackWidth * 0.28;

        return {
            thumbX: (trackWidth * 0.36) + this.scrollOffset + this.startXOffset,
            thumbY: (trackHeight / 2) - (thumbHeight / 2),
            thumbWidth: thumbWidth + widthChange,
            thumbHeight: thumbHeight,
            thumbRound: 8
        };
    }

    getGripperGradientSettings() {
        return (
            <defs key={'gradient'}>
                <linearGradient id="resizeFill" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style={{ stopColor: 'gray', stopOpacity: 1 }} />
                    <stop offset="100%" style={{ stopColor: 'white', stopOpacity: 1 }} />
                </linearGradient>
            </defs>
        );
    }

    getStepperProps() {
        const {
            width,
            plannerRef,
            getDateRangeLength,
            changeDateRange,
            changeDateToggleOption,
            visibleRangeStart,
            visibleRangeEnd,
            viewMode,
            startDate,
            endDate,
            viewModeOptions,
            triggerSubMenuAction,
            hideWeekends,
            hideWeekendsToggleData,
            onHideWeekendsChanged,
            staticMessages,
            getEndDateOffset,
            dateToggleOptions,
            customRangeEnabled
        } = this.props;

        return {
            width,
            plannerRef,
            getDateRangeLength,
            changeDateRange,
            changeDateToggleOption,
            visibleRangeStart,
            visibleRangeEnd,
            viewMode,
            startDate,
            endDate,
            viewModeOptions,
            triggerSubMenuAction,
            staticMessages,
            hideWeekends,
            hideWeekendsToggleData,
            onHideWeekendsChanged,
            getEndDateOffset,
            dateToggleOptions,
            customRangeEnabled
        };
    }

    // scrolling functions

    wheelScrollFunc(event) {
        event.preventDefault();
        this.scrollStart(event);
        let newValue = event.deltaY * (-0.5);
        this.scrollOffset = newValue;
        this.props.scroll(false, this.scrollOffset, this.initialCalendarX);
        this.scrollEnd();
    }

    setupScrollWheelObservable() {
        let scrollComponent = document.querySelector(`#${this.props.scrollCompId}`);

        if (scrollComponent) {
            const wheel$ = fromEvent(scrollComponent, 'wheel', { passive: true });

            wheel$
                .pipe(filter((event) => event.shiftKey), takeUntil(this.unsubscribe$))
                .subscribe((e) => { this.wheelScrollFunc(e) });
        }
    }

    // render methods

    renderTrack() {
        const { trackX, trackY, trackWidth } = this.getTrackSetings();
        return <line ref={this.props.dateTrackRef} x1={trackX} y1={trackY} x2={trackWidth} y2={trackY} className={styles.track} />
    }

    renderResizeGrips(startX, startY, width, height, lineCount, spacing, key) {
        let lineArray = [];

        const style = {
            strokeWidth: width,
            stroke: "#354051"
        };

        for (let i = 0; i < lineCount; i++) {
            const x = startX + (i * (spacing + width))
            lineArray.push(
                (<line style={style} key={`${key}-${i}`} x1={x} y1={startY} x2={x} y2={startY + height} />)
            );
        }

        return lineArray;
    }

    renderResizeHandles(thumbX, thumbY, thumbHeight, thumbWidth) {
        const
            padding = 0,
            radius = 8,
            resizerWidth = 18,
            resizerHeight = thumbHeight - (padding * 2),
            gripPadding = 5
            ;

        const leftHandle = {
            x: thumbX + padding,
            y: thumbY + padding,
            width: resizerWidth - radius,
            height: resizerHeight
        };

        const rightHandle = {
            x: thumbX + thumbWidth - resizerWidth - padding + (radius / 2),
            y: thumbY + padding,
            width: resizerWidth - radius,
            height: resizerHeight
        };

        const grip = {
            width: 1,
            lineCount: 3,
            spacing: 2
        };

        const leftLines = this.renderResizeGrips(
            leftHandle.x + gripPadding,
            leftHandle.y + gripPadding,
            grip.width,
            leftHandle.height - (gripPadding * 2),
            grip.lineCount,
            grip.spacing,
            'leftHandleLines'
        );

        const rightLines = this.renderResizeGrips(
            rightHandle.x + gripPadding,
            rightHandle.y + gripPadding,
            grip.width,
            rightHandle.height - (gripPadding * 2),
            grip.lineCount,
            grip.spacing,
            'rightHandleLines'
        );

        const leftHandleGroupDragStyle = this.state.leftHandleActive === true ? 'left-handle-drag' : '';
        const rightHandleGroupDragStyle = this.state.rightHandleActive === true ? 'right-handle-drag' : '';
        const resizeEnabledStyle = this.props.customRangeEnabled ? 'resizeEnabled' : '';
        const resizerClassName = `thumbResizerGroup ${resizeEnabledStyle}`;

        return [
            <g
                onMouseOver={this.resizerMouseOver}
                onMouseOut={this.resizerMouseOut}
                onMouseDown={this.setupResizeObservable}
                key="leftHandleGroup"
                className={`${resizerClassName} ${leftHandleGroupDragStyle}`}
                id="leftHandleContainer"
            >
                <rect key={'leftHandleCorner'} x={leftHandle.x} y={leftHandle.y} width={radius} height={leftHandle.height} className="thumbResizer" rx={radius} ry={radius} />
                <rect key={'leftHandle'} x={leftHandle.x + (radius / 2)} y={leftHandle.y} width={leftHandle.width} height={leftHandle.height} className="thumbResizer" />
                {leftLines}
            </g>,
            <g
                onMouseOver={this.resizerMouseOver}
                onMouseOut={this.resizerMouseOut}
                onMouseDown={this.setupResizeObservable}
                key="rightHandleGroup"
                className={`${resizerClassName} ${rightHandleGroupDragStyle}`}
                id="rightHandleContainer"
            >
                <rect key={'rightHandle'} x={rightHandle.x} y={rightHandle.y} width={rightHandle.width} height={rightHandle.height} className="thumbResizer" />
                <rect key={'rightHandleCorner'} x={rightHandle.x + rightHandle.width - (radius / 2)} y={rightHandle.y} width={radius} height={rightHandle.height} className="thumbResizer" rx={radius} ry={radius} />
                {rightLines}
            </g>
        ];
    }

    getVisibleEndDateOffset(rangeEnd) {
        const { hideWeekends, viewMode, viewModeOptions } = this.props;
        const { mode } = viewMode;
        const { VIEW_MODE_YEAR, VIEW_MODE_MONTH } = PLANNER_VIEW_MODES;
        const endDateDayOfTheWeek = rangeEnd.get('day');
        const sundayOffset = -2;
        const mondayOffset = -3;
        let endDateOffset = -1;

        if (hideWeekends && isMinorUnitDay(viewModeOptions)) {
            if (endDateDayOfTheWeek == dayValues.monday) {
                endDateOffset = mondayOffset;
            } else if (endDateDayOfTheWeek == dayValues.sunday) {
                endDateOffset = sundayOffset;
            }
        } else if (mode === VIEW_MODE_MONTH || VIEW_MODE_YEAR === mode) {
            endDateOffset = 0;
        }

        return endDateOffset;
    }

    renderDates(thumbX, thumbY, thumbHeight, thumbWidth) {
        const { visibleRangeStart, visibleRangeEnd, hideWeekends, viewMode} = this.props;
        const dateFormat = thumbWidth < 200
            ? getDisplayLongDateFormat(DISPLAY_DATE_TIME_FORMATS.NO_YEAR)
            : getDisplayLongDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR);
        const fieldInfo = { dataType: 'DateTime' };
        const options = { dateFormat: dateFormat, keepLocalTime: true };
        const { rangeStart, rangeEnd } = getVisibleRanges(visibleRangeStart, visibleRangeEnd, viewMode, hideWeekends);
        const endDateOffset = this.getVisibleEndDateOffset(rangeEnd);

        const startDate = formatFieldValue(getUtcFullDate(rangeStart), fieldInfo, options);
        const endDate = formatFieldValue(addDays(getUtcFullDate(rangeEnd), endDateOffset), fieldInfo, options);
        const xCord = thumbX + (thumbWidth / 2);
        const yCord = thumbY + (thumbHeight / 2);
        const thumbWidthCalc = thumbWidth / 2 / 2;

        return [
            <rect key={'thumbDateBox'} fill="none" x={xCord} y={yCord} width={thumbWidthCalc <= 0 ? 0 : thumbWidthCalc} height={thumbHeight}></rect>,
            <text
                key={'thumbDate'}
                className={styles.svgMajorDate}
                textAnchor="middle"
                dominantBaseline="central"
                x={xCord}
                y={yCord}
            >
                {startDate} - {endDate}
            </text>
        ];
    }

    renderThumb() {
        const { thumbX, thumbY, thumbWidth, thumbHeight, thumbRound } = this.getThumbSetings();

        return (
            <g id="dateBarThumbContainer" className="dateBarThumb" onMouseDown={this.setupSlideObservable} >
                <rect id="dateBarThumb" ref={this.props.dateThumbRef} x={thumbX} y={thumbY} rx={thumbRound} ry={thumbRound} width={thumbWidth <= 0 ? 0 : thumbWidth} height={thumbHeight} className="thumb" />

                {this.renderResizeHandles(thumbX, thumbY, thumbHeight, thumbWidth)}
                {this.renderDates(thumbX, thumbY, thumbHeight, thumbWidth)}
            </g>
        );
    }

    renderScroller() {
        const dateTrackDimensions = this.getDateTrackDimensions();
        const { width, height } = dateTrackDimensions;

        return (
            <div id='dateBarScroller' className="dateTrackStyle">
                <svg width={width <= 0 ? 0 : width} height={height}>
                    {this.getGripperGradientSettings()}
                    {this.renderTrack()}
                    {this.renderThumb()}
                </svg>
            </div>

        );
    }

    render() {
        const controlsMainStyle = {
            ...this.props.style,
            height: this.props.height,
            width: this.props.width
        };

        return (
            <div id={this.props.id} style={controlsMainStyle} className={styles.controlsMainStyle}>
                {this.renderScroller()}
                <Stepper {...this.getStepperProps()} />
            </div>
        );
    }
}

const DotCoordProp = PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired
});

DateBarControls.propTypes = {
    id: PropTypes.string,
    width: PropTypes.number,
    height: PropTypes.number,
    widthRatio: PropTypes.number,
    heightRatio: PropTypes.number,
    style: PropTypes.object,
    viewMode: PropTypes.object,
    viewModeOptions: PropTypes.object.isRequired,
    startDate: PropTypes.object,
    endDate: PropTypes.object,
    previewWindowRef: PropTypes.object,
    plannerRef: PropTypes.object,
    calendarRef: PropTypes.object,
    dateThumbRef: PropTypes.object,
    dateTrackRef: PropTypes.object,
    setRenderFrom: PropTypes.func,
    setRenderTo: PropTypes.func,
    renderedTo: DotCoordProp.isRequired,
    renderedFrom: DotCoordProp.isRequired,
    getSVGViewBox: PropTypes.func,
    datePivot: PropTypes.shape({
        date: PropTypes.object.isRequired,
        position: PropTypes.number.isRequired
    }),
    autoscrollStarted: PropTypes.bool,
    visibleRangeStart: PropTypes.object,
    visibleRangeEnd: PropTypes.object,
    beforeScroll: PropTypes.func,
    snapComponents: PropTypes.func,
    startAutoscroll: PropTypes.func,
    endAutoscroll: PropTypes.func,
    isAutoscrollStarted: PropTypes.func,
    scroll: PropTypes.func,
    resize: PropTypes.func,
    getDateTrackDomObject: PropTypes.func,
    getDateThumbDomObject: PropTypes.func,
    getCalendarDomObject: PropTypes.func,
    getPreviewWindowDomObject: PropTypes.func,
    getDateRangeLength: PropTypes.func,
    datebarOperations: PropTypes.object,
    getResizePxLimit: PropTypes.func,
    onDateRangeChanged: PropTypes.func,
    changeDateToggleOption: PropTypes.func,
    workspaceGuid: PropTypes.string,
    staticMessages: PropTypes.object,
    hideWeekends: PropTypes.bool,
    onHideWeekendsChanged: PropTypes.func,
    onDragStart: PropTypes.func
};

export { DateBarControls };