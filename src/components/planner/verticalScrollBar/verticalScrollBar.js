import React from 'react';
import PropTypes from 'prop-types';
import { debounce } from 'lodash';

import { fromEvent, timer, Subject } from 'rxjs';
import { mergeMap, takeUntil, finalize, filter, debounceTime } from 'rxjs/operators';
import { Icon } from '../../../lib';
import styles from './css/verticalScrollBar.less';
import { messageService } from '../../common-components/helpers/messageService';
import { rlUpdateDormantSubject } from '../../../lib/recordsList/recordsList';
import { PlannerVerticalScrollBarResizeObservable } from '../../../observables/planner/plannerGridResizeObservable';

class VerticalScrollBar extends React.Component {
    constructor(props) {
        super(props);
        this.vertScroller = React.createRef();
        this.initialY = 0;
        this.initialCalendarY = 0;
        this.yOffset = props.yOffset;
        this.yOffsetMaxUp = 0;
        this.yOffsetMaxDown = 0;
        this.scrollRatio = props.scrollRatio;
        this.unsubscribe$ = new Subject();
        this.dragInProgress = false;
        this.scrollToPos = this.scrollToPos.bind(this);
    }

    setDragInProgress(dragInProgress) {
        this.dragInProgress = dragInProgress;
    }

    dragSelectorMove(e) {
            let newValue = e.clientY - this.initialY
            let down = newValue > this.yOffset;
            this.yOffset = newValue > this.yOffsetMaxUp ? newValue < this.yOffsetMaxDown? newValue : this.yOffsetMaxDown : this.yOffsetMaxUp;

            this.scroll(down);
    }

    setTranslate(xPos = 0, yPos = 0, el) {
        el.style.transform = "translate3d(" + xPos + "px, " + yPos + "px, 0)";
    }

    scrollComponents(){
        const yScrollPosition = Math.round(this.yOffset * this.scrollRatio);

        this.recordsListBody.scrollTop = yScrollPosition;
        rlUpdateDormantSubject.next();
        if (this.props.calendarRef.current) {
            this.props.calendarRef.current.viewBox.baseVal.y = this.initialCalendarY + yScrollPosition;
        }

        if (this.props.onScroll) {
            this.props.onScroll(yScrollPosition);
        }
    }

    scroll(down){
        this.scrollComponents();
        this.setTranslate(undefined, this.yOffset, this.vertScroller.current);

        if(down && this.recordsListBody.scrollTop > this.loadTrigger && this.props.canScroll && false == this.props.wait){
            this.loadTrigger = this.recordsListBody.scrollHeight;
            const debouncedScrollData = debounce(() => {
                this.props.scrollPosChangeFunc(this.yOffset, this.scrollRatio);
                this.props.scrollDataFunc();
                this.setupVertScroll();
            }, 25, { leading: true, trailing: false });
            debouncedScrollData();
        }
    }

    scrollUpFunc(){
        let newValue = this.initialY - 100 / this.scrollRatio;
        this.yOffset = newValue > this.yOffsetMaxUp? newValue : this.yOffsetMaxUp;

        this.scroll(false);

        this.initialY = this.yOffset;
    }

    scrollDownFunc(){
        let newValue = this.initialY + 100 / this.scrollRatio ;
        this.yOffset = newValue < this.yOffsetMaxDown ? newValue : this.yOffsetMaxDown;

        this.scroll(true);

        this.initialY = this.yOffset;
    }

    scrollBarClickFunc(e){
        e.preventDefault();

        if (this.scrollBar === e.target) {
            let down = e.offsetY > this.yOffset;
            this.yOffset =  e.offsetY < this.yOffsetMaxDown? e.offsetY : this.yOffsetMaxDown;

            this.scroll(down);
            this.initialY = this.yOffset;
        }
    }

    wheelScrollFunc(e){
        if (!e.target
            || !e.target.parentElement
            || !e.target.parentElement.offsetParent  
            || (e.target.parentElement.className.indexOf('fieldPickerMenu') <= -1
            && e.target.parentElement.offsetParent.parentElement.className.indexOf('fieldPickerMenu') <= -1 
            && e.target.parentElement.offsetParent.parentElement.className.indexOf('ant-menu') <= -1)) {
            e.preventDefault();
    
            let newValue = this.initialY + e.deltaY / this.scrollRatio;
            let isDown = newValue > this.yOffset;
            this.yOffset = newValue > this.yOffsetMaxUp ? newValue < this.yOffsetMaxDown? newValue : this.yOffsetMaxDown : this.yOffsetMaxUp;
    
            this.scroll(isDown);
    
            this.initialY = this.yOffset;
        }
    }

    setupVertScroll() {
        this.recordsListScrollHeight = this.recordsListBody.scrollHeight;
        let vertScroller = this.vertScroller.current;
        let scrollBarBorders = vertScroller.parentElement.getBoundingClientRect();
        let scrollerMinHeight = scrollBarBorders.height * 0.02;
        let scrollerHeight = (this.recordsListBody.offsetHeight / this.recordsListScrollHeight) * scrollBarBorders.height;
        let scrollerDiff = 0;
        if (scrollerHeight < scrollerMinHeight) {
            scrollerDiff = scrollerMinHeight - scrollerHeight;
            scrollerHeight = scrollerMinHeight;
        }
        vertScroller.style.height = scrollerHeight + 'px';
        let vertScrollBorders = vertScroller.getBoundingClientRect();
        if (this.props.calendarRef.current) {
            this.props.calendarRef.current.viewBox.baseVal.y = this.initialCalendarY + (this.yOffset * this.scrollRatio);
        }
        this.scrollRatio = ((this.recordsListScrollHeight) / (scrollBarBorders.height - scrollerDiff));

        const yOffsetMaxDown = (scrollBarBorders.height) - (vertScrollBorders.height);
        this.yOffsetMaxDown = yOffsetMaxDown > 0 ? yOffsetMaxDown : 0;
        this.loadTrigger = this.recordsListScrollHeight - this.recordsListBody.offsetHeight * 1.2;
        //this.loadTrigger = this.loadTrigger > 0 ? this.loadTrigger : this.recordsListBody.scrollHeight;

        if (!this.dragInProgress) {
            const calendarYOffset = (this.getCurrentCalendarY() || 0) - this.initialCalendarY;
            const yOffsetNewValue = calendarYOffset / (this.props.setupScrollRatio || this.scrollRatio);
            this.yOffset =
                yOffsetNewValue > this.yOffsetMaxUp
                    ? yOffsetNewValue < this.yOffsetMaxDown
                        ? yOffsetNewValue
                        : this.yOffsetMaxDown
                    : this.yOffsetMaxUp;
            this.initialY = this.yOffset;
        }

        this.scroll();
    }

    getCurrentCalendarY() {
        return this.props.getViewBoxRef().y || this.props.yOffset;
    }

    componentWillUnmount() {
        this.unsubscribe$.next(true);
        this.unsubscribe$.complete();
        this.plannerScrollBareResizeObserver.unsubscribe();

        if (this.scrollToPosSubscription) {
            this.scrollToPosSubscription.unsubscribe();
        }
    }

    componentDidMount() {
        let mainComponent = document.querySelector(`#${this.props.scrollCompId}`);
        let upButton = document.querySelector("#scrollUp");
        let scrollBar = document.querySelector("#scrollBar");
        let downButton = document.querySelector("#scrollDown");
        let vertScroller = this.vertScroller.current;
        this.initialCalendarY = (this.getCurrentCalendarY() || 0);
        this.scrollBar = scrollBar;
        this.recordsListBody = document.querySelector('#rl-records-container');
        const {scrollPosChangeFunc} = this.props;

        this.setupVertScroll();

        const move$ = fromEvent(document, 'mousemove');
        const down$ = fromEvent(vertScroller, 'mousedown');
        const up$ = fromEvent(document, 'mouseup');
        const enter$ = fromEvent(vertScroller, 'mouseenter');
        const leave$ = fromEvent(vertScroller, 'mouseleave');
        const upButton$ = fromEvent(upButton, 'mousedown');
        const scrollBarClick$ = fromEvent(scrollBar, 'mousedown');
        const downButton$ = fromEvent(downButton, 'mousedown');
        const scroll$ = fromEvent(mainComponent, 'wheel', { passive: true });

        const slide$ = down$.pipe(
            mergeMap((e) => {
                e.preventDefault();
                this.setDragInProgress(true);
                this.initialY = e.clientY - this.yOffset;
                return move$.pipe(
                    takeUntil(up$),
                    finalize(() => {
                        this.setDragInProgress(false);
                        this.initialY = this.yOffset;
                    })
                );
            })
        );

        this.plannerScrollBareResizeObserver = PlannerVerticalScrollBarResizeObservable.toObservable()
            .subscribe(() => {
                this.setupVertScroll();
            });

        down$.subscribe(()=>{vertScroller.classList.add(styles.scrollBarPressed)})
        up$.subscribe(()=>{vertScroller.classList.remove(styles.scrollBarPressed)})

        const scrollUp$ = upButton$.pipe(
            mergeMap((e) => {
                e.preventDefault();
                return timer(20,100).pipe(takeUntil(up$));
            })
        )

        const scrollDown$ = downButton$.pipe(
            mergeMap((e) => {
                e.preventDefault();
                return timer(20,100).pipe(takeUntil(up$));
            })
        )

        scroll$
        .pipe(takeUntil(this.unsubscribe$),filter((event) => !event.shiftKey))
        .subscribe((e) => {this.wheelScrollFunc(e)});

        scrollUp$.subscribe((e) => {this.scrollUpFunc(e)});
        scrollBarClick$.subscribe((e) => {this.scrollBarClickFunc(e)});
        scrollDown$.subscribe((e) => {this.scrollDownFunc(e)});

        slide$.subscribe(this.dragSelectorMove.bind(this));

        scroll$
        .pipe(takeUntil(this.unsubscribe$), debounceTime(150))
        .subscribe(() => {scrollPosChangeFunc(this.yOffset, this.scrollRatio)});

        scrollBarClick$
        .pipe(takeUntil(this.unsubscribe$), debounceTime(250))
        .subscribe(() => {scrollPosChangeFunc(this.yOffset, this.scrollRatio)});

        slide$
        .pipe(takeUntil(this.unsubscribe$), debounceTime(150))
        .subscribe(() => {scrollPosChangeFunc(this.yOffset, this.scrollRatio)});

        this.scrollToPosSubscription = messageService.getMessage().subscribe(this.scrollToPosMessageListener.bind(this));
    }

    scrollToPosMessageListener ({id, scrollPos}) {
        if (id && id === 'CGScrollToPos') {
            this.scrollToPos(scrollPos);
        }

        this.scrollToPosSubscription.unsubscribe();
    }

    scrollToPos(scrollPos) {

        if(scrollPos !== 0) {
            let newValue = this.initialY + scrollPos / this.scrollRatio;
            this.yOffset = newValue > this.yOffsetMaxUp ? newValue < this.yOffsetMaxDown? newValue : this.yOffsetMaxDown : this.yOffsetMaxUp;
            this.initialY = this.yOffset;

            this.scrollComponents();
            this.setTranslate(undefined, this.yOffset, this.vertScroller.current);
            this.props.scrollPosChangeFunc(this.yOffset, this.scrollRatio);
        }
    }

    componentDidUpdate(/*prevProps, prevState*/){
        if(this.recordsListScrollHeight !== this.recordsListBody.scrollHeight){
            if (!this.dragInProgress) {
                this.yOffset = this.props.yOffset;
            }

            this.setupVertScroll();
        }
        if(this.yOffset !== 0 && (this.getCurrentCalendarY() || 0) !== this.initialCalendarY + this.yOffset * this.scrollRatio){
            this.scrollComponents();
        }
    }

    render() {
        const { style, commonStaticMessages } = this.props;
        const { scrollDownText, scrollBarText, scrollUpText } = commonStaticMessages || {};

        const iconStyles = {
            height: style.width,
            width: style.width
        }

        const scrollBarStyle = {
            height: style.height - (2 * style.width),
            width: style.width
        }

        const vertScrollerStyle = {
            maxHeight: style.height,
            width: style.width
        }

        return (
            <div id={this.props.id} style={this.props.style}>
                <Icon id="scrollUp" type="up" role="button" ariaLabel={scrollUpText} ariaHidden={false} tabIndex="0" className={styles.iconStyles + ' ' + styles.iconUp} style={{ ...iconStyles }}/>
                <div id="scrollBar" role="button" tabIndex="0" aria-label={scrollBarText} className={styles.scrollBarStyle} style={scrollBarStyle} >
                    <div id="vertScroller" ref={this.vertScroller} className={styles.vertScrollerStyle} style={vertScrollerStyle}></div>
                </div>
                <Icon id="scrollDown" type="down" role="button" tabIndex="0" ariaLabel={scrollDownText} ariaHidden={false} className={styles.iconStyles + ' ' + styles.iconDown} style={{ ...iconStyles }}/>
            </div>
        )
    }
}

VerticalScrollBar.propTypes = {
    style: PropTypes.object,
    id: PropTypes.string,
    calendarRef: PropTypes.object,
    scrollDataFunc: PropTypes.func.isRequired,
    getViewBoxRef: PropTypes.func,
    onScroll: PropTypes.func,
    setRenderTo: PropTypes.func.isRequired,
    renderedTo: PropTypes.object.isRequired,
    scrollPosChangeFunc: PropTypes.func.isRequired,
    yOffset: PropTypes.number,
    scrollRatio: PropTypes.number,
    wait: PropTypes.bool,
    canScroll: PropTypes.bool,
    commonStaticMessages: PropTypes.object
};

export default VerticalScrollBar;
