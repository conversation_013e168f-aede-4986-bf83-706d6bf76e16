import React from 'react';
import PropTypes from 'prop-types';
import { Button, Modal, message } from 'antd';
import { FormattedMessage } from 'react-intl';
import { bindAll } from 'lodash';
import { messageService } from '../helpers';

class UnsavedChangesPrompt extends React.Component {
    constructor(props) {
        super(props);

        bindAll(this, [
            'closeModal', 'saveChanges', 'handleCancel', 'renderFooter', 'discardChanges'
        ]);
        this.state = {
            modalVisible: false
        };
    }

    componentDidUpdate(prevProps) {
        const { shouldPrompt, currentSelectedDateRange } = this.props;

        if ((prevProps.currentSelectedDateRange !== currentSelectedDateRange) && shouldPrompt) {
            this.setState(() => ({
                modalVisible: true
            }));
        }
    }

    closeModal(callback) {
        this.setState({
            modalVisible: false
        }, callback);
        Modal.destroyAll();
    }

    handleCancel() {
        this.closeModal();
    }

    discardChanges() {
        const { form, onDiscardChanges, messageServiceId} = this.props;
        form.resetFields();
        this.closeModal();
        onDiscardChanges();

        messageService.sendMessage({ id: messageServiceId, isGridModified: false, errorStatus: false });
    }

    saveChanges() {
        this.closeModal(() => {
            const { onSaveChanges, messages, hasError, messageServiceId } = this.props;
            const { toasterDefaultUnsavedChangesMessage } = messages;

            if (!hasError) {
                onSaveChanges();
                messageService.sendMessage({ id: messageServiceId, isGridModified: false, errorStatus: false });
            } else {
                message.error(toasterDefaultUnsavedChangesMessage);
            }
        });
    }

    renderFooter() {
        return (
            <div className="action-bar-modal-buttons">
                <Button
                    key="submit"
                    type="primary"
                    className="ant-btn-primary"
                    onClick={this.saveChanges}
                >
                    <span><FormattedMessage id="actionBarSaveButtonLabel" /></span>
                </Button>
                <Button className="ant-btn-secondary" key="discard" type="secondary-button" onClick={this.discardChanges}>
                    <span><FormattedMessage id="actionBarDiscardChangesLabel" /></span>
                </Button>
                <Button className="ant-btn-tertiary" key="cancel" type="link secondary-button" onClick={this.handleCancel}>
                    <span><FormattedMessage id="actionBarCancelButtonLabel" /></span>
                </Button>
            </div>
        );
    }

    render() {
        return (
            (<Modal
                open={this.state.modalVisible}
                footer={this.renderFooter()}
                closable={false}
            >
                <p><FormattedMessage id="actionBarUnsavedChanges" /></p>
            </Modal>)
        );
    }
}

UnsavedChangesPrompt.propTypes = {
    form: PropTypes.any,
    shouldPrompt: PropTypes.bool,
    currentSelectedDateRange: PropTypes.object,
    onDiscardChanges: PropTypes.func,
    onSaveChanges: PropTypes.func,
    hasError: PropTypes.bool,
    messageServiceId: PropTypes.string
};

export {
    UnsavedChangesPrompt
};