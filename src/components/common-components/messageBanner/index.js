import React, { useState, useEffect } from 'react';
import styles from './styles.less';
import { Button } from 'antd';

const MessageBanner = ({ localStorageKey, description = '', dismissLabel = '' }) => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const dismissed = localStorage.getItem(localStorageKey);
        if (!dismissed) {
            setIsVisible(true);
        }
    }, []);

    const handleDismiss = () => {
        localStorage.setItem(localStorageKey, 'true');
        setIsVisible(false);
    };

    if (!isVisible) {
        return null;
    }

    return (
        <div className={styles.messageBanner}>
            <p>{description}</p>
            <Button onClick={handleDismiss}>{dismissLabel}</Button>
        </div>
    );
};

export default MessageBanner;