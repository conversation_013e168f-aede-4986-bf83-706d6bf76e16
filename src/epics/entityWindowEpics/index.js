import * as actionTypes from '../../actions/actionTypes';
import { combineEpics } from 'redux-observable';
import { combineAPIEpics } from '../middlewareExtensions';
import { take } from 'rxjs/operators';
import schema from 'async-validator';
import {
    entityWindowSubmitInsertTableDataSuccess,
    entityWindowSubmitInsertTableDataError,
    entityWindowSubmitUpdatePagedDataSuccess,
    entityWindowSubmitUpdatePagedDataError,
    entityWindowSubmitUpdateTableDataSuccess,
    entityWindowSubmitUpdateTableDataError,
    entityWindowSubmitUpdateGrouppedDataSuccess,
    entityWindowSubmitUpdateGrouppedDataError,
    entityWindowSubmitDeleteTableDataSuccess,
    entityWindowSubmitDeleteTableDataError,
    entityWindowSubmitDeleteGrouppedTableDataSuccess,
    entityWindowSubmitDeleteGrouppedTableDataError,
    entityWindowContextualEditPagedDataSuccess,
    entityWindowContextualEditPagedDataError,
    entityWindowContextualEditTableDataSuccess,
    entityWindowContextualEditTableDataError,
    entityWindowContextualEditGrouppedDataSuccess,
    entityWindowContextualEditGrouppedDataError,
    entityWindowUpdateEntity,
    entityWindowUpdateAllEntities,
    entityWindowSetFieldErrors,
    entityWindowSubmitInsertGrouppedDataBatchSuccess,
    entityWindowSubmitInsertGrouppedDataBatchError,
    entityWindowClearHighlightField,
    entityWindowClose,
    entityWindowSubmitInsertGrouppedDataSuccess,
    entityWindowSubmitInsertGrouppedDataError,
    entityWindowHideSectionContent,
    entityWindowSetMessages,
    entityWindowUpdateEntityTemplate,
    submitUpdateBatchRequestSuccess,
    submitUpdateBatchRequestError,
    entityWindowOpenForMultiple,
    entityWindowOpen,
    entityWindowLazyLoadActiveEntity,
    attemptRoleProgressToRequested,
    entityWindowFieldChanged,
    entityWindowSubmitInsertTableDataBatchSuccess,
    entityWindowSubmitInsertTableDataBatchError,
    entityWindowDrivenFieldChanged,
    entityWindowUpdateMessages,
    entityWindowSetActiveTab
} from '../../actions/entityWindowActions';
import { editResourceSkillsWindowUpdateSkills } from '../../actions/editResourceSkillsWindowActions';
import { resourceSkillsDiscardChanges } from '../../actions/resourceSkillsActions';
import { getData, getEntityWindowModulesForPage, getOmittedFields, getPageAliasForEntityModule, isAssigneeBudgetModal, lowerCaseObjectKeys, omit } from '../../utils/commonUtils';
import { getFieldInfo, getTableStructure, getFieldInfos, getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { getResourceSkillsCanSaveSelector, resourceSkillsHasChangesSelector } from '../../selectors/resourceSkillsSelectors';
import {
    getIsCustomField,
    getIsSystemField,
    isSubmittableField,
    getAppFieldsData,
    getIsMandatoryField,
    getIsSystemReadOnlyField,
    getIsTimeAllocationField,
    getIsDateRangeField,
    getFieldExistInSections,
    getIsHistoryField,
    getIsMilestonesHistoryField
} from '../../utils/fieldUtils';
import { tableDataApiName, insertTableDataRequest$, deleteTableDataRequest$, patchTableDataRequest$, batchInsertTableDataRequest$ } from '../tableDataEpics';

import { getApiCallEpicConfig, createAPICallEpic } from '../epicGenerators/apiCallEpicGenerator';
import { PLANNER_BOOKING_GROUPS_ALIAS, PLANNER_MASTER_REC_ALIAS, PLANNER_SUB_REC_ALIAS, ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, PLANNER_PAGE_ALIAS, PLANNER_TABLE_DATAS_SUFFIX, UNASSIGNED_BOOKINGS_RESOURCE, PLANNER_ROLEREQUESTS_ALIAS, PLANNER_PAGE_TABLE_TO_GROUP_ALIAS, BAR_TABLE_NAMES, PLANNER_ROLEREQUESTGROUP_ALIAS } from '../../constants/plannerConsts';
import { chargeModeFieldByTableName, CHARGERATE_CURRENT_VALUE_FIELDNAME, FIELD_DATA_TYPES, JOB_BILLINGTYPE_GUID, JOB_BUDGET, JOB_DESCRIPTION, JOB_FIXEDPRICE, RESOURCE_DESCRIPTION, RESOURCE_GUID, ROLEMARKETPLACE_FIELDS, ROLEREQUESTGROUP_FIELDS, ROLEREQUESTRESOURCE_FIELDS, ROLEREQUEST_FIELDS, BOOKINGTYPE_GUID, BOOKING_SERIES_GUID, RESOURCE_USERSTATUS, BOOKING_START, BOOKING_END, BOOKING_RESOURCE_GUIDS } from '../../constants/fieldConsts';
import { ENTITY_WINDOW_MODULES, JOBS_PAGE_ALIAS, TABLE_NAMES, ERROR_STATUS, UNASSIGNED_ROLE_GROUP_NAME } from '../../constants';
import { filter, mergeMap, switchMap, catchError, map, delay } from 'rxjs/operators';
import { of, concat, empty, from, forkJoin, merge, EMPTY } from 'rxjs';
import { createPlannerResourceSummaryFieldWithParams } from '../../utils/plannerQuerySelectionUtils';
import { createRoleResourceFieldWithParams } from '../../utils/demandDataUtils/roleRequestDetailsQuerySelectionUtils';
import { getLinkedTableData, getFlatTableData, getLinkedSelectionFields, getCustomLookupSelectionFields } from '../../utils/linkedDataUtils';
import { DATA_GRID_TABLE_DATAS_SUFFIX } from '../../constants/dataGridConsts';
import {
    bookingTotalCostField,
    bookingTotalRevenueField,
    bookingResourcesSectionField,
    roleRequestStatusField,
    bookingStatusSectionField,
    roleRequestHasCriteriaField,
    jobHoursBudgetField,
    jobTotalHoursBookedField,
    jobHoursPercentageBudgetField,
    jobProfitMarginField,
    jobMarginPercentageTargetField,
    jobProfitMarginTargetField,
    jobRevenuePercentageTargetField
} from '../../state/entityWindow/fieldsConfig';
import { sectionsTableNames } from '../../state/entityWindow/index';
import { createHandleFieldDrivenActionsEpic$ } from './fieldDrivenActionsEpic';
import { createHandleEventDrivenActionsEpic$ } from './eventDrivenActionsEpic';
import { drivenActionsEpics$ } from './drivenActionsEpics';
import { ENTITY_WINDOW_CUSTOM_CONTROL_TYPES, ENTITY_WINDOW_SECTION_KEYS, ENTITY_WINDOW_SECTION_TITLES, ENTITY_WINDOW_TAB_KEYS, ENTITY_WINDOW_SECTION_TYPES, EDIT_ALL_ENTITY_ID, ENTITY_WINDOW_OPERATIONS, BILLING_TYPE_TIME_MATERIAL, CRITERIA_BUDGET_SECTIONS } from '../../constants/entityWindowConsts';
import { getCriteriaFieldRules, getFieldRules } from '../../utils/validationUtils';
import { getFormField } from '../../connectedComponents/connectedEntityWindow/tabs';
import { dataSelectorWindowAliasMap, instanceSelectorsWindowAliasMap } from '../../connectedComponents/connectedEntityWindow/';
import { JOBS_PAGE_MODAL_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE } from '../../constants/jobsPageConsts';
import { entityWindowAddSection, entityWindowExtendSection } from '../../actions/entityWindowActions';
import { getSystemInfoSectionFields } from '../../state/entityWindow/fieldsConfig';
import { loadAudit } from '../../actions/auditActions';
import { cloneDeep, isEmpty, isEqual, keys, map as lodashMap, mergeWith, unionBy, uniqBy } from 'lodash';

import { loadPagedComments } from '../../actions/commentsActions';
import { entityWindowHasSection, getEWFieldInfoSelector, getEntityWindowSectionByKey, isEntityWindowSectionContentHidden, getEntityWindowTemplateSelector, getEntityWindowTableName, getSectionLicenseValueSelector, getEntityWindow, getSelectedDetailsPaneTabKey } from '../../selectors/entityWindowSelectors';
import { getNewSectionInsertIndex } from '../../state/entityWindow/settingsBuilders';
import { injectDynamicConfiguration, setBookingWorkNonWorkDaysExplanation, setTimeAllocationReadOnlyExplanation, updateEntityWindowSectionMessages, updateRolerequestEntityWindowSectionMessages } from '../../actions/entityWindowDrivenActions';
import { hideContextMenu } from '../../actions/contextMenuActions';
import { deleteAttachment, discardAttachmentsChanges, discardMultipleEntitiesAttachmentsChanges, digestInsertAttachment, loadAttachments } from '../../actions/attachmentsActions';
import { getErrorStaticMessagesSelector } from '../../selectors/errorSelectors';
import { addToTranslationConfig, delayedPopulateEntityWindowTranslationMessages } from '../../actions/internationalizationActions';
import { getConfigChunk } from '../../utils/translationUtils';
import { API_KEYS, SUCCESS_STATUS } from '../../constants/apiConsts';
import { processMilestonesHistoryField, processHistoryField } from '../../lib/historyField/utils';
import { batchDeleteGroupedData, loadMoreTableDataSuccess, patchMultipleGrouppedTableDataSuccess, batchDeleteTableData } from '../../actions/tableDataActions';
import { getBarGroupsGuidByTable, getSelectedWorkspaceSettings } from '../../selectors/workspaceSelectors';
import { getAttachmentsChanges, attachmentsHaveChanges } from '../../selectors/attachmentsSelectors';
import { getEntityOptionFields } from '../../connectedComponents/connectedEntityLookupWindow';
import { getFieldTableName, getTableDatasAlias, hasDefaultValue, isLookupField } from '../../utils/tableStructureUtils';
import { loadResourceSkills } from '../../actions/plannerDataActions';
import { hasDefaultValueInTemplate, isEditAllEntity } from '../../utils/entityStructureUtils';
import { setPageState } from '../../actions/pageStateActions';
import { pushUrl } from '../../actions/navigateActions';
import { ROLEGROUPLISTDETAILSPAGE } from '../../pages/pages';
import { loadRoleRequestGroupList } from '../../actions/jobPageRoleGroupListDPActions';
import { loadAvatars } from '../../actions/avatarActions';
import { AVATAR_SIZES } from '../../constants/avatarConsts';
import { batchActions } from 'redux-batched-actions';
import { getFieldFromSections, isDependantListControl, isFieldAccessReadonly } from '../../utils/fieldControlUtils';
import { getBatchUpdateActions, getEntitiesPatchData, isExcludedSectionModule, shouldSelectHistoryTabOnOpen, shouldValidateField } from '../../utils/entityWindowUtils';
import { getIsJobDpItemVisibleCollapse } from '../../selectors/jobsPageRoleGroupSelectors';
import { getJobDescriptionForEntity } from '../../utils/plannerDataUtils';
import { performRoleTransition$ } from '../roleGroupEpics';
import { CRITERIA_SECTIONS, ROLE_ITEM_STATUS_KEYS, ROLES_MODAL, TRANSITION_PAYLOAD_KEYS, REQUIRED_ROLE_TOTALS_CALCULATION_FIELDS } from '../../constants/rolesConsts';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_PAGED_DATA } from '../../constants/roleInboxPageConsts';
import { NOTIFICATIONS_PAGE_ALIAS, NOTIFICATIONS_BOOKING_ALIAS } from '../../constants/notificationsPageConsts';
import { createStaticBatchInsertDigestEpic } from '../plannerDataEntityGroupEpics';
import { getAssigneesBudgetDataForRole, getCriteriasForRoleSelector, getRolePatchDataSuccessAliasByPage, getRolerequestFieldSelection } from '../../selectors/roleRequestsSelector';
import { getTableDataRoleRequestStatusGuidSelector } from '../../selectors/roleRequestsSelector';
import { getCurrentPageAliasSelector } from '../../selectors/navigationSelectors';
import { patchMultiplePagedDataSuccess } from '../../actions/pagedDataActions';
import { createRoleInboxResourceFieldWithParams } from '../../utils/roleInboxQuerySelectionUtils';
import { getCriteriaSectionsFieldsSelector, getRequirementSectionHasChangesSelector, getRequirementsSectionIsReadOnlyMode } from '../../selectors/requirementsSelectors';
import { expandRoleCriterias, populateCriteriasForRoles, setCriteriaFieldsError } from '../../actions/criteriaActions';
import { expandCriteriaValues } from '../../utils/requirementsUtils';
import { getEntityWindowOperation } from '../../selectors/entityWindowSelectors';
import { shouldRenderField } from '../../connectedComponents/connectedEntityWindow/connectedEntityWindowCreator';
import { saveRoleRequestData$ } from '../rolerequestEpics';
import { getOpenRoleEntityWindowActions } from '../../utils/rolerequestActionsUtils';
import { getActiveRoleEntitySelector } from '../../selectors/userEntityAccessSelectors';
import { BAD_REQUEST_STATUS } from '../../constants';
import { showRolesTransitionPrompt } from '../../actions/roleTransitionDialogsActions';
import { getRolesTransitionPromptContext } from '../../utils/promptUtils';
import { getPageEntryDataSelector, getPageTableDatasSelector, getTableDataLoadedActionsForPageSelector } from '../../selectors/tableDataSelectors';
import { buildEntityWindowMessageOptions } from '../../utils/messages/entityWindowMessagesOptions';
import { getCriteriaRoleAssigneesResDataMap, getIsCriteriaRole, getRoleInfoSelection, getRoleWithChargerateValue } from '../../utils/roleRequestsUtils';
import { loadRoleGroupDetails } from '../../actions/roleGroupDetailsActions';
import { EDIT_REPEAT_BOOKING_TYPE, FEATURE_FLAGS, FILTER_FIELD_NAMES, OPERATORS, ROLE_ID, STANDARD_CHARGE_RATE, STANDARD_COMPANY_DIARY } from '../../constants/globalConsts';
import { buildRoleTransitionRequestPayload } from '../../api/utils';
import { getAvatars, resourceAvatarLoaded } from '../../selectors/avatarSelectors';
import { loadRoleRequestsAssigneesInfo, processLoadedRoleAssignees } from '../../actions/rolerequestActions';
import { getIsMultipleAssigneesEnabled } from '../../selectors/functionalityConfigurationSelectors';
import { MARKETPLACE_PAGE_ALIAS } from '../../constants/marketplacePageConsts';
import { PROFILE_PAGE_ALIAS, PROFILE_BOOKING_ALIAS } from '../../constants/talentProfileConsts';
import { PREVIEW_ENTITY_PAGE_ALIAS } from '../../constants/marketplacePageConsts';
import { browserHistory } from '../../history';
import { loadResourceWorkHistory } from '../../actions/workHistoryActions';
import { getWorkHistoryAlias } from '../../utils/workHistoryUtils';
import { TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_DATAS_ALIAS } from '../../constants/tableViewPageConsts';
import { getRoleMarketplaceDynamicConfig } from '../../utils/entityDynamicConfigUtils';
import { getEntityWindowMessagesSelector, getEntityWindowPublishRoleMessagesOptions } from '../../selectors/entityWindowMessagesSelectors';
import { loadUserEntityAccess } from '../../actions/userEntityAccessActions';
import globalCreateModalEpics$ from './globalCreateModalEpics';
import { getRoleCarouselDefaultTranslationSelector } from '../../connectedComponents/connectedCarouselElements/carouselSelectors';
import { parseToUtcDate, parseUtcToLocalDate, startOfDay } from '../../utils/dateUtils';
import { repeatBookingUpdateResourceSuggestions } from '../../actions/repeatBookingActions';
import { FILTER_GROUP_OPERATORS } from '../../constants/advancedFilterConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';

const plannerTableNameAlias = `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`;
const jobsTableNameAlias = `${JOBS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;
const roleInboxTableNameAlias = `${ROLE_INBOX_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;
const notificationTableNamesAlias = `${NOTIFICATIONS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;
const marketplaceTableNamesAlias = `${MARKETPLACE_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;
const profileTableNamesAlias = `${PROFILE_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;
const previewEntityTableNamesAlias = `${PREVIEW_ENTITY_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;
const tableViewTableNameAlias = `${TABLE_VIEW_PAGE_ALIAS}_${TABLE_VIEW_PAGE_DATAS_ALIAS}`;

const ADDITIONAL_DETAILS_KEY = 'additionalDetails';
const ADDITIONAL_DETAILS_TITLE = 'Additional details';

const {
    SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
    SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS,
    SUBMIT_UPDATE_BATCH_REQUEST_SUCCESS
} = actionTypes.ENTITY_WINDOW;

const roleListSectionModuleNames = [
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE
];

const {
    FIELDS_LIST_SECTION_TYPE,
    RESOURCE_SUMMARY_SECTION_TYPE,
    OVERLAPPING_BOOKING_SECTION_TYPE,
    CRITERIA_BUDGET_SECTION_TYPE,
    CRITERIA_ESTIMATE_BUDGET_SECTION_TYPE,
    MILESTONES_SECTION_TYPE,
    TAB_SECTION_TYPE,
    REQUIREMENTS_SECTION_TYPE
} = ENTITY_WINDOW_SECTION_TYPES;

const buildEntityPayload = (entity, omittedFields, batchGuids = [], guidsField) => {
    let result = omit(entity, omittedFields);

    if (batchGuids && batchGuids.length > 0) {
        result = batchGuids.map(guid => {
            return {
                ...omit(entity, omittedFields),
                [guidsField]: guid
            };
        });
    }

    return result;
};

export const mapEntityWindowSubmitAction = (state$, action) => {
    let { payload } = action;
    let { tableName, entityPayloadAlias, moduleName, isBatch } = payload;
    const getFieldInfo = getFieldInfoSelector(state$.value);
    const getEWFieldInfo = getEWFieldInfoSelector(state$.value);

    // This duplicates getOmittedFields in commonUtils. Both functions should be combined
    // Function name does not correspond with returned value an should be changed
    const getOmittedFields = (entity) => {
        let omittedFields = [];

        // temp until got proper fieldInfo with readonly values
        if (tableName === TABLE_NAMES.BOOKING) {
            omittedFields = [bookingTotalCostField.name, bookingTotalRevenueField.name, bookingResourcesSectionField.name];
        }

        let fieldInfo, fieldInfoEW, guidsField, batchGuids;

        for (let fieldName in entity) {
            fieldInfo = getFieldInfo(tableName, fieldName);
            fieldInfoEW = getEWFieldInfo(moduleName, tableName, fieldName);

            if (fieldInfoEW.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL) {
                if (Array.isArray(entity[fieldInfoEW.name]) && entity[fieldInfoEW.name].length != 0) {
                    guidsField = fieldInfoEW.actualFieldName;
                    batchGuids = entity[fieldInfoEW.name].map(id => id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ? null : id);
                }
            }

            if (getIsHistoryField(fieldInfo)) {
                entity[fieldName] = processHistoryField(entity[fieldName], fieldInfo);
            }

            if (getIsMilestonesHistoryField(fieldInfo)) {
                entity[fieldName] = processMilestonesHistoryField(entity[fieldName], fieldInfo);
            }

            if (fieldInfo && entity[fieldName] === undefined) {
                entity[fieldName] = null;
            }

            if (fieldInfo && fieldInfo.dataType === FIELD_DATA_TYPES.STRING && entity[fieldName]) {
                entity[fieldName] = entity[fieldName].trim();
            }

            if (!isSubmittableField(fieldName, tableName, fieldInfo)) {
                omittedFields.push(fieldName);
            }

            if (tableName === TABLE_NAMES.JOB && fieldName === JOB_BILLINGTYPE_GUID) {
                const tableDatas = getPageTableDatasSelector(state$.value);
                const fieldTableName = getFieldTableName(fieldInfo, tableName);
                const value = getData(tableDatas, fieldTableName, entity[fieldName]);

                value[`${fieldTableName}_description`] === BILLING_TYPE_TIME_MATERIAL ? entity[JOB_FIXEDPRICE] = null : entity[JOB_BUDGET] = null;
            }
        }

        return { guidsField, batchGuids, omittedFields };
    };

    if (isBatch) {
        let patchedData = payload[entityPayloadAlias];
        let data = [];

        patchedData.forEach((el) => {
            const { tableData, tableDataEntryGuid } = el;

            const { guidsField, batchGuids, omittedFields } = getOmittedFields(tableData);

            data.push({ tableDataEntryGuid, tableData: buildEntityPayload(tableData, omittedFields, batchGuids, guidsField) });
        });

        return {
            ...action,
            payload: {
                ...action.payload,
                [entityPayloadAlias]: data
            }
        };
    } else {
        let entity = { ...payload[entityPayloadAlias] };

        const { guidsField, batchGuids, omittedFields } = getOmittedFields(entity);

        return {
            ...action,
            payload: {
                ...action.payload,
                [entityPayloadAlias]: buildEntityPayload(entity, omittedFields, batchGuids, guidsField),
                guidsField,
                batchGuids
            }
        };
    }
};

const mapEntityWindowFieldSubminAction = (state$, action) => {
    const newAction = {
        ...action,
        payload: {
            ...action.payload,
            entityPayloadAlias: 'tableData'
        }
    };

    return mapEntityWindowSubmitAction(state$, newAction);
};

export const validateFormAndGetAction$ = (state$, action) => {
    const state = state$.value;
    const { payload: { moduleName, isBatch, patchData, tableDataEntryGuid } } = action;

    return isBatch
        ? validateBatchFormAndGetAction$(patchData, state, moduleName, action)
        : validateSingleFormAndGetAction$(state, moduleName, action, null, tableDataEntryGuid);
};

const validateBatchFormAndGetAction$ = (patchData, state, moduleName, action) => {
    const validationErrors$ = patchData
        .filter((entityData) => !isEditAllEntity(entityData.tableDataEntryGuid))
        .map((entityData) => {
            const { tableDataEntryGuid, tableData: entity } = entityData;

            return validateSingleFormAndGetAction$(state, moduleName, action, entity, tableDataEntryGuid);
        });

    return forkJoin(validationErrors$);
};

const validateSingleFormAndGetAction$ = (state, moduleName, action, singleEntity = null, tableDataEntryGuid = null) => {
    let { payload } = action;
    const { entityPayloadAlias, tableName, isBatch } = payload;
    let entity = singleEntity ? singleEntity : { ...payload[entityPayloadAlias] };

    const props = getValidationProps(state, moduleName, tableDataEntryGuid, isBatch);
    const { entityFormStructure, rules } = getEntityWindowStructureAndRules(state, moduleName, props, tableName, entity);
    const validator = new schema(rules);
    const promise = validator.validate(entityFormStructure);

    const validationResult$ = from(promise).pipe(
        map(() => {
            return singleEntity ? undefined : {
                ...action,
                errorsFound: false
            };
        }),
        catchError((result) => {
            const { fields } = result;
            const fieldsErrors = {};
            const criteriaErrors = {};

            Object.keys(fields).map((fieldKey) => {
                if (fields[fieldKey][0].isCriteriaField) {
                    criteriaErrors[fieldKey] = {
                        errors: fields[fieldKey]
                    };
                } else {
                    fieldsErrors[fieldKey] = {
                        errors: fields[fieldKey]
                    };
                }
            });

            const actions = [];

            if (Object.keys(fieldsErrors).length != 0) {

                const fieldsActionsToDispatch = tableDataEntryGuid
                    ? entityWindowSetFieldErrors(moduleName, fieldsErrors, tableDataEntryGuid)
                    : entityWindowSetFieldErrors(moduleName, fieldsErrors);
                actions.push({
                    ...fieldsActionsToDispatch,
                    errorsFound: true
                });
            }

            if (Object.keys(criteriaErrors).length != 0) {
                actions.push({
                    ...setCriteriaFieldsError(criteriaErrors, tableDataEntryGuid),
                    errorsFound: true
                });
            }

            return from(actions);
        })
    );

    return validationResult$;
};

const hasHiddenSelectedField = (sectionField, entity, tableName) => {
    const { leadingFieldName } = sectionField;
    let isCurrentSelectedFieldHidden = false;

    if (BAR_TABLE_NAMES.includes(tableName) && leadingFieldName === chargeModeFieldByTableName[tableName]) {
        isCurrentSelectedFieldHidden = !sectionField.fields.some(field => field.key === entity[chargeModeFieldByTableName[tableName]]);
    }

    return isCurrentSelectedFieldHidden;
};

const shouldAddFieldToEntityStructure = (fieldInfo, sectionField, getData, getLinkedData, { entity, tableName, operation, moduleName, uiEntity }) => {
    return !isFieldAccessReadonly(fieldInfo)
        && !hasHiddenSelectedField(sectionField, entity, tableName)
        && shouldRenderField({ entity, tableName, operation, moduleName, getLinkedData, uiEntity }, sectionField)
        && shouldValidateField({ entity, tableName, moduleName }, sectionField, getData);
};

const buildValidationStructure = (sections, getFieldInfo, getData, props, errorMessages, state, entityWindowData) => {
    const { tableName, entity, uiEntity, operation, moduleName, window } = entityWindowData;

    let entityFormStructure = {};
    let rules = {};

    sections.forEach(section => {
        const { sectionType } = section;

        if (sectionType === FIELDS_LIST_SECTION_TYPE || CRITERIA_BUDGET_SECTIONS.includes(sectionType)) {
            const fieldsInSection = section.fields.reduce((accumulator, field) => {
                const nestedFields = field.fields || [];

                nestedFields.length > 0 && isDependantListControl(field)
                    ? nestedFields.forEach(nestedField => {
                        accumulator = { ...accumulator, [nestedField.name]: nestedField };
                    })
                    : accumulator = { ...accumulator, [field.name]: field };

                return accumulator;
            }, {});

            for (let fieldName in fieldsInSection) {
                const fieldInfo = getFieldInfo(tableName, fieldName);
                const sectionField = fieldsInSection[fieldName];

                if (shouldAddFieldToEntityStructure(fieldInfo, sectionField, getData, props.getLinkedData, { entity, tableName, operation, moduleName, uiEntity })) {
                    rules[fieldName] = getFieldRules(fieldInfo, sectionField, getFieldInfo, entity, undefined, errorMessages, getData);
                    entityFormStructure[fieldName] = getFormField(props, { ...fieldInfo, ...sectionField }).value;
                }
            }
        } else if (sectionType === REQUIREMENTS_SECTION_TYPE) {
            const { entity, tableDataEntryGuid: entityId } = props;

            if (getIsCriteriaRole(entity)) {
                const criteriaSectionsFields = getCriteriaSectionsFieldsSelector(state, entityId);
                const requirementsSectionInReadOnlyMode = getRequirementsSectionIsReadOnlyMode({
                    state,
                    entityId,
                    statusId: entity[ROLEREQUEST_FIELDS.STATUS_GUID],
                    entityWindow: window
                });

                if (!requirementsSectionInReadOnlyMode) {
                    Object.keys(criteriaSectionsFields).forEach(criteriaSection => {
                        criteriaSectionsFields[criteriaSection]
                            .filter(criteriaField => !criteriaField.isHidden)
                            .forEach(criteriaField => {
                                const { fieldName, value } = criteriaField;

                                entityFormStructure[criteriaField.fieldName] = { items: value };
                                rules[fieldName] = getCriteriaFieldRules(criteriaField, errorMessages);
                            });
                    });
                }
            }
        } else if (sectionType === TAB_SECTION_TYPE && (section.subSections || []).length > 0) {
            const tabStructure = buildValidationStructure(section.subSections, getFieldInfo, getData, props, errorMessages, state, entityWindowData);

            rules = { ...rules, ...tabStructure.rules };
            entityFormStructure = { ...entityFormStructure, ...tabStructure.entityFormStructure };
        } else if (sectionType === TAB_SECTION_TYPE && (section.subSectionItems || []).length > 0) {
            const tabStructure = buildValidationStructure(section.subSectionItems, getFieldInfo, getData, props, errorMessages, state, entityWindowData);

            rules = { ...rules, ...tabStructure.rules };
            entityFormStructure = { ...entityFormStructure, ...tabStructure.entityFormStructure };
        }
    });

    return { rules, entityFormStructure };
};

const getEntityWindowStructureAndRules = (state, moduleName, props, tableName, entity) => {
    const { getEntityWindowSectionsSelector } = instanceSelectorsWindowAliasMap[moduleName].getSelectors();
    const { entityWindow, resourceSkills, skillStructure, plannerPage } = state;

    const getData = dataSelectorWindowAliasMap[moduleName] ? dataSelectorWindowAliasMap[moduleName](state) : () => () => null;
    const getFieldInfo = getFieldInfoSelector(state);
    const errorMessages = getErrorStaticMessagesSelector(state);
    const window = entityWindow.window[moduleName];
    const uiEntity = window.uiEntity;
    const sections = getEntityWindowSectionsSelector({
        entityWindow: window,
        sections: entityWindow.settings[moduleName].sections,
        resourceSkills,
        skillStructure,
        applicationSettings: state.applicationSettings,
        moduleName,
        adminSetting: state.adminSetting,
        [MARKETPLACE_PAGE_ALIAS]: state[MARKETPLACE_PAGE_ALIAS],
        plannerPage
    });

    const operation = getEntityWindowOperation(state, moduleName);
    const entityWindowData = { tableName, entity, uiEntity, operation, moduleName, window };

    const { rules, entityFormStructure } = buildValidationStructure(sections, getFieldInfo, getData, props, errorMessages, state, entityWindowData);

    return { entityFormStructure, rules };
};

const getValidationProps = (state, moduleName, tableDataEntryGuid, isBatch) => {
    const { getLinkedDataWrappedSelector, getMultiValueLinkedFieldListItemWrappedSelector } = instanceSelectorsWindowAliasMap[moduleName].getSelectors();
    const { entityWindow } = state;
    const currentWindow = entityWindow.window[moduleName];

    const getFieldInfo = getFieldInfoSelector(state);

    const getMultiValueLinkedFieldListItem = getMultiValueLinkedFieldListItemWrappedSelector({
        tableStructure: getTableStructure(state),
        dataCollections: getPageTableDatasSelector(state),
        primaryTableName: currentWindow.tableName
    });

    const getLinkedData = getLinkedDataWrappedSelector({
        tableStructure: getTableStructure(state),
        primaryTableName: currentWindow.tableName,
        autoComplete: entityWindow.autoComplete[moduleName],
        dataCollections: getPageTableDatasSelector(state)
    });

    const window = isBatch ? currentWindow.windows[tableDataEntryGuid] : currentWindow;

    return {
        entity: window.entity,
        uiEntity: window.uiEntity,
        getLinkedData,
        getMultiValueLinkedFieldListItem,
        getFieldInfo,
        autoComplete: entityWindow.autoComplete[moduleName],
        tableDataEntryGuid
    };
};

const getCriteriasForRoleQuerySelection = (entityId) => {

    return {
        fields: [],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [{
                field: ROLEREQUEST_FIELDS.GUID,
                operator: 'Equals',
                value: entityId,
                tableName: TABLE_NAMES.ROLEREQUEST,
                parameters: {},
                isNot: false
            }]
        },
        order: {
            orderFields: [
                {
                    order: 'Descending',
                    field: ROLEREQUEST_FIELDS.CREATEON
                }
            ]
        },
        getCriterias: true
    };
};

export const formValidationMapAction = (state$, action) => {
    const actionFromSubmit = mapEntityWindowSubmitAction(state$, action);

    return validateFormAndGetAction$(state$, actionFromSubmit);
};

export const createEntityWindowSubmitInsertEpic = (alias, successActionHandler, errorActionHandler) => {
    return createAPICallEpic(
        alias,
        getApiCallEpicConfig(
            actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
            tableDataApiName,
            insertTableDataRequest$,
            successActionHandler,
            errorActionHandler,
            null,
            formValidationMapAction
        )
    );
};

export const createEntityWindowBatchInsertEpic = (alias, successActionHandler, errorActionHandler) => {
    const submitEpic = createAPICallEpic(
        alias,
        getApiCallEpicConfig(
            actionTypes.ENTITY_WINDOW.BATCH_SUBMIT_INSERT,
            tableDataApiName,
            batchInsertTableDataRequest$,
            successActionHandler,
            errorActionHandler,
            null,
            formValidationMapAction,
            (data) => data,
            true
        )
    )();

    const resubmitEpic = createAPICallEpic(
        alias,
        getApiCallEpicConfig(
            actionTypes.ENTITY_WINDOW.BATCH_RESUBMIT_INSERT,
            tableDataApiName,
            batchInsertTableDataRequest$,
            successActionHandler,
            errorActionHandler,
            null,
            null,
            (data) => data,
            true
        )
    )();

    return combineEpics(
        submitEpic,
        resubmitEpic
    );
};

const getPatchMultipleDataSuccessAction = (page) => {
    let result = empty;

    if (page === PLANNER_PAGE_ALIAS) {
        result = patchMultipleGrouppedTableDataSuccess;
    } else if (page === ROLE_INBOX_PAGE_ALIAS) {
        result = patchMultiplePagedDataSuccess;
    }

    return result;
};

export const entityWindowCreateRolegroupBookingRequestEpic = () => {
    const createDraftRoleSuccessAndProgressActions = successActionBuilder => (alias, metaData, response) => {
        const actions = [successActionBuilder(alias, metaData, response)];
        const { tableData } = metaData;
        const roleGuidsToProgress = [];
        const avatarGuids = [];
        const tableDataIsArray = Array.isArray(tableData);
        if (Array.isArray(response)) {
            response
                .filter(roleResponse => roleResponse.type === SUCCESS_STATUS)
                .forEach(roleResponse => {
                    const roleData = tableDataIsArray ? tableData[response.indexOf(roleResponse)] : tableData;
                    roleGuidsToProgress.push({ roleId: roleResponse.id, roleData });
                    avatarGuids.push(roleResponse.id);
                });
        } else {
            roleGuidsToProgress.push({ roleId: response });
        }
        actions.push(loadAvatars(avatarGuids, AVATAR_SIZES.TINY));
        actions.push(attemptRoleProgressToRequested(roleGuidsToProgress));
        actions.push(entityWindowClose(metaData.moduleName));

        return actions;
    };

    const createRoleInboxDraftRolesEpic = createAPICallEpic(
        TABLE_NAMES.ROLEREQUEST,
        getApiCallEpicConfig(
            actionTypes.ENTITY_WINDOW.BATCH_SUBMIT_INSERT_REQUEST,
            API_KEYS.ROLE_REQUEST_API_KEY,
            saveRoleRequestData$,
            createDraftRoleSuccessAndProgressActions(entityWindowSubmitInsertTableDataBatchSuccess),
            entityWindowSubmitInsertTableDataBatchError,
            null,
            formValidationMapAction,
            undefined,
            true
        )
    )();

    const apiFiltered$ = (api, payload) => {
        const { transitionPayload, nextState } = payload;
        const newRolesPayload = transitionPayload[TRANSITION_PAYLOAD_KEYS[TABLE_NAMES.ROLEREQUEST]].map(role => {
            return {
                [ROLE_ID]: role.roleId || role.id
            };
        });
        const newPayload = buildRoleTransitionRequestPayload(newRolesPayload, nextState);

        return performRoleTransition$(api, newPayload);
    };
    const createPlannerDraftRolesEpic = createAPICallEpic(
        PLANNER_ROLEREQUESTS_ALIAS,
        getApiCallEpicConfig(
            actionTypes.ENTITY_WINDOW.BATCH_SUBMIT_INSERT_REQUEST,
            API_KEYS.ROLE_REQUEST_API_KEY,
            saveRoleRequestData$,
            createDraftRoleSuccessAndProgressActions(entityWindowSubmitInsertGrouppedDataBatchSuccess),
            entityWindowSubmitInsertGrouppedDataBatchError,
            null,
            formValidationMapAction,
            undefined,
            true
        )
    )();

    const getRoleCarouselData = (rolesPayload, progressResponse, responseIndex, state) => {
        const { JOB_GUID: rolerequest_job_guid, ROLE_GROUP_GUID, RESOURCE_GUID: rolerequest_resource_guid, STATUS } = ROLEREQUEST_FIELDS;
        const { RESOURCE, JOB, ROLEREQUESTGROUP } = TABLE_NAMES;

        const getEntryData = getPageEntryDataSelector(state);

        const isDraft = progressResponse.type === BAD_REQUEST_STATUS;
        const rolerequestgroup_description_key = ROLEREQUESTGROUP_FIELDS.DESCRIPTION;
        const currentRole = rolesPayload[responseIndex] || {};
        const currentRoleData = currentRole.roleData;

        const job_guid_value = currentRoleData[rolerequest_job_guid];
        const jobEntity = job_guid_value ? getEntryData(JOB, job_guid_value) : {};
        const job_description = jobEntity[JOB_DESCRIPTION];

        const resource_guid_value = currentRoleData[rolerequest_resource_guid];
        const resourceEntity = resource_guid_value ? getEntryData(RESOURCE, resource_guid_value) : {};
        const resource_description = resourceEntity[RESOURCE_DESCRIPTION] || null;

        const rolerequestgroup_guid = currentRoleData[ROLE_GROUP_GUID];
        const rolegroupEntity = rolerequestgroup_guid ? getEntryData(ROLEREQUESTGROUP, rolerequestgroup_guid) : {};

        const { ungrouped } = getRoleCarouselDefaultTranslationSelector(state);

        const rolerequestgroup_description = rolegroupEntity[rolerequestgroup_description_key]
            || ungrouped
            || UNASSIGNED_ROLE_GROUP_NAME;

        const roleStatusDescription = isDraft ? ROLE_ITEM_STATUS_KEYS.DRAFT : ROLE_ITEM_STATUS_KEYS.REQUESTED;

        const roleData = {
            ...currentRoleData,
            [STATUS]: roleStatusDescription,
            [JOB_DESCRIPTION]: job_description,
            [rolerequestgroup_description_key]: rolerequestgroup_description,
            [RESOURCE_DESCRIPTION]: resource_description,
            [RESOURCE_GUID]: resource_guid_value
        };

        return {
            ...progressResponse,
            roleData
        };
    };

    const progressDraftRoleToRequestedEpic = createAPICallEpic(null, {
        action: actionTypes.ATTEMPT_ROLEREQUEST_BOOKING_REQUEST_PROGRESS_TO_REQUESTED,
        apiName: API_KEYS.ROLE_REQUEST_API_KEY,
        apiHandler: apiFiltered$,
        responseDataHandler: (response, payload, state) => ({ response, payload, state }),
        successActionHandler: (alias, metaData, { response, payload, state }) => {
            const pageAlias = getCurrentPageAliasSelector(state);
            const funcAlias = ROLES_MODAL.CREATE_AND_REQUEST_ROLES_DATA_ALIAS;
            const tableName = TABLE_NAMES.ROLEREQUEST;
            const requestedStatusGuid = getTableDataRoleRequestStatusGuidSelector(state)(ROLE_ITEM_STATUS_KEYS.REQUESTED);
            const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);

            const errorsCollection = response.map((progressResponse, responseIndex) => {
                const { transitionPayload = {} } = payload;
                const roles = transitionPayload[TRANSITION_PAYLOAD_KEYS[TABLE_NAMES.ROLEREQUEST]];

                return getRoleCarouselData(roles, progressResponse, responseIndex, state);
            }).filter(progressResponse => progressResponse.type !== SUCCESS_STATUS);
            const totalCount = response.length;
            const failedCount = errorsCollection.length;

            if (failedCount === 0) {
                const records = response
                    .filter(progressResponse => progressResponse.type === SUCCESS_STATUS)
                    .map(roleResponse => ({ rolerequest_guid: roleResponse.id, [roleRequestStatusField.name]: requestedStatusGuid }));

                const barAlias = getRolePatchDataSuccessAliasByPage(state);
                const tableData = getFlatTableData(records, tableName, getFieldInfoWrapped);
                const tableDataGuid = getTableDataGuid(state, { payload: { tableName } });
                const roleGuids = response.map(item => item.id);

                const patchSuccessAction = getPatchMultipleDataSuccessAction(pageAlias);

                return [
                    patchSuccessAction(
                        barAlias,
                        {
                            tableDataGuid,
                            tableName,
                            tableData,
                            response: true
                        },
                        tableData
                    ),
                    loadUserEntityAccess(roleGuids, TABLE_NAMES.ROLEREQUEST)
                ];
            } else {
                const successCount = totalCount - failedCount;
                const context = getRolesTransitionPromptContext(funcAlias, errorsCollection, totalCount, successCount);

                return showRolesTransitionPrompt(pageAlias, {}, context, funcAlias);
            }

        }
    })();

    return combineEpics(
        createPlannerDraftRolesEpic,
        createRoleInboxDraftRolesEpic,
        progressDraftRoleToRequestedEpic
    );
};

export const createEntityWindowSubmitUpdateEpic = (alias, successActionHandler, errorActionHandler) => {
    return createAPICallEpic(
        alias,
        getApiCallEpicConfig(
            actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
            tableDataApiName,
            patchTableDataRequest$,
            successActionHandler,
            errorActionHandler,
            null,
            formValidationMapAction
        )
    );
};

export const createEntityWindowSubmitDeleteEpic = (alias, successActionHandler, errorActionHandler) => createAPICallEpic(
    alias,
    getApiCallEpicConfig(
        actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
        tableDataApiName,
        deleteTableDataRequest$,
        successActionHandler,
        errorActionHandler
    )
);

export const createFieldControlUpdateApplyEpic = (alias, successActionHandler, errorActionHandler) => createAPICallEpic(
    alias,
    getApiCallEpicConfig(
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUBMIT,
        tableDataApiName,
        patchTableDataRequest$,
        successActionHandler,
        errorActionHandler,
        null,
        mapEntityWindowFieldSubminAction
    )
);

const hasNestedFields = (field) => field.fields != null;

const getNonLinkedSelectionField = (fieldInfo, field, state, pageAlias) => {
    let selectionField = null;

    if (fieldInfo !== null && !fieldInfo.appInternalField) {
        if (fieldInfo.parameters) {
            if (pageAlias == ROLE_GROUP_DETAILS_PAGE) {
                selectionField = createRoleResourceFieldWithParams(fieldInfo, state);
            } else if (pageAlias == ROLE_INBOX_PAGE_ALIAS) {
                selectionField = createRoleInboxResourceFieldWithParams(fieldInfo, state);
            } else if (pageAlias == PLANNER_PAGE_ALIAS) {
                selectionField = createPlannerResourceSummaryFieldWithParams(fieldInfo, state);
            }
        } else {
            selectionField = {
                fieldName: field.requestFieldName || fieldInfo.name,
                fieldAlias: field.requestFieldName ? field.name : fieldInfo.name
            };
        }
    }

    return selectionField;
};

export const getSelectionFields = (fields = [], state) => {
    const selectionFields = [];
    const { tableStructure } = state.applicationSettings;

    fields.map((field) => {
        if (hasNestedFields(field)) {
            selectionFields.push(...getSelectionFields(field.fields, state));

            if (field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL) {
                selectionFields.push(...getSelectionFields([field.valueKey], state));
            } else if (field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DATE_RANGE_CONTROL) {
                selectionFields.push(...getSelectionFields([field.startDateField, field.endDateField], state));
            }
        } else {
            if (field.type !== ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL) {
                const fieldInfo = getFieldInfo(tableStructure, field.table, field.name);
                const currentPageAlias = getCurrentPageAliasSelector(state);
                const selectionField = getNonLinkedSelectionField(fieldInfo, field, state, currentPageAlias);
                selectionField && selectionFields.push(selectionField);
            }
        }
    });

    return selectionFields;
};

const getSectionSpecificInitialSelectionFields = (sections, alias, operation, state) => {
    const initialSelectionFields = [];

    sections
        .filter(section => !isExcludedSectionModule(alias, operation, section.excludeSectionModules))
        .forEach(section => {
            (section.subSectionItems || []).length && initialSelectionFields.push(...getSectionSpecificInitialSelectionFields(section.subSectionItems, alias, operation, state));
            switch (section.sectionType) {
                case FIELDS_LIST_SECTION_TYPE:
                case MILESTONES_SECTION_TYPE:
                    initialSelectionFields.push(...getSelectionFields(section.fields, state));
                    break;
                case RESOURCE_SUMMARY_SECTION_TYPE:
                    initialSelectionFields.push(...getResourceSummarySelectionFields(section.fields, state));
                    break;
                case OVERLAPPING_BOOKING_SECTION_TYPE:
                    initialSelectionFields.push(...getOverLappingSelectionFields(section.fields, state));
                    break;
                //TODO: criteria budget estimates field are to be added with estimation data loading PBI
                case CRITERIA_BUDGET_SECTION_TYPE:
                    initialSelectionFields.push(...getSelectionFields(section.fields, state));
                    initialSelectionFields.push({ fieldName: ROLEREQUEST_FIELDS.STATUS_GUID });
                    initialSelectionFields.push(...REQUIRED_ROLE_TOTALS_CALCULATION_FIELDS.map(field => { return { fieldName: field }; }));
                    initialSelectionFields.push({ fieldName: ROLEREQUEST_FIELDS.DESCRIPTION });
                    break;
                case CRITERIA_ESTIMATE_BUDGET_SECTION_TYPE: {
                    initialSelectionFields.push(...getSelectionFields(section.fields, state));
                    break;
                }
                case TAB_SECTION_TYPE: {
                    const { subSections } = section;

                    (subSections || []).length && initialSelectionFields.push(...getSectionSpecificInitialSelectionFields(subSections, alias, operation, state));
                    break;
                }
                default:
                    break;
            }
        });

    return initialSelectionFields;
};

export const getLoadEntitiesQuerySelection = (tableName, alias, entityIds, state, operation) => {
    const windowSettings = state.entityWindow.settings[alias];

    const entityWindowSections = windowSettings.sections[tableName];
    const entityWindowAdditionalEntityFields = windowSettings.additionalEntityFields[tableName];
    const entityWindowEntityFieldsByModuleName = (windowSettings.additionalEntityFieldsByTableName || {})[tableName];
    const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
    const entityTemplate = getEntityWindowTemplateSelector(state)(alias, tableName);
    const isEnabledBudgetHoursAndRevenue = getFeatureFlagSelector(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE)(state);

    const entityObjectsTemplate = Object.keys(entityTemplate).reduce((accumulator, key) => {
        const fieldInfo = getFieldInfoWrapped(tableName, key);

        if (fieldInfo && fieldInfo.defaultValue) {
            accumulator.push({ fieldName: key });
        } else if (tableName === TABLE_NAMES.BOOKING && fieldInfo && fieldInfo.name === BOOKING_SERIES_GUID) {
            accumulator.push({ fieldName: key }); // Add bookingseries_guid to /filter request payload
        }

        return accumulator;
    }, []);

    let initialSelectionFields = [
        ...entityObjectsTemplate,
        ...getSelectionFields(entityWindowAdditionalEntityFields, state)
    ];

    initialSelectionFields.push(...getSelectionFields(entityWindowEntityFieldsByModuleName, state));

    initialSelectionFields.push(
        ...getSectionSpecificInitialSelectionFields(entityWindowSections, alias, operation, state)
    );

    entityWindowSections
        .forEach(section =>
            initialSelectionFields.push(...getSelectionFields(section.requestedfields, state)));

    const selectionFields = [
        { fieldName: `${tableName}_guid` },
        ...initialSelectionFields,
        ...getLinkedSelectionFields(tableName, initialSelectionFields, getFieldInfoWrapped),
        ...getCustomLookupSelectionFields(tableName, initialSelectionFields, getFieldInfoWrapped)
    ];

    let requestFields = uniqBy(selectionFields, 'fieldName');

    if (!isEnabledBudgetHoursAndRevenue) {
        const excludeFields = [
            jobHoursBudgetField.name,
            jobTotalHoursBookedField.name,
            jobHoursPercentageBudgetField.name,
            jobProfitMarginField.name,
            jobMarginPercentageTargetField.name,
            jobProfitMarginTargetField.name,
            jobRevenuePercentageTargetField.name
        ];
        requestFields = requestFields.filter(field => !excludeFields.includes(field.fieldName));
    }

    return {
        fields: requestFields,
        filter: {
            filterGroupOperator: 'And',
            filterLines: [{
                field: `${tableName}_guid`,
                operator: 'Contains',
                value: entityIds
            }]
        }
    };
};

export const loadEntitiesRequest$ = (tableName, querySelection, apis) => {
    return apis[API_KEYS.TABLE_DATA].getTableData$(tableName, querySelection);
};

export const loadRoleEntitiesRequest$ = (querySelection, apis) => {
    const roleCriteriaQuerySelection = { ...querySelection, getCriterias: true };

    return apis[API_KEYS.ROLE_REQUEST_API_KEY].getRoles$(roleCriteriaQuerySelection);
};

const getOverLappingSelectionFields = (fields = [], state) => {
    const selectionFields = [];
    const { tableStructure } = state.applicationSettings;

    fields.map((field) => {
        const fieldInfo = getFieldInfo(tableStructure, field.table, field.name);
        const currentPageAlias = getCurrentPageAliasSelector(state);
        const selectionField = getNonLinkedSelectionField(fieldInfo, field, state, currentPageAlias);
        selectionField && selectionFields.push(selectionField);
    });

    return selectionFields;
};

export const getResourceSummarySelectionFields = (fields = [], state) => {
    const selectionFields = [];
    const { tableStructure } = state.applicationSettings;

    fields.map((field) => {
        if (hasNestedFields(field)) {
            selectionFields.push(...getSelectionFields(field.fields, state));

            if (field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL) {
                selectionFields.push(...getSelectionFields([field.valueKey], state));
            } else if (field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DATE_RANGE_CONTROL) {
                selectionFields.push(...getSelectionFields([field.startDateField, field.endDateField], state));
            }
        } else {
            if (field.type !== ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL) {
                const fieldInfo = getFieldInfo(tableStructure, field.table, field.name);
                const currentPageAlias = getCurrentPageAliasSelector(state);
                const selectionField = getNonLinkedSelectionField(fieldInfo, field, state, currentPageAlias);
                selectionField && selectionFields.push(selectionField);
            }
        }
    });

    return selectionFields;
};

export const getProcessedEntityData = (responseEntity, tableData, tableName) => {
    const appInternalFieldsData = getAppFieldsData(tableName, responseEntity);

    return {
        ...tableData,
        ...appInternalFieldsData
    };
};

export const getAdditionalLoadEntityActions = (state, tableName, moduleName, entityIds = [], additionalResponseData = [], response = []) => {
    let actions = [];

    if (tableName === TABLE_NAMES.ROLEREQUEST) {
        const expandedCriteria = expandCriteriaValues(additionalResponseData, state);

        const mappedCriterias = entityIds.reduce((accumulator, guid, index) => {
            return {
                ...accumulator,
                [guid]: {
                    [CRITERIA_SECTIONS.MUST_MEET]: expandedCriteria[index]
                }
            };
        }, {});

        actions.push(
            of(populateCriteriasForRoles(entityIds, mappedCriterias)),
            of(expandRoleCriterias(moduleName)),
            of(processLoadedRoleAssignees(moduleName, response))
        );
    }

    return actions;
};

export const loadEntityWindowSingleDataResponseHandler = (tableName, response) => {
    let responseEntity;
    let additionalResponseData;

    if (tableName === TABLE_NAMES.ROLEREQUEST) {
        const { body, criterias } = response[0];
        responseEntity = body;
        additionalResponseData = [criterias];
    } else {
        [responseEntity, ...additionalResponseData] = response;
    }

    return {
        responseEntity,
        additionalResponseData
    };
};

export const loadEntityWindowData = (alias, pageTableNameAlias) => {
    return (action$, state$, { apis }) => {
        const { ENTITY_WINDOW: { OPEN, EDIT } } = actionTypes;

        const loadEntityFields$ = action$
            .ofType(
                `${OPEN}_${alias}`,
                `${EDIT}_${alias}`
            ).pipe(
                filter(({ payload: { lazyLoadEntityData } }) => lazyLoadEntityData),
                switchMap(({ payload: { tableName, entityId, operation } }) => {
                    const state = state$.value;
                    const querySelection = getLoadEntitiesQuerySelection(tableName, alias, [entityId], state, operation);

                    return tableName === TABLE_NAMES.ROLEREQUEST
                        ? loadRoleEntitiesRequest$(querySelection, apis)
                        : loadEntitiesRequest$(tableName, querySelection, apis);
                },
                (action, r) => [action, r])
            );

        return loadEntityFields$.pipe(
            mergeMap(([action, response]) => {
                if (response.status === ERROR_STATUS) {
                    return empty();
                }

                const { payload } = action;
                const { tableName, operation, moduleName, entityId } = payload;
                const handledResponse = loadEntityWindowSingleDataResponseHandler(tableName, response);
                const { responseEntity, additionalResponseData } = handledResponse;
                const lowerCaseResponseEntity = lowerCaseObjectKeys(responseEntity);
                const { repeatBookingDialog: { bookingSeries, editType } } = state$.value;

                const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state$.value), tableName, fieldName);

                const linkedTableDatas = getLinkedTableData([lowerCaseResponseEntity], tableName, getFieldInfoWrapped);
                const linkedTableDatasLoadedActions = getTableDataLoadedActionsForPageSelector(state$.value)(pageTableNameAlias, linkedTableDatas);
                const tableData = getFlatTableData([lowerCaseResponseEntity], tableName, getFieldInfoWrapped);
                const processedEntityFieldsData = getProcessedEntityData(lowerCaseResponseEntity, tableData[0], tableName);

                let chain = [];
                linkedTableDatasLoadedActions.forEach(action => chain.push(of(action)));

                const { value: { entityWindow, navigation = {} } } = state$;
                const { subPage } = navigation;

                const { activeTab } = entityWindow.window[alias];
                if (activeTab === ENTITY_WINDOW_TAB_KEYS.HISTORY) {
                    chain.push(of(loadAudit(tableName, entityId)));
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE, alias)) {
                    chain.push(of(loadPagedComments(moduleName, tableName, entityId))); // could pass pageSize from sections config
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.ROLE_GROUP_LIST_SECTION_TYPE, alias) &&
                    tableName === TABLE_NAMES.JOB &&
                    moduleName === ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE) {
                    chain.push(of(loadRoleRequestGroupList([entityId])));
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.ROLE_LIST_SECTION_TYPE, alias) &&
                    tableName === TABLE_NAMES.ROLEREQUESTGROUP &&
                    (moduleName === ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE && subPage === ROLE_GROUP_LIST_PAGE) ||
                    roleListSectionModuleNames.includes(moduleName)) {
                    const addNewEntity = false;
                    const activeEntityIndex = 0;
                    const selectedTab = getSelectedDetailsPaneTabKey(state$.value);
                    const additionalProps = { rolerequestgroupGuid: entityId, selectedTab };
                    chain.push(of(loadRoleGroupDetails(addNewEntity, activeEntityIndex, additionalProps)));
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.RESOURCE_SUMMARY_SECTION_TYPE, alias) &&
                    tableName === TABLE_NAMES.RESOURCE) {
                    chain.push(of(loadAvatars([entityId], AVATAR_SIZES.SMALL)));
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE, alias, operation) &&
                    getSectionLicenseValueSelector(state$.value)(tableName, alias, ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE)) {
                    chain.push(of(loadAttachments(alias, tableName, entityId)));
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.WORK_HISTORY_SECTION_TYPE, alias)) {
                    chain.push(of(loadResourceWorkHistory(getWorkHistoryAlias(alias), entityId)));
                }

                chain.push(...getAdditionalLoadEntityActions(state$.value, tableName, moduleName, [entityId], additionalResponseData, response));

                if (tableName === TABLE_NAMES.BOOKING
                    && bookingSeries
                    && Object.keys(bookingSeries).length > 0
                    && editType === EDIT_REPEAT_BOOKING_TYPE.ALL) {
                    const booking = JSON.parse(bookingSeries.bookingseries_booking);
                    if (booking) {
                        processedEntityFieldsData[BOOKING_START] = booking[BOOKING_START];
                        processedEntityFieldsData[BOOKING_END] = booking[BOOKING_END];
                    }
                }

                return concat(
                    ...chain,
                    of(entityWindowUpdateEntity(alias, tableName, processedEntityFieldsData, operation, true))
                );
            })
        );
    };
};

const hasChangedDefaultValue = (entityTemplate) => {
    return (field) => {
        return hasDefaultValue(field) || (!hasDefaultValue(field) && hasDefaultValueInTemplate(entityTemplate, field.name));
    };
};

const getFieldsWithChangedDefaultValue = (allFields, entityTemplate) => {
    return allFields.filter(field => hasChangedDefaultValue(entityTemplate)(field));
};

const getDefaultValuesTableData = (defaultedFields = [], tableName) => {
    let defaultFieldsTableData = {};

    defaultedFields.forEach((field) => {
        if (isLookupField(tableName, field) && hasDefaultValue(field)) {
            const primaryFieldTableName = getFieldTableName(field, tableName);
            const optionFields = getEntityOptionFields(field.defaultValue, primaryFieldTableName);

            if (!defaultFieldsTableData[primaryFieldTableName]) {
                defaultFieldsTableData[primaryFieldTableName] = [optionFields];
            } else {
                defaultFieldsTableData[primaryFieldTableName].push(optionFields);
            }
        }
    });

    return defaultFieldsTableData;
};

const getDefaultValuesLoadActions = (defaultFieldsTableData, pageAlias) => {
    let defaultValuesLoadActions = [];

    if (!isEmpty(defaultFieldsTableData)) {
        const tableDatasAlias = getTableDatasAlias(pageAlias);

        Object.keys(defaultFieldsTableData).forEach((table) => {
            defaultValuesLoadActions.push(
                loadMoreTableDataSuccess(
                    tableDatasAlias,
                    { tableDataGuid: table, tableNames: [table] },
                    defaultFieldsTableData[table]
                )
            );
        });
    }

    return defaultValuesLoadActions;
};

const getSystemFieldNames = (tableName) => {
    return {
        updatedByGuid: `${tableName}_updatedby_resource_guid`,
        updatedByValue: `${tableName}_updatedby_resource_guid.resource_description`,
        updatedOn: `${tableName}_updatedon`
    };
};

const buildSystemInfoFieldsStructure = (fields, { updatedByGuid, updatedByValue, updatedOn }) => {
    return {
        [updatedByGuid]: {
            id: fields[updatedByGuid],
            value: fields[updatedByValue]
        },
        [updatedOn]: fields[updatedOn]
    };
};

const fieldInfoUiDefaultValues = {
    [ROLEREQUEST_FIELDS.DIARY_GROUP]: {
        fieldName: ROLEREQUEST_FIELDS.DIARY_GROUP,
        fieldTableName: TABLE_NAMES.ROLEREQUEST,
        primaryTable: TABLE_NAMES.DIARYGROUP,
        value: STANDARD_COMPANY_DIARY
    },
    [ROLEREQUEST_FIELDS.CHARGE_RATE_GUID]: {
        fieldName: ROLEREQUEST_FIELDS.CHARGE_RATE_GUID,
        fieldTableName: TABLE_NAMES.ROLEREQUEST,
        primaryTable: TABLE_NAMES.CHARGERATE,
        value: STANDARD_CHARGE_RATE
    },
    [BOOKINGTYPE_GUID]: {
        fieldName: BOOKINGTYPE_GUID,
        fieldTableName: TABLE_NAMES.BOOKING,
        primaryTable: TABLE_NAMES.BOOKINGTYPE
    }
};

const buildUiDefaultValuesSelection = (data) => {
    const { value, primaryTable } = data;

    const filterLines = [];

    value && filterLines.push({
        field: `${primaryTable}_description`,
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        value
    });

    return {
        fields: [{ fieldName: `${primaryTable}_guid` }, { fieldName: `${primaryTable}_description` }],
        filter: {
            filterGroupOperator: 'And',
            filterLines
        }
    };
};

const pageOpenActions = Object.values(actionTypes.PAGE_ACTIONS.OPEN);

export const updateEntityTemplatesEpic = () => {
    const modulesToUpdate = [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM,
        ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT,
        ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL,
        ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL,
        ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL,
        ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL,
        ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED,
        ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL
    ];

    return (action$, state$, { apis }) => action$
        .ofType(actionTypes.LOAD_TABLE_DATA_FIELD_INFOS_SUCCESS)
        .pipe(
            //Templates will be set once for all EW app load and observable will be unsubscribed after
            take(1),
            switchMap(action =>
                action$.ofType(...pageOpenActions)
                    .pipe(
                        //Making sure this will execute just once and will not be listened to after for other page opens
                        take(1),
                        //Delaying so the page open logic is initialized first
                        delay(10),
                        mergeMap(_ => {
                            const { mappedFieldInfos } = action.payload;
                            let defaultUiData = [];

                            Object.values(fieldInfoUiDefaultValues).forEach(fieldData => {
                                const { primaryTable, fieldTableName } = fieldData;

                                if (fieldTableName in mappedFieldInfos) {
                                    const selection = buildUiDefaultValuesSelection(fieldData);

                                    const loadData$ = apis[API_KEYS.TABLE_DATA].getTableData$(primaryTable, selection).pipe(
                                        map(response => {
                                            return { [primaryTable]: { data: Array.isArray(response) ? response : [] } };
                                        })
                                    );

                                    defaultUiData.push(loadData$);
                                }
                            });

                            return forkJoin(defaultUiData).pipe(
                                map(data => {
                                    const additionalData = data.reduce((accumulator, responseData) => {
                                        const table = Object.keys(responseData)[0];

                                        accumulator[table] = responseData[table];

                                        return accumulator;
                                    }, {});

                                    return { action, additionalData };
                                })
                            );
                        }),
                        map(({ action, additionalData }) => {
                            let chain = [];
                            const { mappedFieldInfos } = action.payload;
                            let valuesToAddToTableData = {};

                            modulesToUpdate.forEach(alias => {
                                Object.keys(mappedFieldInfos).forEach((tableName) => {
                                    const tableFields = mappedFieldInfos[tableName];
                                    const entityTemplate = getEntityWindowTemplateSelector(state$.value)(alias, tableName);
                                    const fieldsWithDefaultValue = getFieldsWithChangedDefaultValue(cloneDeep(tableFields), entityTemplate);
                                    const defaultFieldsTableData = getDefaultValuesTableData(fieldsWithDefaultValue, tableName);

                                    Object.keys(defaultFieldsTableData).forEach(tableName => {
                                        if (!valuesToAddToTableData[tableName]) {
                                            valuesToAddToTableData[tableName] = [];
                                        }

                                        defaultFieldsTableData[tableName].forEach(newDefaultValueData => {
                                            const valueHasNotBeenAdded = !valuesToAddToTableData[tableName].some(defaultValueData =>
                                                isEqual(newDefaultValueData, defaultValueData));

                                            if (valueHasNotBeenAdded) {
                                                valuesToAddToTableData[tableName].push(newDefaultValueData);
                                            }
                                        });
                                    });

                                    chain.push(
                                        entityWindowUpdateEntityTemplate(
                                            fieldsWithDefaultValue,
                                            tableName,
                                            alias,
                                            additionalData
                                        )
                                    );
                                });
                            });

                            const valuesToAddToTableDataCopy = cloneDeep(valuesToAddToTableData);

                            const mergedDefaultValues = mergeWith(
                                valuesToAddToTableDataCopy,
                                additionalData,
                                (objValue, srcValue, key) => unionBy([...(objValue || []), ...(srcValue.data || [])], `${key}_guid`)
                            );

                            chain.push(
                                ...getDefaultValuesLoadActions(mergedDefaultValues, PLANNER_PAGE_ALIAS),
                                ...getDefaultValuesLoadActions(mergedDefaultValues, JOBS_PAGE_ALIAS),
                                ...getDefaultValuesLoadActions(mergedDefaultValues, ROLE_GROUP_DETAILS_PAGE),
                                ...getDefaultValuesLoadActions(mergedDefaultValues, ROLE_INBOX_PAGE_ALIAS),
                                ...getDefaultValuesLoadActions(mergedDefaultValues, NOTIFICATIONS_PAGE_ALIAS),
                                ...getDefaultValuesLoadActions(mergedDefaultValues, PROFILE_PAGE_ALIAS)
                            );

                            return batchActions(chain);
                        })
                    ))
        );
};

const getPlannerEntityWindowOpenActions = (moduleName, tableName, state) => {
    let actions = [hideContextMenu(PLANNER_PAGE_ALIAS)];

    if (moduleName === ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL) {
        actions.push(entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED));

        if (tableName === TABLE_NAMES.BOOKING &&
            true == isEntityWindowSectionContentHidden(getEntityWindowSectionByKey(state, moduleName, tableName, ENTITY_WINDOW_SECTION_KEYS.BUDGET))) {
            actions.push(entityWindowHideSectionContent(moduleName, tableName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, false));
        }
    }

    return actions;
};

const getRoleInboxPageEntityWindowOpenActions = (moduleName, tableName, state, actionPayload) => {
    let actions = [];

    if (moduleName === ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL) {
        actions.push(entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED));
    }

    if (moduleName === ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL || moduleName === ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL) {
        const { entityId } = actionPayload;
        const dataCollections = getPageTableDatasSelector(state);
        const roleEntity = getData(dataCollections, TABLE_NAMES.ROLEREQUEST, entityId);
        const messageOptions = getEntityWindowPublishRoleMessagesOptions(state)({ roleId: entityId, roleEntity, moduleName }, getIsCriteriaRole(roleEntity));

        actions.push(entityWindowUpdateMessages(moduleName, tableName, messageOptions));
    }

    return actions;
};

const getCreateTemplateEntityWindowOpenActions = () => {
    return [entityWindowClose(ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL)];
};

const getTableViewPageEntityWindowOpenActions = (moduleName, tableName, state) => {
    let actions = [hideContextMenu(TABLE_VIEW_PAGE_ALIAS)];

    if (moduleName === ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL) {
        actions.push(entityWindowClose(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED));

        if (tableName === TABLE_NAMES.BOOKING &&
            true == isEntityWindowSectionContentHidden(getEntityWindowSectionByKey(state, moduleName, tableName, ENTITY_WINDOW_SECTION_KEYS.BUDGET))) {
            actions.push(entityWindowHideSectionContent(moduleName, tableName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, false));
        }
    }

    return actions;
};

export const openEntityWindowEpic = (alias, getFollowUpOpenActions = () => []) => {
    const { ENTITY_WINDOW: { OPEN, EDIT } } = actionTypes;

    return (action$, state$) =>
        action$
            .ofType(`${OPEN}_${alias}`, `${EDIT}_${alias}`)
            .pipe(
                switchMap((action) => {
                    const { tableName, operation, moduleName, activeTab, entityId } = action.payload;
                    const state = state$.value;
                    const staticMessages = getEntityWindowMessagesSelector(state)(moduleName);
                    const messageOptions = buildEntityWindowMessageOptions(action.payload, state);

                    const actions = [
                        entityWindowSetMessages(alias, tableName, { operation, staticMessages, ...messageOptions }),
                        ...getFollowUpOpenActions(alias, tableName, state, action.payload)
                    ];

                    shouldSelectHistoryTabOnOpen(operation, tableName, activeTab)
                        && actions.push(entityWindowSetActiveTab(alias, activeTab, entityId));

                    return from(actions);
                })
            );
};

const loadEntityWindowMultipleDataResponseHandler = (tableName, response) => {
    let responseEntities = [];
    let additionalResponseData = [];

    if (tableName === TABLE_NAMES.ROLEREQUEST) {
        response.forEach(({ body, criterias }) => {
            responseEntities.push(body);
            additionalResponseData.push(criterias);
        });
    } else {
        responseEntities = response;
    }

    return {
        responseEntities,
        additionalResponseData
    };
};

export const loadMultipleEntityWindowData = (alias, pageTableNameAlias) => {
    return (action$, state$, { apis }) => {
        const { ENTITY_WINDOW: { OPEN_FOR_MULTIPLE /*, EDIT */ } } = actionTypes;

        const loadEntityFields$ = action$
            .ofType(
                `${OPEN_FOR_MULTIPLE}_${alias}`
                // `${EDIT}_${alias}`
            ).pipe(
                filter(({ payload: { lazyLoadEntityData } }) => lazyLoadEntityData),
                switchMap(({ payload: { tableName, entityIds, operation } }) => {
                    const querySelection = getLoadEntitiesQuerySelection(tableName, alias, entityIds, state$.value, operation);

                    return tableName === TABLE_NAMES.ROLEREQUEST
                        ? loadRoleEntitiesRequest$(querySelection, apis)
                        : loadEntitiesRequest$(tableName, querySelection, apis);
                },
                (action, r) => [action, r])
            );

        return loadEntityFields$.pipe(
            mergeMap(([action, response]) => {
                if (response.status === ERROR_STATUS) {
                    return empty();
                }

                const { payload } = action;
                const { tableName, operation, moduleName, activeEntity, entityIds } = payload;

                const { responseEntities, additionalResponseData } = loadEntityWindowMultipleDataResponseHandler(tableName, response);

                const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state$.value), tableName, fieldName);

                const linkedTableDatas = getLinkedTableData(responseEntities, tableName, getFieldInfoWrapped);
                const linkedTableDatasLoadedActions = getTableDataLoadedActionsForPageSelector(state$.value)(pageTableNameAlias, linkedTableDatas);
                const tableDatas = getFlatTableData(responseEntities, tableName, getFieldInfoWrapped);
                const processedTableDatas = responseEntities.map((responseEntity, index) => getProcessedEntityData(responseEntity, tableDatas[index], tableName));

                let chain = [];
                linkedTableDatasLoadedActions.forEach(action => chain.push(of(action)));
                const window = state$.value.entityWindow.window[alias];
                const { showEditAllTab } = window;
                const entityGuidsInResponse = processedTableDatas.map((data) => data[`${tableName}_guid`]);
                const availableIds = entityIds.filter(entityId => entityGuidsInResponse.includes(entityId));
                let editAllEntity = {};

                if (tableName === TABLE_NAMES.ROLEREQUEST) {
                    const orderedIds = responseEntities.map(entity => entity.rolerequest_guid);
                    chain.push(...getAdditionalLoadEntityActions(state$.value, tableName, moduleName, orderedIds, additionalResponseData, response));
                }

                if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE, alias)) {
                    //We add this here because we can add and edit comments in the read only modals as well
                    chain.push(of(loadPagedComments(moduleName, tableName, activeEntity)));
                }

                if (!showEditAllTab) {
                    const { activeTab } = window;

                    if (activeTab === ENTITY_WINDOW_TAB_KEYS.HISTORY) {
                        chain.push(of(loadAudit(tableName, availableIds[0])));
                    }

                    if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.SKILLS_LIST_SECTION_TYPE, alias)) {
                        chain.push(of(loadResourceSkills(activeEntity)));
                    }

                    if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.ROLE_GROUP_LIST_SECTION_TYPE, alias) &&
                        tableName === TABLE_NAMES.JOB && moduleName === ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE) {
                        chain.push(of(loadRoleRequestGroupList(availableIds)));
                    }

                    if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.RESOURCE_SUMMARY_SECTION_TYPE, alias) &&
                        tableName === TABLE_NAMES.RESOURCE) {
                        chain.push(of(loadAvatars(availableIds, AVATAR_SIZES.SMALL)));
                    }

                    if (entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE, alias, operation) &&
                        getSectionLicenseValueSelector(state$.value)(tableName, alias, ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE)) {
                        chain.push(of(loadAttachments(alias, tableName, activeEntity)));
                    }
                } else if (tableName === TABLE_NAMES.ROLEREQUEST) {
                    editAllEntity = {
                        [roleRequestHasCriteriaField.name]: getIsCriteriaRole(responseEntities[0])
                    };
                }

                return concat(
                    ...chain,
                    of(entityWindowUpdateAllEntities(alias, tableName, availableIds, processedTableDatas, operation, editAllEntity))
                );
            })
        );
    };
};

const getLazyLoadActiveEntityDefaultSuccessActions = () => [];

const getLazyLoadManageTemplatesModalEntitySuccessActions = () => [of(expandRoleCriterias(ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL))];

export const lazyLoadActiveEntityData = (alias, getFollowUpSuccessActions = getLazyLoadActiveEntityDefaultSuccessActions) => {
    return (action$, state$, { apis }) => {
        return action$
            .ofType(`${actionTypes.ENTITY_WINDOW.SET_ACTIVE_ENTITY}_${alias}`)
            .pipe(
                filter((action) => {
                    const { activeEntity, windows } = state$.value.entityWindow.window[alias];
                    const { entityLoaded } = windows[activeEntity];

                    return !entityLoaded;
                }),
                switchMap((action) => {
                    const state = state$.value;
                    const { activeEntity, tableName, moduleName, operation } = state.entityWindow.window[alias];
                    let chain = [];

                    if (entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE, alias)) {
                        chain.push(of(loadPagedComments(moduleName, tableName, activeEntity)));
                    }

                    if (entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.SKILLS_LIST_SECTION_TYPE, alias)) {
                        chain.push(of(loadResourceSkills(activeEntity)));
                    }

                    if (entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.ROLE_GROUP_LIST_SECTION_TYPE, alias) &&
                        tableName === TABLE_NAMES.JOB && moduleName === ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE) {
                        chain.push(of(loadRoleRequestGroupList([activeEntity])));
                    }

                    if (entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.RESOURCE_SUMMARY_SECTION_TYPE, alias) &&
                        tableName === TABLE_NAMES.RESOURCE) {
                        chain.push(of(loadAvatars([activeEntity], AVATAR_SIZES.SMALL)));
                    }

                    if (entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE, alias, operation) &&
                        getSectionLicenseValueSelector(state$.value)(tableName, alias, ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE)) {
                        chain.push(of(loadAttachments(alias, tableName, activeEntity)));
                    }

                    if (entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.WORK_HISTORY_SECTION_TYPE, alias)) {
                        chain.push(of(loadResourceWorkHistory(getWorkHistoryAlias(alias), activeEntity)));
                    }

                    chain.push(of(entityWindowLazyLoadActiveEntity(alias)));
                    chain.push(...getFollowUpSuccessActions());

                    return concat(...chain);
                })
            );
    };
};

//Note: the name of this epic is chosen considering its future role
export function buildEntityWindowSettingEpic() {

    const moduleNames = [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM,
        ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
        ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL,
        ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL,
        ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL,
        ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL,
        ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL,
        ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL
    ];

    const buildSectionsActions = (state, tableName, moduleName) => {
        const customFields = getFieldInfos(getTableStructure(state), tableName, getIsCustomField);
        const systemFieldInfos = getFieldInfos(getTableStructure(state), tableName, getIsSystemField);
        const hasCommentsSection = entityWindowHasSection(state, tableName, ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE, moduleName);
        const currentSettings = state.entityWindow.settings[moduleName] || {};
        const { skipSystemSections = false } = currentSettings;
        const currentSections = (currentSettings.sections || {})[tableName] || [];
        let sectionsCount = currentSections.length;
        const configPath = `sections/${tableName}`;
        let chain = [];

        if (!skipSystemSections && Object.keys(customFields).length > 0 && !(currentSections.some((section) => section.key === ADDITIONAL_DETAILS_KEY))) {
            const insertIndex = getNewSectionInsertIndex(sectionsCount, hasCommentsSection);
            sectionsCount++;
            chain.push(
                entityWindowAddSection(
                    ADDITIONAL_DETAILS_KEY,
                    ADDITIONAL_DETAILS_TITLE,
                    true,
                    customFields,
                    tableName,
                    insertIndex,
                    false,
                    moduleName,
                    ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE
                )
            );

            chain.push(
                addToTranslationConfig(
                    configPath,
                    getConfigChunk(ADDITIONAL_DETAILS_KEY, ENTITY_WINDOW_SECTION_TITLES.ADDITIONAL_INFO),
                    insertIndex,
                    `${moduleName}_settings`
                )
            );
        }

        if (!skipSystemSections && sectionsTableNames.includes(tableName) && Object.keys(systemFieldInfos).length > 0) {
            const systemInfoSectionFields = getSystemInfoSectionFields(tableName, systemFieldInfos);

            if (Object.keys(systemInfoSectionFields).length > 0 && !(currentSections.some((section) => section.key === ENTITY_WINDOW_SECTION_KEYS.SYSTEM_INFO))) {
                const insertIndex = getNewSectionInsertIndex(sectionsCount, hasCommentsSection);

                chain.push(
                    entityWindowAddSection(
                        ENTITY_WINDOW_SECTION_KEYS.SYSTEM_INFO,
                        ENTITY_WINDOW_SECTION_TITLES.SYSTEM_INFO,
                        true,
                        systemInfoSectionFields,
                        tableName,
                        insertIndex,
                        false,
                        moduleName,
                        ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE
                    )
                );

                chain.push(
                    addToTranslationConfig(
                        configPath,
                        getConfigChunk(ENTITY_WINDOW_SECTION_KEYS.SYSTEM_INFO, ENTITY_WINDOW_SECTION_TITLES.SYSTEM_INFO),
                        insertIndex,
                        `${moduleName}_settings`
                    )
                );
            }
        }

        return chain;
    };

    // Future consideration: we should build EW settings structure here, afterwards add custom fields section
    // we should pipe the following epic to the newly created one once it is being defined

    return (action$, state$) => action$
        .ofType(actionTypes.LOAD_TABLE_DATA_FIELD_INFOS_SUCCESS)
        .pipe(
            //Additional sections will be added once for all EW on app load and observable will be unsubscribed after
            take(1),
            switchMap(action =>
                action$.ofType(...pageOpenActions)
                    .pipe(
                        //Making sure aditional sections will not be re-populated on consequent page loads
                        take(1),
                        //Delaying so the page open logic is initialized first
                        delay(10),
                        mergeMap(_ => {
                            const { mappedFieldInfos } = action.payload;
                            const chain = [];
                            Object.keys(mappedFieldInfos).forEach((tableName) => {
                                moduleNames.forEach(module => chain.push(...buildSectionsActions(state$.value, tableName, module)));
                            });

                            return [batchActions(chain), delayedPopulateEntityWindowTranslationMessages()];
                        })
                    ))
        );
}

const getSimplifiedDialogMandatoryFields = (state, tableName, currentSections) => {
    let entityWindowMandatoryFields = {};
    let allMandatoryFields = getFieldInfos(getTableStructure(state), tableName, getIsMandatoryField) || {};

    if (tableName === TABLE_NAMES.BOOKING || tableName === TABLE_NAMES.ROLEREQUEST) {
        Object.keys(allMandatoryFields).forEach((key) => {
            const fieldInfo = allMandatoryFields[key];
            if (
                !getIsSystemReadOnlyField(fieldInfo)
                && !fieldInfo.readOnly
                && !getFieldExistInSections(fieldInfo.name, currentSections)
                && !getIsTimeAllocationField(fieldInfo.name, tableName)
                && !getIsDateRangeField(fieldInfo.name, tableName)
                && fieldInfo.name !== bookingStatusSectionField.name
            ) {
                entityWindowMandatoryFields[key] = fieldInfo;
            }
        });
    }

    return entityWindowMandatoryFields;
};

export function extendSimplifiedEntityWindowSectionsEpic() {
    const simplifiedModules = [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
        ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED
    ];

    return (action$, state$) => {
        return action$
            .ofType(actionTypes.LOAD_TABLE_DATA_FIELD_INFOS_SUCCESS)
            .pipe(
                //Simplified EW sections update only once when any page is opened, then unsubscribe
                take(1),
                mergeMap(action =>
                    action$.ofType(...pageOpenActions)
                        .pipe(
                            //Making sure simplified EW templates are never re-generated on consequent page open actions
                            take(1),
                            //Delaying so the page open logic is initialized first
                            delay(10),
                            map(_ => {
                                const { mappedFieldInfos } = action.payload;
                                const chain = [];

                                simplifiedModules.forEach(moduleName =>
                                    Object.keys(mappedFieldInfos).forEach((tableName) => {
                                        const currentSections = (((state$.value.entityWindow.settings || {})[moduleName] || {}).sections || {})[tableName] || [];
                                        const { dynamicLoadMandatoryFields } = state$.value.entityWindow.window[moduleName] || {};

                                        if (dynamicLoadMandatoryFields) {
                                            const mandatoryFields = getSimplifiedDialogMandatoryFields(state$.value, tableName, currentSections);
                                            if (Object.keys(mandatoryFields).length !== 0) {
                                                chain.push(entityWindowExtendSection(
                                                    ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
                                                    ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
                                                    mandatoryFields,
                                                    tableName,
                                                    moduleName
                                                ));
                                            }
                                        }
                                    }));

                                return batchActions(chain);
                            })
                        ))
            );
    };
}

export const onDetailsPaneSuccessEdit$ = (alias) => {
    return (action$, state$, { apis }) => {
        return action$
            .ofType(`${actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_SYSTEM_INFO}_${alias}`)
            .pipe(
                switchMap(({ payload }) => {
                    const { tableName, tableDataEntryGuid } = payload;
                    const { updatedByGuid, updatedByValue, updatedOn } = getSystemFieldNames(tableName);
                    const selection = {
                        'fields': [
                            {
                                fieldName: updatedByGuid
                            },
                            {
                                fieldName: updatedOn
                            },
                            {
                                fieldName: updatedByValue
                            }
                        ],
                        'filter': {
                            'filterGroupOperator': 'And',
                            'filterLines': [
                                {
                                    'field': `${tableName}_guid`,
                                    'operator': 'Contains',
                                    'value': [tableDataEntryGuid]
                                }]
                        }
                    };

                    return apis['tableData'].getFilterTableAccessData$(tableName, selection);
                },
                (action, result) => [action, result]),
                mergeMap(
                    ([action, result]) => {
                        let chain = [];
                        const { tableName } = action.payload;
                        const getFieldInfo = getFieldInfoSelector(state$.value);
                        const fields = buildSystemInfoFieldsStructure(result[0], getSystemFieldNames(tableName));

                        Object.keys(fields).forEach(fieldName => {
                            chain.push(of(entityWindowFieldChanged(alias, getFieldInfo(tableName, fieldName), fields[fieldName])));
                        });

                        return 0 < chain.length ? concat(...chain) : empty();
                    }
                )
            );
    };
};

export const createTranslateVisibleExplanationsEpic$ = () => {
    return (action$, state$) => {
        return action$
            .ofType(actionTypes.POPULATE_TRANSLATION_MESSAGES)
            .pipe(
                switchMap(() => {
                    let chain = [];
                    const { window } = state$.value.entityWindow;
                    lodashMap(ENTITY_WINDOW_MODULES, function (value) {
                        if (keys(window).includes(value) && window[value].visible) {
                            chain.push(of(setBookingWorkNonWorkDaysExplanation({ moduleName: value }))),
                            chain.push(of(setTimeAllocationReadOnlyExplanation({ moduleName: value })));
                        }
                    });

                    return 0 < chain.length ? concat(...chain) : empty();
                })
            );
    };
};

const ewSkillsActionFilter = ({ payload = {} }) => TABLE_NAMES.RESOURCE === payload.tableName;

export const entityWindowSubmitSkillsEpic$ = (action$, state$) => {
    return action$
        .ofType(
            `${SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${PLANNER_MASTER_REC_ALIAS}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
            `${actionTypes.BATCH_PATCH_GROUPED_TABLE_DATA}_${PLANNER_MASTER_REC_ALIAS}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.RESOURCE}`,
            `${SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`
        )
        .pipe(
            filter(ewSkillsActionFilter),
            mergeMap(({ payload }) => {
                const { tableDataEntryGuid, isBatch, patchData } = payload;
                let skillUpdateAction = [];
                if (isBatch) {
                    const tableDataEntryGuids = patchData.map((entry) => entry.tableDataEntryGuid);
                    tableDataEntryGuids.forEach((entryGuid) => {
                        if (getResourceSkillsCanSaveSelector(state$.value)(entryGuid)) {
                            skillUpdateAction.push(editResourceSkillsWindowUpdateSkills(entryGuid));
                        }
                    });
                } else {
                    if (getResourceSkillsCanSaveSelector(state$.value)(tableDataEntryGuid))
                        skillUpdateAction = of(editResourceSkillsWindowUpdateSkills(tableDataEntryGuid));
                }

                return skillUpdateAction;
            })
        );
};

export const entityWindowSubmitAttachmentsEpic$ = (action$, state$) => {
    return action$
        .ofType(
            // Plans page
            `${SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${PLANNER_MASTER_REC_ALIAS}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`,
            `${SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
            `${SUBMIT_UPDATE_BATCH_REQUEST_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${NOTIFICATIONS_BOOKING_ALIAS}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${PROFILE_BOOKING_ALIAS}`,

            // Jobs page
            `${SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${TABLE_NAMES.JOB}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.CLIENT}`,
            `${SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.RESOURCE}`
        ).pipe(
            mergeMap(({ payload }) => {
                const {
                    moduleName,
                    tableDataEntryGuid,
                    patchData = [],
                    tableName
                } = payload;

                const entityIds = [
                    ...patchData.map(({ tableDataEntryGuid }) => tableDataEntryGuid),
                    tableDataEntryGuid
                ].filter(entityId => entityId != null);

                const { attachments } = state$.value;
                let actions = [];

                entityIds.forEach(entityId => {
                    const { inserts, deletes } = getAttachmentsChanges({
                        attachments,
                        moduleName,
                        entityId
                    });

                    if (inserts.orderedKeys.length) {
                        actions = [
                            ...actions,
                            ...Object.values(inserts.map).map(({ name, url, documentType, expiryDate }) =>
                                of(digestInsertAttachment(moduleName, tableName, entityId, name, url, documentType, expiryDate)))
                        ];
                    }

                    if (deletes.orderedKeys.length) {
                        actions = [
                            ...actions,
                            ...deletes.orderedKeys.map(id =>
                                of(deleteAttachment(moduleName, tableName, entityId, id)))
                        ];
                    }
                });

                return actions.length ? concat(...actions) : empty();
            })
        );
};

export const entityWindowDiscardSkillsChangesEpic$ = (action$, state$) => {
    return action$
        .ofType(
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`
        )
        .pipe(
            filter(ewSkillsActionFilter),
            mergeMap(({ payload }) => {
                const { tableDataEntryGuid } = payload;
                let skillUpdateAction = empty();

                if (resourceSkillsHasChangesSelector(state$.value)(tableDataEntryGuid))
                    skillUpdateAction = of(resourceSkillsDiscardChanges(tableDataEntryGuid));

                return skillUpdateAction;
            })
        );
};

export const entityWindowDiscardAttachmentChangesEpic$ = (action$, state$) => {
    return action$
        .ofType(
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`,
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL}`,
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL}`,
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL}`,
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL}`,
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL}`,
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED}`
        )
        .pipe(
            mergeMap(({ payload = {} }) => {
                const { moduleName, tableDataEntryGuid, batchIds = [] } = payload;

                let action;

                if (batchIds.length) {
                    action = discardMultipleEntitiesAttachmentsChanges(moduleName, batchIds);
                } else {
                    action = discardAttachmentsChanges(moduleName, tableDataEntryGuid);
                }

                return moduleName &&
                    attachmentsHaveChanges({
                        attachments: state$.value.attachments,
                        moduleName,
                        entityId: tableDataEntryGuid
                    })
                    ? of(action)
                    : empty();
            })
        );
};

export const entityWindowPreviewPageClose$ = (action$, state$) => {
    return action$
        .ofType(
            `${actionTypes.ENTITY_WINDOW.CLOSE}_${ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL}`
        )
        .pipe(
            mergeMap(({ payload = {} }) => {
                const { entityId } = payload;

                browserHistory.push('/rolesboard');

                return entityWindowOpen(
                    ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE,
                    TABLE_NAMES.ROLEREQUEST,
                    TABLE_NAMES.ROLEREQUEST,
                    ENTITY_WINDOW_OPERATIONS.READ,
                    {},
                    entityId
                );
            })
        );
};

const highlightEWFieldEpic$ = (alias) => {
    return (action$) => {
        return action$
            .ofType(`${actionTypes.ENTITY_WINDOW.HIGHLIGHT_FIELD}_${alias}`)
            .pipe(
                switchMap(({ payload }) => {
                    const { highlightDuration, field } = payload;

                    return of(entityWindowClearHighlightField(alias, field)).pipe(delay(highlightDuration));
                })
            );
    };
};

export const validateEWEpic$ = (alias) => {
    return (action$, state$) => {
        return action$
            .ofType(actionTypes.ENTITY_WINDOW.VALIDATE)
            .pipe(
                filter(({ errorsFound }) => errorsFound !== false),
                switchMap((action) => validateFormAndGetAction$(state$, action))
            );
    };
};

const getLoadMoreTableDataAction = (payload, state) => {
    const { fieldInfo, fieldValue, moduleName } = payload;
    const tableName = getEntityWindowTableName(state.entityWindow, moduleName);
    const primaryFieldTableName = getFieldTableName(fieldInfo, tableName);

    const pageAlias = getPageAliasForEntityModule(moduleName);
    const tableDatasAlias = getTableDatasAlias(pageAlias);

    const tableDatas = getEntityOptionFields(fieldValue, primaryFieldTableName);

    return fieldValue && !isEmpty(fieldValue) && fieldValue.id
        ? [
            loadMoreTableDataSuccess(
                tableDatasAlias,
                { tableDataGuid: primaryFieldTableName, tableNames: [primaryFieldTableName] },
                [tableDatas]
            )
        ]
        : [];
};

const loadValuesForModuleEpic$ = (alias) => (action$, state$) => {
    const actionsOfInterest = [
        `${actionTypes.ENTITY_WINDOW.FIELD_CHANGED}_${alias}`,
        `${actionTypes.ENTITY_WINDOW.FIELD_CHANGED_CONTEXTUALLY}_${alias}`,
        `${actionTypes.ENTITY_WINDOW.LOAD_FIELD_VALUES}_${alias}`
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            filter(({ payload }) => {
                const tableName = getEntityWindowTableName(state$.value.entityWindow, payload.moduleName);

                return isLookupField(tableName, payload.fieldInfo || {});
            }),
            switchMap(({ payload }) => {
                return from(getLoadMoreTableDataAction(payload, state$.value));
            })
        );
};

export const getTableDataGuid = (state, action) => {
    const { plannerPage, jobsPage, navigation } = state;
    const { page } = navigation;
    const { tableName } = action.payload;
    let tableDataGuid;

    if (page === PLANNER_PAGE_ALIAS) {
        const currentWSSettings = getSelectedWorkspaceSettings(plannerPage.workspaces);
        const { masterRecTableName, subRecTableName } = currentWSSettings;
        if (tableName === TABLE_NAMES.BOOKING) {
            const bookingGroupsGuid = getBarGroupsGuidByTable(currentWSSettings, TABLE_NAMES.BOOKING);
            tableDataGuid = bookingGroupsGuid;
        } else if (tableName === TABLE_NAMES.ROLEREQUEST) {
            const roleGroupsGuid = getBarGroupsGuidByTable(currentWSSettings, TABLE_NAMES.ROLEREQUEST);
            tableDataGuid = roleGroupsGuid;
        } else if (tableName === masterRecTableName) {
            tableDataGuid = currentWSSettings.pagedMasterRecPlannerDataGuid;
        } else if (tableName === subRecTableName) {
            tableDataGuid = currentWSSettings.subRecPlannerDataGuid;
        }
    } else if (page === JOBS_PAGE_ALIAS) {
        if (tableName === jobsPage.tableName) {
            tableDataGuid = jobsPage.tableName;
        }
    } else if (page === ROLE_INBOX_PAGE_ALIAS) {
        if (tableName === state[page].tableName) {
            tableDataGuid = state[page].tableName;
        }
    }

    return tableDataGuid;
};

export const batchedEWSubmitUpdateRequestEpic$ = (alias, moduleAlias) => {
    return (action$, state$) => {

        const validateEditAllEntity$ = action$
            .ofType(`${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST}_${alias}`)
            .pipe(
                map((action) => {
                    const { moduleName } = action.payload;
                    const entityWindow = state$.value.entityWindow.window[moduleName];
                    const { showEditAllTab } = entityWindow;
                    const hasEditAllErrors = entityWindow.windows[EDIT_ALL_ENTITY_ID].errors;

                    if (showEditAllTab && hasEditAllErrors) {
                        return submitUpdateBatchRequestError(alias);
                    }

                    return action;
                })
            );

        const getPatchData$ = validateEditAllEntity$
            .pipe(
                map((action) => {
                    let patchData = [];
                    if (action.type !== `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST_ERROR}_${alias}`) {
                        const { moduleName, tableName } = action.payload;
                        const state = state$.value;
                        const entityWindow = state.entityWindow.window[moduleName];
                        const settings = state.entityWindow.settings[moduleName];
                        const sections = settings.sections[tableName] || [];
                        const { showEditAllTab } = entityWindow;

                        const getFieldInfo = getFieldInfoSelector(state);
                        const selectedFieldsKeys = (settings.selectedFieldsKeys || [])
                            .filter(key => !sections.map(section => section.key).includes(key))
                            .reduce((accumulator, key) => {
                                const field = getFieldFromSections(key, sections);

                                if (isDependantListControl(field)) {
                                    (field.fields || []).forEach(nestedField => {
                                        accumulator.push(nestedField.name);
                                    });
                                } else {
                                    accumulator.push(field.name);
                                }

                                return accumulator;
                            }, []);

                        const additionalProps = {
                            getHasRequirementsChanges: getRequirementSectionHasChangesSelector(state),
                            getCriteriasForRole: getCriteriasForRoleSelector(state),
                            getEntityOmittedFields: (entity, tableName) => getOmittedFields(state, entity, tableName),
                            getAssigneesBudgetData: getAssigneesBudgetDataForRole(state)
                        };

                        patchData = getEntitiesPatchData(entityWindow.windows, showEditAllTab, getFieldInfo, selectedFieldsKeys, additionalProps);
                    }

                    return [patchData, action];
                })
            );


        const validatePatchData$ = getPatchData$.pipe(
            filter(([patchData]) => patchData.length !== 0),
            switchMap(([patchData, action]) => {
                const { moduleName, tableName, collectionAlias, entityPayloadAlias } = action.payload;
                const entityWindow = state$.value.entityWindow.window[moduleName];
                const { isBatch } = entityWindow;

                const tableDataGuid = getTableDataGuid(state$.value, action);
                const payload = {
                    alias,
                    tableName,
                    tableDataGuid,
                    patchData,
                    moduleName,
                    collectionAlias,
                    entityPayloadAlias,
                    isBatch
                };

                const actionsToDispatch = getBatchUpdateActions(payload);

                const actionFromSubmit = mapEntityWindowSubmitAction(state$, actionsToDispatch);

                const validatedActionsToDispatch = validateFormAndGetAction$(state$, actionFromSubmit);

                return forkJoin([validatedActionsToDispatch, of(actionFromSubmit)]);
            })
        );

        const submitPatchData$ = validatePatchData$.pipe(
            filter(([validateErrors]) => validateErrors.filter(a => a !== undefined).length === 0),
            switchMap(([, action]) => {
                return from([
                    action,
                    submitUpdateBatchRequestSuccess(alias, action.payload),
                    entityWindowClose(moduleAlias)
                ]);
            })
        );

        const handleSubmitPatchError$ = validatePatchData$.pipe(
            filter(([validateErrors]) => validateErrors.filter(a => a !== undefined).length > 0),
            switchMap(([validateErrors]) => {
                return from([
                    ...validateErrors.filter(a => a !== undefined),
                    submitUpdateBatchRequestError(alias)
                ]);
            })
        );

        const onEmptyPatchDataFollowUp$ = getPatchData$.pipe(
            filter(([patchData]) => patchData.length === 0),
            filter(([, action]) => action.type !== `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST_ERROR}_${alias}`),
            switchMap(([, action]) => {
                const { moduleName, tableName } = action.payload;
                const entityWindow = state$.value.entityWindow.window[moduleName];
                const { batchIds } = entityWindow;
                let chain = [];

                if (tableName === TABLE_NAMES.RESOURCE) {
                    batchIds.forEach(id => {
                        if (getResourceSkillsCanSaveSelector(state$.value)(id)) {
                            chain.push(of(editResourceSkillsWindowUpdateSkills(id)));
                        }
                    });
                }

                chain.push(of(entityWindowClose(moduleAlias)));

                return concat(...chain);
            })
        );

        return merge(
            submitPatchData$,
            handleSubmitPatchError$,
            onEmptyPatchDataFollowUp$
        );
    };
};

export const batchedEWDeleteEpic$ = (moduleNameAlias) => {
    return (action$, state$) => {
        return action$
            .ofType(
                `${actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
                `${actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${PLANNER_ROLEREQUESTS_ALIAS}`,
                `${actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${TABLE_NAMES.ROLEREQUEST}`
            )
            .pipe(
                filter(({ payload: { moduleName } }) => moduleName === moduleNameAlias),
                switchMap((action) => {
                    const { moduleName, tableName, pageAlias, tableNames, tableDataEntryGuids = [] } = action.payload;
                    const entityWindow = state$.value.entityWindow.window[moduleName];
                    let tableDataEntryGuidsForRequest = tableDataEntryGuids;

                    if (tableDataEntryGuidsForRequest == null || tableDataEntryGuidsForRequest.length == 0) {
                        tableDataEntryGuidsForRequest = Object.keys(entityWindow.windows)
                            .filter(key => !isEditAllEntity(key))
                            .map((key) => {
                                tableDataEntryGuidsForRequest.push(key);
                            });
                    }

                    const tableData = tableDataEntryGuidsForRequest
                        .map((key) => {
                            const { entity } = entityWindow.windows[key];

                            return {
                                ...entity,
                                [`${tableName}_job_guid`]: //We need this until job_guid is part of rolerequest entity
                                    entity[`${tableName}_job_guid`]
                                    || getJobDescriptionForEntity(state$.value, key, tableName)
                            };
                        });

                    const tableDataGuid = getTableDataGuid(state$.value, action);

                    let batchDeleteAction = {};

                    if (pageAlias === PLANNER_PAGE_ALIAS) {
                        const groupAlias = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName];

                        batchDeleteAction = batchDeleteGroupedData(
                            groupAlias,
                            tableDataGuid,
                            [tableName, TABLE_NAMES.JOB, TABLE_NAMES.RESOURCE],
                            tableName,
                            tableDataEntryGuids,
                            tableData
                        );
                    } else if (pageAlias === ROLE_INBOX_PAGE_ALIAS) {
                        batchDeleteAction = batchDeleteTableData(
                            ROLE_INBOX_PAGE_PAGED_DATA,
                            tableDataGuid,
                            tableNames,
                            tableName,
                            tableDataEntryGuids,
                            tableData
                        );
                    }

                    return from([
                        batchDeleteAction,
                        entityWindowClose(moduleNameAlias)
                    ]);
                })
            );
    };
};

export const openBatchedEWEpic$ = (alias) => {
    return (action$, state$) => {
        return action$
            .ofType(
                `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${alias}`
            )
            .pipe(
                mergeMap((action) => {
                    const { collectionAlias, tableName, entityIds, operation, singleEntityModuleName, activeTab } = action.payload;
                    let openEWActions = [];

                    if (entityIds.length > 1) {
                        openEWActions.push(
                            entityWindowOpenForMultiple(
                                alias,
                                tableName,
                                collectionAlias,
                                operation,
                                [],
                                entityIds,
                                undefined,
                                undefined,
                                undefined,
                                undefined,
                                activeTab
                            )
                        );
                    } else {
                        openEWActions.push(
                            entityWindowClose(alias),
                            entityWindowOpen(
                                singleEntityModuleName,
                                tableName,
                                collectionAlias,
                                operation,
                                {},
                                entityIds[0],
                                true,
                                undefined,
                                undefined,
                                activeTab
                            )
                        );
                    }

                    shouldSelectHistoryTabOnOpen(operation, tableName, activeTab)
                        && openEWActions.push(entityWindowSetActiveTab(alias, activeTab, entityIds[0]));

                    return from(openEWActions);
                })
            );
    };
};

export const openEntityWindowRequestEpic$ = (alias) => {
    return (action$, state$) => {
        return action$
            .ofType(`${actionTypes.ENTITY_WINDOW.OPEN_REQUEST}_${alias}`)
            .pipe(
                mergeMap(({ payload }) => {
                    const { tableName, collectionAlias, operation, entity, lazyLoadEntityData } = payload;
                    let openEWActions = [];
                    const entity_guid = `${tableName}_guid`;
                    if (tableName === TABLE_NAMES.ROLEREQUEST) {
                        openEWActions.push(...getOpenRoleEntityWindowActions(state$.value, payload, alias));
                    } else {
                        openEWActions.push(entityWindowOpen(alias, tableName, collectionAlias, operation, entity, entity[entity_guid], lazyLoadEntityData));
                    }

                    return from(openEWActions);
                })
            );
    };
};

export const entityWindowGetAllResourceWithAvailabilityEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(...[
            `${actionTypes.REPEAT_BOOKING_DIALOG_ACTIONS.GET_ALL_RESOURCE_AVAILABILITY}`,
            `${actionTypes.ENTITY_WINDOW.OPEN_REQUEST}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`
        ])
        .pipe(
            switchMap(({ payload }) => {
                const { bookingStart, bookingEnd, entity, operation, tableName, bookingResourceGuids = [] } = payload;

                let bookingStartDate = bookingStart;
                let bookingEndDate = bookingEnd;
                let resourceGuids = bookingResourceGuids || [];

                if (entity && entity[BOOKING_START] && entity[BOOKING_END] && entity[BOOKING_RESOURCE_GUIDS]) {
                    bookingStartDate = entity[BOOKING_START];
                    bookingEndDate = entity[BOOKING_END];
                    resourceGuids = entity[BOOKING_RESOURCE_GUIDS] || [];
                }

                resourceGuids = resourceGuids.filter(guid => guid !== UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID);

                if (resourceGuids.length > 0 && bookingStartDate && bookingEndDate && operation === ENTITY_WINDOW_OPERATIONS.CREATE && tableName === TABLE_NAMES.BOOKING) {
                    const selection = {
                        fields: [
                            { fieldName: RESOURCE_DESCRIPTION, fieldAlias: 'value' },
                            { fieldName: RESOURCE_GUID, fieldAlias: 'id' },
                            { fieldName: RESOURCE_USERSTATUS },
                            {
                                fieldName: FILTER_FIELD_NAMES.AVAILABILITY,
                                parameters: { startDate: bookingStartDate, endDate: bookingEndDate }
                            },
                            {
                                fieldName: FILTER_FIELD_NAMES.AVAILABLE_HOURS,
                                parameters: { startDate: bookingStartDate, endDate: bookingEndDate }
                            }
                        ],
                        filter: {
                            filterGroupOperator: FILTER_GROUP_OPERATORS.AND,
                            filterLines: [
                                {
                                    field: RESOURCE_USERSTATUS,
                                    operator: OPERATORS.DB_OPERATORS.EQUALS,
                                    value: true
                                },
                                {
                                    field: RESOURCE_GUID,
                                    operator: OPERATORS.DB_OPERATORS.CONTAINS,
                                    value: resourceGuids
                                }
                            ]
                        }
                    };

                    return apis['tableData'].getFilterTableAccessData$('resource', selection);
                }

                return EMPTY;
            }),
            mergeMap(result => [repeatBookingUpdateResourceSuggestions(result)])
        );
};

export const entityWindowRedirectOnEditEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(
            `${actionTypes.ENTITY_WINDOW.EDIT}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE}`
        )
        .pipe(
            switchMap(({ payload = {} }) => {
                const { entityId, tableName } = payload;
                const selection = {
                    'fields': [
                        {
                            fieldName: 'rolerequestgroup_surrogate_id'
                        },
                        {
                            fieldName: 'rolerequestgroup_description'
                        }
                    ],
                    'filter': {
                        'filterGroupOperator': 'And',
                        'filterLines': [
                            {
                                'field': 'rolerequestgroup_guid',
                                'operator': 'Contains',
                                'value': [entityId]
                            }]
                    }
                };

                return apis['tableData'].getFilterTableAccessData$(tableName, selection);
            }),
            filter(result => Array.isArray(result) && result.length > 0),
            mergeMap(result => {
                const { rolerequestgroup_description, rolerequestgroup_surrogate_id } = result[0];
                const { rolegroupListPage } = state$.value;
                const { pageState } = rolegroupListPage;
                const { jobName, jobGuid, jobSurrogateId } = pageState;

                const params = {
                    rolegroupName: rolerequestgroup_description,
                    rolegroupSurrogateId: rolerequestgroup_surrogate_id,
                    jobName: jobName,
                    jobId: jobGuid,
                    jobSurrogateId: jobSurrogateId
                };

                const newParams = {
                    subPageOption: {
                        subPageNavLink: ROLEGROUPLISTDETAILSPAGE.navigationLink,
                        subPagesParams: params
                    }
                };
                const actions = [];
                const shouldRoleGroupDPItemClose = getIsJobDpItemVisibleCollapse(rolegroupListPage, ROLE_GROUP_LIST_PAGE);
                actions.push(setPageState(ROLE_GROUP_DETAILS_PAGE, params));
                actions.push(pushUrl(newParams));
                if (shouldRoleGroupDPItemClose) {
                    actions.push(entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE));
                }

                return actions;
            })
        );
};

export const populateCriteriasForRoleOnDPOpenEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(
            `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE}`
        )
        .pipe(
            filter(({ payload }) => {
                const state = state$.value;
                const activeRoleEntity = getActiveRoleEntitySelector(state);

                return getIsCriteriaRole(activeRoleEntity);
            }),
            switchMap(() => {
                const state = state$.value;
                const { [ROLEREQUEST_FIELDS.GUID]: roleId } = getActiveRoleEntitySelector(state);

                const querySelection = getCriteriasForRoleQuerySelection(roleId);

                return apis[API_KEYS.ROLE_REQUEST_API_KEY].getRoles$(querySelection);
            },
            (action, response) => [action, response]),
            mergeMap(([action, response]) => {

                if (!response || response.status == 'error') {
                    return empty();
                }

                const state = state$.value;
                const { [ROLEREQUEST_FIELDS.GUID]: roleId } = getActiveRoleEntitySelector(state);
                const { criterias } = response[0];
                const expandedCriteria = expandCriteriaValues([criterias], state)[0];

                const mappedCriterias = {
                    [roleId]: {
                        [CRITERIA_SECTIONS.MUST_MEET]: expandedCriteria
                    }
                };

                return of((populateCriteriasForRoles([roleId], mappedCriterias)));
            })
        );
};

const loadRoleAssigneesForEwEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.PROCESS_LOADED_ROLE_ASSIGNEES)
        .pipe(
            switchMap(
                (action) => {
                    const { moduleName, response } = action.payload;

                    if (response.status === ERROR_STATUS) {
                        return empty();
                    }

                    const state = state$.value;
                    const { RESOURCE_GUID } = ROLEREQUESTRESOURCE_FIELDS;
                    const avatars = getAvatars(state);
                    const useMultipleAssignees = getIsMultipleAssigneesEnabled(state);
                    const dataCollections = getPageTableDatasSelector(state);

                    const chain = [];

                    const resourceIds = [];

                    response.forEach((singleEntityResponse) => {
                        const roleObject = singleEntityResponse || {};
                        const { id: roleId, body: roleEntity = {}, assignedResources = [] } = roleObject;
                        const currentResourceGuids = assignedResources.map(assignee => (assignee || {})[RESOURCE_GUID]);
                        const uniqueResourceGuids = currentResourceGuids.filter(resGuid => !resourceIds.includes(resGuid));
                        resourceIds.push(...uniqueResourceGuids);

                        const assigneesResDataMap = getCriteriaRoleAssigneesResDataMap(roleId, roleEntity, assignedResources, dataCollections, useMultipleAssignees);
                        const expandedRole = getRoleWithChargerateValue(roleEntity, assigneesResDataMap);

                        chain.push(entityWindowDrivenFieldChanged(moduleName, { name: CHARGERATE_CURRENT_VALUE_FIELDNAME }, expandedRole[CHARGERATE_CURRENT_VALUE_FIELDNAME]));
                    });

                    const avatarGuidsToLoad = resourceIds
                        .filter(id => resourceAvatarLoaded(avatars, id, AVATAR_SIZES.TINY.label));

                    if (resourceIds.length) {
                        const loadAssigneesInfoAdditionalProps = {
                            validateAssignees: isAssigneeBudgetModal(moduleName),
                            moduleAlias: moduleName
                        };

                        const loadPotentialConflicts = false;
                        chain.push(loadRoleRequestsAssigneesInfo(resourceIds, response, loadAssigneesInfoAdditionalProps, loadPotentialConflicts));
                    }

                    if (avatarGuidsToLoad.length) {
                        chain.push(loadAvatars(avatarGuidsToLoad, AVATAR_SIZES.TINY));
                    }

                    chain.push(updateEntityWindowSectionMessages({ moduleName, tableName: TABLE_NAMES.ROLEREQUEST }));

                    return from(chain);
                }
            )
        );
};

const updateRolerequestEwMessages$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.CRITERIA_ROLE_ASSIGNMENT_ACTIONS.POPULATE_ASSIGNEES_FOR_ALL)
        .pipe(
            switchMap(
                (action) => {
                    const chain = [];
                    const currentPageAlias = getCurrentPageAliasSelector(state$.value);
                    const moduleNamesForPage = getEntityWindowModulesForPage(currentPageAlias);

                    moduleNamesForPage.forEach((moduleName) => {
                        chain.push(updateRolerequestEntityWindowSectionMessages({ moduleName, tableName: TABLE_NAMES.ROLEREQUEST }));
                    });

                    return from(chain);
                }
            )
        );
};

const loadManageBudgetEwInfoEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.OPEN_MANAGE_BUDGET_EW)
        .pipe(
            switchMap(
                (action) => {
                    const state = state$.value;
                    const { roleId, moduleName } = action.payload || {};
                    const tableName = TABLE_NAMES.ROLEREQUEST;
                    const getSelectionFieldsWrapped = (fields) => getSelectionFields(fields, state);
                    const requestFields = getRolerequestFieldSelection(state)(moduleName, { getSelectionFieldsWrapped });
                    const querySelection = getRoleInfoSelection(tableName, roleId, requestFields);

                    return apis[API_KEYS.ROLE_REQUEST_API_KEY].getRoles$(querySelection);
                },
                (action, response) => [action, response]
            ),
            switchMap(
                ([action, response]) => {
                    if (response.status === ERROR_STATUS) {
                        return empty();
                    }

                    const { moduleName } = action.payload;
                    const state = state$.value;

                    const chain = [];
                    const roleObject = (response || [])[0] || {};
                    const { id: roleId, body: roleEntity = {}, assignedResources = [] } = roleObject;

                    const useMultipleAssignees = getIsMultipleAssigneesEnabled(state);
                    const dataCollections = getPageTableDatasSelector(state);
                    const assigneesResDataMap = getCriteriaRoleAssigneesResDataMap(roleId, roleEntity, assignedResources, dataCollections, useMultipleAssignees);
                    const expandedRole = getRoleWithChargerateValue(roleEntity, assigneesResDataMap);

                    chain.push(
                        entityWindowOpen(
                            moduleName,
                            TABLE_NAMES.ROLEREQUEST,
                            TABLE_NAMES.ROLEREQUEST,
                            ENTITY_WINDOW_OPERATIONS.EDIT,
                            expandedRole,
                            roleId
                        )
                    );

                    return from(chain);
                }
            )
        );
};

export const mapPublishRoleToMarketplaceAction = (state$, action) => {
    const { tableDataEntryGuid: roleId, tableData, moduleName } = action.payload;
    const {
        [ROLEMARKETPLACE_FIELDS.CATEGORY_GUID]: categoryId,
        [ROLEMARKETPLACE_FIELDS.PUBLISHEDON]: publishedOn,
        [ROLEMARKETPLACE_FIELDS.CRITERIA_MATCH]: criteriaMatch
    } = tableData;

    const currentEntityWindow = getEntityWindow(state$.value, moduleName);
    const isVisible = currentEntityWindow.visible;

    const parsedDate = parseUtcToLocalDate(startOfDay(parseToUtcDate(publishedOn)));

    const payloadToPass = {
        roleId,
        categoryId,
        publishedOn: parsedDate,
        criteriaMatch
    };

    return isVisible
        ? formValidationMapAction(state$, { ...action, payload: { ...action.payload, selection: payloadToPass } })
        : of({ ...action, payload: { ...action.payload, selection: payloadToPass } });
};

export const mapEditRolePublicationAction = (state$, action) => {
    const { tableData, moduleName } = action.payload;
    const {
        [ROLEMARKETPLACE_FIELDS.CATEGORY_GUID]: categoryId,
        [ROLEMARKETPLACE_FIELDS.PUBLISHEDON]: publishedOn,
        [ROLEREQUEST_FIELDS.PUBLICATION_GUID]: publicationId,
        [ROLEMARKETPLACE_FIELDS.CRITERIA_MATCH]: criteriaMatch
    } = tableData;

    const currentEntityWindow = getEntityWindow(state$.value, moduleName);
    const isVisible = currentEntityWindow.visible;

    const parsedDate = parseUtcToLocalDate(startOfDay(parseToUtcDate(publishedOn)));

    const payloadToPass = {
        categoryId,
        publishedOn: parsedDate,
        criteriaMatch
    };

    return isVisible
        ? formValidationMapAction(state$, { ...action, payload: { ...action.payload, selection: payloadToPass, id: publicationId } })
        : of({ ...action, payload: { ...action.payload, selection: payloadToPass, id: publicationId } });
};

const mapLoadPublishRoleEWAction = ({ value: state }, action) => {
    const { roleId } = action.payload || {};
    const tableName = TABLE_NAMES.ROLEREQUEST;
    const querySelection = {
        fields: [
            {
                fieldName: ROLEREQUEST_FIELDS.HASCRITERIA
            }
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [{
                field: ROLEREQUEST_FIELDS.GUID,
                operator: OPERATORS.DB_OPERATORS.EQUALS,
                value: roleId,
                tableName,
                parameters: {},
                isNot: false
            }]
        },
        assigneeFilter: {},
        order: {},
        getCriterias: false
    };

    return of({ ...action, payload: { ...action.payload, tableName: TABLE_NAMES.ROLEMARKETPLACE, selection: querySelection } });
};

const loadPublishRoleEWSuccessHandler = (...args) => {
    const [, roleData, result] = args;
    const { state } = result;
    const { moduleName, roleEntity, roleId, tableName } = roleData;
    const actionsToDispatch = [];
    const roleEndDate = parseToUtcDate(roleEntity[ROLEREQUEST_FIELDS.END]);

    const dynamicConfiguration = getRoleMarketplaceDynamicConfig(moduleName, tableName, state, { roleEndDate });

    actionsToDispatch.push(
        injectDynamicConfiguration({ dynamicConfiguration, moduleName }),
        entityWindowOpen(
            moduleName,
            TABLE_NAMES.ROLEMARKETPLACE,
            TABLE_NAMES.ROLEMARKETPLACE,
            ENTITY_WINDOW_OPERATIONS.CREATE,
            {
                [ROLEMARKETPLACE_FIELDS.CRITERIA_MATCH]: true
            },
            roleId,
            false
        )
    );

    return actionsToDispatch;
};

const loadPublishRoleEWErrorHandler = (...args) => {
    return empty();
};

const loadPublishRoleEWInfoEpic$ = (successHandler, errorHandler) => createAPICallEpic(
    null,
    getApiCallEpicConfig(
        actionTypes.OPEN_PUBLISH_ROLE_EW,
        API_KEYS.ROLE_REQUEST_API_KEY,
        (api, { selection }) => api.getRoles$(selection),
        successHandler,
        errorHandler,
        null,
        ({ value }, action) => mapLoadPublishRoleEWAction({ value }, action),
        (...args) => {
            const [response, , state] = args;

            return { response, state };
        }
    )
);

const mapLoadEditRolePublicationEWAction = ({ value: state }, action) => {
    const { roleEntity } = action.payload || {};

    const querySelection = {
        fields: [
            {
                fieldName: `${ROLEMARKETPLACE_FIELDS.CATEGORY_GUID}.rolecategory_description`,
                fieldAlias: ROLEREQUEST_FIELDS.CATEGORY
            },
            {
                fieldName: ROLEMARKETPLACE_FIELDS.CATEGORY_GUID,
                fieldAlias: ROLEREQUEST_FIELDS.CATEGORY_GUID
            },
            {
                fieldName: ROLEMARKETPLACE_FIELDS.PUBLISHEDON,
                fieldAlias: ROLEREQUEST_FIELDS.PUBLISHEDON
            },
            {
                fieldName: ROLEMARKETPLACE_FIELDS.CRITERIA_MATCH,
                fieldAlias: ROLEREQUEST_FIELDS.CRITERIA_MATCH
            }
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: ROLEMARKETPLACE_FIELDS.PUBLICATION_GUID,
                    operator: 'Equals',
                    value: roleEntity[ROLEREQUEST_FIELDS.PUBLICATION_GUID]
                }
            ]
        }
    };

    return of({ ...action, payload: { ...action.payload, tableName: TABLE_NAMES.ROLEMARKETPLACE, selection: querySelection } });
};

const loadEditPublicationRoleEWSuccessHandler = (...args) => {
    const [, roleData, result] = args;
    const { state, response } = result;
    const {
        [ROLEREQUEST_FIELDS.CRITERIA_MATCH]: criteriaMatch,
        [ROLEREQUEST_FIELDS.PUBLISHEDON]: publishedOn,
        [ROLEREQUEST_FIELDS.CATEGORY]: categoryDescription,
        [ROLEREQUEST_FIELDS.CATEGORY_GUID]: categoryGuid
    } = response[0];

    const { moduleName, roleEntity, roleId, tableName } = roleData;

    const actionsToDispatch = [];

    const fieldInfo = getFieldInfoSelector(state)(TABLE_NAMES.ROLEMARKETPLACE, ROLEMARKETPLACE_FIELDS.CATEGORY_GUID);
    const dynamicConfiguration = getRoleMarketplaceDynamicConfig(
        moduleName,
        tableName,
        state,
        {
            roleEndDate: roleEntity[ROLEREQUEST_FIELDS.END],
            publishedOn
        }
    );

    actionsToDispatch.push(
        injectDynamicConfiguration({ dynamicConfiguration, moduleName }),
        entityWindowOpen(
            moduleName,
            TABLE_NAMES.ROLEMARKETPLACE,
            TABLE_NAMES.ROLEMARKETPLACE,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            {
                [ROLEMARKETPLACE_FIELDS.CRITERIA_MATCH]: criteriaMatch,
                [ROLEMARKETPLACE_FIELDS.PUBLISHEDON]: publishedOn,
                [ROLEREQUEST_FIELDS.PUBLICATION_GUID]: roleEntity[ROLEREQUEST_FIELDS.PUBLICATION_GUID]
            },
            roleId,
            false
        ),
        entityWindowFieldChanged(
            moduleName,
            fieldInfo,
            {
                value: categoryDescription,
                id: categoryGuid
            },
            roleId,
            TABLE_NAMES.ROLEMARKETPLACE
        )
    );

    return actionsToDispatch;
};

const loadEditRolePublicationEWInfoEpic$ = (successHandler, errorHandler) => createAPICallEpic(
    null,
    getApiCallEpicConfig(
        actionTypes.OPEN_EDIT_ROLE_PUBLICATION_EW,
        API_KEYS.TABLE_DATA,
        (api, { tableName, selection }) => api.getFilterTableAccessData$(tableName, selection),
        successHandler,
        errorHandler,
        null,
        ({ value }, action) => mapLoadEditRolePublicationEWAction({ value }, action),
        (...args) => {
            const [response, , state] = args;

            return { response, state };
        }
    )
);

export default function () {
    return combineEpics(
        // Insert
        combineAPIEpics(
            createEntityWindowSubmitInsertEpic(PLANNER_BOOKING_GROUPS_ALIAS, entityWindowSubmitInsertGrouppedDataSuccess, entityWindowSubmitInsertGrouppedDataError)(),
            createEntityWindowBatchInsertEpic(PLANNER_BOOKING_GROUPS_ALIAS, entityWindowSubmitInsertGrouppedDataBatchSuccess, entityWindowSubmitInsertGrouppedDataBatchError),
            createEntityWindowSubmitInsertEpic(JOBS_PAGE_MODAL_ALIAS, entityWindowSubmitInsertTableDataSuccess, entityWindowSubmitInsertTableDataError)(),
            createEntityWindowSubmitInsertEpic(ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, entityWindowSubmitInsertTableDataSuccess, entityWindowSubmitInsertTableDataError)(),
            createEntityWindowSubmitInsertEpic(PLANNER_MASTER_REC_ALIAS, entityWindowSubmitInsertTableDataSuccess, entityWindowSubmitInsertTableDataError)(),
            createEntityWindowSubmitInsertEpic(PLANNER_SUB_REC_ALIAS, entityWindowSubmitInsertTableDataSuccess, entityWindowSubmitInsertTableDataError)(),
            createEntityWindowSubmitInsertEpic(PROFILE_PAGE_ALIAS, entityWindowSubmitInsertTableDataSuccess, entityWindowSubmitInsertTableDataError)(),
            createEntityWindowSubmitInsertEpic(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL, entityWindowSubmitInsertTableDataSuccess, entityWindowSubmitInsertTableDataError)(),

            entityWindowCreateRolegroupBookingRequestEpic()
        ),
        // Update
        combineAPIEpics(
            createEntityWindowSubmitUpdateEpic(PLANNER_MASTER_REC_ALIAS, entityWindowSubmitUpdatePagedDataSuccess, entityWindowSubmitUpdatePagedDataError)(),
            createEntityWindowSubmitUpdateEpic(PLANNER_SUB_REC_ALIAS, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdateTableDataError)(),
            createEntityWindowSubmitUpdateEpic(PLANNER_BOOKING_GROUPS_ALIAS, entityWindowSubmitUpdateGrouppedDataSuccess, entityWindowSubmitUpdateGrouppedDataError)(),
            createEntityWindowSubmitUpdateEpic(JOBS_PAGE_MODAL_ALIAS, entityWindowSubmitUpdatePagedDataSuccess, entityWindowSubmitUpdatePagedDataError)(),
            createEntityWindowSubmitUpdateEpic(ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdateTableDataError)(),
            createEntityWindowSubmitUpdateEpic(TABLE_NAMES.RESOURCE, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdateTableDataError)(),
            createEntityWindowSubmitUpdateEpic(TABLE_NAMES.JOB, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdateTableDataError)(),
            createEntityWindowSubmitUpdateEpic(NOTIFICATIONS_BOOKING_ALIAS, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdatePagedDataError)(),
            createEntityWindowSubmitUpdateEpic(PROFILE_BOOKING_ALIAS, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdatePagedDataError)(),
            createEntityWindowSubmitUpdateEpic(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL, entityWindowSubmitUpdateTableDataSuccess, entityWindowSubmitUpdatePagedDataError)()
        ),
        // Delete
        combineAPIEpics(
            createEntityWindowSubmitDeleteEpic(ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, entityWindowSubmitDeleteTableDataSuccess, entityWindowSubmitDeleteTableDataError)(),
            createEntityWindowSubmitDeleteEpic(TABLE_NAMES.JOB, entityWindowSubmitDeleteTableDataSuccess, entityWindowSubmitDeleteTableDataError)(),
            createEntityWindowSubmitDeleteEpic(PLANNER_BOOKING_GROUPS_ALIAS, entityWindowSubmitDeleteGrouppedTableDataSuccess, entityWindowSubmitDeleteGrouppedTableDataError)(),
            createEntityWindowSubmitDeleteEpic(PLANNER_ROLEREQUESTS_ALIAS, entityWindowSubmitDeleteGrouppedTableDataSuccess, entityWindowSubmitDeleteGrouppedTableDataError)(),
            createEntityWindowSubmitDeleteEpic(TABLE_NAMES.ROLEREQUEST, entityWindowSubmitDeleteTableDataSuccess, entityWindowSubmitDeleteTableDataError)(),
            createEntityWindowSubmitDeleteEpic(NOTIFICATIONS_BOOKING_ALIAS, entityWindowSubmitDeleteGrouppedTableDataSuccess, entityWindowSubmitDeleteGrouppedTableDataError)(),
            createEntityWindowSubmitDeleteEpic(PROFILE_BOOKING_ALIAS, entityWindowSubmitDeleteGrouppedTableDataSuccess, entityWindowSubmitDeleteGrouppedTableDataError)()
        ),
        // Inline
        combineAPIEpics(
            createFieldControlUpdateApplyEpic(PLANNER_MASTER_REC_ALIAS, entityWindowContextualEditPagedDataSuccess, entityWindowContextualEditPagedDataError)(),
            createFieldControlUpdateApplyEpic(PLANNER_SUB_REC_ALIAS, entityWindowContextualEditTableDataSuccess, entityWindowContextualEditTableDataError)(),
            createFieldControlUpdateApplyEpic(PLANNER_BOOKING_GROUPS_ALIAS, entityWindowContextualEditGrouppedDataSuccess, entityWindowContextualEditGrouppedDataError)(),
            createFieldControlUpdateApplyEpic(TABLE_NAMES.JOB, entityWindowContextualEditPagedDataSuccess, entityWindowContextualEditPagedDataError)(),
            createFieldControlUpdateApplyEpic(TABLE_NAMES.ROLEREQUESTGROUP, entityWindowContextualEditPagedDataSuccess, entityWindowContextualEditPagedDataError)(),
            createFieldControlUpdateApplyEpic(TABLE_NAMES.RESOURCE, entityWindowContextualEditTableDataSuccess, entityWindowContextualEditTableDataError)(),
            createFieldControlUpdateApplyEpic(TABLE_NAMES.JOB, entityWindowContextualEditTableDataSuccess, entityWindowContextualEditTableDataError)(),
            createFieldControlUpdateApplyEpic(PLANNER_ROLEREQUESTS_ALIAS, entityWindowContextualEditGrouppedDataSuccess, entityWindowContextualEditGrouppedDataError)(),
            createFieldControlUpdateApplyEpic(TABLE_NAMES.ROLEREQUEST, entityWindowContextualEditPagedDataSuccess, entityWindowContextualEditPagedDataError)(),
            createFieldControlUpdateApplyEpic(PLANNER_ROLEREQUESTGROUP_ALIAS, entityWindowContextualEditPagedDataSuccess, entityWindowContextualEditPagedDataError)()
        ),
        //
        combineAPIEpics(
            loadEntityWindowData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL, plannerTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE, plannerTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL, plannerTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL, jobsTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE, jobsTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, jobsTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE, jobsTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE, jobsTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE, jobsTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL, roleInboxTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE, roleInboxTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, roleInboxTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL, roleInboxTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL, roleInboxTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL, notificationTableNamesAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE, marketplaceTableNamesAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL, profileTableNamesAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL, previewEntityTableNamesAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL, tableViewTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED, tableViewTableNameAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL, marketplaceTableNamesAlias),
            loadEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL, jobsTableNameAlias),

            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL, plannerTableNameAlias),
            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE, plannerTableNameAlias),
            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL, roleInboxTableNameAlias),
            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE, roleInboxTableNameAlias),
            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE, jobsTableNameAlias),
            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL, jobsTableNameAlias),
            loadMultipleEntityWindowData(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE, jobsTableNameAlias),

            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE),
            lazyLoadActiveEntityData(ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL, getLazyLoadManageTemplatesModalEntitySuccessActions)
        ),

        // Field driven actions epic
        createHandleFieldDrivenActionsEpic$(),

        // Event driven actions epic
        createHandleEventDrivenActionsEpic$(),

        // Translate field explanation for visible EW module
        createTranslateVisibleExplanationsEpic$(),

        // Driven actions
        drivenActionsEpics$,
        onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
        onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE),
        onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE),
        onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE),
        onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE),
        onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE),
        updateEntityTemplatesEpic(),

        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL),
        loadValuesForModuleEpic$(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL),

        //Update entity window sections
        buildEntityWindowSettingEpic(),

        //extend simplified EW with all mandatory fields
        extendSimplifiedEntityWindowSectionsEpic(),

        openEntityWindowEpic(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL, getPlannerEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED, getPlannerEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL, getPlannerEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL, getRoleInboxPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED, getRoleInboxPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, getRoleInboxPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL, getRoleInboxPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL, getRoleInboxPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL, getCreateTemplateEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL, getTableViewPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED, getTableViewPageEntityWindowOpenActions),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL),
        openEntityWindowEpic(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL),

        entityWindowSubmitSkillsEpic$,
        entityWindowDiscardSkillsChangesEpic$,
        entityWindowSubmitAttachmentsEpic$,
        entityWindowDiscardAttachmentChangesEpic$,
        entityWindowPreviewPageClose$,

        // Clear highlighted fields
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE),
        highlightEWFieldEpic$(ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL),

        validateEWEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED),
        validateEWEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED),
        validateEWEpic$(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED),

        batchedEWSubmitUpdateRequestEpic$(PLANNER_BOOKING_GROUPS_ALIAS, ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(PLANNER_ROLEREQUESTS_ALIAS, ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(PLANNER_SUB_REC_ALIAS, ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(PLANNER_MASTER_REC_ALIAS, ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(TABLE_NAMES.JOB, ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(TABLE_NAMES.ROLEREQUEST, ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(TABLE_NAMES.JOB, ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
        batchedEWSubmitUpdateRequestEpic$(TABLE_NAMES.RESOURCE, ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
        batchedEWDeleteEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        batchedEWDeleteEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),

        openBatchedEWEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL),
        openBatchedEWEpic$(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL),
        openBatchedEWEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),

        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL),
        openEntityWindowRequestEpic$(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL),

        entityWindowRedirectOnEditEpic$,
        entityWindowGetAllResourceWithAvailabilityEpic$,

        createStaticBatchInsertDigestEpic(TABLE_NAMES.ROLEREQUEST),
        populateCriteriasForRoleOnDPOpenEpic$,
        loadRoleAssigneesForEwEpic$,
        updateRolerequestEwMessages$,
        loadManageBudgetEwInfoEpic$,
        loadPublishRoleEWInfoEpic$(loadPublishRoleEWSuccessHandler, loadPublishRoleEWErrorHandler)(),
        loadEditRolePublicationEWInfoEpic$(loadEditPublicationRoleEWSuccessHandler, loadPublishRoleEWErrorHandler)(),

        globalCreateModalEpics$()
    );
}