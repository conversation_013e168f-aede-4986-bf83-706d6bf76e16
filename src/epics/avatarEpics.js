import { empty, from, of } from 'rxjs';
import { mergeMap, map, switchMap } from 'rxjs/operators';
import { combineEpics } from 'redux-observable';
import { uniq } from 'lodash';
import * as actionTypes from '../actions/actionTypes';
import { getApiCallEpicConfig, createAPICallEpic } from './epicGenerators/apiCallEpicGenerator';
import { getCurrentPlannerPagedData, getCurrentPlannerPagedDataObject, getCurrentPlannerPageNumber, getCurrentPlannerSubRecData } from '../selectors/plannerPageSelectors';
import { getSelectedWorkspaceSettings } from '../selectors/workspaceSelectors';
import { loadAvatar, loadAvatarSuccess, removeAvatar, loadAvatarsBatchSuccess, loadAvatars } from '../actions/avatarActions';
import { avatarImageLoaded, getAvatarConfig, resourceAvatarLoaded } from '../selectors/avatarSelectors';
import { updateAvatarWindowUploadAvatarSuccess, updateAvatarWindowRemoveAvatarSuccess } from '../actions/updateAvatarWindowActions';
import { AVATAR_SIZES } from '../constants/avatarConsts';
import { ROLE_REQUEST_FORM, TABLE_NAMES } from '../constants';
import { getCurrentTableViewPagedData, getCurrentTableViewPagedDataObject, getCurrentTableViewPageNumber, getCurrentTableViewSubRecData } from '../selectors/tableViewSelectors';
import { TABLE_VIEW_DATA_LOADED, TABLE_VIEW_DATA_PAGE_LOADED, TABLE_VIEW_DATA_PATCH, TABLE_VIEW_DATA_SCROLLED } from '../actions/tableViewActions/actions';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';

const avatarApiName = 'avatars';

const loadAvatar$ = (api, payload) => {
    return api.getAvatar$(payload.resGuid, payload.size);
};

const loadAvatars$ = (api, payload) => {
    return api.getAvatars$(payload.resGuids, payload.size);
};

const uploadAvatar$ = (api, payload) => {
    // eslint-disable-next-line no-undef
    let avatarFormData = new FormData();
    avatarFormData.append('file', payload.avatar, payload.fileName);
    return api.insertAvatar$(payload.resourceGuid, avatarFormData);
};

const removeAvatar$ = (api, payload) => {
    return api.deleteAvatar$(payload.resourceGuid);
};


const getAvatarLoadActions = (avatars, avatarConfig, resourceGuid) => {
    let result = [];

    for(let imageSizeKey in avatarConfig.images){
        if(avatarImageLoaded(avatars, resourceGuid, imageSizeKey)){
            result.push(loadAvatar(resourceGuid, imageSizeKey));
        }
    }

    return result;
};

export const loadSingleAvatar$ = (resGuid, sizeOptions, avatarsAPI) => {
    return loadAvatar$(avatarsAPI, {
        resGuid,
        size: sizeOptions.label
    }).pipe(
        map((result) => {
            return {
                resGuid,
                size: sizeOptions.label,
                avatar: result
            };
        })
    );
};

export const loadMultipleAvatars$ = (resGuids, sizeOptions, avatarsAPI) => {
    return loadAvatars$(avatarsAPI, {
        resGuids,
        size: sizeOptions.label
    }).pipe(
        map((result) => {
            let { response = [], code } = result;

            if (!Array.isArray(response)) {
                response = [];
            }

            return response.map((imgData) => {
                const { resourceId, content } = imgData;

                return {
                    resGuid: resourceId,
                    size: sizeOptions.label,
                    avatar: {
                        code,
                        fileContents: content.fileContents,
                    }
                };
            });
        })
    );
};

export const getLoadResourceAvatarRequests = (resIdCollection, sizeOptions, apis) => {
    return resIdCollection.map(({ resource_guid }) => {
        return loadSingleAvatar$(resource_guid, sizeOptions, apis.avatars);
    });
};

export const loadAvatarsEpic = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.AVATARS.LOAD_MANY)
        .pipe(
            mergeMap(
                ({ payload }) => {
                    const { resGuids, size } = payload;
                    const avatars = state$.value.avatars;

                    const avatarRequests = Array.from(new Set(resGuids))
                        .filter((guid, ind, arr) => (resourceAvatarLoaded(avatars, guid, size) && arr.indexOf(guid) === ind));

                    return loadMultipleAvatars$(avatarRequests, size, apis.avatars);
                },
                (_action, result) => loadAvatarsBatchSuccess(result)
            )
        )
};

const loadPlannerDataAvatarsActions = [
    actionTypes.PLANNER_DATA_LOADED,
    actionTypes.PLANNER_DATA_SCROLLED,
    actionTypes.PLANNER_DATA_PAGE_LOADED,
    actionTypes.PLANNER_DATA_PATCH
];

export const handleLoadPlannerDataAvatarsEpic = (action$, state$, { apis }) => {
    return action$.ofType(...loadPlannerDataAvatarsActions).pipe(
        mergeMap(
            (action) => {
                const state = state$.value;
                const plannerPage = state.plannerPage;
                const avatars = state.avatars;

                const selectedWorkspaceSettings = getSelectedWorkspaceSettings(plannerPage.workspaces);
                const { masterRecTableName } = selectedWorkspaceSettings;
                const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state);

                let resDataCollection = [];
                if (masterRecTableName === TABLE_NAMES.RESOURCE) {
                    resDataCollection = getCurrentPlannerPagedData(plannerPage).data;
                } else if (masterRecTableName === TABLE_NAMES.JOB) {
                    resDataCollection = getCurrentPlannerSubRecData(plannerPage).data;
                }

                if (action.type === actionTypes.PLANNER_DATA_SCROLLED) {
                    resDataCollection = resDataCollection.slice(action.payload.from);
                }

                if (!plannerUnassignedRolesToggleFeatureEnabled) {
                    // This logic will be removed when this feature flag is removed (hence the use of !) as it is flawed. 
                    // It does not slice the resDataCollection correctly because it's [from] and [to] arguements are incorrect.
                    if (action.type === actionTypes.PLANNER_DATA_PAGE_LOADED) {
                        const currPagedMasterRecPlannerData = getCurrentPlannerPagedDataObject(state.plannerPage);
                        const plannerPageNumber = getCurrentPlannerPageNumber(state.plannerPage);
                        const { from, to } = currPagedMasterRecPlannerData.loadedPagesMap[plannerPageNumber - 1] || {};
                        resDataCollection = resDataCollection.slice(from, to);
                    }
                }

                const avatarRequestsIDs = resDataCollection
                    .filter(({ resource_guid }) => resourceAvatarLoaded(avatars, resource_guid, AVATAR_SIZES.TINY.label))
                    .map(entity => entity.resource_guid);

                return of(loadAvatars(avatarRequestsIDs, AVATAR_SIZES.TINY));

            }
        )
    );
};

const loadTableViewDataAvatarsActions = [
    TABLE_VIEW_DATA_LOADED,
    TABLE_VIEW_DATA_SCROLLED,
    TABLE_VIEW_DATA_PATCH,
    TABLE_VIEW_DATA_PAGE_LOADED
];

export const handleLoadTableViewDataAvatarsEpic = (action$, state$, { apis }) => {
    return action$.ofType(...loadTableViewDataAvatarsActions).pipe(
        mergeMap(
            (action) => {
                const tableViewPage = state$.value.tableViewPage;
                const avatars = state$.value.avatars;

                const selectedWorkspaceSettings = getSelectedWorkspaceSettings(tableViewPage.workspaces);
                const { masterRecTableName } = selectedWorkspaceSettings;

                let resDataCollection = [];
                if (masterRecTableName === TABLE_NAMES.RESOURCE) {
                    resDataCollection = getCurrentTableViewPagedData(tableViewPage).data;
                } else if (masterRecTableName === TABLE_NAMES.JOB) {
                    resDataCollection = getCurrentTableViewSubRecData(tableViewPage).data;
                }

                if (action.type === TABLE_VIEW_DATA_SCROLLED) {
                    resDataCollection = resDataCollection.slice(action.payload.from);
                }

                if (action.type === TABLE_VIEW_DATA_PAGE_LOADED) {
                    const currPagedMasterRecTableViewData = getCurrentTableViewPagedDataObject(tableViewPage);
                    const tableViewPageNumber = getCurrentTableViewPageNumber(tableViewPage);
                    const { from, to } = currPagedMasterRecTableViewData.loadedPagesMap[tableViewPageNumber - 1] || {};

                    resDataCollection = resDataCollection.slice(from, to);
                }

                const avatarRequestsIDs = resDataCollection
                    .filter(({ resource_guid }) => resourceAvatarLoaded(avatars, resource_guid, AVATAR_SIZES.TINY.label))
                    .map(entity => entity.resource_guid);

                return of(loadAvatars(avatarRequestsIDs, AVATAR_SIZES.TINY));

            }
        )
    );
};

export const handleAutocompleteResourceSearchSuccessEpic = (action$, state$, { apis }) => {
    return action$.ofType(actionTypes.AUTOCOMPLETE_RESOURCE_SEARCH_SUCCESS).pipe(
        map((action) => {
            return action.payload.suggestions.filter(({ id }) => id);
        }),
        switchMap(
            (avatarGuids) => {
                const avatars = state$.value.avatars;
                const loadAvatarIds = avatarGuids
                    .filter(({ id }) => resourceAvatarLoaded(avatars, id, AVATAR_SIZES.SMALL.label))
                    .map(({ id }) => id);

                return of(loadAvatars(loadAvatarIds, AVATAR_SIZES.SMALL));
            }
        )
    );
};

export const handleUploadAvatarSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.UPDATE_AVATAR_WINDOW.UPLOAD_AVATAR_SUCCESS).pipe(
        mergeMap((action) => {
            const { resourceGuid } = action.payload;
            const avatars = state$.value.avatars;
            const resAvatarConfig = getAvatarConfig(avatars, resourceGuid);

            const actions = getAvatarLoadActions(avatars, resAvatarConfig, resourceGuid);

            return from(actions);
        })
    );
};

export const handleRemoveAvatarSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.UPDATE_AVATAR_WINDOW.REMOVE_AVATAR_SUCCESS).pipe(
        mergeMap((action) => {
            const { resourceGuid } = action.payload;
            const avatars = state$.value.avatars;
            const resAvatarConfig = getAvatarConfig(avatars, resourceGuid);

            const actions = getAvatarLoadActions(avatars, resAvatarConfig, resourceGuid);

            return from([removeAvatar(resourceGuid), ...actions]);
        })
    );
};

export const handleLoadRoleListAvatarsEpic = (action$, state$, { apis }) => {
    return action$.ofType(actionTypes.ROLE_GROUP_DP.ROLE_GROUP_BUILD_ROLES).pipe(
        switchMap(
            (action) => {
                const avatars = state$.value.avatars;
                const { roles } = action.payload;
                let avatarRequestsIDs = [];

                roles
                    .filter(role => resourceAvatarLoaded(avatars, role.rolerequest_resource_guid, AVATAR_SIZES.TINY.label))
                    .forEach(role => {
                        const resourceGuid = role.rolerequest_resource_guid;
                        if (resourceGuid && !avatarRequestsIDs.includes(resourceGuid)) {
                            avatarRequestsIDs.push(resourceGuid);
                        }
                    });

                return avatarRequestsIDs.length
                    ? of(loadAvatars(avatarRequestsIDs, AVATAR_SIZES.TINY))
                    : empty();
            }
        )
    );
};

export const handleLoadRoleDetailsAvatarsEpic = (action$, state$, { apis }) => {
    return action$.ofType(`${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE}_${ROLE_REQUEST_FORM}`).pipe(
        mergeMap(
            (action) => {
                const avatars = state$.value.avatars;
                const resource_guid = `${TABLE_NAMES.RESOURCE}_guid`;

                const avatarRequestsIDs = action.payload.entities
                    .filter(role => {
                        const resourceGuid = role.entity[`${role.tableName}_${resource_guid}`];

                        return resourceAvatarLoaded(avatars, resourceGuid, AVATAR_SIZES.TINY.label);
                    })
                    .map(role => role.entity[`${role.tableName}_${resource_guid}`]);

                return of(loadAvatars(uniq(avatarRequestsIDs), AVATAR_SIZES.TINY));
            }
        )
    );
};

const createLoadAvatarEpic$ = createAPICallEpic(
    null,
    getApiCallEpicConfig(actionTypes.AVATARS.LOAD, avatarApiName, loadAvatar$, loadAvatarSuccess, null)
)();

const createUploadAvatarEpic$ = createAPICallEpic(
    null,
    getApiCallEpicConfig(actionTypes.UPDATE_AVATAR_WINDOW.UPLOAD_AVATAR, avatarApiName, uploadAvatar$, updateAvatarWindowUploadAvatarSuccess, null)
)();

const createRemoveAvatarEpic$ = createAPICallEpic(
    null,
    getApiCallEpicConfig(actionTypes.UPDATE_AVATAR_WINDOW.REMOVE_AVATAR, avatarApiName, removeAvatar$, updateAvatarWindowRemoveAvatarSuccess, null)
)();

export default function () {
    return combineEpics(
        handleLoadPlannerDataAvatarsEpic,
        handleLoadTableViewDataAvatarsEpic,
        handleUploadAvatarSuccessEpic,
        handleAutocompleteResourceSearchSuccessEpic,
        handleRemoveAvatarSuccessEpic,
        loadAvatarsEpic,
        handleLoadRoleDetailsAvatarsEpic,
        createLoadAvatarEpic$,
        createUploadAvatarEpic$,
        createRemoveAvatarEpic$,
        handleLoadRoleListAvatarsEpic
    );
}