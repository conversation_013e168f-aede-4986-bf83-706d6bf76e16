import { combineEpics } from 'redux-observable';
import { map, switchMap, delay, filter, mergeMap } from 'rxjs/operators';
import { of, concat, EMPTY, forkJoin } from 'rxjs';
import { ACCEPT_COOKIE_POLICY, APPLICATION_USER, ENTITY_WINDOW, SETUP_COOKIE_CONSENT_BANNER } from '../actions/actionTypes';
import * as appUserAC from '../actions/applicationUserActions';
import { ENTITY_WINDOW_MODULES, JOBS_PAGE_ALIAS, TABLE_NAMES } from '../constants';
import { getTableDataByIdSelection } from '../api/querySelectionUtils/index';
import { getPersistedItemsForUser } from '../localStorage';
import { RESOURCE_EMAIL, RESOURCE_FIRSTNAME, RESOURCE_SURROGATE_ID } from '../constants/fieldConsts';
import { API_KEYS } from '../constants/apiConsts';
import { APPLICATION_USER_RESOURCE_ROLE_GUID, FILTER_FIELD_NAMES } from '../constants/globalConsts';

const claimsApiKey = 'claims';
const permissionsApiKey = 'permissions';
const ENTITY_ACCESS_API_NAME = 'entityAccess';

export const loadApplicationUserData$ = (action$, state$, { apis }) => {
    return action$
        .ofType(APPLICATION_USER.LOAD.DATA)
        .pipe(
            switchMap(() => {
                return concat(
                    of(appUserAC.loadApplicationUserPermissions())
                );
            })
        );
};

export const claimUserRights$ = (action$, state$, { apis }) => {
    return action$
        .ofType(APPLICATION_USER.LOAD.CLAIMS)
        .pipe(
            switchMap(() => apis[claimsApiKey].getClaims$()),
            map(result => appUserAC.populateUserClaims(result))
        );
};

export const getUserPermissions$ = (action$, state$, { apis }) => {
    return action$
        .ofType(APPLICATION_USER.LOAD.PERMISSIONS)
        .pipe(
            switchMap(() => apis[permissionsApiKey].getPermissions$()),
            switchMap(result => {
                let permissions = result;

                // permissions === {} or null
                if (!Array.isArray(permissions)) {
                    permissions = [];
                }

                return concat(
                    of(appUserAC.populateUserPermissions(permissions)),
                    of(appUserAC.loadUserResourceData())
                );
            })
        );
};

export const loadUserResourceData$ = (action$, state$, { apis }) => {
    return action$
        .ofType(APPLICATION_USER.LOAD.RESOURCE_DATA)
        .pipe(
            delay(1),
            switchMap(() => {
                const resourceId = state$.value.applicationUser.id;
                const selectionFields = [RESOURCE_SURROGATE_ID, RESOURCE_EMAIL, RESOURCE_FIRSTNAME, FILTER_FIELD_NAMES.RESOURCE_MANAGER, FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME];

                return apis[API_KEYS.TABLE_DATA].getTableData$(TABLE_NAMES.RESOURCE, getTableDataByIdSelection(TABLE_NAMES.RESOURCE, resourceId, selectionFields));
            }),

            //Adding place for loading user resource data. Placeholder for now.
            switchMap(result => {
                const { [RESOURCE_SURROGATE_ID]: surrogateId, [RESOURCE_EMAIL]: email, [RESOURCE_FIRSTNAME]: firstName, [FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME]: resourceManagerName, [FILTER_FIELD_NAMES.RESOURCE_MANAGER]: resourceManager } = result[0];

                return concat(
                    of(appUserAC.populateUserSurrogateId(surrogateId)),
                    of(appUserAC.populateUserResourceData({ [RESOURCE_EMAIL]: email, [RESOURCE_FIRSTNAME]: firstName, [FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME]: resourceManagerName, [FILTER_FIELD_NAMES.RESOURCE_MANAGER]: resourceManager })),
                    of(appUserAC.userDataLoadedHandle({})),
                    of(appUserAC.forceRehydrate(surrogateId))
                );
            })
        );
};

export const forceRehydrateStoreEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(`${APPLICATION_USER.PERSIST.FORCE_REHYDRATE_STATE}`)
        .pipe(
            switchMap(({ payload }) => {
                const { surrogateId } = payload;
                const itemKey = 'persist:jobsPagePersist';
                const options = getPersistedItemsForUser(itemKey, surrogateId);
                const chain = [];

                Object.keys(options).forEach(key => {
                    const currentValues = options[key];

                    if (currentValues) {
                        chain.push(of(appUserAC.rehydrateState(JOBS_PAGE_ALIAS, key, currentValues)));
                    }
                });

                return concat(
                    ...chain
                );
            })
        );
};

export const setupCookieConsentBannerEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(SETUP_COOKIE_CONSENT_BANNER)
        .pipe(
            filter(() => state$.value.applicationUser.cookiePolicyStatus == null),
            switchMap(() => apis[permissionsApiKey].getCookiePolicyStatus$()),
            switchMap((response) => {

                return response === null ? EMPTY : of(appUserAC.populateUserResourceData({ cookiePolicyStatus: response }));
            })
        );
};

export const acceptCookiePolicyEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(ACCEPT_COOKIE_POLICY)
        .pipe(
            switchMap(() => apis[permissionsApiKey].acceptCookiePolicy$()),
            map(() => appUserAC.populateUserResourceData({ cookiePolicyStatus: new Date().toISOString() }))
        );
};

export default combineEpics(
    loadApplicationUserData$,
    claimUserRights$,
    getUserPermissions$,
    loadUserResourceData$,
    forceRehydrateStoreEpic$,
    acceptCookiePolicyEpic$,
    setupCookieConsentBannerEpic$
);
