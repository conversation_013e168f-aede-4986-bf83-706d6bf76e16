import { combineEpics } from 'redux-observable';
import { debounceTime, map, switchMap } from 'rxjs/operators';
import * as actionTypes from '../../actions/actionTypes';
import { ERROR_STATUS } from '../../constants';
import { getLibrarySectionsSelector } from '../../selectors/adminSettingSelectors/skillConfigurationSelectors';
import { getImportLibraryStatusFailure, getImportLibraryStatusSuccess } from '../../actions/adminSettings/importLibrarySkillsActions';
import store from '../../store/configureStore';
import { requestFailed } from '../../actions/httpRequestsActions';

// NOTE: The Import Library uses skillsConfigurationReducer since it is part of skillsConfiguration
const API_NAME = 'adminsetting';
const LONG_RUNNING_TASK_API_NAME = 'longRunningTask';
const TYPE = 'RetainLibraryImport';

const { IMPORT_LIBRARY_SKILLS, API_SUFFIX } = actionTypes;

const responseHandler = (response, action) => {
    if (response?.status === ERROR_STATUS) {
        return { type: `${action}${API_SUFFIX.FAILURE}`, response };
    }

    return { type: `${action}${API_SUFFIX.SUCCESS}`, payload: response };
};

const mapSkillsToPayload = (selectedSkillRowKeys) => {
    const skillIds = selectedSkillRowKeys.map(x => ({ id:x }));

    return { type: TYPE, importInputs: skillIds };
};

const fetchLibrarySectionsEpic = (action$, _, { apis }) => {
    const action = IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS;

    return action$
        .ofType(action)
        .pipe(
            switchMap(() => {
                return apis[API_NAME].fetchLibrarySections$();
            }),
            map((result) => responseHandler(result, action))
        );
};

const fetchLibrarySkillsEpic = (action$, _, { apis }) => {
    const action = IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS;

    return action$
        .ofType(action)
        .pipe(
            switchMap(({ payload: { categoryId, subCategoryId, checkSkills } }) => {

                return apis[API_NAME].fetchLibrarySkills$(categoryId, subCategoryId)
                    .pipe(
                        map((data) => responseHandler({ categoryId, subCategoryId, data, checkSkills }, action))
                    );
            })
        );
};

const importLibrarySkillsEpic = (action$, state$, { apis }) => {
    const action = IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_SKILLS;

    return action$
        .ofType(action)
        .pipe(
            switchMap(() => {
                const librarySections = getLibrarySectionsSelector(state$.value)();
                const { selectedSkillRowKeys } = librarySections;
                const payload = mapSkillsToPayload(selectedSkillRowKeys);

                return apis[LONG_RUNNING_TASK_API_NAME].startTask$(payload);
            }),
            map((response) => {
                if (response?.status === ERROR_STATUS) {
                    // Dispatch response for the error to show in UI
                    store.dispatch(requestFailed({ serverResponse:response }));

                    return getImportLibraryStatusFailure();
                }

                return getImportLibraryStatusSuccess(true);
            })
        );
};

const getImportStatusEpic = (action$, _, { apis }) => {
    const action = IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_STATUS;

    return action$
        .ofType(action)
        .pipe(
            switchMap(() => {
                return apis[LONG_RUNNING_TASK_API_NAME].getStatusByType$(TYPE);
            }),
            map((result) => responseHandler(result, action))
        );
};

const searchImportLibrarySkillsEpic = (action$, _, { apis }) => {
    const action = IMPORT_LIBRARY_SKILLS.SEARCH_SKILL;

    return action$
        .ofType(action)
        .pipe(
            debounceTime(3000), // Run with some delay
            switchMap(({ payload: { searchTerm } }) => {
                return apis[API_NAME].searchImportLibrarySkill$(searchTerm);
            }),
            map((result) => responseHandler(result, action))
        );
};

const getImportLibrarySkillsEpics = () => {
    return combineEpics(
        fetchLibrarySectionsEpic,
        fetchLibrarySkillsEpic,
        importLibrarySkillsEpic,
        getImportStatusEpic,
        searchImportLibrarySkillsEpic
    );
};

export default getImportLibrarySkillsEpics;