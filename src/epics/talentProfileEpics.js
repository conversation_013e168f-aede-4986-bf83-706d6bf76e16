import { combineEpics } from 'redux-observable';
import { map, switchMap, mergeMap, filter } from 'rxjs/operators';
import * as actionTypes from '../actions/actionTypes';
import * as tpAc from '../actions/talentProfileActions';
import * as rsAc from '../actions/resourceSkillsActions';
import { of, concat, empty, forkJoin } from 'rxjs';
import { getTPResourceId, getTPConfig, getTPUIEntity, getEditLinkedFields, profileSkillsSectionVisible, getTalentProfileMessages, getTPResourceSurrogateId, getSecurityProfileId, getTPEntity } from '../selectors/talentProfileSelectors';
import { buildTPSelection, buildTPSelectionLegacy, getProfileFieldNamesFromConfig, getProfileFieldNamesFromConfigLegacy, getResourceSectionAuditFields, getSectionsUpdateAuditInfo, transformProfileData } from '../utils/talentProfileUtils';
import { getLinkedTableData, getTableDatasLoadedActions, getFlatTableData } from '../utils/linkedDataUtils';
import { loadMoreTableDataSuccess } from '../actions/tableDataActions';
import { getApiCallEpicConfig, createAPICallEpic } from './epicGenerators/apiCallEpicGenerator';
import { ENTITY_WINDOW_MODULES, FIELD_DATA_TYPES } from '../constants';
import { ERROR_STATUS } from '../constants/apiConsts';
import { AVATAR_SIZES } from '../constants/avatarConsts';
import { loadAvatar, setAvatarConfig } from '../actions/avatarActions';
import { hasAvatarConfig } from '../selectors/avatarSelectors';
import { getFieldInfo, getTableStructure, getFieldInfos } from '../selectors/tableStructureSelectors';
import { getIsCustomField, getIsHistoryField } from '../utils/fieldUtils';
import { processHistoryField } from '../lib/historyField/utils';
import _, { result } from 'lodash';
import { getLoadFilterAccessData$ } from './dataGridPageDataEpics';
import { getFieldTableName, isTableFieldLinked } from '../utils/tableStructureUtils';
import { createTPSection, createTPSectionField } from '../state/talentProfile/configBuilders';
import { CRUD_OPERATIONS, LICENSE_KEYS_ADMIN_SETTINGS, TABLE_NAMES, URL_PARAMS, FEATURE_FLAGS, SKILL_PREFERENCE_TYPES } from '../constants/globalConsts';
import { FIELD_DATA_ACCESS_LEVEL, RECOMMENDATION_VIEWED } from '../constants/fieldConsts';
import { getIsCustomPlanningDataField, getIsCustomLookupField } from '../utils/fieldUtils';
import { getEntityOptionFields } from '../connectedComponents/connectedEntityLookupWindow';
import { TALENT_PROFILE_LINKED_DATA_ALIAS, TALENT_PROFILE_CONFIG_KEYS, TALENT_PROFILE_ALIAS, PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';
import { loadAttachments } from '../actions/attachmentsActions';
import { setPageState } from '../actions/pageStateActions';
import { replaceUrl } from '../actions/navigateActions';
import { replaceBrowserHistoryUrl } from '../history';
import { ERROR_PAGES } from '../pages/pages';
import { loadUserEntityAccess } from '../actions/userEntityAccessActions';
import { createTableDataCRUDPipe } from '../api/pipes.api';
import { FIELD_DATA_UIFIELD_CATEGORIES } from '../constants/fieldConsts';
import { getLicenseValuesByKeySelector } from '../selectors/commonSelectors';
import { entityWindowClose } from '../actions/entityWindowActions';
import { createEntityWindowTableDataChangeInterceptorEpic } from './epicGenerators/entityWindowInterceptors';
import { getWorkHistoryAlias } from '../utils/workHistoryUtils';
import { loadResourceWorkHistory } from '../actions/workHistoryActions';
import { educationSectionLoadData } from '../actions/educationSectionActions';
import { EDUCATION_SECTION_ALIAS } from '../constants/educationSectionConsts';
import { experienceSectionLoadData } from '../actions/experienceSectionActions';
import { EXPERIENCE_SECTION_ALIAS } from '../constants/experienceSectionConstants';
import { getCurrentPageAliasSelector } from '../selectors/navigationSelectors';
import * as ADMIN_CONSTS from '../constants/adminSettingConsts';
import { parseToUtcDate } from '../utils/dateUtils';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors.js';
import { getGroupedResourceSkills } from '../selectors/resourceSkillsSelectors';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';

const { SKILL_CONSTS } = ADMIN_CONSTS;
const {
    TALENT_PROFILE,
    RESOURCE_SKILLS
} = actionTypes;

const {
    RESOURCE_SURROGATE_ID,
    RESOURCE_NAME
} = URL_PARAMS;

const { licenseAttachmentsEnabled } = LICENSE_KEYS_ADMIN_SETTINGS;

const apiKey = 'tableData';
const resourceSkillsApiKey = 'resourceSkills';
const profileTableName = TABLE_NAMES.RESOURCE;

const doGetTPDataApiCall$ = (apis, profileTableName, selection) => {
    return apis[apiKey].getTableData$(profileTableName, selection);
};

const doGetTPConfigApiCall$ = (apis) => {
    return apis['adminSettingsConsumer'].getTPConfiguration$();
};

const doPatchTPDataApiCall$ = (api, payload) => {
    const { tableName, tableDataEntryGuid, tableData } = payload;

    return api.withPipe(createTableDataCRUDPipe(payload, CRUD_OPERATIONS.UPDATE)).patchTableData$(tableName, tableDataEntryGuid, tableData);
};

const mapFieldValueSubmitAction = (fieldInfo, uiEntity) => {
    let fieldValue = uiEntity[fieldInfo.name].value;


    if (fieldInfo && getIsHistoryField(fieldInfo)) {
        fieldValue = processHistoryField(fieldValue, fieldInfo);
    }

    if (fieldInfo && fieldInfo.dataType === FIELD_DATA_TYPES.STRING && fieldValue) {
        fieldValue = fieldValue.trim();
    }

    if (uiEntity[fieldInfo.name].value === undefined) {
        fieldValue = null;
    }

    return fieldValue;
};

const getLinkedFields = (primaryTableName, displayFields, getFieldInfo) => {
    return displayFields.reduce((accumulator, fieldName) => {
        const fieldInfo = getFieldInfo(primaryTableName, fieldName);

        if (fieldInfo) {
            const isLinkedField = isTableFieldLinked(primaryTableName, fieldInfo);

            if (isLinkedField) {
                accumulator.push({
                    linkedTableName: getFieldTableName(fieldInfo, primaryTableName),
                    fieldName
                });
            }
        }

        return accumulator;
    }, []);
};

const getLinkedRecordsIds = (data, linkedFields, shouldAddRecordId = () => true) => {
    return data.reduce((accumulator, record) => {
        linkedFields.forEach(({ fieldName, linkedTableName }) => {
            if (record[fieldName] && shouldAddRecordId(record[fieldName])) {
                if (!(linkedTableName in accumulator)) {
                    accumulator[linkedTableName] = {};
                }

                accumulator[linkedTableName][record[fieldName]] = true;
            }
        });

        return accumulator;
    }, {});
};

const getLoadLinkedTableData$ = (table, ids, apis) => {
    return getLoadFilterAccessData$(
        table,
        ids,
        [
            `${table}_guid`,
            `${table}_description`
        ],
        apis
    );
};

const loadLinkedDatasBase$ = (alias, data, fieldNames, tableName, apis, state) => {
    const dataPage = state[alias];
    tableName = tableName || dataPage.tableName;

    const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);

    const linkedFields = getLinkedFields(tableName, fieldNames, getFieldInfoWrapped);
    const tableSelections = getLinkedRecordsIds(data, linkedFields);

    const wrappedGetLoadData$ = (table) => getLoadLinkedTableData$(
        table,
        Object.keys(tableSelections[table]),
        apis
    );

    const loadDatas$ = Object
        .keys(tableSelections)
        .map(wrappedGetLoadData$);

    return loadDatas$.length > 0
        ? forkJoin(loadDatas$)
        : of(undefined);
};

export const responseFollowUp = (response, successChain) => {
    let followUp = empty();

    if (response.status !== ERROR_STATUS) {
        followUp = successChain;
    }

    return followUp;
};

export const loadProfileLinkedData$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.LOAD.LINKED_DATA)
        .pipe(
            switchMap((action) => {
                const { profileData } = action;
                const alias = PROFILE_PAGE_ALIAS;
                const config = getTPConfig(state$.value);
                // Determine the correct set of profile field names based on the feature flag.
                const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state$.value);
                const fieldNames = talentProfilePageTransformedEnabled ? getProfileFieldNamesFromConfig(config) : getProfileFieldNamesFromConfigLegacy(config);

                return loadLinkedDatasBase$(alias, profileData, fieldNames, profileTableName, apis, state$.value);

            },
            (action, result) => {
                return [result, action];
            }),
            switchMap(([results = [], action]) => {
                const getTableDataLoadedAction = (tableDataResult) => {
                    const { table, data } = tableDataResult;

                    return of(loadMoreTableDataSuccess(
                        TALENT_PROFILE_LINKED_DATA_ALIAS,
                        {
                            tableDataGuid: table,
                            tableNames: [table]
                        },
                        data
                    ));
                };

                return concat(
                    ...results.map(getTableDataLoadedAction)
                );
            })
        );
};

export const loadTPPageData$ = (action$, state$) => {
    return action$
        .ofType(TALENT_PROFILE.LOAD.DATA)
        .pipe(
            switchMap(() => {
                const resourceSurrogateId = getTPResourceSurrogateId(state$.value);
                const config = getTPConfig(state$.value);
                const loadExternalEntryData = true;

                let followingAction = of(tpAc.loadTpEntryData(resourceSurrogateId, loadExternalEntryData));

                if (0 === Object.keys(config).length)
                    followingAction = of(tpAc.loadTPConfig());

                return followingAction;
            })
        );
};

const tpConfigAddCustomFieldsSection = (tpConfig, state) => {
    let config = { ...tpConfig };
    if (config && config.status !== ERROR_STATUS) {
        const customFields = getFieldInfos(getTableStructure(state), TABLE_NAMES.RESOURCE, getIsCustomField) || {};
        const customFieldsCount = Object.keys(customFields).length;

        if (0 !== Object.keys(config).length && 0 !== customFieldsCount) {
            //setting the following as local consts as this will come from back-end in future and we don't need these consts anywhere else
            const mainSectionName = TALENT_PROFILE_CONFIG_KEYS.SECTIONS;
            const contentType = 'Section';
            const { additionalDetailsSectionTitle = 'Additional details' } = getTranslationsSelector(state, { sectionName: 'talentProfilePage', idsArray: ['additionalDetailsSectionTitle'] });
            const newSection = createTPSection({
                sectionTitle: additionalDetailsSectionTitle,
                sectionKey: 'section_additionalDetails',
                displayType: 'columnarFields',
                canNavigate: true,
                numberOfColumns: 2,
                maxFieldsPerColumn: Math.ceil(customFieldsCount / 2),
                fields: Object.keys(customFields).map((key) => createTPSectionField(customFields[key]), contentType),
                contentType
            });
            //currnetly we want to insert the custom fields section before the last existing section 'skills'
            config[mainSectionName].splice(config[mainSectionName].length - 1, 0, newSection);
        }
    }

    return config;
};

const tpConfigHiddenFieldsFromSections = (tpConfig, state) => {
    const tableStructure = getTableStructure(state);
    let newConfig = {};
    Object.keys(tpConfig).forEach(sectionFilter => {
        newConfig[sectionFilter] = Array.isArray(tpConfig[sectionFilter]) ? [...tpConfig[sectionFilter]] : { ...tpConfig[sectionFilter] };
        Object.keys(tpConfig[sectionFilter])
            .forEach(section => {
                if (tpConfig[sectionFilter][section].Fields) {
                    newConfig[sectionFilter][section].Fields = tpConfig[sectionFilter][section].Fields
                        .filter(field => {
                            const fieldInfo = getFieldInfo(tableStructure, field.TableName.toLowerCase(), field.FieldName);
                            const { accessLevel = FIELD_DATA_ACCESS_LEVEL.HIDDEN } = fieldInfo || {};

                            return accessLevel !== FIELD_DATA_ACCESS_LEVEL.HIDDEN;
                        });
                }
            });
    });

    return newConfig;
};

const tpConfgAddTitleForProfileHeaderSection = (config) => {
    let { ProfileHeaderSection: { HeaderFieldGroup } } = config;
    if (HeaderFieldGroup && !HeaderFieldGroup.Title) {
        HeaderFieldGroup.Title = 'Name';
    }

    return config;
};

export const loadTPPageConfig$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.LOAD.CONFIG)
        .pipe(
            switchMap(() => doGetTPConfigApiCall$(apis)),
            map((result) => tpConfigAddCustomFieldsSection(result, state$.value)),
            map((result) => tpConfgAddTitleForProfileHeaderSection(result)),
            map((result) => tpConfigHiddenFieldsFromSections(result, state$.value)),
            switchMap(result => {
                const staticMessages = getTalentProfileMessages(state$.value);
                const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state$.value);
                // If the feature flag is enabled, transform the TP metadata JSON config.
                // otherwise use the original result as-is.
                const finalResult = talentProfilePageTransformedEnabled
                    ? transformProfileData(result)
                    : result;

                return responseFollowUp(
                    finalResult,
                    concat(
                        of(tpAc.populateTPConfig(finalResult, staticMessages)),
                        of(tpAc.loadTPData())
                    )
                );
            })
        );
};

export const loadProfileEntryData$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.LOAD.ENTRY_DATA)
        .pipe(
            switchMap((action) => {
                const { surrogateId } = action.payload;
                const resourceId = getApplicationUserId(state$.value);
                const SkillApprovalFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state$.value);
                const config = getTPConfig(state$.value);
                const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state$.value), tableName, fieldName);
                const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state$.value);
                const tpSelection = talentProfilePageTransformedEnabled ? buildTPSelection(parseInt(surrogateId), profileTableName, config, getFieldInfoWrapped, resourceId, SkillApprovalFeatureFlag) : buildTPSelectionLegacy(parseInt(surrogateId), profileTableName, config, getFieldInfoWrapped, resourceId, SkillApprovalFeatureFlag);

                return doGetTPDataApiCall$(apis, profileTableName, tpSelection);
            },
            (action, result) => {
                return [result, action];
            }),
            switchMap(([result, action]) => {

                if (Array.isArray(result) && result.length == 0 || result.status === ERROR_STATUS) {
                    replaceBrowserHistoryUrl(ERROR_PAGES.NOT_FOUND.navigationLink);

                    return empty();
                }

                const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state$.value), tableName, fieldName);
                const linkedTableDatas = getLinkedTableData(result, profileTableName, getFieldInfoWrapped);
                const linkedTableDatasLoadedActions = getTableDatasLoadedActions(TALENT_PROFILE_LINKED_DATA_ALIAS, linkedTableDatas);
                const profileData = getFlatTableData(result, profileTableName, getFieldInfoWrapped);

                const {
                    resource_guid: resourceId,
                    resource_surrogate_id: resourceSurrogateId,
                    resource_firstname: resourceFirstName,
                    resource_lastname: resourceLastName
                } = result[0];

                const config = getTPConfig(state$.value);
                const sectionsNames = ['EDUCATION', 'SKILLS', 'EXPERIENCE', 'CME'];

                const sectionsUpdateAuditInfo = getSectionsUpdateAuditInfo(getResourceSectionAuditFields(config.ProfileSections, sectionsNames, result[0]));
                const newParams = {
                    [RESOURCE_SURROGATE_ID]: resourceSurrogateId,
                    [RESOURCE_NAME]: `${resourceFirstName} ${resourceLastName}`,
                    resourceId
                };

                let chain = [
                    of(setPageState(PROFILE_PAGE_ALIAS, { params: newParams })),
                    of(replaceUrl(newParams))
                ];

                if (true === action.payload.loadExternalData) {
                    chain.push(
                        of(tpAc.loadTPExternalEntryData(resourceId))
                    );
                }

                linkedTableDatasLoadedActions.forEach(action => chain.push(of(action)));

                chain.push(of(loadResourceWorkHistory(getWorkHistoryAlias(TALENT_PROFILE_ALIAS), resourceId)));
                chain.push(of(educationSectionLoadData(`${EDUCATION_SECTION_ALIAS}_${TALENT_PROFILE_ALIAS}`, resourceId)));
                chain.push(of(experienceSectionLoadData(`${EXPERIENCE_SECTION_ALIAS}_${TALENT_PROFILE_ALIAS}`, resourceId)));
                chain.push(of(tpAc.clearTalentProfileAudit()));
                chain.push(of(tpAc.populateTalentProfileAudit(sectionsUpdateAuditInfo)));
                const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state$.value);

                return concat(
                    ...chain,
                    of(tpAc.populateTPEntryData(profileData[0] || {}, talentProfilePageTransformedEnabled))
                );
            })
        );
};

export const loadProfileExternalData$ = (action$, state$) => {
    return action$
        .ofType(TALENT_PROFILE.LOAD.ENTRY_EXTERNAL_DATA)
        .pipe(
            switchMap((action) => {
                const { resourceId } = action.payload;
                const attachmentsEnabled = (getLicenseValuesByKeySelector(state$.value)(licenseAttachmentsEnabled) || {}).subscribedCount;

                let chain = [
                    hasAvatarConfig(state$.value.avatars, resourceId)
                        ? empty()
                        : of(setAvatarConfig(resourceId)),
                    of(loadAvatar(resourceId, AVATAR_SIZES.MEDIUM.label)),
                    of(loadAvatar(resourceId, AVATAR_SIZES.TINY.label)),
                    of(loadUserEntityAccess([resourceId], TABLE_NAMES.RESOURCE))
                ];
                attachmentsEnabled && chain.push(of(loadAttachments(TALENT_PROFILE_ALIAS, TABLE_NAMES.RESOURCE, resourceId)));

                if (profileSkillsSectionVisible(state$.value)) {
                    chain.push(of(rsAc.loadResourceSkills(resourceId)));
                }

                return concat(...chain);
            })
        );
};

export const loadProfileSkillsData$ = (action$, state$, { apis }) => {
    return action$
        .ofType(RESOURCE_SKILLS.LOAD.SKILLS)
        .pipe(
            filter(() => true === profileSkillsSectionVisible(state$.value)),
            switchMap(() => {
                const resourceId = getTPResourceId(state$.value);

                return apis[resourceSkillsApiKey].getResourceSkills$(resourceId);
            }, (action, result) => {
                return [action, result];
            }),
            // After receiving the result, dispatch actions to populate resource skills,
            // and if the transformed talent profile page is enabled, also update the entity's skills
            switchMap(([{ id }, result]) => {
                const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state$.value);

                return of(
                    rsAc.populateResourceSkills(null, { entityId: id }, result),
                    ...(talentProfilePageTransformedEnabled ? [tpAc.updateEntitySkillsAfterResourceEdit(id)] : [])
                );
            })
        );
};

/**
 * Epic to update a talent profile entity with the latest primary and secondary skills
 * after a resource edit.
 *
 * @param {Observable} action$ - Stream of Redux actions
 * @param {Observable} state$ - Stream of Redux state
 * @returns {Observable} A new action to populate updated talent profile entry data
 */

export const updateEntityWithLatestSkills$ = (action$, state$) =>
    action$.ofType(TALENT_PROFILE.POPULATE.UPDATE_ENTITY_SKILLS_AFTER_RESOURCE_EDIT).pipe(
        map(action => {
            const state = state$.value;
            const { entityId } = action;
            if (!entityId) return empty();

            const resourceSkillsGroups = getGroupedResourceSkills(state)(entityId);
            if (!resourceSkillsGroups) return empty();

            let primarySkill = null;
            let secondarySkill = null;

            for (const group of Object.values(resourceSkillsGroups)) {
                const skills = group?.skills;

                if (Array.isArray(skills)) {
                    for (const { skillPreference, name } of skills) {
                        if (!primarySkill && skillPreference === SKILL_PREFERENCE_TYPES.PRIMARY_SKILL) {
                            primarySkill = name;
                        } else if (!secondarySkill && skillPreference === SKILL_PREFERENCE_TYPES.SECONDARY_SKILL) {
                            secondarySkill = name;
                        }
                        if (primarySkill && secondarySkill) break;
                    }
                }
            }

            const entity = getTPEntity(state);
            if (!entity) return empty();

            const updatedEntity = {
                ...entity,
                resource_primary_skill: primarySkill,
                resource_secondary_skill: secondarySkill
            };

            return tpAc.populateTPEntryData(updatedEntity, true);
        })
    );



export const submitProfileField$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.EDIT.CONTEXTUAL_EDIT_APPLY)
        .pipe(
            map(action => {
                const { fieldInfo } = action.payload;
                const uiEntity = getTPUIEntity(state$.value);
                const resourceId = getTPResourceId(state$.value);
                const fieldValue = mapFieldValueSubmitAction(fieldInfo, uiEntity);
                const tableData = {
                    [fieldInfo.name]: fieldValue
                };

                return tpAc.talentProfileContextualEditApplySubmit(resourceId, profileTableName, fieldInfo, tableData);
            })
            // //TODO add errorHandling and validation
            // map(result => tpAc.loadTpEntryData(getTPResourceId(state$.value), {}))
        );
};

export const submitMultipleProfileField$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.EDIT.MULTIPLE_CONTEXTUAL_EDIT_APPLY)
        .pipe(
            switchMap(action => {
                const { fieldInfo } = action.payload;
                const uiEntity = getTPUIEntity(state$.value);
                const resourceId = getTPResourceId(state$.value);
                const config = getTPConfig(state$.value);
                const editLinkedFieldNames = getEditLinkedFields(config, fieldInfo.name);

                //Remove fields that have read only access
                const tableStructure = getTableStructure(state$.value);
                const fields = editLinkedFieldNames.filter(field => {
                    const fieldInfo = getFieldInfo(tableStructure, TABLE_NAMES.RESOURCE, field);

                    return fieldInfo.accessLevel === FIELD_DATA_ACCESS_LEVEL.EDITABLE &&
                        fieldInfo.uiFieldCateory !== FIELD_DATA_UIFIELD_CATEGORIES.SYSTEM_READONLY;
                });

                const tableData = {};

                fields.forEach(field => tableData[field] = uiEntity[field].value);

                return doPatchTPDataApiCall$(apis[apiKey], { tableDataEntryGuid: resourceId, tableName: profileTableName, tableData });
            }),
            //TODO add errorHandling and validation
            map(result => tpAc.loadTpEntryData(getTPResourceSurrogateId(state$.value)))
        );
};

export const loadPatchedEntry$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.EDIT.CONTEXTUAL_EDIT_APPLY_SECCESS)
        .pipe(
            mergeMap(action => {
                const { tableData, fieldInfo } = action.payload;
                let following = empty();

                if (FIELD_DATA_TYPES.ID === fieldInfo.dataType) {
                    following = of(tpAc.loadProfileLinkedData([tableData]));
                } else if (getIsCustomPlanningDataField(fieldInfo) || getIsCustomLookupField(fieldInfo)) {
                    const uiEntity = getTPUIEntity(state$.value);
                    const primaryFieldTableName = getFieldTableName(fieldInfo, profileTableName);
                    const fieldValue = {
                        id: uiEntity[fieldInfo.name].value,
                        value: uiEntity[fieldInfo.name].displayValue
                    };
                    const tableDatas = getEntityOptionFields(fieldValue, primaryFieldTableName);

                    if (tableDatas) {
                        following = of(
                            loadMoreTableDataSuccess(
                                TALENT_PROFILE_LINKED_DATA_ALIAS,
                                { tableDataGuid: primaryFieldTableName, tableNames: [primaryFieldTableName] },
                                [tableDatas]
                            )
                        );
                    }
                }

                return following;
            })
        );
};

export const patchTPFieldControlEpic$ = createAPICallEpic(
    null,
    getApiCallEpicConfig(
        TALENT_PROFILE.EDIT.CONTEXTUAL_EDIT_APPLY_SUBMIT,
        apiKey,
        doPatchTPDataApiCall$,
        tpAc.talentProfileContextualEditApplySuccess,
        tpAc.talentProfileContextualEditApplyError
    )
)();

export const talentProfileEntityWindowJobUpdateSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.JOB,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL)
    ]
)();

export const talentProfileEntityWindowJobUpdateErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.JOB,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL)
    ]
)();

export const loadTalentProfileSectionAuditData$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.LOAD.AUDIT_SECTION_DATA)
        .pipe(
            filter(() => getCurrentPageAliasSelector(state$.value) === PROFILE_PAGE_ALIAS),
            switchMap(({ payload }) => {
                const { sectionType } = payload;
                const selection = {
                    fields: [
                        {
                            fieldName: 'resource_section_lastupdated',
                            fieldAlias: `resource_${sectionType}_lastupdated`,
                            parameters: {
                                SectionName: sectionType
                            }
                        }
                    ],
                    filter: {
                        filterGroupOperator: 'And',
                        filterLines: [
                            {
                                field: 'resource_surrogate_id',
                                operator: 'Equals',
                                value: getTPResourceSurrogateId(state$.value)
                            }
                        ],
                        subFilters: null
                    }
                };

                return doGetTPDataApiCall$(apis, profileTableName, selection);
            }, (action, result) => ({ action, result })),
            switchMap(({ action: { payload: { sectionType } }, result }) => {
                if (Array.isArray(result) && result.length == 0 || result.status === ERROR_STATUS) {
                    replaceBrowserHistoryUrl(ERROR_PAGES.NOT_FOUND.navigationLink);

                    return empty();
                }

                const sectionsUpdateInfo = getSectionsUpdateAuditInfo([
                    { ...result[0], sectionType }
                ]);

                return of(tpAc.populateTalentProfileAudit(sectionsUpdateInfo));
            })
        );
};

export const updateRecommendationViewed$ = (action$, state$, { apis }) => {
    return action$
        .ofType(TALENT_PROFILE.UPDATE_RECOMMENDATION_VIEWED).pipe(
            switchMap((action) => {
                const { resourceId } = action.payload;
                const tableData = { [RECOMMENDATION_VIEWED]: parseToUtcDate(new Date()) };

                return doPatchTPDataApiCall$(apis[apiKey], { tableDataEntryGuid: resourceId, tableName: profileTableName, tableData });
            }),
            //TODO add errorHandling and validation
            map(result => tpAc.loadTpEntryData(getTPResourceSurrogateId(state$.value)))
        );
};

export default combineEpics(
    loadTalentProfileSectionAuditData$,
    loadProfileLinkedData$,
    loadProfileEntryData$,
    loadProfileExternalData$,
    loadProfileSkillsData$,
    updateEntityWithLatestSkills$,
    submitProfileField$,
    submitMultipleProfileField$,
    loadTPPageData$,
    loadTPPageConfig$,
    patchTPFieldControlEpic$,
    loadPatchedEntry$,
    talentProfileEntityWindowJobUpdateSuccessInterceptorEpic,
    talentProfileEntityWindowJobUpdateErrorInterceptorEpic,
    updateRecommendationViewed$
);