import { of, concat, empty, from, forkJoin, EMPTY } from 'rxjs';
import { switchMap, mergeMap, map, filter, takeUntil, concatMap, debounceTime } from 'rxjs/operators';
import { complete<PERSON>hain } from './chainUtils';
import { isEmpty, mergeWith, unionBy } from 'lodash';
import * as actionTypes from '../actions/actionTypes';
import {
    plannerDataLoaded,
    plannerDataScrolled,
    reloadPlannerData,
    loadPlannerData,
    plannerDataPatch,
    updatePlannerData,
    clearPlannerSelection,
    buildBarColours,
    loadCustomColourSchemeSuccess,
    batchCRUDErrorPrompt,
    assignResourceToRolePlannerPage,
    batchUpdatePlannerRolerequestResource,
    digestPlannerDataLoaded,
    plannerDataPageLoaded,
    setPlannerPageNumber,
    loadPlannerRolerequestStatusesSuccess,
    splitBarsSuccessAction,
    splitBarsErrorAction
} from '../actions/plannerDataActions';
import { loadPagedAccessDataSuccess, loadPagedResultsDataSuccess, patchMultiplePagedDataSuccess, pagedDataRegisterKeepAlive, clearCachedPaging } from '../actions/pagedDataActions';
import { ENTITY_WINDOW_MODULES, TABLE_NAMES, ERROR_STATUS, SUCCESS_STATUS } from '../constants';
import { JOBS_PAGE_ALIAS, JOB_PAGE_PAGED_DATA, ROLE_GROUP_PAGE_TABLE_DATA_ALIAS } from '../constants/jobsPageConsts';
import { PLANNER_MASTER_REC_ALIAS, PLANNER_SUB_REC_ALIAS, PLANNER_BOOKING_GROUPS_ALIAS, PLANNER_TABLE_DATAS_SUFFIX, PLANNER_PAGE_ALIAS, ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, PLANNERPAGE_FILTER_ALIAS, COLOUR_FIELD, COLOUR_TYPE_SELECTION_ACTORS, PLANNER_VIEW_MODES, PLANNER_ROLEREQUESTS_ALIAS, PLANNER_PAGE_TABLE_TO_GROUP_MAP, PLANNER_PAGE_TABLE_TO_GROUP_ALIAS, PLANNER_DATA_OPERATIONS, BAR_OPTIONS_DISPLAY_TABLE_NAMES, PLANNER_ROLEREQUESTGROUP_ALIAS } from '../constants/plannerConsts';
import * as wss from '../selectors/workspaceSelectors';
import * as plannerPageSelectors from '../selectors/plannerPageSelectors';
import { combineEpics } from 'redux-observable';
import * as tableDataEpics from './tableDataEpics';
import * as pagedDataEpics from './pagedDataEpics';
import { createWorkspacePagedDataInterceptorEpic, createWorkspaceSubRecDataInterceptorEpic } from './epicGenerators/workspaceTableDataChangeInterceptor';
import { createDataGridAddFieldsEpic } from './dataGridPageDataEpics';
import { getFlatTableData, getLinkedTableData, getTableDatasLoadedActions, getTableDataByTable } from '../utils/linkedDataUtils';
import { plannerPageFieldsChangedSuccess } from '../actions/workspaceSettingsActions';
import {
    getPlannerFieldToLoad,
    getPlannerPageSelection,
    getLoadPlannerAdditionalDataActions,
    getLoadPlannerDataDateSensitiveActions,
    getUpdatedMasterRecIds,
    getPlannerView,
    getDateSensitiveFieldsFilters,
    getLoadPlannerPotentialConflictsAction
} from '../utils/plannerQuerySelectionUtils';
import { getFieldInfoSelector } from '../selectors/tableStructureSelectors';

import {
    loadTableDataSuccess,
    digestInsertTableDataSuccess,
    digestInsertTableDataError,
    digestDeleteTableDataSuccess,
    digestPatchTableDataSuccess,
    loadMoreTableDataSuccess,
    patchTableDataSuccess,
    patchMultipleTableDataSuccess,
    selectEdits,
    persistSelectedEdits,
    batchPatchTableDataError,
    batchDigestPatchTableDataSuccess,
    batchPatchTableDataSuccess,
    batchPatchGroupedTableData
} from '../actions/tableDataActions';

import { setSuccessToaster, setToasterConfig, showToasterMessage } from '../actions/adminSettings/adminSettingActions';
import { entityWindowClose, entityWindowOpen, entityWindowOpenForMultiple, entityWindowSetFormErrorMessages } from '../actions/entityWindowActions';
import { digestPatchPagedDataSuccess, patchPagedDataSuccess, loadAdditionalColumnsSuccess, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError, batchPatchPagedTableDataSuccess } from '../actions/pagedDataActions';
import { createEntityWindowTableDataChangeInterceptorEpic } from './epicGenerators/entityWindowInterceptors';
import { dataGridLoadData, reloadJobsPageDataGridAction } from '../actions/dataGridActions';
import { setDetailsPaneCollapsed, setDetailsPaneSelectedTabIsBatched, setDetailsPaneVisibility } from '../actions/detailsPaneActions';
import { browserHistory } from '../history';
import { batchActions } from 'redux-batched-actions';
import { omit, getData } from '../utils/commonUtils';
import { getTableDataEntryGuid, areRecordsExpanded, getToasterMessageConfig, getShowToasterMessagesActionsOfInterest, getPlannerLoadGroupedTableDataActions, getRecordsMap, getBarsData, getPlannerLoadMoreGroupedTableDataActions, getPatchMultipleGrouppedTableDataSuccessActions, buildSelectEditsPayload, getBatchDeleteMultipleGrouppedTableDataSuccessActions, getCurrentRecBars, getMissingBarIdsMap, getBarPersistEditsActions, getToasterMessageCustomType, buildUpdateRoleRequestStatusTableData, filterTableDatas, getUniqueBarColours, getPlannerLoadedGroupsUserEntityAccessHandlerActions, getMappedBarColours, getAdditionalUserAccessLoadActions, getEntityLinkedGuids, getEntityGuids } from '../utils/plannerDataUtils';
import { getDefaultColourSchemeId } from '../selectors/applicationColourSchemesSelectors';
import { combineAPIEpics } from './middlewareExtensions';
import { buildColourTypeSelection, buildColourTypesParamsField, isCustomColourScheme } from '../utils/colourSchemeUtils';
import { CRUD_OPERATIONS, FEATURE_FLAGS, LICENSE_KEYS_ADMIN_SETTINGS, OPERATORS } from '../constants/globalConsts';
import { getCustomOption } from '../utils/dateToggleOptionsUtils';
import { clearUserEntityAccess, handleLoadedUserEntityAccess, loadUserEntityAccess } from '../actions/userEntityAccessActions';
import { isEditAllEntity } from '../utils/entityStructureUtils';
import { getAliasedPlannerDataEntityGroupEpics, getPlannerDataEntityGroupApiEpics, getPlannerDataEntityGroupEpics } from './plannerDataEntityGroupEpics';
import { RESOURCE_GUID, RESOURCE_USERSTATUS, ROLEREQUESTRESOURCE_ASSIGNEE_GUID, ROLEREQUESTRESOURCE_FIELDS, ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { getCurrentPageAliasSelector } from '../selectors/navigationSelectors';
import { getLoadEntitiesQuerySelection, loadEntitiesRequest$, loadRoleEntitiesRequest$ } from './entityWindowEpics';
import { ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { addBarToClipboard } from '../actions/clipboardActions';
import { getMultipleRoleAssignees, getTableDataRoleRequestStatusGuidSelector } from '../selectors/roleRequestsSelector';
import { ROLES_SAVE_ACTION_TYPES, ROLE_ITEM_STATUS_KEYS } from '../constants/rolesConsts';
import { ENTITY_ACTION_KEYS } from '../constants/entityAccessConsts';
import { getRowStructure } from '../utils/plannerRowStructureParser';
import { getIsCriteriaRole } from '../utils/roleRequestsUtils';
import { createDuplicateRoleRequest, populateAssigneesForAll, managePendingTimeAllocationErrorAction, movePendingTimeAllocationActionSuccess, removePendingTimeAllocationActionSuccess } from '../actions/rolerequestActions';
import { getCleanCriterias } from '../utils/rolerequestActionsUtils';
import { isSubmittableField } from '../utils/fieldUtils';
import { createMovePendingTimeAllocationEpic, createRemovePendingTimeAllocationEpic, rolerequestMoveToEpic$ } from './rolerequestEpics';
import { API_KEYS } from '../constants/apiConsts';
import { loadWorkflowEntityAccessSuccess } from '../actions/workflowEntityAccessActions';
import { buildWorkflowAccessesByAssigneeId } from '../utils/workflowsUtils';
import { createPlannerDataKeepAliveEpic } from './plannerDataCommonEpics';
import { getPageLicenseSize, getLicenseValuesByKeySelector } from '../selectors/commonSelectors';
import { PAGINATION_MODES } from '../constants/paginationConsts';
import { createAPICallEpic, getApiCallEpicConfig } from './epicGenerators/apiCallEpicGenerator';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FILTER_GROUP_OPERATORS } from '../constants/advancedFilterConsts';
import { loadAvatars } from '../actions/avatarActions';
import { AVATAR_SIZES } from '../constants/avatarConsts';
import { addDays, parseUtcToLocalDate } from '../utils/dateUtils';

const { licensePlannerPageMaxInfiniteScrollRows } = LICENSE_KEYS_ADMIN_SETTINGS;

const DEFAULT_LOAD_UNASSIGNED = true;
const DEFAULT_LOAD_UNASSIGNED_ON_SCROLL = false;

const getPlannerDataEndChainErrorAction = (state$) => () => {
    let result = null;

    const state = state$.value;
    const { loading } = plannerPageSelectors.getCurrentPlannerData(state);

    if (!loading) {
        result = reloadPlannerData();
    }

    return result;
};

const requestFailedEpic = (action$) => {
    return action$
        .ofType(actionTypes.REQUEST_FAILED)
        .pipe(
            switchMap(
                (action) => {
                    const { payload } = action;
                    const { code } = payload;

                    switch (code) {
                        case 401:
                        case 403:
                        case 404: {
                            browserHistory.push(`/${code}`);
                            break;
                        }
                    }

                    return empty();
                }
            )
        );
};

const loadActionNames = [
    actionTypes.LOAD_PLANNER_DATA,
    actionTypes.SORT_PLANNER_DATA
];

export function createLoadPlannerDataEpic(alias) {
    const loadPlannerDataEpic = (action$, state$, { apis }) => {
        const loadPlanner$ = action$.ofType(...loadActionNames).pipe(
            switchMap(
                ({ payload }) => {
                    const state = state$.value;
                    const pageSize = getPageLicenseSize(state)(LICENSE_KEYS_ADMIN_SETTINGS.licensePlannerPageSize);
                    const { workspaceGuid } = payload;

                    const workspaceSettings = wss.getWorkspaceSettings(state.plannerPage.workspaces, workspaceGuid);
                    const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(workspaceSettings);
                    const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(workspaceSettings);
                    const hideUnassignedResourceRows = plannerPageSelectors.getCurrentViewHideUnassignedResourceRows(workspaceSettings);
                    const selection = getPlannerPageSelection(state);
                    const plannerView = getPlannerView(state);
                    const plannerDataReq = apis['plannerData']
                        .pagedPlannerDataAccess$(plannerView, selection, pageSize, !hideUnassignedResourceRows, !hideHistoricRecords, !hideFutureRecords);

                    return plannerDataReq;
                },
                (action, result) => [result, action]
            )
        );

        return loadPlanner$.pipe(
            switchMap(
                completeChain(getPlannerDataEndChainErrorAction(state$))(
                    ([{ key, rowCount, masterRecords, subRecords, rowStructure, plannerBarRecords }, action]) => {
                        const getFieldInfo = getFieldInfoSelector(state$.value);
                        const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state$.value);

                        const maximumInfiniteScrollRows = getLicenseValuesByKeySelector(state$.value)(licensePlannerPageMaxInfiniteScrollRows)?.subscribedCount;

                        const recordsMap = getRecordsMap(plannerBarRecords);
                        const { barsLinkedTableData = {}, barsGroupData = {} } = getBarsData(recordsMap, getFieldInfo);

                        const { workspaceGuid } = action.payload;
                        const pagedRes = { key, rowCount, data: masterRecords };

                        const workspaceSettings = wss.getWorkspaceSettings(state$.value.plannerPage.workspaces, workspaceGuid);
                        const { masterRecTableName, subRecTableName, plannerDataGuid, pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid } = workspaceSettings;

                        const masterRecLinkedTableDatas = getLinkedTableData(masterRecords, masterRecTableName, getFieldInfo);
                        const masterRecPagedData = getFlatTableData(masterRecords, masterRecTableName, getFieldInfo);

                        const subRecLinkedTableDatas = getLinkedTableData(subRecords, subRecTableName, getFieldInfo);
                        const subRecData = getFlatTableData(subRecords, subRecTableName, getFieldInfo);

                        const additionalMasterRecTableData = getTableDataByTable(masterRecPagedData, masterRecTableName);

                        const tableDatas = filterTableDatas(
                            mergeWith(
                                masterRecLinkedTableDatas,
                                subRecLinkedTableDatas,
                                ...Object.values(barsLinkedTableData),
                                additionalMasterRecTableData,
                                (objValue, srcValue, key) =>unionBy([...(objValue || []), ...(srcValue || [])], `${key}_guid`)
                            ),
                            getFieldInfo
                        );

                        const pageSize = getPageLicenseSize(state$.value)(LICENSE_KEYS_ADMIN_SETTINGS.licensePlannerPageSize);

                        const masterRec = {
                            ...pagedRes,
                            data: masterRecPagedData
                        };

                        const {
                            filterMasterFields,
                            filterSubFields,
                            filterBarFields
                        } = getDateSensitiveFieldsFilters(getPlannerPageSelection(state$.value), workspaceSettings);

                        const barColours = getUniqueBarColours(recordsMap);

                        const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(workspaceSettings);
                        const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(workspaceSettings);

                        return of(
                            setPlannerPageNumber(),
                            batchActions(
                                [
                                    loadPagedAccessDataSuccess(
                                        alias,
                                        masterRecTableName,
                                        masterRec,
                                        pagedMasterRecPlannerDataGuid,
                                        pageSize
                                    ),
                                    ...getPlannerLoadGroupedTableDataActions(workspaceSettings, recordsMap, getFieldInfo),
                                    loadTableDataSuccess(
                                        PLANNER_SUB_REC_ALIAS,
                                        {
                                            tableNames: [
                                                subRecTableName
                                            ],
                                            tableDataGuid: subRecPlannerDataGuid
                                        },
                                        subRecData
                                    )
                                ]
                            ),
                            ...getPlannerLoadedGroupsUserEntityAccessHandlerActions(barsGroupData),
                            handleLoadedUserEntityAccess(masterRecPagedData, masterRecTableName),
                            handleLoadedUserEntityAccess(subRecData, subRecTableName),
                            loadUserEntityAccess(getEntityGuids(tableDatas[TABLE_NAMES.ROLEREQUESTGROUP] || [], TABLE_NAMES.ROLEREQUESTGROUP), TABLE_NAMES.ROLEREQUESTGROUP),
                            ...getAdditionalUserAccessLoadActions(barsGroupData),
                            pagedDataRegisterKeepAlive(alias, masterRecTableName, masterRec),
                            plannerDataLoaded(
                                {
                                    plannerDataGuid,
                                    masterRecTableName,
                                    subRecTableName,
                                    pagedMasterRecPlannerDataGuid,
                                    subRecPlannerDataGuid,
                                    rowStructure: getRowStructure(rowStructure, recordsMap, workspaceSettings),
                                    resetVerticalScroll: true,
                                    barColours,
                                    maximumInfiniteScrollRows,
                                    recordsMap,
                                    plannerUnassignedRolesToggleFeatureEnabled,
                                    historicFutureRecords: { hideHistoricRecords: !hideHistoricRecords, hideFutureRecords: !hideFutureRecords }
                                }
                            ),
                            ...getLoadPlannerAdditionalDataActions(
                                `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
                                workspaceGuid,
                                state$,
                                {
                                    masterRecTableName,
                                    subRecTableName
                                },
                                {
                                    pagedRes: masterRec,
                                    subRecDataRes: subRecData,
                                    groupBarsDataRes: barsGroupData
                                },
                                filterMasterFields,
                                filterSubFields,
                                filterBarFields
                            ),
                            ...getTableDatasLoadedActions(`${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`, tableDatas),
                            digestPlannerDataLoaded(PLANNER_DATA_OPERATIONS.LOAD)
                        );
                    }
                )
            )
        );
    };

    return loadPlannerDataEpic;
}

function createAddFieldsEpic(...args) {
    return createDataGridAddFieldsEpic(...args);
}

export function createLoadPagePlannerDataEpic(alias, action, successActionHandler) {
    const scrollPlannerDataEpic = (action$, state$, { apis }) => {

        const scrollPlanner$ = action$.ofType(action).pipe(
            mergeMap(
                ({ payload }) => {
                    const { pagedDataCount: from, workspaceGuid } = payload;
                    const workspaceSettings = wss.getWorkspaceSettings(state$.value.plannerPage.workspaces, workspaceGuid);
                    const { pagedMasterRecPlannerDataGuid } = workspaceSettings;
                    const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(workspaceSettings);
                    const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(workspaceSettings);

                    const pagedMasterRecPlannerData = state$.value.plannerPage.pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid];
                    const id = pagedMasterRecPlannerData.key;
                    const count = pagedMasterRecPlannerData.pageSize;

                    const selection = getPlannerPageSelection(state$.value);

                    const plannerView = getPlannerView(state$.value);

                    const loadUnassigned = workspaceSettings.masterRecTableName == TABLE_NAMES.RESOURCE && from > 0
                        ? false
                        : !plannerPageSelectors.getCurrentViewHideUnassignedResourceRows(workspaceSettings);

                    return apis['plannerData'].pagedPlannerDataResults$(plannerView, selection, id, from, count, loadUnassigned, !hideHistoricRecords, !hideFutureRecords);
                },
                (action, result) => [result, action]
            )
        );

        return scrollPlanner$.pipe(
            mergeMap(
                completeChain(getPlannerDataEndChainErrorAction(state$))(
                    ([{ masterRecords, subRecords, rowStructure, plannerBarRecords }, action]) => {
                        const { gridRowsCount, pagedDataCount, workspaceGuid } = action.payload;
                        const recordsMap = getRecordsMap(plannerBarRecords);
                        const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state$.value);

                        const maximumInfiniteScrollRows = getLicenseValuesByKeySelector(state$.value)(licensePlannerPageMaxInfiniteScrollRows)?.subscribedCount;

                        const workspaceSettings = wss.getWorkspaceSettings(state$.value.plannerPage.workspaces, workspaceGuid);
                        const { plannerDataGuid, masterRecTableName, pagedMasterRecPlannerDataGuid, subRecTableName, subRecPlannerDataGuid } = workspaceSettings;

                        const getFieldInfo = getFieldInfoSelector(state$.value);

                        const masterRecLinkedTableDatas = getLinkedTableData(masterRecords, masterRecTableName, getFieldInfo);
                        const masterRecPagedData = getFlatTableData(masterRecords, masterRecTableName, getFieldInfo);

                        const subRecLinkedTableDatas = getLinkedTableData(subRecords, subRecTableName, getFieldInfo);
                        const subRecData = getFlatTableData(subRecords, subRecTableName, getFieldInfo);

                        const { barsLinkedTableData = {}, barsGroupData = {} } = getBarsData(recordsMap, getFieldInfo);

                        const additionalMasterRecTableData = getTableDataByTable(masterRecPagedData, masterRecTableName);

                        const tableDatas = filterTableDatas(
                            mergeWith(
                                masterRecLinkedTableDatas,
                                subRecLinkedTableDatas,
                                ...Object.values(barsLinkedTableData),
                                additionalMasterRecTableData,
                                (objValue, srcValue, key) =>unionBy([...(objValue || []), ...(srcValue || [])], `${key}_guid`)
                            ),
                            getFieldInfo
                        );

                        const {
                            filterMasterFields,
                            filterSubFields,
                            filterBarFields
                        } = getDateSensitiveFieldsFilters(getPlannerPageSelection(state$.value), workspaceSettings);

                        const barColours = getUniqueBarColours(recordsMap);

                        const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(workspaceSettings);
                        const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(workspaceSettings);

                        return of(
                            batchActions(
                                [
                                    loadPagedResultsDataSuccess(
                                        alias,
                                        masterRecTableName,
                                        pagedDataCount,
                                        masterRecPagedData,
                                        pagedMasterRecPlannerDataGuid,
                                        maximumInfiniteScrollRows
                                    ),
                                    ...getPlannerLoadMoreGroupedTableDataActions(workspaceSettings, recordsMap, getFieldInfo),
                                    loadMoreTableDataSuccess(
                                        PLANNER_SUB_REC_ALIAS,
                                        {
                                            tableNames: [
                                                subRecTableName
                                            ],
                                            tableDataGuid: subRecPlannerDataGuid
                                        },
                                        subRecData
                                    )
                                ]
                            ),
                            ...getPlannerLoadedGroupsUserEntityAccessHandlerActions(barsGroupData),
                            handleLoadedUserEntityAccess(masterRecPagedData, masterRecTableName),
                            handleLoadedUserEntityAccess(subRecData, subRecTableName),
                            ...getAdditionalUserAccessLoadActions(barsGroupData),
                            successActionHandler({
                                gridRowsCount,
                                pagedDataCount,
                                plannerDataGuid,
                                masterRecTableName,
                                subRecTableName,
                                pagedMasterRecPlannerDataGuid,
                                subRecPlannerDataGuid,
                                rowStructure: getRowStructure(rowStructure, recordsMap, workspaceSettings),
                                barColours,
                                maximumInfiniteScrollRows,
                                recordsMap,
                                plannerUnassignedRolesToggleFeatureEnabled,
                                historicFutureRecords: { hideHistoricRecords: !hideHistoricRecords, hideFutureRecords: !hideFutureRecords }
                            }),
                            ...getLoadPlannerAdditionalDataActions(
                                `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
                                workspaceGuid,
                                state$,
                                {
                                    masterRecTableName,
                                    subRecTableName
                                },
                                {
                                    pagedRes: masterRecPagedData,
                                    subRecDataRes: subRecData,
                                    groupBarsDataRes: barsGroupData
                                },
                                filterMasterFields,
                                filterSubFields,
                                filterBarFields
                            ),
                            ...getTableDatasLoadedActions(`${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`, tableDatas),
                            digestPlannerDataLoaded(PLANNER_DATA_OPERATIONS.SCROLL)
                        );
                    }
                )
            )
        );
    };

    return scrollPlannerDataEpic;
}

const hideDetailsPaneActions = [
    `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${PLANNERPAGE_FILTER_ALIAS}`,
    `${actionTypes.FILTERS_ACTIONS.ADVANCED_FILTERS_APPLY}_${PLANNERPAGE_FILTER_ALIAS}`,
    `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${PLANNERPAGE_FILTER_ALIAS}`,
    actionTypes.HIDE_HISTORIC_RECORDS_CHANGED,
    actionTypes.HIDE_FUTURE_RECORDS_CHANGED,
    actionTypes.HIDE_UNASSIGNED_ROWS_CHANGED,
    actionTypes.HIDE_ROLES_RECORDS_CHANGED,
    actionTypes.HIDE_DRAFT_ROLES_RECORDS_CHANGED,
    actionTypes.HIDE_REQUESTED_ROLES_RECORDS_CHANGED,
    actionTypes.HIDE_LIVE_BARS_CHANGED,
    actionTypes.HIDE_UNASSIGNED_ROLES_CHANGED,
    actionTypes.HIDE_ROLES_BY_NAME_CHANGED,
    actionTypes.HIDE_ROLES_BY_REQUIREMENTS_CHANGED,
    actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED
];

const reloadActionNames = [...hideDetailsPaneActions, actionTypes.RELOAD_PLANNER_DATA];

function createReloadPlannerDataEpic() {
    return (action$, state$, /*{ apis }*/) =>
        action$.ofType(...reloadActionNames).pipe(
            mergeMap(() => {
                const currentWSSettings = wss.getCurrentWSSettingsSelector(state$.value);

                return of(
                    loadPlannerData(currentWSSettings),
                    clearUserEntityAccess()
                );
            })
        );
}

const refreshActionNames = [
    actionTypes.DATE_RANGE_CHANGED,
    actionTypes.HIDE_WEEKENDS_CHANGED,
    actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED,
    actionTypes.SORT_PLANNER_SUB_REC_DATA,
    actionTypes.PLANNER_RESOURCE_ASSIGNED_TO_ROLE,
    `${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION_SUCCESS}_${PLANNER_PAGE_ALIAS}`,
    `${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION_SUCCESS}_${PLANNER_PAGE_ALIAS}`
];

export function createRefreshPlannerDataEpic(alias) {
    return (action$, state$, { apis }) => {

        const getPlannerData = action$.ofType(...refreshActionNames).pipe(
            debounceTime(200),
            filter((action) => {
                let result = true;
                const currentWorkspaceSettings = wss.getCurrentWSSettingsSelector(state$.value);
                const hideWeekends = plannerPageSelectors.getCurrentViewHideWeekends(currentWorkspaceSettings);
                const { viewMode: { dateOption, mode } } = currentWorkspaceSettings;

                if (action.type === actionTypes.HIDE_WEEKENDS_CHANGED && !(hideWeekends && mode === PLANNER_VIEW_MODES.VIEW_MODE_DAY && dateOption !== getCustomOption().value)) {
                    result = false;
                }

                return result;
            }),
            mergeMap(
                (action) => {
                    const state = state$.value;
                    const currentWorkspaceSettings = wss.getCurrentWSSettingsSelector(state);
                    const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(currentWorkspaceSettings);
                    const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(currentWorkspaceSettings);

                    const pagedDataRes = plannerPageSelectors.getCurrentPagePlannerData(state[PLANNER_PAGE_ALIAS] || {});
                    const plannerPageSelection = getPlannerPageSelection(state);
                    const plannerView = getPlannerView(state);

                    const selection = {
                        ...plannerPageSelection,
                        masterRecordIds: pagedDataRes
                            .filter(masterRecord => masterRecord.guid !== null)
                            .map(masterRecord => masterRecord[`${plannerView}_guid`])
                    };

                    const currentPageNumber = plannerPageSelectors.getCurrentPlannerPageNumber(state[PLANNER_PAGE_ALIAS] || {});
                    const { masterRecTableName } = currentWorkspaceSettings;
                    const loadUnassigned = masterRecTableName == TABLE_NAMES.JOB || (masterRecTableName == TABLE_NAMES.RESOURCE && currentPageNumber === 1)
                        ? !plannerPageSelectors.getCurrentViewHideUnassignedResourceRows(currentWorkspaceSettings)
                        : false;

                    return apis['plannerData'].plannerDataRows$(plannerView, selection, loadUnassigned, !hideHistoricRecords, !hideFutureRecords)
                        .pipe(
                            takeUntil(
                                action$.ofType(actionTypes.RELOAD_PLANNER_DATA)
                            )
                        );
                },
                (action, r) => [r, action]
            )
        );

        return getPlannerData.pipe(
            mergeMap(
                completeChain(getPlannerDataEndChainErrorAction(state$))(
                    ([{ subRecords, masterRecords, rowStructure, plannerBarRecords }, action]) => {
                        const state = state$.value;
                        const maximumInfiniteScrollRows = getLicenseValuesByKeySelector(state)(licensePlannerPageMaxInfiniteScrollRows)?.subscribedCount;
                        const recordsMap = getRecordsMap(plannerBarRecords);
                        const currentWSSettings = wss.getCurrentWSSettingsSelector(state);
                        const { masterRecTableName, subRecTableName, plannerDataGuid, pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid } = currentWSSettings;
                        const getFieldInfo = getFieldInfoSelector(state);
                        const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state);

                        const masterRecLinkedTableDatas = getLinkedTableData(masterRecords, masterRecTableName, getFieldInfo);
                        const masterRecPagedData = getFlatTableData(masterRecords, masterRecTableName, getFieldInfo);

                        const subRecLinkedTableDatas = getLinkedTableData(subRecords, subRecTableName, getFieldInfo);
                        const subRecData = getFlatTableData(subRecords, subRecTableName, getFieldInfo);

                        const { barsLinkedTableData = {}, barsGroupData = {} } = getBarsData(recordsMap, getFieldInfo);

                        const currentBars = plannerPageSelectors.getCurrentPlannerBarsMap(state.plannerPage);
                        const missingBarIdsMap = getMissingBarIdsMap(currentBars, recordsMap);

                        const additionalMasterRecTableData = getTableDataByTable(masterRecPagedData, masterRecTableName);

                        const tableDatas = filterTableDatas(
                            mergeWith(
                                subRecLinkedTableDatas,
                                ...Object.values(barsLinkedTableData),
                                masterRecLinkedTableDatas,
                                additionalMasterRecTableData,
                                (objValue, srcValue, key) =>unionBy([...(objValue || []), ...(srcValue || [])], `${key}_guid`)
                            ),
                            getFieldInfo
                        );

                        const barColours = getUniqueBarColours(recordsMap);

                        const currentPage = plannerPageSelectors.getCurrentPlannerPageNumber(state[PLANNER_PAGE_ALIAS]);

                        const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(currentWSSettings);
                        const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(currentWSSettings);

                        return of(
                            batchActions([
                                patchMultiplePagedDataSuccess(
                                    PLANNER_MASTER_REC_ALIAS,
                                    {
                                        tableName: masterRecTableName,
                                        tableDataGuid: pagedMasterRecPlannerDataGuid,
                                        tableData: masterRecPagedData,
                                        response: true
                                    },
                                    masterRecPagedData
                                ),
                                ...getPatchMultipleGrouppedTableDataSuccessActions(currentWSSettings, recordsMap, getFieldInfo),
                                ...getBatchDeleteMultipleGrouppedTableDataSuccessActions(currentWSSettings, missingBarIdsMap),
                                loadTableDataSuccess(
                                    PLANNER_SUB_REC_ALIAS,
                                    {
                                        tableNames: [
                                            subRecTableName
                                        ],
                                        tableDataGuid: subRecPlannerDataGuid
                                    },
                                    subRecData
                                )
                            ]),
                            ...getPlannerLoadedGroupsUserEntityAccessHandlerActions(barsGroupData),
                            handleLoadedUserEntityAccess(masterRecPagedData, masterRecTableName),
                            handleLoadedUserEntityAccess(subRecData, subRecTableName),
                            ...getAdditionalUserAccessLoadActions(barsGroupData),
                            clearCachedPaging(alias, masterRecTableName, pagedMasterRecPlannerDataGuid, currentPage),
                            plannerDataLoaded({
                                plannerDataGuid,
                                masterRecTableName,
                                subRecTableName,
                                pagedMasterRecPlannerDataGuid,
                                subRecPlannerDataGuid,
                                persistRowsExpanded: true,
                                rowStructure: getRowStructure(rowStructure, recordsMap, currentWSSettings),
                                barColours,
                                maximumInfiniteScrollRows,
                                recordsMap,
                                plannerUnassignedRolesToggleFeatureEnabled,
                                historicFutureRecords: { hideHistoricRecords: !hideHistoricRecords, hideFutureRecords: !hideFutureRecords }
                            }),
                            ...getTableDatasLoadedActions(`${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`, tableDatas),
                            {
                                type: 'REFRESH_SENSITIVE_INFO',
                                payload: action.payload
                            },
                            digestPlannerDataLoaded(PLANNER_DATA_OPERATIONS.REFRESH)
                        );
                    }
                )
            )
        );
    };
}

export const updatePlannerDateSensitiveFieldsEpic = (action$, state$) => {
    return action$
        .ofType('REFRESH_SENSITIVE_INFO')
        .pipe(
            debounceTime(200),
            switchMap(
                (action) => {
                    const state = state$.value;

                    const {
                        payload: {
                            workspaceGuid
                        }
                    } = action;

                    const workspaceSettings = getWorkspaceSettings(state);

                    const {
                        filterMasterFields,
                        filterSubFields
                    } = getDateSensitiveFieldsFilters(getPlannerPageSelection(state$.value), workspaceSettings);


                    return concat(of(
                        ...getLoadPlannerDataDateSensitiveActions(
                            `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
                            workspaceGuid,
                            state$,
                            {
                                masterRecTableName: workspaceSettings.masterRecTableName,
                                subRecTableName: workspaceSettings.subRecTableName,
                            },
                            {
                                pagedRes: plannerPageSelectors.getCurrentPlannerPagedData(state.plannerPage).data,
                                subRecDataRes: plannerPageSelectors.getCurrentPlannerSubRecData(state.plannerPage).data,
                                groupBarsDataRes: plannerPageSelectors.getGroupBarsDataSelector(state.plannerPage)
                            },
                            filterMasterFields,
                            filterSubFields
                        )
                    ));
                }
            )
        );
};

export const toggleHidePotentialConflictsEpic$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.HIDE_POTENTIAL_CONFLICTS_CHANGED)
        .pipe(
            debounceTime(200),
            switchMap(
                (_) => {
                    const state = state$.value;
                    const { plannerPage } = state;
                    const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                    const { masterRecTableName, pagedMasterRecPlannerDataGuid } = workspaceSettings;
                    const hidePotentialConflicts = plannerPageSelectors.getCurrentViewHidePotentialConflicts(workspaceSettings);

                    if (hidePotentialConflicts) {
                        return empty();
                    }

                    const plannerPaginationMode = plannerPageSelectors.getPlannerPaginationModeSelector(state);

                    let pagedRes = plannerPageSelectors.getCurrentPlannerPagedData(plannerPage);
                    let subRecDataRes = plannerPageSelectors.getCurrentPlannerSubRecData(plannerPage);

                    const chain = [];

                    if (plannerPaginationMode === PAGINATION_MODES.PAGINATED_VIEW) {
                        pagedRes = plannerPageSelectors.getCurrentPagePlannerData(plannerPage);
                        subRecDataRes = plannerPageSelectors.getCurrentPageSubRecPlannerData(plannerPage);

                        const currentPage = plannerPageSelectors.getCurrentPlannerPageNumber(state[PLANNER_PAGE_ALIAS]);
                        chain.push(clearCachedPaging(PLANNER_MASTER_REC_ALIAS, masterRecTableName, pagedMasterRecPlannerDataGuid, currentPage),);
                    }

                    chain.push(
                        getLoadPlannerPotentialConflictsAction(
                            workspaceSettings,
                            {
                                pagedRes,
                                subRecDataRes
                            }
                        )
                    );

                    return of(...chain);
                }
            )
        );
};

export function createPlannerPageFieldsChangedInterceptor() {
    return (action$, state$/*, { apis }*/) =>
        action$.ofType(actionTypes.PLANNER_PAGE_FIELDS_CHANGED_SUCCESS).pipe(
            switchMap(() => {
                const pageAlias = getCurrentPageAliasSelector(state$.value);
                const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state$.value);

                //Temporary fix until the single tableView workspace is introduced
                return pageAlias === PLANNER_PAGE_ALIAS
                    ? of({ 
                            type: actionTypes.BUILD_BAR_ADDITIONAL_DATA,
                            payload: { plannerUnassignedRolesToggleFeatureEnabled } 
                        })
                    : empty();
            })
        );
}

const clearPlannerSelectEditsActions = [...hideDetailsPaneActions, actionTypes.CLEAR_PLANNER_SELECT_EDITS];

export function createClearPlannerSelectEditsEpic() {
    return (action$, state$, /*{ apis }*/) =>
        action$.ofType(...clearPlannerSelectEditsActions).pipe(
            map((action) => {
                const state = state$.value;
                const {
                    closeDP = true,
                    clearMasterSelections = true,
                    clearSubSelections = true,
                    clearBarSelections = true,
                    clearSelection = true
                } = action.payload;
                const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                const { detailsPaneGuid, plannerDataGuid, masterRecTableName, pagedMasterRecPlannerDataGuid, subRecTableName, subRecPlannerDataGuid } = workspaceSettings;
                const actions = [];

                closeDP && actions.push(
                    entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE),
                    entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
                    setDetailsPaneCollapsed(detailsPaneGuid, true),
                    setDetailsPaneVisibility(false)
                );

                clearMasterSelections && actions.push(
                    selectEdits(
                        PLANNER_MASTER_REC_ALIAS,
                        {
                            dataGuid: pagedMasterRecPlannerDataGuid,
                            editableGuids: []
                        }
                    )
                );

                clearSubSelections && actions.push(
                    selectEdits(
                        PLANNER_SUB_REC_ALIAS,
                        {
                            dataGuid: subRecPlannerDataGuid,
                            editableGuids: []
                        }
                    )
                );

                if (clearBarSelections) {
                    const selectedBars = plannerPageSelectors.getCurrentPlannerBarSelection(state);
                    const getBarGroupGuid = (barTableName) => wss.getBarGroupsGuidByTable(workspaceSettings, barTableName);

                    Object.keys(selectedBars).forEach(tableName => {
                        const { editDataKey, dataGuid } = buildSelectEditsPayload(
                            tableName,
                            pagedMasterRecPlannerDataGuid,
                            masterRecTableName,
                            subRecPlannerDataGuid,
                            subRecTableName,
                            getBarGroupGuid
                        );

                        actions.push(selectEdits(editDataKey, { dataGuid, editableGuids: [] }));
                    });
                }

                clearSelection && actions.push(clearPlannerSelection(plannerDataGuid));

                return batchActions(actions);
            })
        );
}

export function createViewSettingsChangeEpic() {
    return (action$, state$, /*{ apis }*/) =>
        action$.ofType(actionTypes.VIEW_SETTINGS_CHANGED).pipe(
            map(() => {
                const state = state$.value;
                const plannerPage = state.plannerPage;
                const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                const { plannerDataGuid, pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid } = workspaceSettings;
                const { pagedMasterRecPlannerData, subRecPlannerData } = plannerPage;

                return batchActions([
                    entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE),
                    entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
                    ...getBarPersistEditsActions(workspaceSettings, plannerPage),
                    persistSelectedEdits(
                        PLANNER_MASTER_REC_ALIAS,
                        {
                            dataGuid: pagedMasterRecPlannerDataGuid,
                            editableGuids: subRecPlannerData[subRecPlannerDataGuid].currentEdits
                        }
                    ),
                    persistSelectedEdits(
                        PLANNER_SUB_REC_ALIAS,
                        {
                            dataGuid: subRecPlannerDataGuid,
                            editableGuids: pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid].currentEdits
                        }
                    ),
                    clearPlannerSelection(plannerDataGuid)
                ]);
            })
        );
}

export function createUpdatePlannerDataEpic(alias) {
    const updatePlannerDataEpic = (action$, state$, { apis }) => {

        const loadUpdatedPlannerData$ = action$.ofType(actionTypes.UPDATE_PLANNER_DATA).pipe(
            filter((action) => {
                return !state$.value.plannerPage.plannerData[action.payload.plannerDataGuid].loading;
            }),
            mergeMap(
                (action) => {
                    const { updatedMasterIds = [], workspaceSettingsGuid } = action.payload;
                    const state = state$.value;
                    const plannerPageState = state[PLANNER_PAGE_ALIAS] || {};
                    const workspaceSettings = wss.getWorkspaceSettings(plannerPageState.workspaces, workspaceSettingsGuid);
                    const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(workspaceSettings);
                    const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(workspaceSettings);
                    const plannerPageSelection = getPlannerPageSelection(state);

                    const selection = {
                        ...plannerPageSelection,
                        masterRecordIds: updatedMasterIds.filter((guid) => guid !== null)
                    };

                    const plannerView = getPlannerView(state);

                    const { loadedPages } = plannerPageSelectors.getCurrentPlannerPagedDataObject(plannerPageState);
                    const { masterRecTableName } = workspaceSettings;
                    const loadUnassigned = masterRecTableName == TABLE_NAMES.JOB || (masterRecTableName == TABLE_NAMES.RESOURCE && loadedPages.some(page => page === 0))
                        ? !plannerPageSelectors.getCurrentViewHideUnassignedResourceRows(workspaceSettings)
                        : false;

                    return apis['plannerData'].plannerDataRows$(plannerView, selection, loadUnassigned, !hideHistoricRecords, !hideFutureRecords);
                },
                (action, r) => ([r, action])
            )
        );

        const plannerDataUpdated$ = loadUpdatedPlannerData$.pipe(
            mergeMap(
                ([{ subRecords, masterRecords, rowStructure, plannerBarRecords }, action]) => {
                    const state = state$.value;
                    const { workspaceSettingsGuid, tableDataEntryGuid, updatedMasterIds = [], stopEditing } = action.payload;
                    const workspaceSettings = wss.getWorkspaceSettings(state.plannerPage.workspaces, workspaceSettingsGuid);
                    const { masterRecTableName, subRecTableName, plannerDataGuid, pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid, workspace_guid } = workspaceSettings;
                    const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state);

                    const recordsMap = getRecordsMap(plannerBarRecords);

                    if (masterRecTableName === TABLE_NAMES.RESOURCE) {
                        const roleIds = recordsMap[TABLE_NAMES.ROLEREQUEST].map(roleRecord => roleRecord[ROLEREQUEST_FIELDS.GUID]);
                        updatedMasterIds.push(...roleIds);
                    }

                    const currentMasterRecBars = getCurrentRecBars(
                        plannerPageSelectors.getCurrentPlannerBarsMap(state.plannerPage),
                        updatedMasterIds,
                        masterRecTableName
                    );

                    const missingBarIdsMap = getMissingBarIdsMap(currentMasterRecBars, recordsMap);
                    const tableDataRes = masterRecords !== null ? masterRecords : [];
                    const getFieldInfo = getFieldInfoSelector(state);

                    const masterRecLinkedTableDatas = getLinkedTableData(tableDataRes, masterRecTableName, getFieldInfo);
                    const masterRecPagedData = getFlatTableData(tableDataRes, masterRecTableName, getFieldInfo);

                    const subRecLinkedTableDatas = getLinkedTableData(subRecords, subRecTableName, getFieldInfo);
                    const subRecData = getFlatTableData(subRecords, subRecTableName, getFieldInfo);

                    const { barsLinkedTableData = {}, barsGroupData = {} } = getBarsData(recordsMap, getFieldInfo);


                    const additionalMasterRecTableData = getTableDataByTable(masterRecPagedData, masterRecTableName);
                    const additionalPayload = {
                        stopEditing,
                        tableDataEntryGuid
                    };

                    const tableDatas = filterTableDatas(
                        mergeWith(
                            masterRecLinkedTableDatas,
                            subRecLinkedTableDatas,
                            ...Object.values(barsLinkedTableData),
                            additionalMasterRecTableData,
                            (objValue, srcValue, key) =>unionBy([...(objValue || []), ...(srcValue || [])], `${key}_guid`)
                        ),
                        getFieldInfo
                    );

                    const {
                        filterMasterFields,
                        filterSubFields,
                        filterBarFields
                    } = getDateSensitiveFieldsFilters(getPlannerPageSelection(state$.value), workspaceSettings);

                    const barColours = getUniqueBarColours(recordsMap);

                    const hideHistoricRecords = plannerPageSelectors.getCurrentViewHideHistoricRecords(workspaceSettings);
                    const hideFutureRecords = plannerPageSelectors.getCurrentViewHideFutureRecords(workspaceSettings);

                    return of(
                        batchActions([
                            patchMultiplePagedDataSuccess(
                                PLANNER_MASTER_REC_ALIAS,
                                {
                                    tableName: masterRecTableName,
                                    tableDataGuid: pagedMasterRecPlannerDataGuid,
                                    tableDataEntryGuid,
                                    tableData: masterRecPagedData,
                                    response: true,
                                    stopEditing
                                },
                                masterRecPagedData
                            ),
                            ...getPatchMultipleGrouppedTableDataSuccessActions(workspaceSettings, recordsMap, getFieldInfo, additionalPayload),
                            ...getBatchDeleteMultipleGrouppedTableDataSuccessActions(workspaceSettings, missingBarIdsMap, additionalPayload),
                            patchMultipleTableDataSuccess(
                                PLANNER_SUB_REC_ALIAS,
                                {
                                    tableName: subRecTableName,
                                    tableDataGuid: subRecPlannerDataGuid,
                                    tableData: subRecData,
                                    tableDataEntryGuid,
                                    response: true,
                                    stopEditing
                                },
                                subRecData
                            )
                        ]),
                        plannerDataPatch(
                            {
                                plannerDataGuid: plannerDataGuid,
                                masterRecTableName: masterRecTableName,
                                subRecTableName: subRecTableName,
                                pagedMasterRecPlannerDataGuid: pagedMasterRecPlannerDataGuid,
                                subRecPlannerDataGuid: subRecPlannerDataGuid,
                                workspaceGuid: workspace_guid,
                                updatedMasterIds,
                                rowStructure: getRowStructure(rowStructure, recordsMap, workspaceSettings),
                                barColours,
                                recordsMap,
                                plannerUnassignedRolesToggleFeatureEnabled,
                                historicFutureRecords: { hideHistoricRecords: !hideHistoricRecords, hideFutureRecords: !hideFutureRecords }
                            }
                        ),
                        ...getLoadPlannerAdditionalDataActions(
                            `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
                            workspace_guid,
                            state$,
                            {
                                masterRecTableName: masterRecTableName,
                                subRecTableName: subRecTableName,
                            },
                            {
                                pagedRes: masterRecPagedData,
                                subRecDataRes: subRecData,
                                groupBarsDataRes: barsGroupData
                            },
                            filterMasterFields,
                            filterSubFields,
                            filterBarFields
                        ),
                        ...getTableDatasLoadedActions(`${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`, tableDatas),
                        digestPlannerDataLoaded(PLANNER_DATA_OPERATIONS.UPDATE)
                    );
                }
            )
        );

        return plannerDataUpdated$;
    };

    return updatePlannerDataEpic;
}

const loadCustomColourSchemeEpic$ = (action$, state$, { apis }) => {
    const loadScheme$ = action$.ofType(actionTypes.COLOUR_SCHEME.LOAD_CUSTOM_SCHEME)
        .pipe(
            switchMap(() => {
                const currColourSchemeObj = wss.getWorkspaceCurrentColourSchemeObjSelector(state$.value);
                const getFieldInfo = getFieldInfoSelector(state$.value);
                const defaultColourScheme = getDefaultColourSchemeId(state$.value);
                const { workspace_custom_colour_field = [] } = currColourSchemeObj;
                const groupLoadColourRequests = [];

                workspace_custom_colour_field.forEach(field => {
                    const { workspace_colour_entity_name = '', workspace_colour_field_name: fieldName = '', workspace_colour_table_name: tableName = '' } = field;
                    const fieldParams = { workspace_colour_entity_name, tableName, fieldName };
                    const fieldInfo = getFieldInfo(tableName, fieldName);
                    const colourParamFields = buildColourTypesParamsField(currColourSchemeObj, fieldParams, defaultColourScheme);

                    groupLoadColourRequests.push(...getCustomColourThemeWebserviceCall(
                        {
                            actor: COLOUR_TYPE_SELECTION_ACTORS.LOAD_CUSTOM,
                            barTableName: workspace_colour_entity_name,
                            colourParamFields,
                            fieldInformationArgs: {tableName, fieldName, fieldInfo},
                            state$,
                            apis
                        }
                    ));
                });

                return forkJoin(...groupLoadColourRequests);
            },
            (action, result) => [result, action])
        );

    const loadedScheme$ = loadScheme$.pipe(
        switchMap(([result, action]) => {
            const chain = [];
            const colourSchemes = [];

            if (result && result.length > 0) {
                result.forEach(item => {
                    const colourThemeTable = item.barTableName;
                    const { tableName, fieldName } = item.fieldInformationArgs;
                    const colourThemeRules = item[colourThemeTable].map(record => {
                        return {
                            colour: record[`${colourThemeTable}${COLOUR_FIELD.SUFFIX}`],
                            fieldValueDescription: record.description
                        };
                    });

                    colourSchemes.push(
                        {
                            colourThemeTypeFields: [{
                                tableName,
                                fieldName,
                                barTableName: colourThemeTable,
                                colourThemeRules
                            }]
                        }
                    );
                });

                chain.push(loadCustomColourSchemeSuccess(colourSchemes));
            }

            return of(...chain);
        })
    );

    return loadedScheme$;
};

const applyColourSchemeEpic$ = (action$, state$, { apis }) => {
    const actionOfInterest = [actionTypes.CHANGE_COLOUR_SCHEME];
    const loadScheme$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap(() => {
                const groupLoadColourRequests = [];
                const currColourSchemeObj = wss.getWorkspaceCurrentColourSchemeObjSelector(state$.value);
                const defaultColourScheme = getDefaultColourSchemeId(state$.value);

                if (isCustomColourScheme(currColourSchemeObj)) {
                    const { workspace_custom_colour_field = [] } = currColourSchemeObj;
                    workspace_custom_colour_field.forEach(field => {
                        const { workspace_colour_entity_name = '', workspace_colour_field_name: fieldName = '', workspace_colour_table_name: tableName = '' } = field;
                        const fieldParams = { workspace_colour_entity_name, tableName, fieldName };
                        const colourParamFields = buildColourTypesParamsField(currColourSchemeObj, fieldParams, defaultColourScheme);
                        groupLoadColourRequests.push(...getCustomColourThemeWebserviceCall(
                            {
                                actor: COLOUR_TYPE_SELECTION_ACTORS.APPLY,
                                barTableName: workspace_colour_entity_name,
                                colourParamFields,
                                state$,
                                apis
                            }
                        ));
                    });
                } else {
                    const colourParamFields = buildColourTypesParamsField(currColourSchemeObj, {}, defaultColourScheme);
                    groupLoadColourRequests.push(...getBuiltInColourThemeWebserviceCall(
                        {
                            colourParamFields,
                            state$,
                            apis
                        }
                    ));
                }

                return forkJoin(...groupLoadColourRequests);
            },
            (action, result) => result)
        );

    const loadedScheme$ = loadScheme$.pipe(
        switchMap((result) => {
            const chain = [];

            if (result) {
                const uniqueColours = [];

                result.forEach(item => {
                    const colourTable = item.barTableName;
                    const collectionAlias = wss.getBarGroupsGuidByTable(getWorkspaceSettings(state$.value), colourTable);
                    const additionalColourColumns = item[colourTable].reduce((accumulator, record) => {
                        const { [`${colourTable}_guid`]: guid, [`${colourTable}_colour`]: newColour } = record;

                        accumulator[guid] = record;
                        !uniqueColours.includes(newColour) && uniqueColours.push(newColour);

                        return accumulator;
                    }, {});

                    const alias = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[colourTable];

                    chain.push(loadAdditionalColumnsSuccess(alias, collectionAlias, additionalColourColumns));
                });


                const mappedNewColours = getMappedBarColours(uniqueColours);
                chain.push(buildBarColours(mappedNewColours));
            }

            return of(...chain);
        })
    );

    return loadedScheme$;
};

const getCustomColourThemeWebserviceCall = ({barTableName, colourParamFields, fieldInformationArgs = {}, state$, apis, actor}) => {
    let webServiceCalls = [];
    const { colourFieldParamName, colourFieldParamValue } = colourParamFields;
    const barIdsByTableName = plannerPageSelectors.getCurrentPlannerBarTypesIds(state$.value.plannerPage);
    const colourTypeIds = barIdsByTableName[barTableName] || [];

    if (colourTypeIds.length > 0) {
        const selection = buildColourTypeSelection(actor, colourFieldParamName, colourFieldParamValue, { colourTypeIds, ...fieldInformationArgs }, barTableName);
        webServiceCalls.push(apis['tableData'].getTableData$(barTableName, selection)
            .pipe(map(res => ({ [barTableName]: (res.status !== ERROR_STATUS) ? res : [], barTableName, fieldInformationArgs }))));
    }

    return webServiceCalls;
};

const getBuiltInColourThemeWebserviceCall = ({colourParamFields, fieldInformationArgs = {}, state$, apis}) =>{
    const webServiceCalls = [];
    const { colourFieldParamName, colourFieldParamValue } = colourParamFields;
    const barIdsByTableName = plannerPageSelectors.getCurrentPlannerBarTypesIds(state$.value.plannerPage);

    BAR_OPTIONS_DISPLAY_TABLE_NAMES
        .forEach(barTableName => {
            const colourTypeIds = barIdsByTableName[barTableName] || [];

            if (colourTypeIds.length > 0) {
                const selection = buildColourTypeSelection(COLOUR_TYPE_SELECTION_ACTORS.APPLY, colourFieldParamName, colourFieldParamValue, { colourTypeIds, ...fieldInformationArgs }, barTableName);
                webServiceCalls.push(
                    apis['tableData'].getTableData$(barTableName, selection)
                        .pipe(map(res => ({ [barTableName]: (res.status !== ERROR_STATUS) ? res : [], barTableName, fieldInformationArgs })))
                );
            }
        });

    return webServiceCalls;
};

// Subject to further change once this fn starts being used in book patching as well
export const barInsertGetDerivedActionDataFn = (state, action) => {
    const { payload, workspaceSettings } = action;
    const { response, tableData, tableDataEntryGuid, tableDataEntryGuids = [], tableName } = payload;
    const { workspaceSettingsGuid } = workspaceSettings;

    const barGroupsGuid = wss.getBarGroupsGuidByTable(workspaceSettings, tableName);
    const groupName = PLANNER_PAGE_TABLE_TO_GROUP_MAP[tableName];
    const groupedData = state.plannerPage[groupName][barGroupsGuid];
    const currentWorkspaceSettings = wss.getWorkspaceSettings(state.plannerPage.workspaces, workspaceSettingsGuid);

    const updatedMasterIds = [];
    const updatedSubIds = [];

    const grouppedRecordMasterLinkedField = `${tableName}_${currentWorkspaceSettings.masterRecTableName}_guid`;
    const grouppedRecordSubRecLinkedField = `${tableName}_${currentWorkspaceSettings.subRecTableName}_guid`;

    const oldEntryTableDataEntryGuids = [ tableDataEntryGuid, ...tableDataEntryGuids ].filter(guid => guid != null);

    if(oldEntryTableDataEntryGuids.length == 0) {
        oldEntryTableDataEntryGuids.push(response);
    }

    const addUpdatedIDs = (entry) => {
        if (grouppedRecordMasterLinkedField in entry && updatedMasterIds.indexOf(entry[grouppedRecordMasterLinkedField]) === -1) {
            updatedMasterIds.push(entry[grouppedRecordMasterLinkedField]);
        }

        if (grouppedRecordSubRecLinkedField in entry && updatedSubIds.indexOf(entry[grouppedRecordSubRecLinkedField]) === -1) {
            updatedSubIds.push(entry[grouppedRecordSubRecLinkedField]);
        }
    };

    oldEntryTableDataEntryGuids.forEach(entryGuid => {
        if (entryGuid) {
            const oldEntry = getData([groupedData], tableName, entryGuid);
            if (oldEntry.dummy !== true) {
                addUpdatedIDs(oldEntry);
            }
        }
    });

    if(Array.isArray(tableData) && tableData.length > 0){
        tableData.forEach(entry => addUpdatedIDs(entry));
    } else {
        const entry = tableData ? tableData : response;
        addUpdatedIDs(entry);
    }

    return {
        updatedMasterIds,
        updatedSubIds
    };
};

// Planner Paged data patch START
const pagedDataJobPatchSuccessInterceptorEpic = createWorkspacePagedDataInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_PAGED_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS
    ],
    'job',
    patchPagedDataSuccess
);

const pagedDataResourcePatchSuccessInterceptorEpic = createWorkspacePagedDataInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_PAGED_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS
    ],
    'resource',
    patchPagedDataSuccess
);

const entityWindowSubmitUpdateMasterRecDataInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowSubmitUpdateMasterRecDataErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

//TO DO: Check once assignees are shown, that the grid is properly updated
const plannerPageEntityWindowRoleAssigneesBudgetUpdateSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUEST,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL)
    ]
);

const createEntityWindowContextualEditEpic = (alias) => {
    const entityWindowContextualEditMasterRecInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
        PLANNER_MASTER_REC_ALIAS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
        alias,
        [
            {
                type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${alias}`,
                payload: {
                    moduleName: alias
                }
            }
        ]
    )();

    const entityWindowContextualEditMasterRecErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
        PLANNER_MASTER_REC_ALIAS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_ERROR,
        alias,
        [
            {
                type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_ERROR}_${alias}`,
                payload: {}
            }
        ]
    )();

    const entityWindowContextualEditSubRecInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
        PLANNER_SUB_REC_ALIAS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS,
        alias,
        [
            {
                type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${alias}`,
                payload: {
                    moduleName: alias
                }
            }
        ]
    )();

    const entityWindowContextualEditJobErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
        PLANNER_SUB_REC_ALIAS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_ERROR,
        alias,
        [
            {
                type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_ERROR}_${alias}`,
                payload: {}
            }
        ]
    )();

    const entityWindowJobUpdateInlineSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
        TABLE_NAMES.JOB,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
        alias,
        [
            patchPagedDataSuccess(JOB_PAGE_PAGED_DATA, { tableDataGuid: TABLE_NAMES.JOB }, true),
            {
                type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${alias}`,
                payload: {
                    moduleName: alias
                }
            }
        ]
    )();

    const entityWindowRoleGroupDPUpdateInlineDetailsPaneSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
        PLANNER_ROLEREQUESTGROUP_ALIAS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
        alias,
        [
            patchTableDataSuccess(PLANNER_ROLEREQUESTGROUP_ALIAS, { tableDataGuid: TABLE_NAMES.ROLEREQUESTGROUP }, true),
            {
                type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${alias}`,
                payload: {
                    moduleName: alias
                }
            }
        ]
    )();

    return combineEpics(
        ...getAliasedPlannerDataEntityGroupEpics(alias, PLANNER_BOOKING_GROUPS_ALIAS),
        ...getAliasedPlannerDataEntityGroupEpics(alias, PLANNER_ROLEREQUESTS_ALIAS),
        entityWindowContextualEditMasterRecInterceptor,
        entityWindowContextualEditMasterRecErrorInterceptor,
        entityWindowContextualEditSubRecInterceptor,
        entityWindowJobUpdateInlineSuccessInterceptorEpic,
        entityWindowContextualEditJobErrorInterceptor,
        entityWindowRoleGroupDPUpdateInlineDetailsPaneSuccessInterceptorEpic
    );
};

const entityWindowJobUpdateInlineSingleDetailsPaneSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.JOB,
    actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
    [
        patchPagedDataSuccess(JOB_PAGE_PAGED_DATA, { tableDataGuid: TABLE_NAMES.JOB }, true),
        {
            type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE}`,
            payload: {
                moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE
            }
        }
    ]
);

const entityWindowRoleGroupDPUpdateInlineDetailsPaneSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUESTGROUP,
    actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    [
        patchTableDataSuccess(ROLE_GROUP_PAGE_TABLE_DATA_ALIAS, { tableDataGuid: TABLE_NAMES.ROLEREQUESTGROUP }, true),
        {
            type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE}`,
            payload: {
                moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE
            }
        }
    ]
);

const entityWindowJobUpdateInlineBatchDetailsPaneSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.JOB,
    actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE,
    [
        patchPagedDataSuccess(JOB_PAGE_PAGED_DATA, { tableDataGuid: TABLE_NAMES.JOB }, true),
        {
            type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE}`,
            payload: {
                moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE
            }
        }
    ]
);

// Planner Paged data patch END

// Planner Subrec data patch START

const subRecDataJobPatchSuccessInterceptorEpic = createWorkspaceSubRecDataInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_TABLE_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS
    ],
    'job',
    patchTableDataSuccess
);

const subRecDataResourcePatchSuccessInterceptorEpic = createWorkspaceSubRecDataInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_TABLE_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS
    ],
    'resource',
    patchTableDataSuccess
);

const entityWindowSubmitUpdateSubRecDataInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowSubmitUpdateSubRecDataErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

// Planner Subrec data patch END

export const commonDataBatchInterceptorEpic = (alias, props) => (action$, state$) => {
    const {
        actionsOfInterest,
        successHandler,
        requestOperation
    } = props;


    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            concatMap(({ type, payload }) => {
                const state = state$.value;
                const { response = [], insufficientPermissions, ...sharedData } = payload;
                const actions = [];
                const successfulOperations = [];
                const failedOperations = [];

                (response || []).filter(entityRequest => !isEditAllEntity(entityRequest.id)).forEach((res) => {
                    if (res.type === SUCCESS_STATUS){
                        successfulOperations.push(res);
                    } else {
                        failedOperations.push(res);
                    }
                });

                const failedOperationsCount = failedOperations.length;
                const attemptedOperationsCount = (response || []).length;
                const succeededOperationsCount = attemptedOperationsCount - failedOperationsCount;
                const { tableName = TABLE_NAMES.BOOKING } = payload;

                if (successfulOperations.length > 0) {
                    const resultAction = successHandler(alias, { ...sharedData, successfulOperations }, response || []);
                    actions.push(resultAction);
                }

                if (failedOperationsCount > 0) {
                    const errorsCollection = failedOperations.map(res => {
                        const errorIndex = (response || []).indexOf(res);
                        const { result = {}, status } = res;

                        return {
                            id: getTableDataEntryGuid(payload, errorIndex),
                            error: {
                                hasError: status === ERROR_STATUS,
                                message: result.message ? result.message : null
                            }
                        };
                    });

                    const modalContext = {
                        requestOperation,
                        tableName,
                        errorsCollection,
                        failedOperationsCount,
                        attemptedOperationsCount,
                        succeededOperationsCount
                    };
                    const currentPageAlias = getCurrentPageAliasSelector(state);

                    actions.push(batchCRUDErrorPrompt(currentPageAlias, { ...payload }, modalContext));
                } else {
                    const tableData = ((sharedData.patchData || [])[0] || {}).tableData || {};
                    const customType = getToasterMessageCustomType(tableName, tableData, state);

                    actions.push(showToasterMessage(tableName, requestOperation, alias, succeededOperationsCount, customType));
                }

                return from(actions);
            })
        );
};



const createTableDataBatchUpdateInterceptorEpic = (alias) => (action$, state$) => {
    const actionsOfInterest = [
        `${actionTypes.BATCH_DIGEST_PATCH_TABLE_DATA_SUCCESSFUL}_${alias}`,
        `${actionTypes.BATCH_CRUD_ERROR}_${CRUD_OPERATIONS.UPDATE}`
    ];

    const epicProps = {
        actionsOfInterest,
        successHandler: batchPatchTableDataSuccess,
        requestOperation: CRUD_OPERATIONS.UPDATE
    };

    const commonGroupedDataBatchEpic = commonDataBatchInterceptorEpic(alias, epicProps);

    return commonGroupedDataBatchEpic(action$, state$);
};

export const createPagedTableDataBatchUpdateInterceptorEpic = (alias) => (action$, state$) => {
    const actionsOfInterest = [
        `${actionTypes.BATCH_DIGEST_PATCH_PAGED_TABLE_DATA_SUCCESSFUL}_${alias}`,
        `${actionTypes.BATCH_CRUD_ERROR}_${CRUD_OPERATIONS.UPDATE}`
    ];

    const epicProps = {
        actionsOfInterest,
        successHandler: batchPatchPagedTableDataSuccess,
        requestOperation: CRUD_OPERATIONS.UPDATE
    };

    const commonGroupedDataBatchEpic = commonDataBatchInterceptorEpic(alias, epicProps);

    return commonGroupedDataBatchEpic(action$, state$);
};

const showToasterMessageEpic = () => (action$, state$) => {
    const { createActionsArray, editActionsArray, deteleActionsArray, defaultShowToasterAction } = getShowToasterMessagesActionsOfInterest();
    const actionsOfInterest = [
        ...createActionsArray,
        ...editActionsArray,
        ...deteleActionsArray,
        defaultShowToasterAction
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            switchMap(({ payload, type }) => {
                const { tableName, succeededOperationsCount = 1, actionType, customType, suffixEntityTableName = null } = payload;

                return of(setToasterConfig(getToasterMessageConfig(state$.value, tableName, actionType || type, customType, succeededOperationsCount, suffixEntityTableName)));
            })
        );
};

export const loadGroupedTableDataRowsInterceptorEpic = (alias) => (action$, state$) => {
    const actionsOfInterest = [
        `${actionTypes.LOAD_GROUPPED_TABLE_DATA_ROWS_SUCCESSFUL}_${alias}`
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            map(({ payload }) => {
                const state = state$.value;
                const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                const { plannerDataGuid, masterRecTableName, workspace_guid, subRecTableName } = workspaceSettings;
                const barGroupsGuid = wss.getBarGroupsGuidByTable(workspaceSettings, tableName);
                const { tableName, response } = payload;
                const updatedMasterIds = response.map( data => data[`${tableName}_${masterRecTableName}_guid`]);

                const metaData = {
                    ...payload,
                    updatedMasterIds,
                    stopEditing: true,
                    tableNames: [payload.tableName],
                    workspaceSettingsGuid: workspace_guid,
                    barGroupsGuid,
                    plannerDataGuid,
                    masterRecTableName,
                    subRecTableName
                };

                return updatePlannerData(alias, metaData, response);
            })
        );
};

const toggleDetailsPaneEpic = () => (action$, state$) => {
    const actionsOfInterest = [
        actionTypes.PLANNER_TOGGLE_EXPAND_COLLAPSE_ALL,
        actionTypes.VIEW_SETTINGS_CHANGED
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            switchMap(({ payload }) => {
                const plannerDataGuid = payload.plannerDataGuid || payload;
                const workspace = wss.getSelectedWorkspaceSettings(state$.value.plannerPage.workspaces);
                const hasOnlySubRecsSelected = plannerPageSelectors.hasOnlySubRecsSelected(state$.value);
                const { masterRecTableName } = workspace;
                const { plannerPage: { plannerData }} = state$.value;

                if(hasOnlySubRecsSelected){
                    return of(setDetailsPaneCollapsed(workspace.detailsPaneGuid, areRecordsExpanded(plannerData[plannerDataGuid], masterRecTableName)));
                }

                return empty();
            })
        );
};

// This epic should be removed in future refactor. Statuses loading will be done in role request epics
export const loadRoleRequestStatusesEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(
            actionTypes.LOAD_PLANNER_ROLEREQUEST_STATUSES
        )
        .pipe(
            switchMap(
                () => {
                    const requestStatusTableName = TABLE_NAMES.ROLEREQUESTSTATUS;
                    const querySelection = {
                        fields: [
                            { fieldName: `${requestStatusTableName}_guid` },
                            { fieldName: `${requestStatusTableName}_description` }
                        ]
                    };

                    return apis['tableData'].getTableData$(requestStatusTableName, querySelection);
                }
            ),
            switchMap(
                (response) => {
                    const requestStatusTableName = TABLE_NAMES.ROLEREQUESTSTATUS;

                    return concat(
                        of(loadMoreTableDataSuccess(
                            `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
                            {
                                tableDataGuid: requestStatusTableName,
                                tableNames: [requestStatusTableName]
                            },
                            unionBy(response, `${requestStatusTableName}_guid`)
                        )),
                        of(loadPlannerRolerequestStatusesSuccess())
                    );
                }
            )
        );
};

export const unassignResourceFromRoleRequestEpic$ = (action$) => {
    return action$
        .ofType(actionTypes.PLANNER_ASSIGN_RESOURCE_TO_CRITERIA_ROLEREQUEST)
        .pipe(
            switchMap(
                (action) => {
                    const { payload } = action;
                    const { roleRequestGuid, resourceGuid } = payload;

                    return of(batchUpdatePlannerRolerequestResource(roleRequestGuid, TABLE_NAMES.ROLEREQUEST, resourceGuid));
                }
            )
        );
};

export const batchUpdateRoleRequestsStatusEpic$ = (action$, state$, { apis }) => {
    const actionsOfInterest = [
        actionTypes.BATCH_UPDATE_PLANNER_ROLEREQUEST_RESOURCE
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            switchMap(
                (action) => {
                    const state = state$.value;
                    const { payload } = action;
                    const { guids, tableName } = payload;
                    const guidsArray = Array.isArray(guids) ? guids : [guids];

                    const workspaceSettings = wss.getSelectedWorkspaceSettings(state.plannerPage.workspaces);
                    const barGroupsGuid = wss.getBarGroupsGuidByTable(workspaceSettings, tableName);

                    const patchData = guidsArray.map(guid => {
                        return {
                            tableDataEntryGuid: guid,
                            tableData: buildUpdateRoleRequestStatusTableData(action, state)
                        };
                    });

                    const alias = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName];

                    return of(batchPatchGroupedTableData(
                        alias,
                        tableName,
                        barGroupsGuid,
                        patchData,
                        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
                        alias
                    ));
                }
            )
        );
};

const updateAssigneesInPlannerEpic$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.UPDATE_PLANNER_ASSIGNEES)
        .pipe(
            switchMap(({ payload }) => {
                const state = state$.value;
                const { masterRecIds = [] } = payload;

                const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                const { plannerDataGuid, workspace_guid } = workspaceSettings;

                const metaData = {
                    updatedMasterIds: masterRecIds,
                    plannerDataGuid,
                    stopEditing: true,
                    workspaceSettingsGuid: workspace_guid
                };

                return of(updatePlannerData(PLANNER_PAGE_ALIAS, metaData, []));
            })
        );
};

const mapToMasterRecField = (masterRecTableName, tableName) => {
    return masterRecTableName === TABLE_NAMES.RESOURCE && tableName === TABLE_NAMES.ROLEREQUESTRESOURCE
        ? ROLEREQUESTRESOURCE_ASSIGNEE_GUID
        : `${tableName}_${masterRecTableName}_guid`;
};

const reloadGroupedTableDataMasterRowsEpic$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.RELOAD_GROUPED_TABLE_DATA_PLANNER_MASTER_ROWS)
        .pipe(
            switchMap(({ payload }) => {
                const state = state$.value;
                const { barTableNames = [], barGuids = [] } = payload;

                const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                const { plannerDataGuid, masterRecTableName, workspace_guid } = workspaceSettings;
                const uniqueMasterRecIds = new Set();

                barTableNames.forEach(tableName => {
                    const barEntities = plannerPageSelectors.getCurrentPlannerBarGroupDataMappedSelector(state)(tableName) || {};

                    barGuids.forEach(barGuid => {
                        if (barEntities[barGuid]) {
                            const masterRecField = mapToMasterRecField(masterRecTableName, tableName);

                            uniqueMasterRecIds.add(barEntities[barGuid][masterRecField]);
                        }
                    });
                });

                const metaData = {
                    updatedMasterIds: Array.from(uniqueMasterRecIds),
                    plannerDataGuid,
                    stopEditing: true,
                    workspaceSettingsGuid: workspace_guid
                };

                return of(updatePlannerData(PLANNER_PAGE_ALIAS, metaData, []));
            })
        );
};

export const addBarToClipboardEpic$ = (action$, state$, { apis }) => {
    const loadEntityFields$ = action$
        .ofType(
            actionTypes.LOAD_DATA_FOR_CLIPBOARD_ENTITY
        ).pipe(
            switchMap(
                ({ payload }) => {
                    const state = state$.value;
                    const { barTableName, entityGuid } = payload;
                    const querySelection = getLoadEntitiesQuerySelection(barTableName, ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE, [entityGuid], state, ENTITY_WINDOW_OPERATIONS.CREATE);

                    return barTableName === TABLE_NAMES.ROLEREQUEST
                        ? loadRoleEntitiesRequest$(querySelection, apis)
                        : loadEntitiesRequest$(barTableName, querySelection, apis);
                },
                (action, respone) => [action, respone]
            )
        );

    const processDataBeforeAddingToClipboard$ = loadEntityFields$.pipe(
        switchMap(([action, response]) => {
            const { barTableName, operation } = action.payload;
            const state = state$.value;
            let processedData = { ...response[0] };

            if (barTableName === TABLE_NAMES.ROLEREQUEST) {
                const statusGuid = getTableDataRoleRequestStatusGuidSelector(state)(ROLE_ITEM_STATUS_KEYS.DRAFT);
                processedData.body = { ...processedData.body, [ROLEREQUEST_FIELDS.STATUS_GUID]: statusGuid };

                if (operation === ENTITY_ACTION_KEYS.COPY && getIsCriteriaRole(processedData)) {
                    processedData.body = { ...processedData.body, [ROLEREQUEST_FIELDS.RESOURCE_GUID]: null };
                }
            }

            return of(processedData);
        },
        (action, respone) => [action, respone])
    );

    return processDataBeforeAddingToClipboard$.pipe(
        mergeMap(([[action], response]) => {
            const { operation, actor, barTableName } = action.payload;

            return of(addBarToClipboard(response, operation, actor, barTableName));
        })
    );
};

export const rolerequestMoveToCreateEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ROLEREQUEST_MOVE_TO_CREATE)
        .pipe(
            switchMap(
                ({ payload }) => {
                    const state = state$.value;
                    const { collectionAlias, tableDataEntryGuid, tableData, tableName } = payload;
                    const getFieldInfo = getFieldInfoSelector(state);

                    const barOmitFields = Object
                        .keys(tableData)
                        .filter(fieldKey => !isSubmittableField(fieldKey, tableName, getFieldInfo(tableName, fieldKey)));

                    const barTableData = omit(tableData, barOmitFields);

                    return of(createDuplicateRoleRequest(collectionAlias, {
                        operation: CRUD_OPERATIONS.CREATE,
                        tableData: barTableData,
                        tableDataEntryGuid,
                        tableName,
                        moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT,
                        stopEditing: true,
                        batchUpdateActions: [{
                            operation: ROLES_SAVE_ACTION_TYPES.INSERT,
                            body: barTableData,
                            criterias: getCleanCriterias(plannerPageSelectors.getClipboardCriteriasSelector(state))
                        }]
                    }));
                }
            )
        );
};

export const assignResourceToCriteriaSuccessful$ = (action$, state$, { apis }) => {
    return action$
        .ofType(`${actionTypes.ASSIGN_RESOURCE_TO_CRITERIA_SUCCESSFUL}_${PLANNER_PAGE_ALIAS}`)
        .pipe(
            switchMap(
                (action) => {
                    const state = state$.value;
                    const { entityId } = action.payload;
                    const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                    const { workspace_guid } = workspaceSettings;
                    const getBarGroupGuid = (barTableName) => wss.getBarGroupsGuidByTable(workspaceSettings, barTableName);
                    const {
                        pagedMasterRecPlannerDataGuid,
                        subRecPlannerDataGuid,
                        masterRecTableName,
                        subRecTableName
                    } = wss.getSelectedWorkspaceSettings(state.plannerPage.workspaces);

                    const { editDataKey, dataGuid } = buildSelectEditsPayload(
                        TABLE_NAMES.RESOURCE,
                        pagedMasterRecPlannerDataGuid,
                        masterRecTableName,
                        subRecPlannerDataGuid,
                        subRecTableName,
                        getBarGroupGuid
                    );

                    return concat(
                        of(assignResourceToRolePlannerPage(workspace_guid)),
                        of(selectEdits(editDataKey, { dataGuid, editableGuids: [entityId] }))
                    );
                }
            )
        );
};

const loadPlannerCriteriaRoleAssigneesEpic$ = (action$, state$, { apis }) => {
    return action$.ofType(actionTypes.DIGEST_PLANNER_DATA_LOADED)
        .pipe(
            filter((action) => {
                const state = state$.value;
                const { payload: { operation } } = action;
                const workspaceSettings = wss.getSelectedWorkspaceSettings(state.plannerPage.workspaces);
                const masterRecTableName = wss.getWorkspaceMasterRecTableName(workspaceSettings);

                const hideRolesByRequirements = plannerPageSelectors.getCurrentViewHideRolesByRequirementsRecords(workspaceSettings);
                const displayUnassignedCriteriaRolesAsResources = wss.getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspaceSettings);

                return !hideRolesByRequirements
                    && displayUnassignedCriteriaRolesAsResources
                    && (masterRecTableName === TABLE_NAMES.RESOURCE ? operation !== PLANNER_DATA_OPERATIONS.SCROLL : true);
            }),
            switchMap((action) => {
                const state = state$.value;
                const criteriaRoleIds = plannerPageSelectors.getCurrentPlannerCriteriaRoleIds(state.plannerPage);

                const querySelection = {
                    fields: [{ fieldName: ROLEREQUEST_FIELDS.GUID }],
                    filter: {
                        filterGroupOperator: 'And',
                        filterLines: [
                            {
                                field: ROLEREQUEST_FIELDS.GUID,
                                operator: OPERATORS.DB_OPERATORS.CONTAINS,
                                value: criteriaRoleIds
                            }
                        ]
                    },
                    order: { orderFields: [] },
                    getCriterias: false
                };

                return apis[API_KEYS.ROLE_REQUEST_API_KEY].getRoles$(querySelection);
            }),
            switchMap((result) => {
                const criteriaAssigneesData = getMultipleRoleAssignees(state$.value)(result);
                const workflowAccessesByAssigneeId = buildWorkflowAccessesByAssigneeId(criteriaAssigneesData);
                const actions = [];

                if (!isEmpty(criteriaAssigneesData)) {
                    actions.push(populateAssigneesForAll(criteriaAssigneesData));
                    actions.push(loadWorkflowEntityAccessSuccess(workflowAccessesByAssigneeId, TABLE_NAMES.ROLEREQUESTRESOURCE));
                }

                return from(actions);
            })
        );
};

const openPlannerAssigneeDetailsPaneEpic = () => (action$, state$, { apis }) => {
    const loadRoleAssignees$ = action$.ofType(actionTypes.OPEN_PLANNER_ASSIGNEE_DETAILS_PANE).pipe(
        switchMap(action => {
            const state = state$.value;
            const { barId } = action.payload;
            const barTableName = TABLE_NAMES.ROLEREQUESTRESOURCE;
            const getData = plannerPageSelectors.getCgGetDataWrappedSelector(state);

            const criteriaRoleId = getData(barId, barTableName)[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID];

            const querySelection = {
                fields: [{ fieldName: ROLEREQUEST_FIELDS.GUID }],
                filter: {
                    filterGroupOperator: 'And',
                    filterLines: [
                        {
                            field: ROLEREQUEST_FIELDS.GUID,
                            operator: OPERATORS.DB_OPERATORS.EQUALS,
                            value: criteriaRoleId
                        }
                    ]
                },
                order: { orderFields: [] },
                getCriterias: false
            };

            return apis[API_KEYS.ROLE_REQUEST_API_KEY].getRoles$(querySelection);
        }, (action, respone = []) => [action, (respone[0] || {}).assignedResources || []])
    );

    return loadRoleAssignees$.pipe(
        switchMap(([action, assignees]) => {
            const state = state$.value;
            const { barId } = action.payload;

            const selectedEntityId = assignees.find(assignee => assignee[ROLEREQUESTRESOURCE_FIELDS.GUID] === barId)[ROLEREQUESTRESOURCE_FIELDS.RESOURCE_GUID];
            const entityIds = assignees.map((assignee) => assignee[ROLEREQUESTRESOURCE_FIELDS.RESOURCE_GUID]);

            const detailsPane = plannerPageSelectors.getCurrentPlannerDetailsPane(state.plannerPage);
            const { guid: detailsPaneGuid } = detailsPane;

            const isBatchTab = entityIds.length > 1;
            const dpOpenActions = [setDetailsPaneSelectedTabIsBatched(detailsPaneGuid, isBatchTab)];

            const { plannerPage } = state;
            const workspaceSettings = wss.getSelectedWorkspaceSettings(plannerPage.workspaces);
            const collectionAlias = plannerPageSelectors.getCollectionAliasForTableName(workspaceSettings, TABLE_NAMES.RESOURCE);

            if (isBatchTab) {
                dpOpenActions.push(
                    entityWindowOpenForMultiple(
                        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
                        TABLE_NAMES.RESOURCE,
                        collectionAlias,
                        ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT,
                        [],
                        entityIds,
                        selectedEntityId
                    )
                );
            } else {
                dpOpenActions.push(
                    entityWindowOpen(
                        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
                        TABLE_NAMES.RESOURCE,
                        collectionAlias,
                        ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT,
                        {},
                        selectedEntityId
                    )
                );
            }

            return from(dpOpenActions);
        })
    );
};

// Planner Client START
const entityWindowClientInsertSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowClientInsertErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowClientInsertErrorJobsPageInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
);

const entityWindowClientPatchSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowClientPatchErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowClientDeleteSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowClientDeleteErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);
// Planner Client END

const entityWindowJobsPageClientInsertSuccessInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
);

const entityWindowJobsPageClientPatchSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
);

const entityWindowJobsPageClientDeleteSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
);

const entityWindowJobInsertSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    'job',
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL),
        dataGridLoadData(JOBS_PAGE_ALIAS)
    ]
);

const entityWindowJobInsertErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    'job',
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
);

const entityWindowJobUpdateSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    'job',
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL),
        patchPagedDataSuccess(JOB_PAGE_PAGED_DATA, { tableDataGuid: 'job' }, true)
    ]
);

const entityWindowJobUpdateErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    'job',
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
);


const entityWindowJobDeleteSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    'job',
    actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE),
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE),
        dataGridLoadData(JOBS_PAGE_ALIAS)
    ]
);

const entityWindowJobMasterRecInsertSuccessInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        reloadPlannerData(),
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);


const entityWindowJobMasterRecInsertErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowJobSubRecInsertSuccessInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const entityWindowJobSubRecInsertErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL)
    ]
);

const resourceUpdateMasterRecDataInterceptorEpic = createWorkspacePagedDataInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_TABLE_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS,
        actionTypes.DIGEST_DELETE_TABLE_DATA_SUCCESSFUL,
        actionTypes.LOAD_TABLE_DATA_ROW_SUCCESSFUL,
        actionTypes.BATCH_DIGEST_PATCH_TABLE_DATA_SUCCESSFUL,
        actionTypes.BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL
    ],
    TABLE_NAMES.RESOURCE,
    updatePlannerData,
    getUpdatedMasterRecIds
);

const jobUpdateMasterRecDataInterceptorEpic = createWorkspacePagedDataInterceptorEpic(
    PLANNER_SUB_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_TABLE_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS,
        actionTypes.DIGEST_DELETE_TABLE_DATA_SUCCESSFUL,
        actionTypes.LOAD_TABLE_DATA_ROW_SUCCESSFUL,
        actionTypes.BATCH_DIGEST_PATCH_TABLE_DATA_SUCCESSFUL,
        actionTypes.BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL
    ],
    TABLE_NAMES.JOB,
    updatePlannerData,
    getUpdatedMasterRecIds
);


const resourceUpdateSubRecDataInterceptorEpic = createWorkspacePagedDataInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_PAGED_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
        actionTypes.BATCH_DIGEST_PATCH_PAGED_TABLE_DATA_SUCCESSFUL,
        actionTypes.BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL
    ],
    TABLE_NAMES.RESOURCE,
    updatePlannerData,
    getUpdatedMasterRecIds
);

const jobUpdateSubRecDataInterceptorEpic = createWorkspacePagedDataInterceptorEpic(
    PLANNER_MASTER_REC_ALIAS,
    [
        actionTypes.DIGEST_PATCH_PAGED_DATA_SUCCESSFUL,
        actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
        actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
        actionTypes.BATCH_DIGEST_PATCH_PAGED_TABLE_DATA_SUCCESSFUL,
        actionTypes.BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL
    ],
    TABLE_NAMES.JOB,
    updatePlannerData,
    getUpdatedMasterRecIds
);

const getWorkspaceSettings = (state) => wss.getSelectedWorkspaceSettings(state.plannerPage.workspaces);

const masterRecDataKey = 'pagedMasterRecPlannerData';
const subRecDataKey = 'subRecPlannerData';

const injectWorkspaceSettings = (payload, state, successAction) => {
    const workspaceGuid = wss.getSelectedWorkspaceGuid(state.plannerPage.workspaces);

    return successAction(PLANNER_PAGE_ALIAS, { ...payload, workspaceGuid });
};

export const splitBarsSuccessEpic = () => (action$, state$) => {
    return action$
        .ofType(actionTypes.SPLIT_BARS_SUCCESS)
        .pipe(
            switchMap(({ payload }) => {
                const state = state$.value;
                const workspaceSettings = wss.getCurrentWSSettingsSelector(state);
                const { plannerDataGuid, masterRecTableName, workspace_guid, subRecTableName } = workspaceSettings;
                const barGroups = plannerPageSelectors.getCurrentPlannerBarGroupsObject(state.plannerPage);
                const { splitSelection, response } = payload;

                let tableNames = [];

                const updatedMasterIds = splitSelection.map(selection => {
                    const { id , tableName } = selection;

                    tableNames.indexOf(tableName) === -1 && tableNames.push(tableName);

                    const barGroup = barGroups[tableName] || {};
                    const barIndex = (barGroup.byId[tableName] || {})[id];

                    return (barGroup.data[barIndex] || {})[`${tableName}_${masterRecTableName}_guid`];
                });

                const metaData = {
                    ...payload,
                    updatedMasterIds,
                    stopEditing: true,
                    tableNames: tableNames,
                    workspaceSettingsGuid: workspace_guid,
                    plannerDataGuid,
                    masterRecTableName,
                    subRecTableName
                };

                return from([
                    setSuccessToaster(),
                    updatePlannerData('', metaData, response)]);
            })
        );
};

const splitBarRequest$ = (api, payload) => {
    return api.splitEntity$(payload.splitSelection);
};

const createSplitBarsEpic = (successActionHandler = splitBarsSuccessAction, errorActionHandler = splitBarsErrorAction) => createAPICallEpic(
    null,
    getApiCallEpicConfig(
        actionTypes.SPLIT_BARS,
        API_KEYS.PLANNER_DATA,
        splitBarRequest$,
        successActionHandler,
        errorActionHandler
    )
);

export default function () {
    return combineEpics(
        updatePlannerDateSensitiveFieldsEpic,
        createClearPlannerSelectEditsEpic(),
        createViewSettingsChangeEpic(),
        combineAPIEpics(
            toggleHidePotentialConflictsEpic$,
            ...getPlannerDataEntityGroupApiEpics(PLANNER_BOOKING_GROUPS_ALIAS),
            ...getPlannerDataEntityGroupApiEpics(PLANNER_ROLEREQUESTS_ALIAS),
            createSplitBarsEpic(splitBarsSuccessAction, splitBarsErrorAction)(),
            splitBarsSuccessEpic(),
            createLoadPlannerDataEpic(PLANNER_MASTER_REC_ALIAS),
            createReloadPlannerDataEpic(PLANNER_MASTER_REC_ALIAS),
            createRefreshPlannerDataEpic(PLANNER_MASTER_REC_ALIAS),
            createLoadPagePlannerDataEpic(PLANNER_MASTER_REC_ALIAS, actionTypes.SCROLL_PLANNER_DATA, plannerDataScrolled),
            createLoadPagePlannerDataEpic(PLANNER_MASTER_REC_ALIAS, actionTypes.LOAD_PLANNER_DATA_PAGE, plannerDataPageLoaded),
            createUpdatePlannerDataEpic(PLANNER_MASTER_REC_ALIAS),
            createAddFieldsEpic(
                PLANNER_PAGE_ALIAS,
                PLANNER_TABLE_DATAS_SUFFIX,
                (state, { payload }) => payload.tableName,
                (state, { payload }) => wss.getBarGroupsGuidByTable(getWorkspaceSettings(state), payload.tableName),
                actionTypes.BAR_FIELDS_CHANGED,
                (tableName) => PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName],
                (alias, action) => plannerPageFieldsChangedSuccess(action.payload.workspaceGuid),
                (tableName) => PLANNER_PAGE_TABLE_TO_GROUP_MAP[tableName],
                getPlannerFieldToLoad,
                (action, state$) => plannerPageSelectors.getPlannerPageFieldOptions(state$.value.plannerPage, action.payload.workspaceGuid)
            ),
            //TableData
            //planner_bookings_groups

            tableDataEpics.createBatchPatchTableDataEpic(PLANNER_SUB_REC_ALIAS, batchDigestPatchTableDataSuccess, batchPatchTableDataError)(),
            tableDataEpics.createBatchPatchTableDataEpic(PLANNER_MASTER_REC_ALIAS, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError)(),

            tableDataEpics.createInsertTableDataEpic(TABLE_NAMES.CLIENT, digestInsertTableDataSuccess, digestInsertTableDataError)(),
            tableDataEpics.createInsertTableDataEpic(PLANNER_MASTER_REC_ALIAS, digestInsertTableDataSuccess, digestInsertTableDataError)(),
            tableDataEpics.createInsertTableDataEpic(PLANNER_SUB_REC_ALIAS, digestInsertTableDataSuccess, digestInsertTableDataError)(),
            tableDataEpics.createInsertTableDataEpic(JOB_PAGE_PAGED_DATA, reloadJobsPageDataGridAction)(),

            tableDataEpics.createDeleteTableDataEpic(JOB_PAGE_PAGED_DATA, digestDeleteTableDataSuccess)(),
            tableDataEpics.createDeleteTableDataEpic(TABLE_NAMES.CLIENT, digestDeleteTableDataSuccess)(),


            //planner_sub_rec
            tableDataEpics.createPatchTableDataEpic(PLANNER_SUB_REC_ALIAS, digestPatchTableDataSuccess)(),
            //client
            tableDataEpics.createPatchTableDataEpic(TABLE_NAMES.CLIENT, digestPatchTableDataSuccess)(),
            //PagedData
            pagedDataEpics.createPatchPagedDataEpic(JOB_PAGE_PAGED_DATA, patchPagedDataSuccess)(),
            pagedDataEpics.createPatchPagedDataEpic(PLANNER_MASTER_REC_ALIAS, digestPatchPagedDataSuccess)(),
            pagedDataEpics.createAddFieldsEpic(
                PLANNER_PAGE_ALIAS,
                PLANNER_TABLE_DATAS_SUFFIX,
                (state) => wss.getWorkspaceMasterRecTableName(getWorkspaceSettings(state)),
                (state) => plannerPageSelectors.getMasterRecCollectionAlias(getWorkspaceSettings(state)),
                actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED,
                (tableName) => PLANNER_MASTER_REC_ALIAS,
                (alias, action) => plannerPageFieldsChangedSuccess(action.payload.workspaceGuid),
                (tableName) => masterRecDataKey,
                getPlannerFieldToLoad,
                (action, state$) => plannerPageSelectors.getPlannerPageFieldOptions(state$.value.plannerPage, action.payload.workspaceGuid)
            ),
            pagedDataEpics.createAddFieldsEpic(
                PLANNER_PAGE_ALIAS,
                PLANNER_TABLE_DATAS_SUFFIX,
                (state) => wss.getWorkspaceSubRecTableName(getWorkspaceSettings(state)),
                (state) => plannerPageSelectors.getSubRecCollectionAlias(getWorkspaceSettings(state)),
                actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED,
                (tableName) => PLANNER_SUB_REC_ALIAS,
                (alias, action) => plannerPageFieldsChangedSuccess(action.payload.workspaceGuid),
                (tableName) => subRecDataKey,
                getPlannerFieldToLoad,
                (action, state$) => plannerPageSelectors.getPlannerPageFieldOptions(state$.value.plannerPage, action.payload.workspaceGuid)
            ),
            pagedDataEpics.createAddFieldsEpic(
                PLANNER_PAGE_ALIAS,
                PLANNER_TABLE_DATAS_SUFFIX,
                (state) => wss.getWorkspaceMasterRecTableName(getWorkspaceSettings(state)),
                (state) => plannerPageSelectors.getMasterRecCollectionAlias(getWorkspaceSettings(state)),
                actionTypes.ADD_DATE_SENSITIVE_MASTER_REC,
                (tableName) => PLANNER_MASTER_REC_ALIAS,
                (alias, action) => plannerPageFieldsChangedSuccess(action.payload.workspaceGuid),
                (tableName) => masterRecDataKey,
                getPlannerFieldToLoad,
                (action) => action.payload.fieldOptions
            ),
            pagedDataEpics.createAddFieldsEpic(
                PLANNER_PAGE_ALIAS,
                PLANNER_TABLE_DATAS_SUFFIX,
                (state) => wss.getWorkspaceSubRecTableName(getWorkspaceSettings(state)),
                (state) => plannerPageSelectors.getSubRecCollectionAlias(getWorkspaceSettings(state)),
                actionTypes.ADD_DATE_SENSITIVE_SUB_REC,
                (tableName) => PLANNER_SUB_REC_ALIAS,
                (alias, action) => plannerPageFieldsChangedSuccess(action.payload.workspaceGuid),
                (tableName) => subRecDataKey,
                getPlannerFieldToLoad,
                (action) => action.payload.fieldOptions
            ),
            pagedDataEpics.createAddFieldsEpic(
                PLANNER_PAGE_ALIAS,
                PLANNER_TABLE_DATAS_SUFFIX,
                (state, { payload }) => payload.tableName,
                (state, { payload }) => wss.getBarGroupsGuidByTable(getWorkspaceSettings(state), payload.tableName),
                actionTypes.ADD_DATE_SENSITIVE_BAR_GROUPS,
                (tableName) => PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName],
                (alias, action) => plannerPageFieldsChangedSuccess(action.payload.workspaceGuid),
                (tableName) => PLANNER_PAGE_TABLE_TO_GROUP_MAP[tableName],
                getPlannerFieldToLoad,
                (action) => action.payload.fieldOptions
            ),
            loadRoleRequestStatusesEpic$,
            unassignResourceFromRoleRequestEpic$,
            batchUpdateRoleRequestsStatusEpic$,
            updateAssigneesInPlannerEpic$,
            reloadGroupedTableDataMasterRowsEpic$,
            addBarToClipboardEpic$,
            rolerequestMoveToEpic$,
            rolerequestMoveToCreateEpic$,
            assignResourceToCriteriaSuccessful$,
            loadPlannerCriteriaRoleAssigneesEpic$
        ),

        //Interceptors
        ...getPlannerDataEntityGroupEpics(PLANNER_BOOKING_GROUPS_ALIAS, TABLE_NAMES.BOOKING),
        ...getPlannerDataEntityGroupEpics(PLANNER_ROLEREQUESTS_ALIAS, TABLE_NAMES.ROLEREQUEST),
        resourceUpdateMasterRecDataInterceptorEpic(),
        jobUpdateMasterRecDataInterceptorEpic(),
        resourceUpdateSubRecDataInterceptorEpic(),
        jobUpdateSubRecDataInterceptorEpic(),
        createTableDataBatchUpdateInterceptorEpic(PLANNER_SUB_REC_ALIAS),
        createPagedTableDataBatchUpdateInterceptorEpic(PLANNER_MASTER_REC_ALIAS),
        createPlannerPageFieldsChangedInterceptor(),

        entityWindowSubmitUpdateMasterRecDataInterceptor(),
        entityWindowSubmitUpdateMasterRecDataErrorInterceptor(),

        plannerPageEntityWindowRoleAssigneesBudgetUpdateSuccessInterceptorEpic(),

        openPlannerAssigneeDetailsPaneEpic(),

        createEntityWindowContextualEditEpic(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
        createEntityWindowContextualEditEpic(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE),

        entityWindowJobUpdateInlineSingleDetailsPaneSuccessInterceptorEpic(),
        entityWindowRoleGroupDPUpdateInlineDetailsPaneSuccessInterceptorEpic(),
        entityWindowJobUpdateInlineBatchDetailsPaneSuccessInterceptorEpic(),

        entityWindowSubmitUpdateSubRecDataInterceptor(),
        entityWindowSubmitUpdateSubRecDataErrorInterceptor(),

        entityWindowJobInsertSuccessInterceptorEpic(),
        entityWindowJobInsertErrorInterceptorEpic(),
        entityWindowJobUpdateSuccessInterceptorEpic(),
        entityWindowJobUpdateErrorInterceptorEpic(),
        entityWindowJobDeleteSuccessInterceptorEpic(),
        entityWindowJobMasterRecInsertSuccessInterceptor(),
        entityWindowJobMasterRecInsertErrorInterceptor(),
        entityWindowJobSubRecInsertSuccessInterceptor(),
        entityWindowJobSubRecInsertErrorInterceptor(),

        entityWindowClientInsertSuccessInterceptorEpic(),
        entityWindowClientInsertErrorInterceptorEpic(),
        entityWindowClientInsertErrorJobsPageInterceptorEpic(),
        entityWindowClientPatchSuccessInterceptorEpic(),
        entityWindowClientPatchErrorInterceptorEpic(),
        entityWindowClientDeleteSuccessInterceptorEpic(),
        entityWindowJobsPageClientInsertSuccessInterceptor(),
        entityWindowJobsPageClientPatchSuccessInterceptorEpic(),
        entityWindowJobsPageClientDeleteSuccessInterceptorEpic(),
        entityWindowClientDeleteErrorInterceptorEpic(),
        // Entity window END

        pagedDataJobPatchSuccessInterceptorEpic(),
        pagedDataResourcePatchSuccessInterceptorEpic(),
        subRecDataJobPatchSuccessInterceptorEpic(),
        subRecDataResourcePatchSuccessInterceptorEpic(),
        requestFailedEpic,

        toggleDetailsPaneEpic(),

        createPlannerDataKeepAliveEpic(PLANNER_MASTER_REC_ALIAS),
        loadCustomColourSchemeEpic$,
        applyColourSchemeEpic$,

        showToasterMessageEpic(),
        createMovePendingTimeAllocationEpic(
            PLANNER_PAGE_ALIAS,
            (payload, state) => injectWorkspaceSettings(payload, state, movePendingTimeAllocationActionSuccess),
            managePendingTimeAllocationErrorAction
        ),
        createRemovePendingTimeAllocationEpic(
            PLANNER_PAGE_ALIAS,
            (payload, state) => injectWorkspaceSettings(payload, state, removePendingTimeAllocationActionSuccess),
            managePendingTimeAllocationErrorAction
        )
    );
}
