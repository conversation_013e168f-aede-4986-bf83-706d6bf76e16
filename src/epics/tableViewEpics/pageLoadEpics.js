import { of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { setPageStateDirty } from '../../actions/pageStateActions';
import { addTableDataModel } from '../../actions/tableDataActions';
import { addTableViewDataModel } from '../../actions/tableViewActions/actionCreators';
import { addPagedDataModel, grouppedTableDataModelsLoadedSuccess, handleTableViewPageDataFilters, populateSkillsValues } from '../../actions/workspaceActions';
import { addTableViewFieldOptionsModel, closeSortFloatingActionBar } from '../../actions/workspaceSettingsActions';
import { LICENSE_KEYS_ADMIN_SETTINGS } from '../../constants/globalConsts';
import { TABLEVIEW_MASTER_REC_ALIAS, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS, TABLEVIEW_SUB_REC_ALIAS } from '../../constants/plannerConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../../constants/tableViewPageConsts';
import { getPageLicenseSize } from '../../selectors/commonSelectors';
import { getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { getTableViewWorkspaceLoadedFields } from '../../selectors/tableViewSelectors';
import { getSelectedWorkspaceSettings } from '../../selectors/workspaceSelectors';
import { createPlannerPageFieldOptionsModel } from '../../state/plannerPageFieldOptions';
import { getAddGrouppedTableDataModelActionsTV } from '../../utils/workspaceUtils';
import { LOAD_PAGE_DATA_STRATEGIES } from '../applicationPagesEpics/pageLoadStrategiesUtils';


export const tableViewPageInitialLoadEpic = (action$, state$ /*{ apis }*/) =>
    action$.ofType(`${LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD}_${TABLE_VIEW_PAGE_ALIAS}`).pipe(
        mergeMap((_) => {
            const { workspaces } = state$.value.tableViewPage;
            const wsSettings = getSelectedWorkspaceSettings(workspaces);
            const getFieldInfo = getFieldInfoSelector(state$.value);
            const loadedFields = getTableViewWorkspaceLoadedFields(wsSettings, getFieldInfo);
            const workspaceGuid = wsSettings.workspace_guid;
            const pageSize = getPageLicenseSize(state$.value)(LICENSE_KEYS_ADMIN_SETTINGS.licenseTableViewPageSize);

            const groupTableDataModelLoadActions = Object.values(TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS).map(groupDataAlias =>
                grouppedTableDataModelsLoadedSuccess(groupDataAlias));

            return of(
                closeSortFloatingActionBar(),
                ...getAddGrouppedTableDataModelActionsTV(wsSettings),
                ...groupTableDataModelLoadActions,
                addPagedDataModel(TABLEVIEW_MASTER_REC_ALIAS, wsSettings['pagedMasterRecTableViewDataGuid'], wsSettings.masterRecTableName, pageSize),
                addTableDataModel(TABLEVIEW_SUB_REC_ALIAS, wsSettings['subRecTableViewDataGuid'], wsSettings.subRecTableName),
                addTableViewDataModel(wsSettings['tableViewDataGuid']),
                handleTableViewPageDataFilters(),
                addTableViewFieldOptionsModel(workspaceGuid, createPlannerPageFieldOptionsModel(loadedFields)),
                populateSkillsValues(TABLE_VIEW_PAGE_ALIAS)
            );
        })
    );

/* This epic currently is not used. Will be used once the load from dirty strategy for table view is used */
export const tableViewPageLoadFromDirtyEpic = (action$/*, state$, { apis } */) =>
    action$.ofType(`${LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_DIRTY}_${TABLE_VIEW_PAGE_ALIAS}`)
        .pipe(
            mergeMap((_) => {
                return of(
                    handleTableViewPageDataFilters(),
                    // loadTableViewData(wsSettings),
                    populateSkillsValues(TABLE_VIEW_PAGE_ALIAS),
                    setPageStateDirty(TABLE_VIEW_PAGE_ALIAS, false)
                );
            })
        );

/* This epic currently is not used. Could be used if the load from params strategy for table view is used */
export const tableViewPageLoadFromParamsEpic = (action$/*, state$, { apis } */) =>
    action$.ofType(`${LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS}_${TABLE_VIEW_PAGE_ALIAS}`)
        .pipe(
            mergeMap((_) => {
                return of (
                    handleTableViewPageDataFilters()
                    // loadTableViewData(wsSettings)
                );
            })
        );