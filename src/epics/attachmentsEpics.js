import { combineEpics, ofType } from 'redux-observable';
import { switchMap, mergeMap, map, withLatestFrom } from 'rxjs/operators';
import { saveAs } from 'file-saver';
import { empty, of } from 'rxjs';

import { createAPICallEpic, getApiCallEpicConfig } from './epicGenerators/apiCallEpicGenerator';
import { API_SUFFIX, ATTACHMENTS } from '../actions/actionTypes';
import {
    insertAttachmentError,
    loadAttachmentsSuccess,
    loadAttachmentsError,
    insertAttachmentSuccess,
    getAttachmentDataSuccess,
    getAttachmentDataError,
    deleteAttachmentSuccess,
    deleteAttachmentError,
    setAttachmentMessages,
    insertAttachment,
    loadAttachments,
    moveAttachmentToSuccess,
    moveAttachmentToError
} from '../actions/attachmentsActions';
import { createAttachment } from '../state/attachments';
import { convertBase64ToBlob, getFileExtension, isTalentProfilePageTransformedAndIsResourceTable } from '../utils/attachments';
import {
    getUIEntityAttachmentIds,
    getEntityAttachmentSize,
    getEntityAttachmentConfig,
    getAttachmentsStaticMessagesSelector
} from '../selectors/attachmentsSelectors';
import { simpleMapPipe, simpleMapPipeWithoutRequestFailedDispatch } from '../api/pipes.api';
import { isDetailsPane } from '../utils/commonUtils';
import { batchActions } from 'redux-batched-actions';
import store from '../store/configureStore';
import { ERROR_STATUS } from '../constants';
import { requestFailed } from '../actions/httpRequestsActions';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';
const attachmentsApiName = 'attachments';

const { SUCCESS, FAILURE } = API_SUFFIX;

const downloadAttachment = (file, { tableName }) => {
    const {
        name,
        data
    } = createAttachment(tableName, file);

    if (name && data) {
        const blob = convertBase64ToBlob(data, name);

        saveAs(blob, name);
    }
};

const getAttachmentsPipe = map(({ serverResponse, success }) => {
    let responseObj = null;

    if (!success) {
        responseObj = {
            serverResponse: {},
            status: ERROR_STATUS
        };

        const errorPromptAction = requestFailed(responseObj);
        const { dispatch } = store;

        dispatch(errorPromptAction);
    }

    return responseObj || serverResponse;
});

export const loadAttachmentsRequest$ = (api, { tableName, entityId }) =>
    api.getAttachments$(tableName, entityId);

const deleteAttachmentsRequest$ = (api, payload) => {
    const { tableName, attachmentId, entityId } = payload;

    return api.withPipe(getAttachmentsPipe)
        .deleteAttachment$(tableName, entityId, attachmentId);
};

/**
 * The method is used to call the API endpoint to change the type of an attachment.
 * @param {Object} api Its an array of function endpoints
 * @param {Object} payload Payload of the request.
 * @returns {boolean} Return Observable result.
 */
const moveAttachmentToRequest$ = (api, payload) => {
    const { tableName, entityId, attachmentId, moveDocumentTo } = payload;

    return api.withPipe(getAttachmentsPipe).moveDocumentTo$(tableName, entityId, attachmentId, moveDocumentTo);
};

const createDigestInsertAttachmentEpic$ = (action$, state$) =>
    action$
        .ofType(ATTACHMENTS.DIGEST_INSERT)
        .pipe(
            mergeMap(
                ({ payload = {} }) => {
                    const { url, name, documentType, expiryDate } = payload;
                    const options = {
                        type: getFileExtension(name)
                    };

                    return fetch(url)
                        .then(res => res.blob())
                        // eslint-disable-next-line no-undef
                        .then(blob => new File([blob], name, options))
                        .catch(() => null);
                },
                (action, result) => [action, result]
            ),
            mergeMap(
                ([{ payload = {} }, file]) => file
                    ? of(insertAttachment({ ...payload, data: file }))
                    : empty()
            )
        );

const insertAttachmentRequest$ = (api, { alias, tableName, entityId, documentType, expiryDate, data }, talentProfilePageTransformedEnabled) => {
    // eslint-disable-next-line no-undef
    let formData = new FormData();
    formData.append('file', data);

    // Include the expiry date and document type only when the talent profile page is enabled
    // and the document is uploaded for the resource
    if (isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName)) {
        formData.append('documentType', documentType);
        formData.append('expiryDate', expiryDate);
    }

    return api.withPipe(isDetailsPane(alias) ? simpleMapPipeWithoutRequestFailedDispatch : simpleMapPipe)
        .insertAttachment$(tableName, entityId, formData);
};

const getAttachmentDataRequest$ = (api, payload) => {
    const { tableName, entityId, attachmentId } = payload;

    return api.withPipe(getAttachmentsPipe)
        .getAttachment$(tableName, entityId, attachmentId);
};

export const createLoadAttachmentsEpic$ = (successActionHandler, errorActionHandler) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(
            ATTACHMENTS.LOAD,
            attachmentsApiName,
            loadAttachmentsRequest$,
            successActionHandler,
            errorActionHandler
        )
    );

export const createReloadAttachmentsEpic$ = () => (action$, state$) => action$
    .ofType(
        ATTACHMENTS.INSERT_SUCCESS,
        `${ATTACHMENTS.CHANGE_ATTACHMENT_TYPE}_${SUCCESS}`
    )
    .pipe(
        map(({ payload = {} }) => {
            const {
                alias,
                tableName,
                entityId
            } = payload;

            return loadAttachments(alias, tableName, entityId);
        })
    );

export const createDeleteAttachmentEpic$ = (successActionHandler, errorActionHandler) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(
            ATTACHMENTS.DELETE,
            attachmentsApiName,
            deleteAttachmentsRequest$,
            successActionHandler,
            errorActionHandler
        )
    );

/**
 * The epic is used to change the type of an attachment.
 * @param {function} successActionHandler Function that triggers a dispatch on successful changing of an attachment.
 * @param {function} errorActionHandler Function that triggers a dispatch when an error occurs during attachment type change.
 */
export const createMoveAttachmentToEpic$ = (successActionHandler, errorActionHandler) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(
            ATTACHMENTS.CHANGE_TYPE,
            attachmentsApiName,
            moveAttachmentToRequest$,
            successActionHandler,
            errorActionHandler
        )
    );

/**
 * The epic is used to create attachment.
 * @param {Observable} action$ Payload that contains action types.
 * @param {Observable} state$ Payload that contains redux state.
 * @param {{ apis: Object; }} param0 Array of  functions endpoints
 * @returns {Observable} Returns observable result.
 */
export const createInsertAttachmentEpic$ = (action$, state$, { apis }) => action$.pipe(
    ofType(ATTACHMENTS.INSERT),
    withLatestFrom(state$),
    switchMap(([action, state]) => {
        // Check if the talent profile page is transformed or not.
        const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state);

        // Create payload for the insert attachment
        const { moduleName, alias, tableName, entityId, name, url, file } = action.payload;

        // Common payload for the success and failure action
        const commonPayload = { alias, tableName, entityId, name, url, file, size: file?.size };

        return insertAttachmentRequest$(apis.attachments, action.payload, talentProfilePageTransformedEnabled).pipe(
            map((response) => response.status === ERROR_STATUS
                ? insertAttachmentError(moduleName, commonPayload, response?.error)
                : insertAttachmentSuccess(moduleName, commonPayload))
        );
    })
);

const createGetAttachmentDataEpic$ = (successActionHandler, errorActionHandler) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(
            ATTACHMENTS.GET_ATTACHMENT_DATA,
            attachmentsApiName,
            getAttachmentDataRequest$,
            successActionHandler,
            errorActionHandler,
            undefined,
            undefined,
            downloadAttachment
        )
    );

const createSetAttachmentMessagesEpic$ = (action$, state$) =>
    action$
        .ofType(
            ATTACHMENTS.LOAD_SUCCESS,
            ATTACHMENTS.INSERT_SUCCESS,
            ATTACHMENTS.INSERT_ERROR,
            ATTACHMENTS.DELETE_SUCCESS,
            ATTACHMENTS.UI_ADD,
            ATTACHMENTS.UI_REMOVE
        )
        .pipe(
            switchMap(({ type, payload = {} }) => {
                const state = state$.value;
                const {
                    alias,
                    tableName,
                    entityId,
                    attachmentId,
                    fileName,
                    fileSize,
                    data: insertedAttachmentIds,
                    serverError = {}
                } = payload;

                let attachmentIds = getUIEntityAttachmentIds({
                    attachments: state.attachments,
                    moduleName: alias,
                    entityId
                });

                if (type === ATTACHMENTS.INSERT_SUCCESS) {
                    attachmentIds = [
                        ...attachmentIds,
                        insertedAttachmentIds
                    ];
                }

                const attachments = {
                    fileName,
                    fileSize: fileSize || getEntityAttachmentSize({
                        attachments: state.attachments,
                        attachmentId,
                        entityId,
                        moduleName: alias
                    }),
                    attachmentIds,
                    ...getEntityAttachmentConfig({
                        attachments: state.attachments,
                        tableName,
                        entityId
                    }),
                    serverError
                };

                const staticMessages = getAttachmentsStaticMessagesSelector(state);

                return of(setAttachmentMessages(alias, { tableName, entityId, attachments, staticMessages }));
            })
        );

const createUpdateAttachmentStateEpic$ = (action$, state$) =>
    action$
        .ofType(
            ATTACHMENTS.DELETE_SUCCESS,
            ATTACHMENTS.DELETE_ERROR,
            ATTACHMENTS.GET_ATTACHMENT_DATA_ERROR
        )
        .pipe(
            switchMap(({ payload = {} }) => {
                const { attachmentId, entityId, tableName } = payload;
                const state = state$.value;
                const { attachments: attachmentsState } = state;
                const actionsToDispatch = [];

                Object.keys(attachmentsState.map).forEach((alias) => {
                    if (attachmentsState.map[alias][entityId] && attachmentsState.map[alias][entityId].attachments[attachmentId]) {
                        actionsToDispatch.push(deleteAttachmentSuccess(alias, { alias, tableName, entityId, attachmentId }));
                    }
                });

                return actionsToDispatch.length > -1 ? of(batchActions(actionsToDispatch)) : empty();
            })
        );

export default combineEpics(
    createLoadAttachmentsEpic$(loadAttachmentsSuccess, loadAttachmentsError)(),
    createReloadAttachmentsEpic$(),
    createInsertAttachmentEpic$,
    createGetAttachmentDataEpic$(getAttachmentDataSuccess, getAttachmentDataError)(),
    createDeleteAttachmentEpic$(deleteAttachmentSuccess, deleteAttachmentError)(),
    createMoveAttachmentToEpic$(moveAttachmentToSuccess, moveAttachmentToError)(),
    createSetAttachmentMessagesEpic$,
    createDigestInsertAttachmentEpic$,
    createUpdateAttachmentStateEpic$
);