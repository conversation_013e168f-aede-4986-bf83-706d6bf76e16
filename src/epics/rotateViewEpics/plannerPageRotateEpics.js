import { EMPTY, of } from 'rxjs';
import { reloadPlannerData, togglePlannerExpandCollapseAll } from '../../actions/plannerDataActions';
import { clearStoredFilterViewSettings, plannerPageRotateFilters, RESTORE_PLANNER_VIEW, restoreFilterViewSettings, ROTATE_PLANNER_VIEW } from '../../actions/rotatePlannerViewActions';
import { clearStoredWorkspaceViewSettingsAction, cloneCommonViewHideToggles, restoreWorkspaceViewSettingsAction, storeWorkspaceViewSettingsAction, viewSettingChanged } from '../../actions/workspaceSettingsActions';
import { TABLE_NAMES, RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES, FILTER_TYPES } from '../../constants';
import { PLANNERPAGE_FILTER_ALIAS, PLANNER_PAGE_ALIAS } from '../../constants/plannerConsts';
import { getBarGroupsGuidByTable, getCurrentWSSettingsSelector, getWorkspaceSettings, getRotatedWorkspaceViewSelector } from '../../selectors/workspaceSelectors';
import { getCurrentPlannerData } from '../../selectors/plannerPageSelectors';
import { combineAPIEpics } from '../middlewareExtensions';
import { mergeMap } from 'rxjs/operators';
import * as actionTypes from '../../actions/actionTypes';
import { UI_FILTER_OPERTORS } from '../../constants/advancedFilterConsts';
import { getFilterFieldAliasSelector } from '../../connectedComponents/connectedFilters/selectors';
import { applyAdvancedFilters } from '../../actions/filterActions';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

const createParentFilter = (tableName, fieldName, fieldAlias) => ({
    tableName,
    fieldName,
    fieldAlias,
    operator: UI_FILTER_OPERTORS.IS,
    type: FILTER_TYPES.MULTY_VALUES,
    icon: ''
});

export const rotatePlannerEpic$ = (action$, state$, /*{ apis }*/) =>
    action$.ofType(ROTATE_PLANNER_VIEW).pipe(
        mergeMap((action) => {
            const { payload: { targetView, parentIds } } = action;

            if (!targetView || parentIds.length === 0) {
                return EMPTY;
            }

            const state = state$.value;
            const plannerPageState = state.plannerPage || {};
            const wsSettings = getCurrentWSSettingsSelector(state);
            const { plannerDataGuid, filtersGuid, workspace_guid } = wsSettings;
            const bookingGroupsGuid = getBarGroupsGuidByTable(wsSettings, TABLE_NAMES.BOOKING);
            const rotateChain = [];

            const parentFilterFieldName = `${targetView}_guid`;
            const parentFilterAlias = getFilterFieldAliasSelector(state)(targetView, parentFilterFieldName);
            const filter = createParentFilter(targetView, parentFilterFieldName, parentFilterAlias);
            const plannerUnassignedRolesToggleFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state);

            if (!getRotatedWorkspaceViewSelector(plannerPageState)(workspace_guid, targetView)) {
                rotateChain.push(storeWorkspaceViewSettingsAction(workspace_guid, targetView));
            }

            rotateChain.push(
                cloneCommonViewHideToggles(workspace_guid, wsSettings.masterRecTableName, targetView),
                plannerPageRotateFilters(
                    PLANNERPAGE_FILTER_ALIAS,
                    targetView,
                    filter,
                    parentIds,
                    filtersGuid
                ),
                viewSettingChanged(workspace_guid, bookingGroupsGuid, plannerDataGuid, targetView),
                applyAdvancedFilters(PLANNERPAGE_FILTER_ALIAS, filtersGuid, false)
            );

            const { guid, expandCollapseAllState } = getCurrentPlannerData(state);
            const viewIsCollapsedAll = expandCollapseAllState[targetView] === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.COLLAPSED;

            if (viewIsCollapsedAll) {
                rotateChain.push(togglePlannerExpandCollapseAll({plannerDataGuid: guid, plannerUnassignedRolesToggleFeatureEnabled}));
            }

            return of(...rotateChain);
        })
    );

export const restorePlannerEpic$ = (action$, state$, /*{ apis }*/) =>
    action$.ofType(RESTORE_PLANNER_VIEW).pipe(
        mergeMap((action) => {
            const { payload: { workspaceGuid } } = action;
            const state = state$.value || {};
            const plannerPageState = state.plannerPage || {};

            const workspaceSettings = getWorkspaceSettings(plannerPageState.workspaces || {}, workspaceGuid);
            const { filtersGuid, workspace_guid, masterRecTableName } = workspaceSettings || {};

            return of(
                restoreWorkspaceViewSettingsAction(workspace_guid, masterRecTableName),
                restoreFilterViewSettings(PLANNERPAGE_FILTER_ALIAS, filtersGuid, masterRecTableName),
                reloadPlannerData()
            );
        })
    );

const clearAllStoredViewSettingsAction = [
    actionTypes.DIGEST_SELECT_WORKSPACE,
    actionTypes.DIGEST_SAVE_AS_NEW_PLAN,
    actionTypes.SAVE_WORKSPACE_SETTINGS_SUCCESSFUL
];

const clearSingleStoredViewSettingsActions = [
    `${actionTypes.FILTERS_SETTINGS_CHANGED}_${PLANNER_PAGE_ALIAS}`,
    actionTypes.HIDE_FUTURE_RECORDS_CHANGED,
    actionTypes.HIDE_HISTORIC_RECORDS_CHANGED,
    actionTypes.HIDE_UNASSIGNED_ROWS_CHANGED,
    actionTypes.HIDE_ROLES_RECORDS_CHANGED,
    actionTypes.HIDE_UNASSIGNED_ROLES_CHANGED,
    actionTypes.HIDE_ROLES_BY_NAME_CHANGED,
    actionTypes.HIDE_ROLES_BY_REQUIREMENTS_CHANGED,
    actionTypes.HIDE_DRAFT_ROLES_RECORDS_CHANGED,
    actionTypes.HIDE_REQUESTED_ROLES_RECORDS_CHANGED,
    actionTypes.HIDE_LIVE_BARS_CHANGED,
    actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED,
    actionTypes.HIDE_WEEKENDS_CHANGED
];

const clearStoredViewSettingsActions = [
    ...clearAllStoredViewSettingsAction,
    ...clearSingleStoredViewSettingsActions
];

export const clearStoredViewSettings$ = (action$, state$, /*{ apis }*/) =>
    action$.ofType(...clearStoredViewSettingsActions).pipe(
        mergeMap((action) => {
            const state = state$.value || {};
            const workspaceSettings = getCurrentWSSettingsSelector(state);
            const { filtersGuid, workspace_guid, masterRecTableName, subRecTableName } = workspaceSettings || {};

            const viewsToClear = [masterRecTableName];
            const isClearAllStoredViewSettings = clearAllStoredViewSettingsAction.some(actionType => actionType === action.type);

            if (isClearAllStoredViewSettings) {
                viewsToClear.push(subRecTableName);
            }

            return of(
                clearStoredWorkspaceViewSettingsAction(workspace_guid, viewsToClear),
                clearStoredFilterViewSettings(PLANNERPAGE_FILTER_ALIAS, filtersGuid, viewsToClear)
            );
        })
    );

export default combineAPIEpics(
    rotatePlannerEpic$,
    restorePlannerEpic$,
    clearStoredViewSettings$
);