/** @typedef {import('./standardSkill.jsdoc').StandardSkill} StandardSkill */

/**
 * @typedef {Object} SubCategory
 * @property {number} id - The unique identifier for the subcategory.
 * @property {string} name - The name of the subcategory.
 * @property {StandardSkill[]} skills - skills belonging to the subcategory

 * @typedef {Object} StandardSkillTaxonomy
 * @property {number} id - The unique identifier for the main category.
 * @property {string} name - The name of the main category.
 * @property {SubCategory[]} subCategories - An array of subcategory objects associated with this category.
 */

// Export an empty object to enable imports
export { };
