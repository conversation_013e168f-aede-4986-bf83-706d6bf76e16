/**
 * Entity Access Card Configuration Object
 * @typedef {Object} EntityAccessCardConfig
 * @property {boolean} isParentCard - Indicates if this card is a parent card (read access type).
 * @property {string} fnaName - The name of the FNA rule.
 * @property {string} accessType - The type of access (e.g., READ, EDIT).
 * @property {boolean} isReadAccessType - True if the access type is READ.
 * @property {string} entityAlias - The alias or name of the entity.
 * @property {number} order - The order of the FNA rule.
 * @property {Object} fnaValueProps - The FNA value properties for this rule.
 * @property {string|number} accessLevelId - The ID of the access level.
 * @property {boolean} isEntityAccess - Always true; indicates this is an entity access card.
 * @property {boolean} hideEntityAccessConditions - If true, hides entity access conditions UI.
 * @property {string} key - Unique key for the card.
 */

export {};