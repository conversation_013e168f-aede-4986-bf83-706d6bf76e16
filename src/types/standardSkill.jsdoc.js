/**
 * @typedef {Object} StandardSkill
 * @property {string} externalId - A unique external identifier for the skill.
 * @property {string} description - A detailed description of the skill, including its features, use cases, and history.
 * @property {number} categoryId - The ID representing the main category this skill belongs to.
 * @property {number} subcategoryId - The ID representing the subcategory under the main category.
 * @property {number} id - The unique identifier for the skill entry.
 * @property {string} name - The name of the skill.
 */

// Export an empty object to enable imports
export { };
