import { RESOURCE_SKILLS, SKILLS_CHANGES, EDIT_RESOURCE_SKILLS_WINDOW, RECOMMENDATION_SKILLS, API_SUFFIX, RESOURCE_SKILLS_APPROVALS } from '../actions/actionTypes';
import { buildStructure } from '../state/skillStructure/mapCollection';
import createChangesCollection from '../utils/changesCollectionUtils';
import initialState from '../state/initialState';
import { createResourceSkill } from '../state/resourceSkills/resourceSkill';
import skillsChangesReducer from './resourceSkillsChangesReducer';

const { SUCCESS, FAILURE } = API_SUFFIX;
const initialRecommendationState = { jobTitles: [], jobs: [], primarySkills: [] };
const initialApprovalsState = { pending: [], history: [], actions: [] };

export default (state = initialState.resourceSkills, action) => {
    switch (action.type) {
        case RESOURCE_SKILLS.POPULATE.SKILLS: {
            const {
                entityId,
                skills
            } = action.payload;

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    skills: buildStructure('resourceskill_skill_guid', skills, createResourceSkill),
                    skillsChanges: createChangesCollection(
                        'id',
                        [],
                        [],
                        []
                    )
                }
            };
        }
        case RESOURCE_SKILLS.UI.INIT: { //leaving it only for single skill window form until refactor selectors for init
            const {
                entityId,
                skill
            } = action.payload;

            const { id } = skill;

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    currentEdit: id // decouple from reducer
                }
            };
        }
        case RESOURCE_SKILLS.UI.FIELD_CHANGE: {
            const { payload } = action;
            const { entityId, skillId, fieldInfo, fieldValue, errors, skillInfo } = payload;
            const { skillsChanges, skills = {} } = state[entityId];

            const skillsChangesAction = {
                type: SKILLS_CHANGES.FIELD_CHANGE,
                payload: {
                    skillId,
                    fieldInfo,
                    fieldValue,
                    errors,
                    skill: (skills.map || {})[skillId],
                    skillInfo
                }
            };

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    currentEdit: skillId,
                    skillsChanges: skillsChangesReducer(skillsChanges, skillsChangesAction)
                }
            };
        }
        case SKILLS_CHANGES.REMOVE_SKILL:
        case SKILLS_CHANGES.DISCARD_DELETE:
        case SKILLS_CHANGES.DISCARD_CHANGES:
        case SKILLS_CHANGES.SET_SKILL_FIELDS_ERRORS:
        case SKILLS_CHANGES.ADD_SKILLS: {
            const { entityId } = action.payload;
            const { skillsChanges } = state[entityId];

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    skillsChanges: skillsChangesReducer(skillsChanges, action)
                }
            };
        }
        case EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS:
        case RECOMMENDATION_SKILLS.ACCEPT_RECOMMENDATION_SKILLS: {
            const { entityId } = action.payload;
            const { skillsChanges } = state[entityId];
            const internalAction = {
                ...action,
                type: SKILLS_CHANGES.ADD_SKILLS
            };

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    skillsChanges: skillsChangesReducer(skillsChanges, internalAction)
                }
            };
        }
        case RECOMMENDATION_SKILLS.IGNORE_RECOMMENDATION_SKILLS: {
            const { entityId, skillIds } = action.payload;
            const { ignoredRecommendations } = state[entityId];
            const skillIdsToIgnore = ignoredRecommendations ? [...ignoredRecommendations, ...skillIds] : skillIds;

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    ignoredRecommendations: skillIdsToIgnore
                }
            };
        }
        case RECOMMENDATION_SKILLS.DISCARD_IGNORE_RECOMMENDATION_SKILLS: {
            const { entityId } = action.payload;

            return {
                ...state,
                [entityId]: {
                    ...state[entityId],
                    ignoredRecommendations: []
                }
            };
        }
        case RESOURCE_SKILLS.POPULATE.SKILL_PREFERENCES: {
            const { preferences } = action.payload;

            return {
                ...state,
                skillPreferences: preferences
            };
        }
        case `${RESOURCE_SKILLS.POPULATE.AUTHORIZE_SKILLS}_${SUCCESS}`: {
            const { skills } = action.payload;

            return {
                ...state,
                resourceSkillsAllowed: {
                    ...skills
                },
                loading: false
            };
        }
        case `${RESOURCE_SKILLS.LOAD.AUTHORIZE_SKILLS}_${FAILURE}`: {
            return {
                ...state,
                loading: false
            };
        }
        case `${RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS}`: {
            const { entityId } = action.payload;
            const newState = {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    recommendations: initialRecommendationState,
                    loading: true
                }
            };

            return newState;
        }
        case `${RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS}${SUCCESS}`: {
            const { entityId, recommendationData } = action.payload;

            const newState = {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    recommendations: recommendationData,
                    loading: false
                }
            };

            return newState;
        }
        case `${RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS}${FAILURE}`: {
            const { entityId } = action.payload;

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    recommendations: initialRecommendationState,
                    loading: false
                }
            };
        }
        case `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING}`: {
            const { entityId } = action.payload;
            const newApprovalsState = {
                ...(state[entityId]?.approvals || {}),
                pending: initialApprovalsState.pending,
                loading: true
            };

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    approvals: newApprovalsState
                }
            };
        }
        case `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING}${SUCCESS}`: {
            const { entityId, data } = action.payload;

            const newApprovalsState = {
                ...(state[entityId]?.approvals || {}),
                pending: data,
                loading: true
            };

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    approvals: newApprovalsState
                }
            };
        }
        case `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING}${FAILURE}`: {
            const { entityId } = action.payload;
            const newApprovalsState = {
                ...(state[entityId]?.approvals || {}),
                pending: initialApprovalsState.pending,
                loading: false
            };

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    approvals: newApprovalsState
                }
            };
        }
        case `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY}`: {
            const { entityId } = action.payload;
            const newApprovalsState = {
                ...(state[entityId]?.approvals || {}),
                history: initialApprovalsState.history,
                loading: true
            };

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    approvals: newApprovalsState
                }
            };
        }
        case `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY}${SUCCESS}`: {
            const { entityId, data } = action.payload;
            const newApprovalsState = {
                ...(state[entityId]?.approvals || {}),
                history: data,
                loading: false
            };

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    approvals: newApprovalsState
                }
            };
        }
        case `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY}${FAILURE}`: {
            const { entityId } = action.payload;
            const newApprovalsState = {
                ...(state[entityId]?.approvals || {}),
                history: initialApprovalsState.history,
                loading: false
            };

            return {
                ...state,
                [entityId]: {
                    ...(state[entityId] || {}),
                    approvals: newApprovalsState
                }
            };
        }
        default: {
            return state;
        }
    }
};
