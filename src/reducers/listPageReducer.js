import initialState from '../state/initialState';
import { LIST_PAGE_ACTIONS } from '../actions/actionTypes';

export default (state = initialState.listPage, action) => {
    switch (action.type) {
        case LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW: {
            return {
                ...state,
                activeListView: action.payload
            }
        }
        default: {
            return state;
        }

    }
};