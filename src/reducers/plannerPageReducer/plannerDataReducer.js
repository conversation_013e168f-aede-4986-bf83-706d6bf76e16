import { difference, groupBy } from 'lodash';
import initialState from '../../state/initialState';
import * as actions from '../../actions/actionTypes';
import { RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES } from '../../constants';
import { buildConcurentRowsMap, buildBarConflicts, buildBarPotentialConflicts, getVisibleBarsGroups as getVisibleBarsGroupsFN, getBarTableName, barGroupsData, alocateConcurentBarsRows, getMappedBarColours, canScroll, getPlannerPaginationMode } from '../../utils/plannerDataUtils';
import { getBarTouchedData, isUnassignedBar } from '../../utils/calendarGridUtils';
import { PLANNER_ACTORS, CONTEXT_MENU_ACTIONS, UNASSIGNED_BOOKINGS_RESOURCE, TABLE_NAMES } from '../../constants/index';
import createPlannerDataModel from '../../state/plannerData';
import { transform } from 'lodash';
import { RESOURCE_BOOKING_POTENTIAL_CONFLICT_DATES, ROLEREQUESTRESOURCE_ASSIGNEE_GUID, ROLEREQUESTRESOURCE_FIELDS, ROLEREQUEST_FIELDS } from '../../constants/fieldConsts';
import { getIsCriteriaRole, getIsCriteriaRoleDemandFulfilled } from '../../utils/roleRequestsUtils';
import { getDisplayUnassginedCriteriaRolesAsResourcesFlag } from '../../selectors/workspaceSelectors';
import { insertIntoSortedCollection, isTableEntity, omit } from '../../utils/commonUtils';
import { NEW_ROLE } from '../../constants/rolesConsts';
import { CRITERIA_ROLE_ASSIGNEE_BARS_KEY, DEFAULT_PAGE_NUMBER, UNASSIGNED_CRITERIA_ROLE_BARS_KEY } from '../../constants/plannerConsts';
import { isValidDate, parseToUtcDate } from '../../utils/dateUtils';
import { getCurrentViewHideUnassignedResourceRows } from '../../selectors/plannerPageSelectors';
import { dayjsInstance } from '../../setup/dayjsInstanceSetup';

const getBarGroupsGuids = (workspace = {}) => {
    return { ...workspace.barGroupsGuids, rolerequestResourceGroupsGuid: `rolerequestResourceGroups_${workspace.workspace_guid}` };
};

const getJobsViewOrderedRowBars = (bars) => {
    const roleAssignees = bars.filter(bar => isTableEntity(bar, TABLE_NAMES.ROLEREQUESTRESOURCE));

    //TODO: roleAssignees.length > 0 requires a discussions with a designer in order to decide if we want to display
    // the criteria role bars if no assignees are visible but the demand is still fulfilled
    const canDisplayRoleBar = (bar) => getIsCriteriaRole(bar)
        ? roleAssignees.length > 0 ? !getIsCriteriaRoleDemandFulfilled(bar) : true
        : true;

    return [
        ...bars.filter(bar => isTableEntity(bar, TABLE_NAMES.BOOKING)),
        ...roleAssignees,
        ...bars.filter(bar => (isTableEntity(bar, TABLE_NAMES.ROLEREQUEST) && canDisplayRoleBar(bar)))
    ];
};

const getVisibleBarsGroups = (barsGroupsData, workspace, masterRecTableName, filter = () => true, plannerUnassignedRolesToggleFeatureEnabled = false) => {
    let result = getVisibleBarsGroupsFN(barsGroupsData, workspace, masterRecTableName, filter, plannerUnassignedRolesToggleFeatureEnabled);
    const shouldProcessCriteriaRoleBars = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

    if (shouldProcessCriteriaRoleBars && masterRecTableName === TABLE_NAMES.RESOURCE && result[UNASSIGNED_CRITERIA_ROLE_BARS_KEY]) {
        const unassignedCriteriaRoleBars = [];

        result[UNASSIGNED_CRITERIA_ROLE_BARS_KEY] = result[UNASSIGNED_CRITERIA_ROLE_BARS_KEY].filter(
            bar => {
                const isUnassignedRoleBar = isUnassignedBar(bar, TABLE_NAMES.ROLEREQUEST);
                const isCriteriaRole = getIsCriteriaRole(bar);
                const isUnassignedCriteriaRole = isUnassignedRoleBar && isCriteriaRole;

                if (isUnassignedCriteriaRole) {
                    unassignedCriteriaRoleBars.push(bar);
                }

                return !isUnassignedCriteriaRole;
            }
        );

        unassignedCriteriaRoleBars.forEach(bar => {
            const roleAssignees = (result[CRITERIA_ROLE_ASSIGNEE_BARS_KEY] || [])
                .filter(resBar => resBar[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID] == bar[ROLEREQUEST_FIELDS.GUID])
                .map(resBar => ({
                    ...resBar,
                    [`${TABLE_NAMES.ROLEREQUESTRESOURCE}_${TABLE_NAMES.JOB}_guid`]: bar[ROLEREQUEST_FIELDS.JOB_GUID]
                    //Add Res field also
                }));

            //TODO: roleAssignees.length > 0 requires a discussions with a designer in order to decide if we want to display
            // the criteria role bars if no assignees are visible but the demand is still fulfilled
            result[bar[ROLEREQUEST_FIELDS.GUID]] = getIsCriteriaRoleDemandFulfilled(bar) && roleAssignees.length > 0 ? roleAssignees : [...roleAssignees, bar];
        });
    } else if (shouldProcessCriteriaRoleBars && masterRecTableName === TABLE_NAMES.JOB) {
        Object.entries(result).forEach(([jobId, subRows]) => result[jobId] = getJobsViewOrderedRowBars(subRows));
    }

    return result;
};

export const getProcessedRowsOnExpand = (rowsExpanded = {}, recId) => {
    const newRowsExpanded = Object.assign({}, rowsExpanded);
    const toggleValue = rowsExpanded[recId];

    newRowsExpanded[recId] = !toggleValue;

    return newRowsExpanded;
};

const getResourceConflictDates = (resourcesData) => {
    return resourcesData.map((resource) => {
        return {
            ['resource_guid']: resource['resource_guid'],
            ['conflict dates']: JSON.parse(resource['resource_booking_conflict_dates'] || '[]') // refactor? dependant on the backEnd and DB
        };
    });
};

const getResourceBarPotentialConflictDates = (resourcesData) => {
    return resourcesData.map((resource) => {
        return {
            ['resource_guid']: resource['resource_guid'],
            ['conflict dates']: JSON.parse(resource[RESOURCE_BOOKING_POTENTIAL_CONFLICT_DATES] || '[]') // refactor? dependant on the backEnd and DB
        };
    });
};

export const createDummyRows = (count, masterRecTableName) => {
    const dummyRows = [];

    for (let i = 0; i < count; i++)
        dummyRows.push({
            tableName: masterRecTableName,
            id: `dummyKey${Math.floor(Math.random() * 10000000)}`,
            subRows:[]
        });

    return dummyRows;
};

export const clearDummyRows = (rowsById) => {
    const cleared = { ...rowsById };

    Object.keys(rowsById).forEach(key => {
        if (key.indexOf('dummy') > -1) {
            delete cleared[key];
        }
    });

    return cleared;
};

export const getPageSize = (totalCount, loadedCount, pageSize) => {
    const diff = (totalCount - loadedCount);

    return pageSize > diff ? diff : pageSize;
};

export const toggleRowsExpandCollapse = (rowsExpanded = {}, expanded) => {
    return transform(rowsExpanded, (result, oldExpanded, id) => {
        result[id] = expanded;
    }, {});
};

export const getToggleRowIds = (rowsExpanded = {}, expanded) => {
    return Object.keys(rowsExpanded).filter((id) => rowsExpanded[id] !== expanded);
};

const getConflictedDates = (selectionData, barData, conflictedDates = {}) => {
    let barProps = {};
    if (!('dummy' in barData)) {
        barProps = {
            startDate: parseToUtcDate(barData['booking_start']), // refactor? current selection only works supports booking
            endDate: parseToUtcDate(barData['booking_end']) // refactor? current selection only works supports booking
        };
    }

    const barTouchedData = getBarTouchedData(selectionData, barProps);
    const barInsideSelection = !barTouchedData.selectionStartConflicted && !barTouchedData.selectionEndConflicted;

    return {
        startDate: (barInsideSelection || barTouchedData.selectionStartConflicted) || conflictedDates.startDate,
        endDate: (barInsideSelection || barTouchedData.selectionEndConflicted) || conflictedDates.endDate
    };
};

const getPossibleBookingConflictMessageData = () => {
    return {
        type: 'common',
        data: {
            type: 'warning',
            status: 'warning',
            text: 'Possible booking conflict'
        }
    };
};

const getSelectionConflictData = (actor) => {
    let conflictData = {
        messages: [],
        conflictedActions: []
    };

    if (actor === PLANNER_ACTORS.SINGLE_ROW_SELECTION) {
        const possibleBookingConflictMessage = getPossibleBookingConflictMessageData();
        conflictData = {
            messages: [possibleBookingConflictMessage],
            conflictedActions: [CONTEXT_MENU_ACTIONS.CREATE_BOOKING]
        };
    }

    return conflictData;
};

export const setRowsExpanded = (expandCollapseState, rowsIds = []) => {
    const isExpanded = expandCollapseState === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.EXPANDED;

    return transform(rowsIds, (rowsExpanded, id) => {
        rowsExpanded[id] = isExpanded;
    }, {});
};

const sortBarsByDate = (bars = []) => {
    return [...bars].sort((a, b) => {
        const barTableNameA = getBarTableName(a);
        const startDateA = new Date(a[`${barTableNameA}_start`]).getTime();
        const endDateA = new Date(a[`${barTableNameA}_end`]).getTime();

        const barTableNameB = getBarTableName(b);
        const startDateB = new Date(b[`${barTableNameB}_start`]).getTime();
        const endDateB = new Date(b[`${barTableNameB}_end`]).getTime();

        let result = startDateA - startDateB;

        if (startDateA == startDateB) {
            result = (endDateA - startDateA) - (endDateB - startDateB);
        }

        return result;
    });
};

const areBarDatesValid = (bar, tableName) => {
    const startDate = bar[`${tableName}_start`];
    const endDate = bar[`${tableName}_end`];

    return startDate && endDate && isValidDate(startDate) && isValidDate(endDate);
};

const isBarPartOfSubRow = (bar, subRows, { subRecTableName, tableName }) => {
    const barSubRecField = `${tableName}_${subRecTableName}_guid`;

    return (subRows || []).some((subRow) => subRow.id === bar[barSubRecField]
        || (subRow.id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID && bar[barSubRecField] === null)
        || subRow.id === bar[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID]
        || tableName === TABLE_NAMES.ROLEREQUEST && subRow.id === bar[ROLEREQUEST_FIELDS.GUID]);
};

const getMasterRowBars = (row, bars = [], { subRecTableName }) => {
    const filteredBars = bars.filter((bar) => areBarDatesValid(bar, getBarTableName(bar))
        && isBarPartOfSubRow(bar, row.subRows, { subRecTableName, tableName: getBarTableName(bar) }));

    return sortBarsByDate(filteredBars);
};

const getSubRowBars = (bars = [], { subRecTableName }) => {
    const filteredBars = bars.filter((bar) => areBarDatesValid(bar, getBarTableName(bar)));
    const sortedBars = sortBarsByDate(filteredBars);

    return groupBy(sortedBars, (bar) => bar[`${getBarTableName(bar)}_${subRecTableName}_guid`] || null);
};

const getBarRenderDates = (groupRow = []) => {
    return groupRow.reduce((accumulator, bar) => {
        const barTableName = getBarTableName(bar);

        return {
            ...accumulator,
            [bar[`${barTableName}_guid`]]: {
                startDate: bar[`${barTableName}_start`],
                endDate: bar[`${barTableName}_end`]
            }
        };
    }, {});
};

const addBarsMap = (row, 
    rowBars, 
    { subRecTableName, displayUnassignedCriteriaRolesAsResources }, 
    expand, 
    recordsMap, 
    plannerUnassignedRolesToggleFeatureEnabled = false, 
    historicFutureRecords = {hideHistoricRecords: false, hideFutureRecords: false}
) => {
    const alocatedCleanFilter = (item) => null !== item;
    const barIdSelector = (bar) => bar[`${getBarTableName(bar)}_guid`];
    const barIdWithTableNameSelector = (bar) => {
        const tableName = getBarTableName(bar);

        return {
            guid: bar[`${tableName}_guid`],
            tableName
        };
    };

    const rowId = row.id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ? null : row.id;

    if (plannerUnassignedRolesToggleFeatureEnabled) {
        if (recordsMap && recordsMap.rolerequestresource && recordsMap.rolerequestresource.length > 0) {
            recordsMap.rolerequestresource.forEach(roleRequestResource => {
                if (subRecTableName === TABLE_NAMES.JOB) {
                    if (roleRequestResource[ROLEREQUESTRESOURCE_ASSIGNEE_GUID] === row.id) {
                        row.subRows = row.subRows || [];
                        if (row.subRows) {
                            const subRowFound = row.subRows.find(subRow => subRow.id === roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.JOB_GUID]);
                            if(!subRowFound) {
                                row.subRows.push({
                                    id: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.JOB_GUID],
                                    isFuture: historicFutureRecords.hideFutureRecords,
                                    isHistoric: historicFutureRecords.hideHistoricRecords,
                                    tableName: TABLE_NAMES.JOB,
                                });
                            }
                        }
                    }
                } else if (subRecTableName === TABLE_NAMES.RESOURCE) {
                    if (roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.JOB_GUID] === row.id) {
                        row.subRows = row.subRows || [];
                        if (row.subRows) {
                            const subRowFound = row.subRows.find(subRow => subRow.id === roleRequestResource[ROLEREQUESTRESOURCE_ASSIGNEE_GUID]);
                            if(!subRowFound) {
                                row.subRows.push({
                                    id: roleRequestResource[ROLEREQUESTRESOURCE_ASSIGNEE_GUID],
                                    isFuture: historicFutureRecords.hideFutureRecords,
                                    isHistoric: historicFutureRecords.hideHistoricRecords,
                                    tableName: TABLE_NAMES.RESOURCE,
                                });
                            }
                        }
                    }
                }
            })
        }
    }

    const groupRows = (expand
        ? getSubRowBars(rowBars[rowId], { subRecTableName })
        : getMasterRowBars(row, rowBars[rowId], { subRecTableName })) || [];

    const addBarsMapResult = expand
        ? {
            ...row,
            subRows: (row
                .subRows || [])
                .map(subRow => {
                    const subRowId = subRow.id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID || subRow.tableName === TABLE_NAMES.ROLEREQUEST ? null : subRow.id;
                    let groupRow = [...groupRows[subRowId] || []];

                    if (subRow.tableName === TABLE_NAMES.ROLEREQUEST) {
                        groupRow = groupRow.filter(
                            bar => (getIsCriteriaRole(bar) && bar[ROLEREQUEST_FIELDS.GUID] === subRow.id) || bar[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID] === subRow.id
                        );
                    }

                    if (subRow.tableName === TABLE_NAMES.RESOURCE && subRowId === null && displayUnassignedCriteriaRolesAsResources) {
                        groupRow = groupRow.filter(bar => !getIsCriteriaRole(bar) && getBarTableName(bar) !== TABLE_NAMES.ROLEREQUESTRESOURCE) || [];
                    }

                    return {
                        ...subRow,
                        barsRenderDates: getBarRenderDates(groupRow),
                        bars: groupRow.map(barIdSelector),
                        barsRowsMap: buildConcurentRowsMap(alocateConcurentBarsRows(groupRow), alocatedCleanFilter, barIdWithTableNameSelector)
                    };
                })
        }
        : {
            ...row,
            barsRenderDates: getBarRenderDates(groupRows),
            bars: groupRows.map(barIdSelector),
            barsRowsMap: buildConcurentRowsMap(alocateConcurentBarsRows(groupRows), alocatedCleanFilter, barIdWithTableNameSelector)
        };

    if (plannerUnassignedRolesToggleFeatureEnabled) {
        if (subRecTableName === TABLE_NAMES.RESOURCE) {
            if (recordsMap && recordsMap.rolerequestresource && recordsMap.rolerequestresource.length > 0) {
                if (expand) {
                    if (addBarsMapResult.subRows && addBarsMapResult.subRows.length > 0) {
                        addBarsMapResult.subRows = addBarsMapResult.subRows.map(subRow => {
                            if (subRow.tableName === TABLE_NAMES.RESOURCE) {
                                recordsMap.rolerequestresource.forEach(roleRequestResource => {
                                    if (roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.JOB_GUID] === addBarsMapResult.id 
                                        && roleRequestResource[ROLEREQUESTRESOURCE_ASSIGNEE_GUID] === subRow.id) {
                                        subRow.bars = [...subRow.bars, roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.GUID]];
                                        subRow.barsRenderDates = {
                                            ...subRow.barsRenderDates, 
                                            ...{ 
                                                [roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.GUID]]: { 
                                                    startDate: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.START], 
                                                    endDate: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.END] 
                                                } 
                                            }
                                        };
                                        const addedBarsRowsMapKey = Object.keys(subRow.barsRowsMap).length;
                                        subRow.barsRowsMap = {
                                            ...subRow.barsRowsMap, 
                                            [addedBarsRowsMapKey]: [{
                                                guid: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.GUID],
                                                tableName: TABLE_NAMES.ROLEREQUESTRESOURCE
                                            }]
                                        };
                                    }
                                })
                            }
                            return subRow;
                        })
                        .filter(subRow => {
                            if (subRow.tableName === TABLE_NAMES.ROLEREQUEST) {
                                // To filter out duplicate bars, check if the current roleRequest bar was created from an existing roleRequestResource (Role by Requirement). 
                                // If so remove it because we have already created a role by requirement bar against it's assigned resource.
                                if (recordsMap && recordsMap.rolerequestresource) {
                                    const matchingRoleRequestResource = recordsMap.rolerequestresource.find(roleRequestResource => roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID] === subRow.id);
                                    if (matchingRoleRequestResource) {
                                        return false;
                                    }
                                }
                            }
                            return true;
                        });
                    }
                } else {
                    recordsMap.rolerequestresource.forEach(roleRequestResource => {
                        // Loop through each resource and add it's role by requirement bars if any exists.
                        if (roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.JOB_GUID] === addBarsMapResult.id) {
                            addBarsMapResult.bars = [...addBarsMapResult.bars, roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.GUID]];
                            addBarsMapResult.bars = addBarsMapResult.bars.reduce((accumulator, current) => {
                                if (!accumulator.includes(current)) {
                                    accumulator.push(current);
                                }
                                return accumulator;
                            }, []);
                            addBarsMapResult.barsRenderDates = {
                                ...addBarsMapResult.barsRenderDates, 
                                ...{ 
                                    [roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.GUID]]: { 
                                        startDate: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.START], 
                                        endDate: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.END] 
                                    } 
                                }
                            };
                            const addedBarsRowsMapKey = Object.keys(addBarsMapResult.barsRowsMap).length;
                            addBarsMapResult.barsRowsMap = {
                                ...addBarsMapResult.barsRowsMap, 
                                [addedBarsRowsMapKey]: [{
                                    guid: roleRequestResource[ROLEREQUESTRESOURCE_FIELDS.GUID],
                                    tableName: TABLE_NAMES.ROLEREQUESTRESOURCE
                                }]
                            };

                            const rowsBars = [];
                            // filter to remove duplicated bars
                            for (const key of Object.keys(addBarsMapResult.barsRowsMap).reverse()) {
                                addBarsMapResult.barsRowsMap[key] = addBarsMapResult.barsRowsMap[key].filter(rowBar => {
                                    const duplicateRoleFound = rowsBars.find(item => item.guid === rowBar.guid);
                                    if (!duplicateRoleFound) {
                                        rowsBars.push(rowBar);
                                        return true;
                                    }
                                    return false;
                                })
                            }

                            // FIlter to remove barsRows with no bars
                            addBarsMapResult.barsRowsMap = Object.keys(addBarsMapResult.barsRowsMap).reduce((accumulator, key) => {
                                if (addBarsMapResult.barsRowsMap[key].length > 0) {
                                    accumulator[key] = addBarsMapResult.barsRowsMap[key];
                                }
                                return accumulator;
                            }, {});
                        }
                    })
                }
            }
        }
    }

    return addBarsMapResult;
};

const getPlannerRowStructureById = (
    rowStructure, 
    barGroups, 
    subRecTableName, 
    rowsExpanded, 
    displayUnassignedCriteriaRolesAsResources, 
    recordsMap, 
    plannerUnassignedRolesToggleFeatureEnabled,
    historicFutureRecords) => {
    return Object
        .keys(rowStructure)
        .reduce((acc, rowKey) => {
            return {
                ...acc,
                [rowKey]: {
                    ...addBarsMap(
                        {
                            ...acc[rowKey],
                            ...rowStructure[rowKey],
                            id: rowKey
                        },
                        barGroups,
                        { subRecTableName, displayUnassignedCriteriaRolesAsResources },
                        rowsExpanded[rowKey],
                        recordsMap,
                        plannerUnassignedRolesToggleFeatureEnabled,
                        historicFutureRecords
                    ),
                    expanded: rowsExpanded[rowKey],
                }
            };
        }, {});
};

const getUnassignedCriteriaRoleRowsIds = (rowStructure) =>
    Object.entries(rowStructure)
        .filter(([, { tableName }]) => tableName === TABLE_NAMES.ROLEREQUEST)
        .map(([id]) => id);

const buildUnassignedCriteriaRoleRows = (rowStructure, visibleBarsGroups = {}) =>
    getUnassignedCriteriaRoleRowsIds(rowStructure)
        .sort((a, b) => (((visibleBarsGroups[a] || [])[0] || {})[ROLEREQUEST_FIELDS.DESCRIPTION] || NEW_ROLE)
            .localeCompare(((visibleBarsGroups[b] || [])[0] || {})[ROLEREQUEST_FIELDS.DESCRIPTION] || NEW_ROLE, 'en', { sensitivity: 'base' }));

const getInvalidRowIds = (rowsById, visibleBarsGroups, masterRecTableName) => {
    return Object.keys(rowsById).filter(rowId => {
        const row = rowsById[rowId];

        return row.tableName === TABLE_NAMES.ROLEREQUEST && masterRecTableName === TABLE_NAMES.RESOURCE
            ? visibleBarsGroups[rowId] == null
            : false;
    });
};

const getAddedCriteriaRowIds = (rowsById, rowStructure, masterRecTableName) => {
    return Object.keys(rowStructure).filter(rowId => {
        const row = rowStructure[rowId];

        return masterRecTableName === TABLE_NAMES.RESOURCE && row.tableName === TABLE_NAMES.ROLEREQUEST
            ? rowStructure[rowId] && rowsById[rowId] == null
            : false;
    });
};

const insertRowCompareFn = (visibleBarsGroups, rowsById, displayUnassignedCriteriaRolesAsResources) => (insertEntityId, currentEntityId) => {
    const insertEntity = visibleBarsGroups[insertEntityId] || [];
    const currentEntity = visibleBarsGroups[currentEntityId] || [];

    const { tableName: currentEntityTableName } = rowsById[currentEntityId] || {};
    const { tableName: insertEntityTableName } = rowsById[insertEntityId] || {};

    const isCurrentEntityUnassignedResource = currentEntityTableName === TABLE_NAMES.RESOURCE && currentEntityId === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID;
    const isInsertRoleEntity = insertEntityTableName === TABLE_NAMES.ROLEREQUEST;

    const compareCriteriaEntities = displayUnassignedCriteriaRolesAsResources
        ? currentEntityTableName === TABLE_NAMES.ROLEREQUEST && isInsertRoleEntity
        : false;

    let canInsert = false;

    if (compareCriteriaEntities) {
        canInsert = ((insertEntity[0] || {})[ROLEREQUEST_FIELDS.DESCRIPTION] || NEW_ROLE)
            .localeCompare(((currentEntity[0] || {})[ROLEREQUEST_FIELDS.DESCRIPTION] || NEW_ROLE), 'en', { sensitivity: 'base' }) > 0;
    }

    if (displayUnassignedCriteriaRolesAsResources && isCurrentEntityUnassignedResource && isInsertRoleEntity && !canInsert) {
        canInsert = true;
    }

    return canInsert;
};

const getUpdatedPlannerRowStructure = (
    plannerData, 
    visibleBarsGroups, 
    displayUnassignedCriteriaRolesAsResources, 
    action,
    recordsMap,
    plannerUnassignedRolesToggleFeatureEnabled, 
    historicFutureRecords
) => {
    const { workspace, payload: { rowStructure } } = action;
    const { rowsExpanded: oldRowsExpanded, rowsById: oldRowsById, rows: oldRows, expandCollapseAllState } = plannerData;
    const { masterRecTableName, subRecTableName } = workspace;

    const invalidRowIds = getInvalidRowIds(oldRowsById, visibleBarsGroups, masterRecTableName);
    const addedRowIds = getAddedCriteriaRowIds(oldRowsById, rowStructure, masterRecTableName);

    const isRowExpanded = expandCollapseAllState[masterRecTableName] === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.EXPANDED;
    const newRowsExpanded = setRowsExpanded(expandCollapseAllState[masterRecTableName], addedRowIds);

    const addedRowsById = addedRowIds.reduce((accumulator, rowId) => {
        const row = addBarsMap(
            rowStructure[rowId], 
            visibleBarsGroups, 
            { subRecTableName, displayUnassignedCriteriaRolesAsResources }, 
            isRowExpanded, 
            recordsMap,
            plannerUnassignedRolesToggleFeatureEnabled, 
            historicFutureRecords
        );

        return { ...accumulator, [rowId]: row };
    }, {});

    let updatedRows = difference(oldRows, invalidRowIds);
    const updatedRowsById = { ...omit(oldRowsById, invalidRowIds), ...addedRowsById };
    const rowCompareFn = insertRowCompareFn(visibleBarsGroups, updatedRowsById, displayUnassignedCriteriaRolesAsResources);

    addedRowIds.forEach(rowId => {
        updatedRows = insertIntoSortedCollection(updatedRows, rowId, rowCompareFn);
    });

    const plannerRowStructure = {
        rows: updatedRows,
        rowsById: updatedRowsById,
        rowsExpanded: { ...omit(oldRowsExpanded, invalidRowIds), ...newRowsExpanded }
    };

    return plannerRowStructure;
};

function hasUnassignedResourceRows(pages, masterRecTableName, rowStructure) {
    return pages.some(pageNumber => pageNumber === DEFAULT_PAGE_NUMBER)
        && masterRecTableName === TABLE_NAMES.RESOURCE
        && rowStructure.hasOwnProperty(UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID);
}

function hasHiddenUnssaignedResourceRows(pages, masterRecTableName, hideUnassignedResourceRows, displayUnassignedCriteriaRoles) {
    return pages.some(pageNumber => pageNumber === DEFAULT_PAGE_NUMBER)
        && masterRecTableName === TABLE_NAMES.RESOURCE
        && displayUnassignedCriteriaRoles
        && hideUnassignedResourceRows;
}

function getAdditionalRows(page, masterRecTableName, rowStructure, visibleBarsGroups, workspace) {
    const rows = [];
    const hideUnassignedResourceRows = getCurrentViewHideUnassignedResourceRows(workspace);
    const displayUnassignedCriteriaRoles = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

    if (hasUnassignedResourceRows([page], masterRecTableName, rowStructure)) {
        rows[0] = UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID;

        if (getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace)) {
            rows.push(...(buildUnassignedCriteriaRoleRows(rowStructure, visibleBarsGroups) || []));
        }
    }

    if (hasHiddenUnssaignedResourceRows([page], masterRecTableName, hideUnassignedResourceRows, displayUnassignedCriteriaRoles)) {
        rows.push(...(buildUnassignedCriteriaRoleRows(rowStructure, visibleBarsGroups) || []));
    }

    return rows;
}

function getAdditionalCachedRowIds(cachedPages, masterRecTableName, rowStructure) {
    return hasUnassignedResourceRows(cachedPages, masterRecTableName, rowStructure)
        ? [
            UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID,
            ...getUnassignedCriteriaRoleRowsIds(rowStructure)
        ]
        : [];
}

function cachedRowsFilter(rowId, cachedRowIds, additionalCachedRowIds) {
    return [...additionalCachedRowIds, ...Object.keys(cachedRowIds)].some(id => id === rowId);
}

function getMergedRecordsMap(tableName, existingRecordsMap, newRecordsMap) {
    return [
        ...new Map([...existingRecordsMap, ...newRecordsMap]
            .map(item => [item[`${tableName}_guid`], item]))
            .values()
    ];
}
function getUpdatedRecordsMap(existingRecordsMap, newRecordsMap) {
    return Object.keys(newRecordsMap).reduce((accumulator, key) => {
        accumulator[key] = getMergedRecordsMap(key, existingRecordsMap[key], newRecordsMap[key]);
        return accumulator;
    }, {});
}

function updateRecordsMap(existingRecordsMap, newRecordsMap) {
    return getUpdatedRecordsMap(
        existingRecordsMap || { 
            [TABLE_NAMES.BOOKING]: [], 
            [TABLE_NAMES.ROLEREQUEST]: [], 
            [TABLE_NAMES.ROLEREQUESTRESOURCE]: [] 
        }, 
        newRecordsMap
    );
}

export default function plannerDataReducer(state = initialState.plannerPage.plannerData, action) {
    switch (action.type) {
        case actions.PLANNER_DATA_MODELS_LOADED_SUCCESS: {
            return {
                ...state,
                loading: false
            };
        }
        case actions.ADD_PLANNER_DATA_MODEL: {
            const { payload } = action;
            const { guid } = payload;

            return {
                ...state,
                [guid]: {
                    ...state[guid],
                    ...createPlannerDataModel(guid)
                }
            };
        }
        case actions.LOAD_PLANNER_DATA:
        case actions.LOAD_PLANNER_DATA_PAGE:
        case actions.SORT_PLANNER_DATA: {
            const { payload } = action;
            const { plannerDataGuid } = payload;

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    loading: true
                }
            };
        }
        case actions.SCROLL_PLANNER_DATA: {
            const { payload, pagedMasterRecPlannerData } = action;
            const { masterRecTableName, plannerDataGuid, pagedMasterRecPlannerDataGuid, maximumInfiniteScrollRows } = payload;
            const { pageSize, rowCount, data } = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid];

            let newState = state;

            if (canScroll(rowCount, data.length, maximumInfiniteScrollRows, getPlannerPaginationMode)) {
                const size = getPageSize(rowCount, data.length, pageSize);
                const dummyRows = createDummyRows(size, masterRecTableName);

                newState = {
                    ...state,
                    [plannerDataGuid]: {
                        ...state[plannerDataGuid],
                        rows: [
                            ...state[plannerDataGuid].rows,
                            ...dummyRows.map(r => r.id)
                        ],
                        rowsById: {
                            ...state[plannerDataGuid].rowsById,
                            ...dummyRows.reduce((acc, dummyRow) => {
                                return {
                                    ...acc,
                                    [dummyRow.id]: {
                                        ...dummyRow
                                    }
                                };
                            }, {})
                        }
                    }
                };
            }

            return newState;
        }
        case actions.PLANNER_VER_SCROLL_POS_CHANGED: {
            const { plannerDataGuid, verticalScrollYOffset, verticalScrollRatio } = action.payload;

            return { ...state,
                [plannerDataGuid] : {
                    ...state[plannerDataGuid],
                    scrollSettings: {
                        ...state[plannerDataGuid].scrollSettings,
                        verticalScrollYOffset,
                        verticalScrollRatio
                    }
                }
            };
        }
        case actions.PLANNER_VER_SCROLL_POS_RESET: {
            const { plannerDataGuid } = action.payload;

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    scrollSettings: {
                        ...state[plannerDataGuid].scrollSettings,
                        verticalScrollYOffset: 0,
                        verticalScrollRatio: 0
                    }
                }
            };
        }
        case actions.VIEW_SETTINGS_CHANGED: {
            const { plannerDataGuid } = action.payload;

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    selection: {},
                    scrollSettings: {
                        ...state[plannerDataGuid].scrollSettings,
                        verticalScrollYOffset: 0,
                        verticalScrollRatio: 0
                    }
                }
            };
        }
        case actions.PG_ROW_EXPAND_CHANGED: {
            const { barGroups, workspace } = action;
            const { plannerDataGuid, recId, plannerUnassignedRolesToggleFeatureEnabled } = action.payload;
            const { rowsExpanded, rowsById } = state[plannerDataGuid];
            const { masterRecTableName, subRecTableName } = workspace;
            const displayUnassignedCriteriaRolesAsResources = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

            const newRowsExpanded = getProcessedRowsOnExpand(rowsExpanded, recId);
            const rowId = recId === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ? null : recId;

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    rowsExpanded: newRowsExpanded,
                    rowsById: {
                        ...rowsById,
                        [recId]: {
                            ...addBarsMap(
                                rowsById[recId],
                                getVisibleBarsGroups(
                                    barGroupsData(barGroups, getBarGroupsGuids(workspace)),
                                    workspace,
                                    masterRecTableName,
                                    (bar) => bar[`${getBarTableName(bar)}_${masterRecTableName}_guid`] == rowId
                                        || bar[`${getBarTableName(bar)}_assignee_guid`] == rowId 
                                        || bar[ROLEREQUEST_FIELDS.GUID] === rowId 
                                        || bar[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID] === rowId,
                                    plannerUnassignedRolesToggleFeatureEnabled
                                ),
                                { subRecTableName, displayUnassignedCriteriaRolesAsResources },
                                newRowsExpanded[recId],
                                state[plannerDataGuid].recordsMap,
                                plannerUnassignedRolesToggleFeatureEnabled,
                                state[plannerDataGuid].historicFutureRecords,
                            ),
                            expanded: newRowsExpanded[recId]
                        }
                    },
                    selection: {}
                }
            };
        }
        case actions.PLANNER_DATA_LOADED: {
            const { payload, pagedMasterRecPlannerData, subRecPlannerData, barGroups, plannerTableDatas, workspace, showConflict, uiOptions = {} } = action;
            const { plannerDataGuid, pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid, subRecTableName, persistRowsExpanded, rowStructure, resetVerticalScroll = false, barColours = [], maximumInfiniteScrollRows, recordsMap, plannerUnassignedRolesToggleFeatureEnabled, historicFutureRecords } = payload;
            const masterRec = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid],
                subRec = subRecPlannerData[subRecPlannerDataGuid],
                bookingTypes = plannerTableDatas.bookingtype;
            const { rowCount, data, tableName: masterRecTableName } = masterRec;
            const conflicts = showConflict ? getResourceConflictDates(masterRecTableName === TABLE_NAMES.RESOURCE ? masterRec.data : subRec.data) : [];
            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)), 
                workspace, 
                masterRecTableName, 
                undefined, 
                plannerUnassignedRolesToggleFeatureEnabled
            );
            const displayUnassignedCriteriaRolesAsResources = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

            const { pageNumber = 1 } = uiOptions;
            const pageIndex = pageNumber - 1;
            const { from, to } = (masterRec.loadedPagesMap || {})[pageIndex] || {};
            const newPagedData = masterRec.data.slice(from, to);

            let scrollPossible = canScroll(rowCount, data.length, maximumInfiniteScrollRows, getPlannerPaginationMode);

            const { expandCollapseAllState, rowsExpanded: oldRowsExpanded } = state[plannerDataGuid];

            const plannerRowStructure = {
                rows: [
                    ...getAdditionalRows(pageIndex, masterRecTableName, rowStructure, visibleBarsGroups, workspace),
                    ...newPagedData.map(r => r[`${masterRecTableName}_guid`])
                ],
                rowsById: {}
            };

            let rowsExpanded = setRowsExpanded(expandCollapseAllState[masterRecTableName], plannerRowStructure.rows);

            if (persistRowsExpanded) {
                rowsExpanded = { ...rowsExpanded, ...oldRowsExpanded };
            }

            plannerRowStructure.rowsById = getPlannerRowStructureById(
                rowStructure,
                visibleBarsGroups,
                subRecTableName,
                rowsExpanded,
                displayUnassignedCriteriaRolesAsResources,
                recordsMap,
                plannerUnassignedRolesToggleFeatureEnabled,
                historicFutureRecords
            );

            const mappedBarColours = getMappedBarColours(barColours);

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    ...plannerRowStructure,
                    rowsExpanded,
                    loading: false,
                    loaded: true,
                    scrollSettings :{
                        ...state[plannerDataGuid].scrollSettings,
                        ...(resetVerticalScroll
                            ? {
                                verticalScrollYOffset: 0,
                                verticalScrollRatio: 0
                            }
                            : {}
                        ),
                        canScroll: scrollPossible
                    },
                    isDirty: false,
                    barConflicts: buildBarConflicts(visibleBarsGroups, conflicts, TABLE_NAMES.BOOKING, bookingTypes), //Temporary solution until behavior for roles conflicts is defined
                    barColours: mappedBarColours,
                    recordsMap,
                    historicFutureRecords
                }
            };
        }
        case actions.PLANNER_DATA_PATCH: {
            const { payload, barGroups, workspace } = action;
            const { plannerDataGuid, rowStructure, barColours, recordsMap, plannerUnassignedRolesToggleFeatureEnabled, historicFutureRecords } = payload;
            const { masterRecTableName, subRecTableName } = workspace;
            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)), 
                workspace, 
                masterRecTableName, 
                undefined, 
                plannerUnassignedRolesToggleFeatureEnabled
            );
            const displayUnassignedCriteriaRolesAsResources = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

            const updatedRecordsMap = updateRecordsMap(state[plannerDataGuid].recordsMap, recordsMap);

            const plannerRowStructure = getUpdatedPlannerRowStructure(
                state[plannerDataGuid], 
                visibleBarsGroups, 
                displayUnassignedCriteriaRolesAsResources, 
                action, 
                updatedRecordsMap,
                plannerUnassignedRolesToggleFeatureEnabled,
                historicFutureRecords
            );
            const mappedBarColours = getMappedBarColours(barColours);

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    ...plannerRowStructure,
                    rowsById: {
                        ...plannerRowStructure.rowsById,
                        ...getPlannerRowStructureById(
                            rowStructure,
                            visibleBarsGroups,
                            subRecTableName,
                            plannerRowStructure.rowsExpanded,
                            displayUnassignedCriteriaRolesAsResources,
                            updatedRecordsMap,
                            plannerUnassignedRolesToggleFeatureEnabled,
                            historicFutureRecords
                        )
                    },
                    barColours: {
                        ...state[plannerDataGuid].barColours,
                        ...mappedBarColours
                    },
                    recordsMap: updatedRecordsMap,
                    historicFutureRecords
                }
            };
        }
        //TODO fix scrolling stuff as well
        case actions.PLANNER_DATA_SCROLLED: {
            const { payload, pagedMasterRecPlannerData, barGroups, workspace } = action;
            const { plannerDataGuid, pagedDataCount, pagedMasterRecPlannerDataGuid, subRecTableName, rowStructure, barColours = [], maximumInfiniteScrollRows, recordsMap, plannerUnassignedRolesToggleFeatureEnabled, historicFutureRecords } = payload;
            const currPagedMasterRecPlannerData = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid];
            const { rowCount, data, byId = {}, tableName: masterRecTableName } = currPagedMasterRecPlannerData;
            const newPagedData = currPagedMasterRecPlannerData.data.slice(pagedDataCount);
            const { rows, rowsExpanded, expandCollapseAllState } = state[plannerDataGuid];
            const displayUnassignedCriteriaRolesAsResources = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

            const actualRows = [
                ...rows.slice(0, rows.length - newPagedData.length),
                ...newPagedData.map(r => r[`${masterRecTableName}_guid`])
            ];

            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)), 
                workspace, 
                masterRecTableName,
                undefined,
                plannerUnassignedRolesToggleFeatureEnabled
            );
            let scrollPossible = canScroll(rowCount, data.length, maximumInfiniteScrollRows, getPlannerPaginationMode);

            const newRowsExpanded = setRowsExpanded(expandCollapseAllState[masterRecTableName], [...Object.keys(rowsExpanded), ...Object.keys(byId)]);
            
            // Persist the expanded state of each row
            Object.keys(newRowsExpanded).forEach((key) => {
                newRowsExpanded[key] = rowsExpanded[key];
            })

            const mappedBarColours = getMappedBarColours(barColours);
            const updatedRecordsMap = updateRecordsMap(state[plannerDataGuid].recordsMap, recordsMap);

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    rows: actualRows,
                    rowsById: {
                        ...clearDummyRows(state[plannerDataGuid].rowsById),
                        ...getPlannerRowStructureById(
                            rowStructure,
                            visibleBarsGroups,
                            subRecTableName,
                            newRowsExpanded,
                            displayUnassignedCriteriaRolesAsResources,
                            updatedRecordsMap,
                            plannerUnassignedRolesToggleFeatureEnabled,
                            historicFutureRecords
                        )
                    },
                    rowsExpanded: {
                        ...rowsExpanded,
                        ...newRowsExpanded
                    },
                    loading: false,
                    scrollSettings : {
                        ...state[plannerDataGuid].scrollSettings,
                        canScroll: scrollPossible
                    },
                    barColours: {
                        ...state[plannerDataGuid].barColours,
                        ...mappedBarColours
                    },
                    recordsMap: updatedRecordsMap,
                    historicFutureRecords
                }
            };
        }
        case actions.PLANNER_DATA_PAGE_LOADED: {
            const { payload, pagedMasterRecPlannerData, barGroups, workspace, uiOptions } = action;
            const { plannerDataGuid, pagedMasterRecPlannerDataGuid, subRecTableName, rowStructure, barColours, recordsMap, plannerUnassignedRolesToggleFeatureEnabled, historicFutureRecords } = payload;
            const currPagedMasterRecPlannerData = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid];
            const { byId = {}, tableName: masterRecTableName, loadedPagesMap = {}, loadedPages, data } = currPagedMasterRecPlannerData;
            const { pageNumber } = uiOptions;
            const pageIndex = pageNumber - 1;
            const { from, to } = loadedPagesMap[pageIndex] || {};
            const newPagedData = data.slice(from, to);

            const { rowsExpanded, expandCollapseAllState, rowsById } = state[plannerDataGuid];
            const displayUnassignedCriteriaRolesAsResources = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)), 
                workspace, 
                masterRecTableName,
                undefined,
                plannerUnassignedRolesToggleFeatureEnabled
            );

            const recIds = difference(Object.keys(rowStructure), Object.keys(rowsExpanded));

            const newRowsExpanded = {
                ...rowsExpanded,
                ...setRowsExpanded(expandCollapseAllState[masterRecTableName], recIds)
            };

            const mappedBarColours = getMappedBarColours(barColours);

            const additionalCachedRowIds = getAdditionalCachedRowIds(loadedPages, masterRecTableName, rowsById);

            const filteredRowsById = Object.fromEntries(Object.entries(rowsById)
                .filter(([rowId, _]) => cachedRowsFilter(rowId, byId, additionalCachedRowIds)));

            const updatedRecordsMap = updateRecordsMap(state[plannerDataGuid].recordsMap, recordsMap);

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    rows: [
                        ...getAdditionalRows(pageIndex, masterRecTableName, rowStructure, visibleBarsGroups, workspace),
                        ...newPagedData.map(r => r[`${masterRecTableName}_guid`])
                    ],
                    rowsById: {
                        ...getPlannerRowStructureById(
                            rowStructure,
                            visibleBarsGroups,
                            subRecTableName,
                            newRowsExpanded,
                            displayUnassignedCriteriaRolesAsResources,
                            updatedRecordsMap,
                            plannerUnassignedRolesToggleFeatureEnabled,
                            historicFutureRecords
                        ),
                        ...filteredRowsById
                    },
                    rowsExpanded: newRowsExpanded,
                    loading: false,
                    scrollSettings : {
                        ...state[plannerDataGuid].scrollSettings,
                        verticalScrollYOffset: 0,
                        verticalScrollRatio: 0,
                        canScroll: false
                    },
                    barColours: {
                        ...state[plannerDataGuid].barColours,
                        ...mappedBarColours
                    },
                    recordsMap: updatedRecordsMap,
                    historicFutureRecords
                }
            };
        }
        case actions.OPEN_PLANNER_CACHED_PAGE: {
            const { payload, pagedMasterRecPlannerData, barGroups, workspace } = action;
            const { pageNumber, plannerDataGuid, pagedMasterRecPlannerDataGuid, plannerUnassignedRolesToggleFeatureEnabled } = payload;
            const currPagedMasterRecPlannerData = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid];
            const { tableName: masterRecTableName } = currPagedMasterRecPlannerData;
            const pageIndex = pageNumber - 1;
            const { from, to } = (currPagedMasterRecPlannerData.loadedPagesMap || {})[pageIndex] || {};
            const newPagedData = currPagedMasterRecPlannerData.data.slice(from, to);

            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)), 
                workspace, 
                masterRecTableName,
                undefined,
                plannerUnassignedRolesToggleFeatureEnabled
            );

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    rows: [
                        ...getAdditionalRows(pageIndex, masterRecTableName, state[plannerDataGuid].rowsById, visibleBarsGroups, workspace),
                        ...newPagedData.map(r => r[`${masterRecTableName}_guid`])
                    ],
                    loading: false,
                    scrollSettings : {
                        ...state[plannerDataGuid].scrollSettings,
                        verticalScrollYOffset: 0,
                        verticalScrollRatio: 0,
                        canScroll: false
                    }
                }
            };
        }
        case actions.PLANNER_SELECTION_CREATED: {
            const { payload } = action;
            const { positionData, actor } = payload;
            const plannerDataGuid = payload.plannerDataGuid;

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    selection: {
                        positionData,
                        actor
                    }
                }
            };
        }
        case actions.PLANNER_SELECTION_UPDATED: { // refactor? current selection only works supports booking
            const payload = action.payload;
            const { plannerDataGuid, selectedBars, actor } = payload;

            let conflictData = {
                messages: [],
                conflictedActions: []
            };

            const conflictedDates = Object.assign({}, state[plannerDataGuid].conflictedDates[actor]);
            let newConflictedDates = {};

            if (selectedBars.length > 0) {
                conflictData = getSelectionConflictData(actor);

                if (conflictData.messages.length > 0) {
                    selectedBars.forEach((bar) => {
                        const selection = state[plannerDataGuid].selection;
                        newConflictedDates = getConflictedDates(selection, bar, conflictedDates);
                    });
                }
            }

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    tooltipMessages: {
                        ...state[plannerDataGuid].tooltipMessages,
                        [actor]: conflictData.messages
                    },
                    conflictedActions: {
                        ...state[plannerDataGuid].conflictedActions,
                        [actor]: conflictData.conflictedActions
                    },
                    conflictedDates: {
                        ...state[plannerDataGuid].conflictedDates,
                        [actor]: newConflictedDates
                    }
                }
            };
        }
        case actions.CLEAR_PLANNER_CHILD_ROW_SELECTION: {
            const plannerDataGuid = action.payload;
            const { selection = {} } = state[plannerDataGuid];

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    selection: selection.actor === PLANNER_ACTORS.PARENT_ROW_SELECTION
                        ? selection
                        : {}
                }
            };
        }
        case actions.CLEAR_PLANNER_SELECTION:
        case actions.FILTERS_ACTIONS.CLEAR_ALL_FILTERS: {
            const plannerDataGuid = action.payload;
            const plannerData = state[plannerDataGuid];
            let newState = state;

            if (Object.keys(plannerData.selection).length !== 0) {
                newState = {
                    ...state,
                    [plannerDataGuid]: {
                        ...state[plannerDataGuid],
                        selection: {}
                    }
                };
            }

            return newState;
        }
        case actions.PLANNER_TOGGLE_EXPAND_COLLAPSE_ALL: {
            const { workspace, barGroups } = action;
            const { masterRecTableName, subRecTableName } = workspace;
            const {plannerDataGuid, plannerUnassignedRolesToggleFeatureEnabled} = action.payload;
            const { expandCollapseAllState: oldToggleState, rowsById } = state[plannerDataGuid];
            const newToggleState = oldToggleState[masterRecTableName] === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.EXPANDED ?
                RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.COLLAPSED :
                RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.EXPANDED;
            const { rowsExpanded: oldRowsExpanded } = state[plannerDataGuid];
            const displayUnassignedCriteriaRolesAsResources = getDisplayUnassginedCriteriaRolesAsResourcesFlag(workspace);

            const rowsExpanded = toggleRowsExpandCollapse(oldRowsExpanded, newToggleState === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.EXPANDED);
            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)),
                workspace,
                masterRecTableName,
                undefined,
                plannerUnassignedRolesToggleFeatureEnabled
            );

            const rowsToToggle = {};
            const idsToToggle = getToggleRowIds(oldRowsExpanded, newToggleState === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.EXPANDED);
            (idsToToggle || []).forEach(id => rowsToToggle[id] = rowsById[id]);

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    selection: {},
                    expandCollapseAllState: {
                        ...oldToggleState,
                        [masterRecTableName]: newToggleState
                    },
                    rowsById: {
                        ...rowsById,
                        ...getPlannerRowStructureById(
                            rowsToToggle,
                            visibleBarsGroups,
                            subRecTableName,
                            rowsExpanded,
                            displayUnassignedCriteriaRolesAsResources,
                            state[plannerDataGuid].recordsMap,
                            plannerUnassignedRolesToggleFeatureEnabled,
                            state[plannerDataGuid].historicFutureRecords,
                        )
                    },
                    rowsExpanded
                }
            };
        }
        case actions.BUILD_BAR_ADDITIONAL_DATA: {
            const { pagedMasterRecPlannerData, subRecPlannerData, barGroups, plannerTableDatas, workspace, showConflict } = action;
            const { plannerUnassignedRolesToggleFeatureEnabled } = action.payload;
            const bookingTypes = plannerTableDatas.bookingtype;
            const { pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid, masterRecTableName, plannerDataGuid } = workspace;
            const bookingsTableName = TABLE_NAMES.BOOKING;
            const masterRecData = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid].data;
            const subRecData = subRecPlannerData[subRecPlannerDataGuid].data;
            const conflicts = showConflict ? getResourceConflictDates(masterRecTableName === TABLE_NAMES.RESOURCE ? masterRecData : subRecData) : [];
            const barPotentialConflicts = getResourceBarPotentialConflictDates(masterRecTableName === TABLE_NAMES.RESOURCE ? masterRecData : subRecData);
            const visibleBarsGroups = getVisibleBarsGroups(
                barGroupsData(barGroups, getBarGroupsGuids(workspace)), 
                workspace, 
                masterRecTableName,
                undefined,
                plannerUnassignedRolesToggleFeatureEnabled
            );

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    barConflicts: buildBarConflicts(visibleBarsGroups, conflicts, bookingsTableName, bookingTypes), // refactor? when implementing role conflicts
                    barPotentialConflicts: buildBarPotentialConflicts(visibleBarsGroups, barPotentialConflicts, bookingsTableName, bookingTypes, plannerUnassignedRolesToggleFeatureEnabled)
                }
            };
        }
        case actions.BUILD_BAR_COLOURS: {
            const { payload, workspace } = action;
            const { barColours = [] } = payload;
            const { plannerDataGuid } = workspace;

            return {
                ...state,
                [plannerDataGuid]: {
                    ...state[plannerDataGuid],
                    barColours: {
                        ...state[plannerDataGuid].barColours,
                        ...barColours
                    }
                }
            };
        }
        case actions.SET_PLANNER_DATA_DIRTY : {
            const { payload : { plannerDataGuid, isDirty } } = action;
            const plannerData = state[plannerDataGuid] || {};

            if (!plannerDataGuid) return state;

            if (plannerData.isDirty !== isDirty) {
                return {
                    ...state,
                    [plannerDataGuid]: {
                        ...plannerData,
                        isDirty
                    }
                };
            }

            return state;
        }
        default: {
            return state;
        }
    }
}