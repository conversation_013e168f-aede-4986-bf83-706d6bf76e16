import { omit } from '../../../utils/commonUtils';
import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import { buildStructure, addItem } from '../../../state/skillStructure/mapCollection';
import { swapArrayElementsByIndeces } from '../../../utils/commonUtils';
import { configureWorkspaceSettings } from '../workspacesReducer';
import { startOfDay } from '../../../utils/dateUtils';
import { PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP } from '../../../constants/plannerConsts';
import { applyViewsConfigSort, areAllSubTogglesHidden, getDensityFromFieldsCount, getMaxDisplayedFieldsCount, getRecordListDetailFields, rolesSubTogglesKeys, shouldHideUnassignedRolesToggle } from '../../../utils/workspaceUtils';
import { TABLE_NAMES } from '../../../constants';

// const getRotatedRecordsListView = (viewsConfig, newViewSetting) => {
//     const resourceTableName = TABLE_NAMES.RESOURCE;

//     const field = `${newViewSetting}_description`;
//     const headerField = {
//         table: newViewSetting,
//         field: field,
//         title: newViewSetting === resourceTableName ? "Resource Name" : "Job Name",
//         showAvatar: newViewSetting === resourceTableName ? true : false
//     };

//     const { recordsListConfig } = viewsConfig;
//     const currentParentDisplayFields = recordsListConfig.parentDisplayFields;
//     const currentChildDisplayFields = recordsListConfig.childDisplayFields;

//     const newChildDisplayDetailFields = newViewSetting === resourceTableName
//         ? currentParentDisplayFields.detailFields
//         : currentParentDisplayFields.detailFields.map((detailField) => {
//             if (detailField.field === `${resourceTableName}_description`) {
//                 return {
//                     ...detailField,
//                     showAvatar: true
//                 }
//             }
//             return detailField;
//         });

//     return {
//         ...recordsListConfig,
//         parentDisplayFields: {
//             ...currentParentDisplayFields,
//             headerField,
//             detailFields: [...currentChildDisplayFields.detailFields]
//         },
//         childDisplayFields: {
//             ...currentChildDisplayFields,
//             detailFields: newChildDisplayDetailFields
//         }
//     };
// };



// const getRecordListSelectionFields = (tableName, newFields, currentFields) => {
//     const mandatoryFields = [
//         {
//             fieldName: `${tableName}_guid`
//         },
//         {
//             fieldName: `${tableName}_description`
//         }
//     ];

//     return unionBy(
//         mandatoryFields,
//         newFields.map((fieldName) => { return { fieldName } }),
//         currentFields,
//         'fieldName'
//     )
// }
const isCommonHideToggle = (key, value) => {
    const isHideTogle = key.indexOf('hide') === 0 && typeof value == 'boolean';

    return isHideTogle && ['hideJobTimeline', 'hideJobMilestones'].indexOf(key) == -1;
};
const isNotCommonHideToggle = (key, value) => !isCommonHideToggle(key, value);

const copyKeys = (source, predicate = () => true) => {
    const copied = {};

    Object.entries(source).forEach(
        ([key, value]) => {
            if (predicate(key, value)) {
                copied[key] = value;
            }
        }
    );

    return copied;
};

export default function workspacesSettingsReducer(state = initialState.plannerPage.workspaces.workspacesSettings, action) {
    switch (action.type) {
        case actionTypes.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { workspacesSettings } = payload;

            return {
                ...state,
                ...buildStructure('workspace_guid', workspacesSettings)
            };
        }
        case actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL:
        case actionTypes.LOAD_WORKSPACE_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;

            const loadedWorkspace = configureWorkspaceSettings(data[0]);
            payload.workspaceGuid = loadedWorkspace.workspace_guid;

            return {
                ...state,
                ...addItem(state, (workspace) => workspace, loadedWorkspace)
            };
        }
        case actionTypes.DELETE_WORKSPACE_SUCCESS:
        case actionTypes.DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { defaultWorkspaceGuid, workspaceGuid: deletedWorkspaceGuid } = payload;
            const orderedKeysWithoutRemoved = state.orderedKeys.filter(workspaceGuid => workspaceGuid !== deletedWorkspaceGuid);

            if (deletedWorkspaceGuid === defaultWorkspaceGuid) {
                return {
                    ...state
                };
            }

            return {
                ...state,
                orderedKeys: orderedKeysWithoutRemoved,
                map: { ...omit(state.map, [deletedWorkspaceGuid]) }
            };
        }
        case actionTypes.PLANNER_CHILD_RESIZED: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        primaryWidthRatio: action.payload.primaryWidthRatio
                    }
                }
            };
        }
        case actionTypes.VIEW_SETTINGS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, newViewSetting } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        subRecTableName: state.map[workspaceGuid].masterRecTableName,
                        masterRecTableName: newViewSetting
                        // viewsConfig: {
                        //     ...state.map[workspaceGuid].viewsConfig,
                        //     recordsListConfig: getRotatedRecordsListView(state.map[workspaceGuid].viewsConfig, newViewSetting)
                        // }
                    }
                }
            };
        }
        case actionTypes.HIDE_HISTORIC_RECORDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideHistoricRecords } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideHistoricRecords
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_FUTURE_RECORDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideFutureRecords } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideFutureRecords
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideInactiveResources } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideInactiveResources
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_UNASSIGNED_ROWS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideUnassignedResourceRows } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideUnassignedResourceRows
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_JOB_TIMELINE_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideJobTimeline } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideJobTimeline
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_JOB_MILESTONES_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideJobMilestones } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideJobMilestones
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_ROLES_RECORDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideRolesRecords } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            let newViewsConfigState = {
                ...viewsConfig[masterRecTableName],
                hideRolesRecords
            };
            rolesSubTogglesKeys.forEach((subToggleKey) => newViewsConfigState[subToggleKey] = hideRolesRecords);
            if (hideRolesRecords) {
                newViewsConfigState.hideUnassignedRolesRecords = hideRolesRecords;
                newViewsConfigState.hideLiveBars = hideRolesRecords;
            }

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: newViewsConfigState
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_FIELDS_LABELS_ON_BARS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, barsTableName, shouldHide } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                ...(barsTableName === TABLE_NAMES.BOOKING && { hideBookingBarsLabels: shouldHide }),
                                ...(barsTableName === TABLE_NAMES.ROLEREQUEST && { hideRolesBarsLabels: shouldHide })

                            }
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_UNASSIGNED_ROLES_CHANGED:
        case actionTypes.HIDE_ROLES_BY_NAME_CHANGED:
        case actionTypes.HIDE_ROLES_BY_REQUIREMENTS_CHANGED:
        case actionTypes.HIDE_DRAFT_ROLES_RECORDS_CHANGED:
        case actionTypes.HIDE_REQUESTED_ROLES_RECORDS_CHANGED:
        case actionTypes.HIDE_LIVE_BARS_CHANGED: {
            const { type, payload } = action;
            const { workspaceGuid, toggleKey, toggleValue } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            let newViewConfigState = {
                ...viewsConfig[masterRecTableName],
                [toggleKey]: toggleValue
            };

            if (type === actionTypes.HIDE_ROLES_BY_NAME_CHANGED || type === actionTypes.HIDE_ROLES_BY_REQUIREMENTS_CHANGED) {
                if (shouldHideUnassignedRolesToggle(newViewConfigState)) {
                    newViewConfigState.hideUnassignedRolesRecords = shouldHideUnassignedRolesToggle(newViewConfigState);
                }
            }

            newViewConfigState.hideRolesRecords = areAllSubTogglesHidden(newViewConfigState);

            if (newViewConfigState.hideRolesRecords) {
                rolesSubTogglesKeys.forEach((key) => newViewConfigState[key] = true);
            }

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: newViewConfigState
                        }
                    }
                }
            };
        }
        case actionTypes.HIDE_WEEKENDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hideWeekends } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideWeekends
                            }
                        }
                    }
                }
            };
        }

        case actionTypes.HIDE_POTENTIAL_CONFLICTS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, hidePotentialConflicts } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hidePotentialConflicts
                            }
                        }
                    }
                }
            };
        }

        case actionTypes.DATE_RANGE_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, startDate, endDate } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        startDate: startOfDay(startDate),
                        endDate: startOfDay(endDate)
                    }
                },
                showSortFloatingActionBar: true
            };
        }
        case actionTypes.DATE_TOGGLE_OPTION_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, dateToggleOption, unit } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewMode: {
                            ...state.map[workspaceGuid].viewMode,
                            mode: unit,
                            dateOption: dateToggleOption
                        }
                    }
                }
            };
        }
        case actionTypes.RL_SORT: {
            const { payload } = action;
            const { workspaceGuid, sort } = payload;
            const { table } = sort;
            const isMasterRecOrderChange = table == state.map[workspaceGuid].masterRecTableName;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: applyViewsConfigSort(state.map[workspaceGuid].viewsConfig, sort, !isMasterRecOrderChange)
                    }
                }
            };
        }
        case actionTypes.COLUMN_ORDER_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, startIndex, endIndex } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const swappedColumns = swapArrayElementsByIndeces(recordsListConfig.childDisplayFields.detailFields, startIndex, endIndex);

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    childDisplayFields: {
                                        detailFields: [...swappedColumns]
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.TABLE_COLUMN_RESIZED: {
            const { payload } = action;
            const { workspaceGuid, newWidth, columnIndex } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const currentColumns = [...recordsListConfig.childDisplayFields.detailFields];
            currentColumns[columnIndex] = { ...currentColumns[columnIndex], width: newWidth };

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    childDisplayFields: {
                                        detailFields: [...currentColumns]
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.PLANNER_ROWS_DENSITY_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, density: selectedDensity } = payload;
            const { styleSettings } = state.map[workspaceGuid];
            const { density } = styleSettings;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        styleSettings: {
                            ...styleSettings,
                            density: {
                                ...density,
                                selected: selectedDensity
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.BAR_FIELDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, fields, tableName } = payload;
            const { viewsConfig, styleSettings, masterRecTableName } = state.map[workspaceGuid];
            const { density } = styleSettings;

            const displayFieldsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[tableName];

            const addedDisplayFields = getMaxDisplayedFieldsCount(viewsConfig[masterRecTableName], tableName);
            const maxAddedFields = Math.max(addedDisplayFields, fields.length);

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                barDisplayFields: {
                                    ...viewsConfig[masterRecTableName].barDisplayFields,
                                    [displayFieldsKey]: [...fields]
                                }
                            }
                        },
                        styleSettings: {
                            ...styleSettings,
                            density: {
                                ...density,
                                selected: getDensityFromFieldsCount(density, maxAddedFields)
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, tableName, fields } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const tableDisplayFields = recordsListConfig['childDisplayFields'];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    ['childDisplayFields']: {
                                        ...tableDisplayFields,
                                        detailFields: getRecordListDetailFields(tableName, fields)
                                    }
                                }
                            }
                            // [tableName]: {
                            //     ...tableViewsConfig,
                            //     selection: {
                            //         ...tableSelectionFields,
                            //         fields: getRecordListSelectionFields(tableName, fields, tableSelectionFields.fields)
                            //     }
                            // }
                        }
                    }
                }
            };
        }
        case actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, tableName, fields } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const tableDisplayFields = recordsListConfig['parentDisplayFields'];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]:{
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    ['parentDisplayFields']: {
                                        ...tableDisplayFields,
                                        detailFields: getRecordListDetailFields(tableName, fields)
                                    }
                                }
                            }
                            // [tableName]: {
                            //     ...tableViewsConfig,
                            //     selection: {
                            //         ...tableSelectionFields,
                            //         fields: getRecordListSelectionFields(tableName, fields, tableSelectionFields.fields)
                            //     }
                            // }
                        }
                    }
                }
            };
        }
        case actionTypes.DELETE_WORKSPACE_FILTER_SETTINGS: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...omit(state.map[workspaceGuid], ['filterSettings', 'filterState'])
                    }
                }
            };
        }
        case actionTypes.CLONE_COMMON_VIEW_HIDE_TOGGLES: {
            const { from, to, workspaceGuid } = action.payload;
            const { viewsConfig } = state.map[workspaceGuid];

            const sourceViewConfig = viewsConfig[from];
            const updatedHideToggles = copyKeys(sourceViewConfig, isCommonHideToggle);

            const targetViewConfig = viewsConfig[to];
            const sanitisedWithoutHideToggles = copyKeys(targetViewConfig, isNotCommonHideToggle);

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [to]:{
                                ...sanitisedWithoutHideToggles,
                                ...updatedHideToggles
                            }
                        }
                    }
                }
            };
        }
        case actionTypes.RESTORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, view, viewConfig } = action.payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...state.map[workspaceGuid].viewsConfig,
                            [view]: viewConfig
                        }
                    }
                }
            };
        }
        case actionTypes.CLOSE_SORT_FLOATING_ACTION_BAR: {
            return {
                ...state,
                showSortFloatingActionBar: false
            };
        }
        default: {
            return state;
        }
    }
}
