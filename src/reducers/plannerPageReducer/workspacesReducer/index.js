import { values } from 'lodash';

import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import workspacesStructureReducer from './workspacesStructureReducer';
import workspacesSettingsReducer from './workspacesSettingsReducer';
import workspacesSettingsChangesTrackerReducer from './workspacesSettingsChangesTrackerReducer';
import createChangesReducer from '../../commonReducers/changesReducer';
import { getDefaultWorkspaceStructure, getWorkspacesStructureMap } from '../../../selectors/workspaceSelectors';
import storedWorkspaceSettingsReducer from './storedWorkspaceSettingsReducer';
import { buildWorkspaceSettings } from '../../../utils/workspaceUtils';
import { DEFAULT_FILTER } from '../../../constants/globalConsts';

export const createWsFn = (change, options) => {
    return {
        change,
        options
    };
};

const wsStructureChangesReducer = createChangesReducer(createWsFn);
const wsSettingsChangesReducer = createChangesReducer(createWsFn);

export const configureWorkspaceSettings = (workspace) => {
    return workspace.workspace_settings;
};

export const configureWorkspaceStructure = (workspace) => {
    return {
        ...workspace,
        workspace_accesstype: workspace.workspace_accesstype.toLowerCase(),
        workspace_editrights: workspace.workspace_editrights.toLowerCase()
    };
};

const mostRecentlyUsedWorkspaceStructureLoaded = (mruWorkspaceGuid, allWorkspaces) => {
    let result = false;

    for (let currWSIndex = 0; currWSIndex < allWorkspaces.length; currWSIndex++) {
        if (mruWorkspaceGuid === allWorkspaces[currWSIndex].workspace_guid) {
            result = true;
            break;
        }
    }

    return result;
};

const getWorkspacesState = (state, workspaces) => {
    let newState = { ...state };
    const loadedWSStructures = workspaces[0] ? workspaces[0] : [];
    const loadFirstMostRecentlyUsedWorkspace = workspaces[1][0] ? buildWorkspaceSettings(workspaces[1][0]) : null;

    if (loadFirstMostRecentlyUsedWorkspace && loadFirstMostRecentlyUsedWorkspace.filtersGuid !== DEFAULT_FILTER
        && !loadFirstMostRecentlyUsedWorkspace.filterSettings
        && (!loadFirstMostRecentlyUsedWorkspace.filterState
        || !loadFirstMostRecentlyUsedWorkspace.filterState.views
        || Object.keys(loadFirstMostRecentlyUsedWorkspace.filterState.views).length <= 0)) {
        loadFirstMostRecentlyUsedWorkspace.filterState = {
            guid: loadFirstMostRecentlyUsedWorkspace.filtersGuid,
            title: '',
            selectedView: 'resource',
            hasHiddenFilters: false,
            views: {}
        };
    }

    const allWorkspaces = loadedWSStructures.map((workspace) => configureWorkspaceStructure(workspace));
    const workspacesSettings = loadFirstMostRecentlyUsedWorkspace ? [loadFirstMostRecentlyUsedWorkspace] : [];
    const mostRecentlyUsed = workspaces[1].map(workspace => workspace.workspace_guid);

    return {
        ...newState,
        selected: loadFirstMostRecentlyUsedWorkspace ? loadFirstMostRecentlyUsedWorkspace.workspace_guid : '',
        workspacesStructure: allWorkspaces,
        workspacesSettings,
        mostRecentlyUsed,
        storedWorkspaceSettings: {},
        cachedMostRecentWorkspaces: workspaces[1]
    };
};

export default function workspacesReducer(state = initialState.workspaces, action) {
    switch (action.type) {
        case actionTypes.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;
            const newState = getWorkspacesState(state, data);
            const { selected, workspacesStructure, workspacesSettings, mostRecentlyUsed, cachedMostRecentWorkspaces } = newState;

            return {
                ...state,
                selected,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, {
                    type: actionTypes.LOAD_WORKSPACES_SUCCESSFUL,
                    payload: {
                        workspacesStructure
                    }
                }),
                workspacesSettings: workspacesSettingsReducer(state.workspacesSettings, {
                    type: actionTypes.LOAD_WORKSPACES_SUCCESSFUL,
                    payload: {
                        workspacesSettings
                    }
                }),
                mostRecentlyUsed,
                cachedMostRecentWorkspaces
            };
        }
        case actionTypes.DIGEST_LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data, loadDefaultWorkspace, addDefaultMostRecentlyUsed } = payload;
            const { loadDefaultWorkspaceResult, addDefaultMostRecentlyUsedResult } = data;

            const loadWorkspaceSuccessfulAction = {
                type: actionTypes.LOAD_WORKSPACE_SUCCESSFUL,
                payload: {
                    data: loadDefaultWorkspaceResult
                }
            };

            return {
                ...state,
                workspacesStructure: loadDefaultWorkspace ? workspacesStructureReducer(state.workspacesStructure, loadWorkspaceSuccessfulAction) : state.workspacesStructure,
                workspacesSettings: loadDefaultWorkspace ? workspacesSettingsReducer(state.workspacesSettings, loadWorkspaceSuccessfulAction) : state.workspacesSettings,
                mostRecentlyUsed: addDefaultMostRecentlyUsed ? addDefaultMostRecentlyUsedResult.updatedMostRecentlyUsed.slice() : state.mostRecentlyUsed,
                selected : addDefaultMostRecentlyUsed ? addDefaultMostRecentlyUsedResult.updatedMostRecentlyUsed[0] : state.selected
            };
        }
        case actionTypes.GET_MOST_RECENTLY_USED_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { getWorkspaceDetail, data } = payload;

            let mostRecentlyUsedWorkspacesGuids = data;
            if (getWorkspaceDetail) {
                mostRecentlyUsedWorkspacesGuids = data.map((mruWorkspace) => {
                    return mruWorkspace.workspace_guid;
                });
            }

            //need to make sure mru structure is lodaded to prevent js errors with no longer visible workspaces
            const workspaceStructures = values(getWorkspacesStructureMap(state));
            const visibleMostRecentlyUsedWorkspaces = mostRecentlyUsedWorkspacesGuids.slice().filter(
                mruWorkspaceGuid => mostRecentlyUsedWorkspaceStructureLoaded(mruWorkspaceGuid, workspaceStructures)
            );

            return {
                ...state,
                mostRecentlyUsed: visibleMostRecentlyUsedWorkspaces
            };
        }
        case actionTypes.SELECT_WORKSPACE: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                selected: workspaceGuid
            };
        }
        case `${actionTypes.FILTERS_SETTINGS_CHANGED}_plannerPage`:
        case actionTypes.CLOSE_SORT_FLOATING_ACTION_BAR:
        case actionTypes.DATE_RANGE_CHANGED:
        case actionTypes.DATE_TOGGLE_OPTION_CHANGED:
        case actionTypes.COLUMN_ORDER_CHANGED:
        case actionTypes.VIEW_SETTINGS_CHANGED:
        case actionTypes.HIDE_HISTORIC_RECORDS_CHANGED:
        case actionTypes.HIDE_FUTURE_RECORDS_CHANGED:
        case actionTypes.HIDE_UNASSIGNED_ROWS_CHANGED:
        case actionTypes.HIDE_ROLES_RECORDS_CHANGED:
        case actionTypes.HIDE_DRAFT_ROLES_RECORDS_CHANGED:
        case actionTypes.HIDE_REQUESTED_ROLES_RECORDS_CHANGED:
        case actionTypes.HIDE_LIVE_BARS_CHANGED:
        case actionTypes.HIDE_WEEKENDS_CHANGED:
        case actionTypes.HIDE_POTENTIAL_CONFLICTS_CHANGED:
        case actionTypes.RL_SORT:
        case actionTypes.PLANNER_CHILD_RESIZED:
        case actionTypes.TABLE_COLUMN_RESIZED:
        case actionTypes.PLANNER_ROWS_DENSITY_CHANGED:
        case actionTypes.BAR_FIELDS_CHANGED:
        case actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED:
        case actionTypes.HIDE_UNASSIGNED_ROLES_CHANGED:
        case actionTypes.HIDE_ROLES_BY_NAME_CHANGED:
        case actionTypes.HIDE_ROLES_BY_REQUIREMENTS_CHANGED:
        case actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED:
        case actionTypes.HIDE_JOB_TIMELINE_CHANGED:
        case actionTypes.HIDE_JOB_MILESTONES_CHANGED:
        case actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED:
        case actionTypes.HIDE_FIELDS_LABELS_ON_BARS_CHANGED:
        case actionTypes.CLONE_COMMON_VIEW_HIDE_TOGGLES: {
            return {
                ...state,
                workspacesSettings: workspacesSettingsReducer(state.workspacesSettings, action),
                workspacesSettingsChangesTracker: workspacesSettingsChangesTrackerReducer(state.workspacesSettingsChangesTracker, action)
            };
        }
        case actionTypes.DELETE_WORKSPACE_FILTER_SETTINGS:
        case actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL:
        case actionTypes.CHANGE_COLOUR_SCHEME:
        case actionTypes.LOAD_WORKSPACE_SUCCESSFUL: {
            return {
                ...state,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, action),
                workspacesSettings: workspacesSettingsReducer(state.workspacesSettings, action),
                workspacesSettingsChangesTracker: workspacesSettingsChangesTrackerReducer(state.workspacesSettingsChangesTracker, action)
            };
        }
        case actionTypes.DELETE_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            const mostRecentlyUsedWithoutDeleted = state.mostRecentlyUsed.filter(item => item !== workspaceGuid);

            const getNextSelectedWorksapceGuid = (state, mostRecentlyUsed) => {
                return mostRecentlyUsed.length > 0 ? mostRecentlyUsed[0] : getDefaultWorkspaceStructure(state).workspace_guid;
            };

            return {
                ...state,
                selected: state.selected == workspaceGuid ? getNextSelectedWorksapceGuid(state, mostRecentlyUsedWithoutDeleted) : state.selected,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, action),
                workspacesSettings: workspacesSettingsReducer(state.workspacesSettings, action),
                mostRecentlyUsed: mostRecentlyUsedWithoutDeleted
            };
        }
        case actionTypes.SET_CREATE_WORKSPACE_CHANGE: {
            const { payload } = action;
            const { newWSUUID, newWorkspaceStructure, newWorkspaceSettings } = payload;

            const createStructureActionValue = {
                id: newWSUUID,
                value: newWorkspaceStructure
            };
            const createSettingsActionValue = {
                id: newWSUUID,
                value: newWorkspaceSettings
            };

            const structureChangeAction = {
                type: 'DO_INSERT',
                payload: {
                    id: newWSUUID,
                    value: createStructureActionValue
                }
            };
            const settingsChangeAction = {
                type: 'DO_INSERT',
                payload: {
                    id: newWSUUID,
                    value: createSettingsActionValue
                }
            };

            return {
                ...state,
                workspacesStructureChanges: wsStructureChangesReducer(state.workspacesStructureChanges, structureChangeAction),
                workspacesSettingsChanges: wsSettingsChangesReducer(state.workspacesSettingsChanges, settingsChangeAction)
            };
        }
        case actionTypes.RENAME_WORKSPACE_SUCCESS:
        case actionTypes.MOVE_WORKSPACE_SUCCESS: {
            return {
                ...state,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, action)
            };
        }
        case actionTypes.DIGEST_WORKSPACE_STRUCTURE_CHANGE: {
            const { payload } = action;
            const { workspaceGuid, changes } = payload;

            const updateActionValue = {
                id: workspaceGuid,
                value: {
                    ...changes
                }
            };

            const changeAction = {
                type: 'DO_UPDATE',
                payload: {
                    id: workspaceGuid,
                    value: updateActionValue
                }
            };

            return {
                ...state,
                workspacesStructureChanges: wsStructureChangesReducer(state.workspacesStructureChanges, changeAction)
            };
        }
        case actionTypes.REMOVE_CREATE_WORKSPACE_CHANGE: {
            const { payload } = action;
            const { workspaceChangeUUID } = payload;

            //clear changes
            const structureChangeAction = {
                type: 'REMOVE_CHANGE',
                id: workspaceChangeUUID
            };
            const settingsChangeAction = {
                type: 'REMOVE_CHANGE',
                id: workspaceChangeUUID
            };

            return {
                ...state,
                workspacesStructureChanges: wsStructureChangesReducer(state.workspacesStructureChanges, structureChangeAction),
                workspacesSettingsChanges: wsSettingsChangesReducer(state.workspacesSettingsChanges, settingsChangeAction)
            };
        }
        case actionTypes.DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS: {
            return {
                ...state,
                workspacesSettings: workspacesSettingsReducer(state.workspacesSettings, action)
            };
        }
        case actionTypes.SAVE_WORKSPACE_SETTINGS_SUCCESSFUL: {
            return {
                ...state,
                workspacesSettingsChangesTracker: workspacesSettingsChangesTrackerReducer(state.workspacesSettingsChangesTracker, action)
            };
        }
        case actionTypes.STORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, targetView } = action.payload;
            const workspacesSettings = state.workspacesSettings.map[workspaceGuid] || {};
            const targetViewConfig = workspacesSettings.viewsConfig[targetView] || {};

            const storeAction = {
                ...action,
                payload: { ...action.payload, targetViewConfig }
            };

            return {
                ...state,
                storedWorkspaceSettings: storedWorkspaceSettingsReducer(state.storedWorkspaceSettings, storeAction)
            };
        }
        case actionTypes.RESTORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, view } = action.payload;
            const storedWorkspaceViewSettings = ((state.storedWorkspaceSettings[workspaceGuid] || {}).viewsConfig || {})[view] || {};

            const workspaceSettingsAction = {
                ...action,
                payload: {
                    ...action.payload,
                    viewConfig: storedWorkspaceViewSettings
                }
            };

            return {
                ...state,
                workspacesSettings: workspacesSettingsReducer(state.workspacesSettings, workspaceSettingsAction),
                storedWorkspaceSettings: storedWorkspaceSettingsReducer(state.storedWorkspaceSettings, action)
            };
        }
        case actionTypes.CLEAR_STORED_WORKSPACE_VIEW_SETTINGS: {
            return {
                ...state,
                storedWorkspaceSettings: storedWorkspaceSettingsReducer(state.storedWorkspaceSettings, action)
            };
        }
        default: {
            return state;
        }
    }
}
