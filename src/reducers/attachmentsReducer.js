import initialState from '../state/initialState';
import { ATTACHMENTS } from '../actions/actionTypes';
import { createAttachments } from '../state/attachments';
import createChangesReducer from './commonReducers/changesReducer';
import { omit } from '../utils/commonUtils';
import createChangesCollection from '../utils/changesCollectionUtils';

const createAttachment = ({ lastModifiedDate, ...attachment }) => ({
    ...attachment,
    ...(lastModifiedDate ? { createdOn: lastModifiedDate } : {}),
    uid: attachment.uid || attachment.id
});

const attachmentsChangesReducer = createChangesReducer(createAttachment);

const getEntityAttachmentStateFromAttachmentData = (currentEntityState = {}, tableName, allAttachmentData, loading = false) => {
    return {
        ...currentEntityState,
        attachments: createAttachments(tableName, allAttachmentData).reduce(
            (accumulator, { id, ...attachmentData }) => ({
                ...accumulator,
                [id]: {
                    id,
                    ...attachmentData
                }
            }),
            {}
        ),
        attachmentChanges: createChangesCollection('uid', [], [], []),
        loading
    };
};


export default (state = initialState.attachments, action) => {
    switch (action.type) {
        // To do - need to remove only reference in test file
        case ATTACHMENTS.LOAD_CONFIG: {
            const {
                tableName,
                config
            } = action.payload;

            return {
                ...state,
                config: {
                    ...state.config,
                    [tableName]: config
                }
            };
        }
        case ATTACHMENTS.LOAD_SUCCESS: {
            const {
                alias,
                entityId,
                tableName,
                data = [],
                loading
            } = action.payload;

            let newAttachmentsStateMap = { ...(state.map || {}) };

            new Set([...Object.keys(newAttachmentsStateMap), alias])
                .forEach(moduleName => {
                    if ((state.map[moduleName] || {})[entityId] || moduleName === alias) {
                        const entityAttachmentState = (state.map[moduleName] || {})[entityId];

                        newAttachmentsStateMap[moduleName] = {
                            ...newAttachmentsStateMap[moduleName],
                            [entityId]: getEntityAttachmentStateFromAttachmentData(entityAttachmentState, tableName, data, loading)
                        };
                    }
                });

            return {
                ...state,
                map: newAttachmentsStateMap
            };
        }
        case ATTACHMENTS.LOAD:
        case ATTACHMENTS.INSERT:
        case ATTACHMENTS.INSERT_ERROR:
        case ATTACHMENTS.DELETE_ERROR: {
            const {
                alias,
                entityId,
                loading
            } = action.payload;

            let newAttachmentsStateMap = { ...(state.map || {}) };
            new Set([...Object.keys(newAttachmentsStateMap), alias]).forEach(moduleName => {
                if ((state.map[moduleName] || {})[entityId] || moduleName === alias) {
                    const entityAttachmentState = (state.map[moduleName] || {})[entityId] || {};

                    newAttachmentsStateMap[moduleName] = {
                        ...newAttachmentsStateMap[moduleName],
                        [entityId]: {
                            ...entityAttachmentState,
                            loading
                        }
                    };
                }
            });

            return {
                ...state,
                map: newAttachmentsStateMap
            };
        }
        case ATTACHMENTS.DELETE_SUCCESS: {
            const {
                alias,
                entityId,
                attachmentId,
                loading
            } = action.payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [alias]: {
                        ...state.map[alias],
                        [entityId]: {
                            ...state.map[alias][entityId],
                            attachments: omit(state.map[alias][entityId].attachments, [attachmentId]),
                            loading
                        }
                    }
                }
            };
        }
        case ATTACHMENTS.UI_ADD: {
            const {
                alias,
                entityId,
                attachment,
                documentType,
                expiryDate
            } = action.payload;

            const { uid } = attachment;

            const changeAction = {
                type: 'DO_INSERT',
                payload: {
                    id: uid,
                    value: {
                        id: uid,
                        uid,
                        ...attachment,
                        documentType,
                        expiryDate
                    }
                }
            };

            const { attachmentChanges } = state.map[alias][entityId];

            return {
                ...state,
                map: {
                    ...state.map,
                    [alias]: {
                        ...state.map[alias],
                        [entityId]: {
                            ...state.map[alias][entityId],
                            attachmentChanges: attachmentsChangesReducer(attachmentChanges, changeAction)
                        }
                    }
                }
            };
        }
        case ATTACHMENTS.UI_REMOVE: {
            const {
                alias,
                entityId,
                attachmentId,
                attachmentUid
            } = action.payload;

            const { attachmentChanges } = state.map[alias][entityId];
            const { inserts } = attachmentChanges;

            let changeAction = {};

            if ((inserts.orderedKeys || []).some(uid => uid === attachmentUid)) {
                changeAction = {
                    type: 'DO_REMOVE_INSERT',
                    payload: {
                        id: attachmentUid,
                        value: {
                            id: attachmentId,
                            uid: attachmentUid
                        }
                    }
                };
            } else {
                changeAction = {
                    type: 'DO_REMOVE',
                    payload: {
                        id: attachmentUid,
                        value: {
                            id: attachmentId,
                            uid: attachmentUid
                        }
                    }
                };
            }

            return {
                ...state,
                map: {
                    ...state.map,
                    [alias]: {
                        ...state.map[alias],
                        [entityId]: {
                            ...state.map[alias][entityId],
                            attachmentChanges: attachmentsChangesReducer(attachmentChanges, changeAction)
                        }
                    }
                }
            };
        }
        case ATTACHMENTS.DISCARD_CHANGES: {
            const {
                alias,
                entityId
            } = action.payload;

            const changeAction = {
                type: 'DO_CLEAR_ALL'
            };

            const { attachmentChanges } = state.map[alias][entityId];

            return {
                ...state,
                map: {
                    ...state.map,
                    [alias]: {
                        ...state.map[alias],
                        [entityId]: {
                            ...state.map[alias][entityId],
                            attachmentChanges: attachmentsChangesReducer(attachmentChanges, changeAction)
                        }
                    }
                }
            };
        }
        case ATTACHMENTS.DISCARD_MULTIPLE_ENTITIES_CHANGES: {
            const {
                alias,
                entityIds = []
            } = action.payload;

            const changeAction = {
                type: 'DO_CLEAR_ALL'
            };

            const entitiesAttachments = entityIds.reduce((accumulator, entityId) => {
                const { attachmentChanges } = state.map[alias][entityId];

                return {
                    ...accumulator,
                    [entityId]: {
                        ...state.map[alias][entityId],
                        attachmentChanges: attachmentsChangesReducer(attachmentChanges, changeAction)
                    }
                };
            }, {});

            return {
                ...state,
                map: {
                    ...state.map,
                    [alias]: {
                        ...state.map[alias],
                        ...entitiesAttachments
                    }
                }
            };
        }
        default: {
            return state;
        }
    }
};