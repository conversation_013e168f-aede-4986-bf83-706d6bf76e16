import { combineReducers } from 'redux';
import navigation from './navigateReducer';
import initialState from '../state/initialState';
import { createSimpleListDataReducer } from './commonReducers';
import plannerPageReducer from './plannerPageReducer';
import roleGroupDetailsPageReducer from './roleGroupDetailsPageReducer';
import { createRoleGroupListReducer } from './roleGroupListPageReducer';
import { reducer as oidcReducer } from 'redux-oidc';
import entityWindowReducer from './entityWindowReducer';
import adminSettingReducer from './adminSettingPageReducer';
import persistDataReducer from './persistDataReducer';
import skillStructureReducer from './skillStructureReducer';
import { createDataGridReducer } from './dataGridReducer';
import requestErrorReducer from './requestErrorReducer';
import clipboardReducer from './clipboardReducer';
import entityLookupWindow from './entityLookupWindow';
import talentProfilePageReducer from './talentProfilePageReducer';
import applicationUserReducer from './applicationUserReducer';
import reportReducer from './reportReducer';
import companyInformationReducer from './companyInformationReducers/companyInformationReducer';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import applicationSettingsReducers from './applicationSettingsReducers';
import hotKeysHelpWindowReducer from './hotKeysHelpWindowReducer';
import applicationErrorReducer from './applicationErrorReducer';
import updateAvatarWindowReducer from './updateAvatarWindowReducer';
import avatarsReducer from './avatarsReducer';
import { enableBatching } from 'redux-batched-actions';
import colourSchemeLegendReducer from './colourSchemeLegendReducer';
import toasterMessageReducer from './toasterMessageReducer/toasterMessageReducer';
import internationalizationReducers from './internationalizationReducers';
import createTranslationReducer from './translationReducer';
import promptModalReducer from './promptModalReducer';
import multiValueInputReducer from './multiValueInputReducer/multiValueInputReducer';
import editResourceSkillsWindowReducer from './editResourceSkillsWindowReducer';
import resourceSkillsReducer from './resourceSkillsReducer';
import auditReducer from './auditReducer/auditReducer';
import attachmentsReducer from './attachmentsReducer';
import roleRequestsReducer from './entityWindowReducer/demandDataReducers/roleRequestReducer';
import jobsPageRoleGroupListReducer from './entityWindowReducer/demandDataReducers/jobsPageRoleGroupListReducer';
import progressRoleDialogReducer from './roleTransitionDialogReducer/progressRoleDialogReducer';
import rejectRoleDialogReducer from './roleTransitionDialogReducer/rejectRoleDialogReducer';
import pagesTooltipsReducer from './tooltipReducers/pagesTooltipsReducer';
import pagesTooltipContextualMenuReducer from './tooltipReducers/pagesTooltipContextualMenusReducer';
import { ROLES_MODAL } from '../constants/rolesConsts';
import { createRequirementsReducer } from './requirementsReducer';
import timesheetsPageReducer from './timesheetsPageReducer';
import notificationsPageReducer from './notificationsPageReducer';
import notificationServiceReducer from './notificationServiceReducer';
import suggestedResourcesReducer from './suggestedResourcesReducer';
import rollForwardDialogReducer from './rollForwardDialogReducers';
import repeatBookingDialogReducer from './repeatBookingReducers';
import jobDuplicateDialogReducer from './jobDuplicateReducer';
import roleGroupDuplicateReducer from './roleGroupDuplicateReducer';
import peopleFinderReducer from './peopleFinderDialogReducers';
import { keepUnmutatedState } from '../utils/commonUtils';
import { ADMIN_SETTINGS_PAGE } from '../pages/pages';
import { createRoleAssigneesReducer } from './roleAssigneesReducer';
import roleInboxPageReducer from './roleInboxPageReducer';
import { ROLE_ASSIGNEES_STATE_ALIAS } from '../constants/globalConsts';
import marketplacePageReducer from './marketplacePageReducer';
import previewEntityReducer from './previewEntityReducer';
import tableViewPageReducer from './tableViewReducer';
import assigneesPotentialConflictsReducer from './assigneesPotentialConflictsReducer';
import saveAsTemplateCreationModalReducer from './saveAsTemplateCreationModalReducer';
import { callerMeetsCriteriaReducer } from './callerMeetsCriteriaReducer';
import educationSectionReducers from './educationSectionReducers';
import experienceSectionReducers from './experienceSectionReducers';
import jobFilterDialogReducer from './jobFilterDialogReducer';
import longRunningTasksReducer from './longRunningTasksReducer';
import operationLogDialogReducer from './operationLogDialogReducer';
import cMeProfilingReducer from './cMeProfilingReducer';
import configurableSelectReducer from './configurableSelectReducer';
import summaryPageReducer from './summaryPageReducer';
import featureManagementReducer from './featureManagementReducer';
import listPageReducer from './listPageReducer';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';

const { PROGRESS_ROLES_DATA_ALIAS, REJECT_ROLES_DATA_ALIAS, PROGRESS_MODAL, REJECT_MODAL } = ROLES_MODAL;

const hotKeysHelpWindow = (state, action) => { return createTranslationReducer(hotKeysHelpWindowReducer, state, action, initialState.hotKeysHelpWindow, 'hotKeysHelpWindow'); };
const roleGroupListPageReducer = createRoleGroupListReducer(ROLE_GROUP_LIST_PAGE, initialState.rolegroupListPage);
const progressRoleDialog = progressRoleDialogReducer(PROGRESS_ROLES_DATA_ALIAS, initialState.rolesModalDialogTransition[PROGRESS_MODAL]);
const rejectRoleDialog = rejectRoleDialogReducer(REJECT_ROLES_DATA_ALIAS, initialState.rolesModalDialogTransition[REJECT_MODAL]);
const roleRequirementsReducer = createRequirementsReducer('roleRequirements', initialState.roleRequirementsState);
const roleAssigneesReducer = createRoleAssigneesReducer(initialState.roleAssigneesState, ROLE_ASSIGNEES_STATE_ALIAS);

const combinedReducer = combineReducers({
    oidcReducer,
    applicationUser: applicationUserReducer,
    navigation,
    // pages,
    // tableStructure: tableStructureReducer,
    pagesTooltips: pagesTooltipsReducer,
    pagesTooltipContextualMenu: pagesTooltipContextualMenuReducer,
    stub: createSimpleListDataReducer('stub', initialState.stub),
    plannerPage: plannerPageReducer,
    entityWindow: entityWindowReducer,
    audit: auditReducer,
    adminSetting: adminSettingReducer,
    persistData: persistDataReducer,
    jobsPage: createDataGridReducer(JOBS_PAGE_ALIAS, initialState.jobsPage), // TODO: to be refactored to use common data grid reducer
    skillStructure: skillStructureReducer,
    resourceSkills: resourceSkillsReducer,
    editResourceSkillsWindow: editResourceSkillsWindowReducer,
    requestError: requestErrorReducer,
    talentProfilePage: talentProfilePageReducer,
    clipboard: clipboardReducer,
    entityLookupWindows: entityLookupWindow,
    reportPage: reportReducer,
    applicationSettings: applicationSettingsReducers,
    hotKeysHelpWindow,
    applicationError: applicationErrorReducer,
    updateAvatarWindow: updateAvatarWindowReducer,
    avatars: avatarsReducer,
    companyInformation: companyInformationReducer,
    colourSchemeLegend: colourSchemeLegendReducer,
    toasterMessage: toasterMessageReducer,
    internationalization: internationalizationReducers,
    promptModal: promptModalReducer,
    multiValueInputReducer: multiValueInputReducer,
    attachments: attachmentsReducer,
    roleRequests: roleRequestsReducer,
    rolegroupListPage: roleGroupListPageReducer,
    rolegroupDetailsPage: roleGroupDetailsPageReducer,
    roleInboxPage: roleInboxPageReducer,
    marketplacePage: marketplacePageReducer,
    tableViewPage: tableViewPageReducer,
    timesheetsPage: timesheetsPageReducer,
    jobRoleGroupList: jobsPageRoleGroupListReducer,
    progressRolesWindow: progressRoleDialog,
    rejectRolesWindow: rejectRoleDialog,
    roleRequirementsState: roleRequirementsReducer,
    roleAssigneesState: roleAssigneesReducer,
    suggestedResources: suggestedResourcesReducer,
    notificationsPage: notificationsPageReducer,
    notificationService: notificationServiceReducer,
    rollForwardDialog: rollForwardDialogReducer,
    repeatBookingDialog: repeatBookingDialogReducer,
    jobDuplicateDialog: jobDuplicateDialogReducer,
    operationsLogDialog: operationLogDialogReducer,
    roleGroupDuplicateDialog: roleGroupDuplicateReducer,
    peopleFinderDialog: peopleFinderReducer,
    jobFilterDialog: jobFilterDialogReducer,
    previewEntityPage: previewEntityReducer,
    assigneesPotentialConflicts: assigneesPotentialConflictsReducer,
    templateCreationModal: saveAsTemplateCreationModalReducer,
    education: educationSectionReducers,
    experience: experienceSectionReducers,
    callerMeetsCriteriaData: callerMeetsCriteriaReducer,
    longRunningTasks: longRunningTasksReducer,
    configurableSelect: configurableSelectReducer,
    cMeProfiling: cMeProfilingReducer,
    summaryPage: summaryPageReducer,
    featureManagement: featureManagementReducer,
    listPage: listPageReducer,
    resourcesPage: createDataGridReducer(JOBS_PAGE_ALIAS, initialState.jobsPage), // TODO: to be refactored to use common data grid reducer
});

const rootReducer = (state, action) => {
    const intermediateState = combinedReducer(state, action);
    let newState = intermediateState;

    //This limits the adminSettingsReducer causing re-renders only when on admin page.
    if (intermediateState.navigation.page != ADMIN_SETTINGS_PAGE.name) {
        newState = keepUnmutatedState(state, intermediateState);
    }

    return newState;
};

export default enableBatching(rootReducer);