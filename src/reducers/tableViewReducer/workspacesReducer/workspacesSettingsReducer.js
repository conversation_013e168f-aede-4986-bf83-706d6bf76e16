import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import { startOfDay } from '../../../utils/dateUtils';
import { DEFAULT_TABLE_VIEW_WORKSPACE_NAME, TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS } from '../../../constants/tableViewPageConsts';
import { applyViewsConfigSort, getRecordListDetailFields } from '../../../utils/workspaceUtils';
import { swapArrayElementsByIndeces } from '../../../utils/commonUtils';
import { TABLE_VIEW_CHILD_RESIZED } from '../../../actions/tableViewActions/actions';

export default function workspacesSettingsReducer(state = initialState[TABLE_VIEW_PAGE_ALIAS].workspaces.workspacesSettings, action) {
    //Remove this when multiple workspaces are implemented
    const workspaceGuid = DEFAULT_TABLE_VIEW_WORKSPACE_NAME;

    switch (action.type) {
        case `${actionTypes.VIEW_SETTINGS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ newViewSetting } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        subRecTableName: state.map[workspaceGuid].masterRecTableName,
                        masterRecTableName: newViewSetting
                        // viewsConfig: {
                        //     ...state.map[workspaceGuid].viewsConfig,
                        //     recordsListConfig: getRotatedRecordsListView(state.map[workspaceGuid].viewsConfig, newViewSetting)
                        // }
                    }
                }
            };
        }
        case `${actionTypes.HIDE_HISTORIC_RECORDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ hideHistoricRecords } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideHistoricRecords
                            }
                        }
                    }
                }
            };
        }
        case `${actionTypes.HIDE_FUTURE_RECORDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ hideFutureRecords } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideFutureRecords
                            }
                        }
                    }
                }
            };
        }
        case `${actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            const { workspaceGuid, hideInactiveResources } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                hideInactiveResources
                            }
                        }
                    }
                }
            };
        }
        case `${actionTypes.DATE_RANGE_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ startDate, endDate } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        startDate: startOfDay(startDate),
                        endDate: startOfDay(endDate)
                    }
                },
                showSortFloatingActionBar: true
            };
        }
        case `${actionTypes.DATE_TOGGLE_OPTION_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ dateToggleOption, unit } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        tableViewViewMode: {
                            ...state.map[workspaceGuid].tableViewViewMode,
                            mode: unit,
                            dateOption: dateToggleOption
                        }
                    }
                }
            };
        }
        case `${actionTypes.RL_SORT}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ sort } = payload;
            const { table } = sort;
            const isMasterRecOrderChange = table == state.map[workspaceGuid].masterRecTableName;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: applyViewsConfigSort(state.map[workspaceGuid].viewsConfig, sort, !isMasterRecOrderChange)
                    }
                }
            };
        }
        case `${actionTypes.COLUMN_ORDER_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            const { workspaceGuid, startIndex, endIndex } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const swappedColumns = swapArrayElementsByIndeces(recordsListConfig.childDisplayFields.detailFields, startIndex, endIndex);

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    childDisplayFields: {
                                        detailFields: [...swappedColumns]
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
        case `${actionTypes.TABLE_COLUMN_RESIZED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ newWidth, columnIndex } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const currentColumns = [...recordsListConfig.childDisplayFields.detailFields];
            currentColumns[columnIndex] = { ...currentColumns[columnIndex], width: newWidth };

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    childDisplayFields: {
                                        detailFields: [...currentColumns]
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
        case `${actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableVie
            const { /*workspaceGuid,*/ tableName, fields } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const tableDisplayFields = recordsListConfig['childDisplayFields'];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    ['childDisplayFields']: {
                                        ...tableDisplayFields,
                                        detailFields: getRecordListDetailFields(tableName, fields)
                                    }
                                }
                            }
                            // [tableName]: {
                            //     ...tableViewsConfig,
                            //     selection: {
                            //         ...tableSelectionFields,
                            //         fields: getRecordListSelectionFields(tableName, fields, tableSelectionFields.fields)
                            //     }
                            // }
                        }
                    }
                }
            };
        }
        case `${actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`: {
            const { payload } = action;
            //Uncomment once multiple workspaces are implemented for tableView
            const { /*workspaceGuid,*/ tableName, fields } = payload;
            const { viewsConfig, masterRecTableName } = state.map[workspaceGuid];
            const { recordsListConfig } = viewsConfig[masterRecTableName];
            const tableDisplayFields = recordsListConfig['parentDisplayFields'];

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...viewsConfig,
                            [masterRecTableName]: {
                                ...viewsConfig[masterRecTableName],
                                recordsListConfig: {
                                    ...recordsListConfig,
                                    ['parentDisplayFields']: {
                                        ...tableDisplayFields,
                                        detailFields: getRecordListDetailFields(tableName, fields)
                                    }
                                }
                            }
                            // [tableName]: {
                            //     ...tableViewsConfig,
                            //     selection: {
                            //         ...tableSelectionFields,
                            //         fields: getRecordListSelectionFields(tableName, fields, tableSelectionFields.fields)
                            //     }
                            // }
                        }
                    }
                }
            };
        }
        case `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${TABLE_VIEW_PAGE_FILTER_ALIAS}`:
        case `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${TABLE_VIEW_PAGE_FILTER_ALIAS}`: {
            return {
                ...state,
                map: {
                    ...state.map
                }
            };
        }
        case TABLE_VIEW_CHILD_RESIZED: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        primaryWidthRatio: action.payload.primaryWidthRatio
                    }
                }
            };
        }
        case actionTypes.CLOSE_SORT_FLOATING_ACTION_BAR: {
            return {
                ...state,
                showSortFloatingActionBar: false
            };
        }
        default: {
            return state;
        }
    }
}
