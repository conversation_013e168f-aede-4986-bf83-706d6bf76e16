import _ from 'lodash';

import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import workspacesSettingsReducer from './workspacesSettingsReducer';
import { createTransform, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { DEFAULT_TABLE_VIEW_WORKSPACE_NAME, TABLE_VIEW_DATE_TOGLE_OPTIONS, TABLE_VIEW_MODE_OPTIONS, TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS } from '../../../constants/tableViewPageConsts';
import store from '../../../store/configureStore';
import { getMergedFilterSettingsWorkspaceObject } from '../../../selectors/tableViewSelectors';
import hardSet from 'redux-persist/lib/stateReconciler/hardSet';
import { TABLE_VIEW_CHILD_RESIZED } from '../../../actions/tableViewActions/actions';
import { getLocalStorageItem } from '../../../localStorage';
import { parseWorkspaceSettings } from '../../../utils/workspaceUtils';
import { TABLE_VIEW_WORKSPACE_LOCAL_STORAGE_KEY } from '../../../constants/localStorageConsts';

const getDefaultState = (key) => {
    let result;

    switch (key) {
        case 'mapField': {
            result = 'workspace_guid';
            break;
        }
        case 'orderedKeys': {
            result = [DEFAULT_TABLE_VIEW_WORKSPACE_NAME];
            break;
        }
        case 'map': {
            result = initialState[TABLE_VIEW_PAGE_ALIAS].workspaces.workspacesSettings[key];
            break;
        }
        default: {
            result = {};
        }
    }

    return result;
};

const transformState = createTransform(
    (inboundState, key) => {
        const defaultState = getDefaultState(key);
        let result = defaultState;

        if (key === 'map') {
            const storedItem = JSON.parse(getLocalStorageItem(`persist:${TABLE_VIEW_WORKSPACE_LOCAL_STORAGE_KEY}`));
            const state = store.getState();

            if (storedItem) {
                result = JSON.parse(storedItem[key]);
            }

            const filterSettings = state[TABLE_VIEW_PAGE_ALIAS].filters.loading
                ? result[DEFAULT_TABLE_VIEW_WORKSPACE_NAME].filterSettings
                : getMergedFilterSettingsWorkspaceObject(state)(defaultState);

            result = {
                ...result,
                [DEFAULT_TABLE_VIEW_WORKSPACE_NAME]: {
                    ...result[DEFAULT_TABLE_VIEW_WORKSPACE_NAME],
                    ...inboundState[DEFAULT_TABLE_VIEW_WORKSPACE_NAME],
                    filterSettings
                }

            };

            result[DEFAULT_TABLE_VIEW_WORKSPACE_NAME] = parseWorkspaceSettings(
                result[DEFAULT_TABLE_VIEW_WORKSPACE_NAME],
                'tableViewViewMode',
                TABLE_VIEW_MODE_OPTIONS,
                TABLE_VIEW_DATE_TOGLE_OPTIONS
            );
        }

        return result;
    },
    (outboundState, key) => {
        return outboundState;
    },
    {
        whitelist: ['map', 'mapField', 'orderedKeys']
    }
);

const persistConfig = {
    key: TABLE_VIEW_WORKSPACE_LOCAL_STORAGE_KEY,
    storage: storage,
    transforms: [transformState],
    stateReconciler: hardSet
};

const persistedWorkspacesSettingsReducer = persistReducer(persistConfig, workspacesSettingsReducer);

export default function workspacesReducer(state, action) {
    switch (action.type) {
        case `${actionTypes.FILTERS_SETTINGS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.DATE_RANGE_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case actionTypes.CLOSE_SORT_FLOATING_ACTION_BAR:
        case `${actionTypes.DATE_TOGGLE_OPTION_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.COLUMN_ORDER_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.VIEW_SETTINGS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.HIDE_HISTORIC_RECORDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.HIDE_FUTURE_RECORDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.HIDE_INACTIVE_RESOURCES_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.RL_SORT}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.TABLE_COLUMN_RESIZED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.PLANNER_ROWS_DENSITY_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.RECORDS_LIST_SUB_REC_FIELDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.RECORDS_LIST_MASTER_REC_FIELDS_CHANGED}_${TABLE_VIEW_PAGE_ALIAS}`:
        case `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${TABLE_VIEW_PAGE_FILTER_ALIAS}`:
        case `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${TABLE_VIEW_PAGE_FILTER_ALIAS}`:
        case `${TABLE_VIEW_CHILD_RESIZED}`: {
            return {
                ...state,
                workspacesSettings: persistedWorkspacesSettingsReducer(state.workspacesSettings, action)
            };
        }
        default: {
            return {
                ...state,
                workspacesSettings: persistedWorkspacesSettingsReducer(state.workspacesSettings, action)
            };
        }
    }
}
