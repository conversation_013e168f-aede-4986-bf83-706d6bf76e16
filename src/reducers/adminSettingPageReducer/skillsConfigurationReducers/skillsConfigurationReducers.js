import { SKIL<PERSON>_CONFIGURATION_ACTIONS as ACTION_TYPES, API_SUFFIX , IMPORT_LIBRARY_SKILLS } from '../../../actions/actionTypes';
import { uniqueId, cloneDeep } from 'lodash';
import { IntlCollatorCompare } from '../../../components/common-components/helpers';
import { LOCALE_EN,adminSettingConsts } from '../../../../src/constants';
import { FIELD_TYPES } from '../../../components/adminSetting/system/fieldProperties/fieldPropeties.Const';
import { LICENSE_KEYS_ADMIN_SETTINGS } from '../../../constants/globalConsts';
/** @typedef {import('../../../types/category.jsdoc').Category} Category */

const { LOOKUP, MULTI_SELECT_LOOKUP, PLANNING_DATA_LOOKUP, HISTORY_LOOKUP, TAGS } = FIELD_TYPES;
const { SKILLS_CONFIGURATION_CONSTANTS } = adminSettingConsts;
const { LEVELS_DROPDOWN_OPTIONS } = SKILLS_CONFIGURATION_CONSTANTS;
const { NO_LEVELS,CUSTOM_LEVELS,USE_LEVELS_FROM } = LEVELS_DROPDOWN_OPTIONS;

const { SUCCESS, FAILURE } = API_SUFFIX;
const { licenseSkillsType } = LICENSE_KEYS_ADMIN_SETTINGS;

let autoIncKey = 0;

const skillsConfigurationReducer = (initialState) => {
    let newState = {};

    return (state = initialState, action) => {
        const { type } = action;
        let { payload = {} } = action;
        if (!payload) { payload = {};}
        switch (type) {
            case ACTION_TYPES.FETCH_SKILLS_CONFGIGURATION_SECTIONS:
            {
                let { loading, isNewListItemAdded, hasSectionLevelError,levelsErrorStatus,levelsGridModified } = state;
                loading = true;
                isNewListItemAdded = false;
                hasSectionLevelError = false;
                levelsErrorStatus = false;
                levelsGridModified = false;
                let loadingSections = true;
                newState = { ...state, loading, isNewListItemAdded, skillsConfigurationSections: [],
                    skillsConfigurationSectionDetails: {}, updatedSkillsConfigurationSectionDetails:{},
                    isUpdateAPICallFinishState:payload.isUpdateAPICallFinishState,
                    hasSectionLevelError,levelsErrorStatus,levelsGridModified,
                    loadingSections,
                    updateFailed:false,
                    isAddedItemDeleted:false };
                break;
            }
            case `${ACTION_TYPES.FETCH_SKILLS_CONFGIGURATION_SECTIONS}${SUCCESS}`:
            {
                /**
                * @type {{
                    *   loading: boolean,
                    *   skillsConfigurationSections: string[],
                    *   skillsConfigurationSectionDetails: Object,
                    *   initialskillsConfigurationSectionDetails: Object,
                    *   activeTabDetails: Object|undefined
                * }}
                **/
                let { loading, skillsConfigurationSections, skillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails,activeTabDetails = undefined } = state;
                skillsConfigurationSections = getSections(payload);
                skillsConfigurationSectionDetails = getSectionDetails(payload,activeTabDetails);
                initialskillsConfigurationSectionDetails = getSectionDetails(payload,activeTabDetails);
                loading = false;
                newState = { ...state,
                    skillsConfigurationSections,
                    skillsConfigurationSectionDetails,
                    initialskillsConfigurationSectionDetails,
                    loading,
                    newlyAddedSectionName:undefined,
                    newlyAddedSectionId:undefined,
                    loadingSections:false,
                    activeTabDetails:undefined };
                break;
            }
            case ACTION_TYPES.FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID:
            {
                let { loading } = state;
                loading = true;
                newState = { ...state, loading };
                break;
            }
            case `${ACTION_TYPES.FETCH_SKILLS_CONFGIGURATION_BY_SECTION_ID}${SUCCESS}`:
            {
                let { loading, skillsConfigurationSectionDetails = {}, initialskillsConfigurationSectionDetails = {} } = state;
                skillsConfigurationSectionDetails = getFullSectionDetails(cloneDeep(skillsConfigurationSectionDetails), payload);
                initialskillsConfigurationSectionDetails = getFullSectionDetails(cloneDeep(initialskillsConfigurationSectionDetails), payload);
                loading = false;
                newState = { ...state,
                    skillsConfigurationSectionDetails,
                    initialskillsConfigurationSectionDetails,
                    loading };
                break;
            }
            case ACTION_TYPES.DELETE_SKILLS_CONFIGURATION:
            {
                let { skillsConfigurationSectionDetails = {}, skillsConfigurationSections = [], updatedSkillsConfigurationSectionDetails = {} } = state;
                const { details, sections, updatedDetails } = deleteSkillsConfiguration(skillsConfigurationSectionDetails, skillsConfigurationSections, updatedSkillsConfigurationSectionDetails, payload);
                const isNewListItemAdded = determinseStatusOfNewlyAddedSkills(updatedDetails);
                const hasSectionLevelError = determineRowError(updatedDetails);
                newState = { ...state,
                    skillsConfigurationSectionDetails:details,
                    skillsConfigurationSections:sections,
                    updatedSkillsConfigurationSectionDetails:updatedDetails,
                    loading: false,
                    isNewListItemAdded,
                    hasSectionLevelError,
                    isAddedItemDeleted: true
                };
                break;
            }

            case `${ACTION_TYPES.DUPLICATE_CONFIGURATION_SECTION_USING_API}${SUCCESS}`: {
                const { skillsConfigurationSections } = state;
                const duplicateName = getDuplicateName(payload.name, skillsConfigurationSections);
                const duplicateData = createDuplicate(duplicateName, payload.name, state, payload);
                newState = { ...state, ...duplicateData };
                break;
            }
            case ACTION_TYPES.DUPLICATE_CONFIGURATION_SECTION: {
                const { skillsConfigurationSections, updatedSkillsConfigurationSectionDetails } = state;
                const updatedData = updatedSkillsConfigurationSectionDetails[payload.menuDetails.name];
                let updatedName = payload.menuDetails.name;
                if (updatedData && updatedData.editTitleInfo && updatedData.editTitleInfo.name) {
                    updatedName = updatedData.editTitleInfo.name;
                }
                const duplicateName = getDuplicateName(updatedName, skillsConfigurationSections);
                const duplicateData = createDuplicate(duplicateName, payload.menuDetails.name, state);
                newState = { ...state, ...duplicateData };
                break;
            }
            case ACTION_TYPES.CREATE_SKILLS_CONFIGURATION_SECTION:
            {
                let { skillsConfigurationSectionDetails = {}, skillsConfigurationSections = [], updatedSkillsConfigurationSectionDetails = {}, initialskillsConfigurationSectionDetails = {} } = state;
                const { details, sections, updatedDetails, initialDetails } = addSkillsConfiguration(skillsConfigurationSectionDetails, skillsConfigurationSections, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails, payload);
                newState = { ...state,
                    skillsConfigurationSectionDetails:details,
                    skillsConfigurationSections:sections,
                    updatedSkillsConfigurationSectionDetails:updatedDetails,
                    initialskillsConfigurationSectionDetails:initialDetails,
                    loading: false,
                    isNewListItemAdded: true,
                    newlyAddedSectionName: payload.sectionName,
                    newlyAddedSectionId: details[payload.sectionName].id
                };
                break;
            }
            case ACTION_TYPES.ADD_NEW_SKILL_ROW:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, loading, initialskillsConfigurationSectionDetails } = state;
                let newSkillData = generateNewSkillData(payload);
                updatedSkillsConfigurationSectionDetails = addNewSkillInTable(skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload, newSkillData);
                initialskillsConfigurationSectionDetails = addNewSkillInitialDetails(initialskillsConfigurationSectionDetails, payload, newSkillData);
                loading = false;
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, loading, initialskillsConfigurationSectionDetails };
                break;
            }
            case ACTION_TYPES.REMOVE_SKILLS_ROW:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = removeSkillFromTable(skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload);
                initialskillsConfigurationSectionDetails = removeNewlyAddedInitialDetail(initialskillsConfigurationSectionDetails, payload);
                const hasSectionLevelError = determineRowError(updatedSkillsConfigurationSectionDetails);
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails, hasSectionLevelError };
                break;
            }
            case ACTION_TYPES.CANCEL_REMOVE_SKILL_ROW:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = cancelRemoveSkillFromTable(updatedSkillsConfigurationSectionDetails, payload);
                const hasSectionLevelError = determineRowError(updatedSkillsConfigurationSectionDetails);
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, hasSectionLevelError };
                break;
            }
            case ACTION_TYPES.SET_SKILLS_TABLE_ROW_SORT_ORDER:
            {
                let { sortTableRowOrder } = state;
                sortTableRowOrder = action.payload;
                newState = { ...state, sortTableRowOrder };
                break;
            }
            case ACTION_TYPES.EDITED_SKILL_INFO:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = buildSkillRecordsOnEdit(skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload);
                const hasSectionLevelError = determineRowError(updatedSkillsConfigurationSectionDetails);
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, hasSectionLevelError };
                break;
            }
            case ACTION_TYPES.EDIT_SKILL_CATEGORY_DETAILS:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = buildUpdatedSkillCategory(payload, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails);
                const hasSectionLevelError = determineRowError(updatedSkillsConfigurationSectionDetails);
                newState = { ...state, updatedSkillsConfigurationSectionDetails, hasSectionLevelError };
                break;
            }
            case ACTION_TYPES.UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS:
            {
                let { loading } = state;
                loading = true;
                newState = { ...state, loading };
                break;
            }
            case `${ACTION_TYPES.UPDATE_BULK_SKILLS_CONFGIGURATION_SECTIONS}${FAILURE}`:
            {
                let { loading, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails } = state;
                loading = false;
                const updateFailed = true;

                let updatedSkillsConfiguration = {};
                Object.keys(updatedSkillsConfigurationSectionDetails).forEach((item) => {
                    const { skillFields: skillTypeFieldsInitial = [] } = initialskillsConfigurationSectionDetails[item] || {};
                    const { skillFields: skillTypeFieldsUpdated = [] } = updatedSkillsConfigurationSectionDetails[item] || {};

                    updatedSkillsConfiguration = {
                        ...updatedSkillsConfigurationSectionDetails,
                        [item]: {
                            ...updatedSkillsConfigurationSectionDetails[item],
                            skillFields : getUpdatedSkillTypeFields(skillTypeFieldsUpdated, skillTypeFieldsInitial)
                        }
                    };
                });
                newState = { ...state, loading, updateFailed, updatedSkillsConfigurationSectionDetails: updatedSkillsConfiguration };
                break;
            }
            case ACTION_TYPES.UPDATE_SKILL_TITLE_NAME:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = buildUpdatedSkillsTitles(payload, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails);
                const hasSectionLevelError = determineRowError(updatedSkillsConfigurationSectionDetails);
                newState = { ...state, updatedSkillsConfigurationSectionDetails, hasSectionLevelError };
                break;
            }
            case ACTION_TYPES.ON_EXPAND_KEY_SET:
            {
                let { skillOnExpandedKeys } = state;
                skillOnExpandedKeys = buildUpdatedSkillOnExpandedKeys(payload, skillOnExpandedKeys);
                newState = { ...state, skillOnExpandedKeys };
                break;
            }
            case ACTION_TYPES.SKILL_FIELDS_EXPANDED_KEYS: {
                let { skillOnFieldsExpandedKeys } = state;
                skillOnFieldsExpandedKeys = buildUpdatedSkillOnExpandedKeys(payload, skillOnFieldsExpandedKeys);
                newState = { ...state, skillOnFieldsExpandedKeys };
                break;
            }
            case ACTION_TYPES.RESET_SKILLS_UPDATE_FINISH_STATE:
            {
                let { isUpdateAPICallFinishState } = state;
                const { value } = payload;
                isUpdateAPICallFinishState = value;
                newState = { ...state, isUpdateAPICallFinishState };
                break;
            }
            case ACTION_TYPES.CREATE_SKILLS_CONFIGURATION_SECTION_USING_HOTKEY:
            {
                let { skillsConfigurationSectionDetails = {}, skillsConfigurationSections = [], updatedSkillsConfigurationSectionDetails = {}, initialskillsConfigurationSectionDetails = {} } = state;
                const { adminSettingCommon :{ adminSettingsLicenseInfo } } = payload;
                const { subscribedCount } = adminSettingsLicenseInfo && adminSettingsLicenseInfo[licenseSkillsType] || {};
                const currentItemCnt = Object.keys(skillsConfigurationSectionDetails).filter((item) => !(skillsConfigurationSectionDetails[item] && skillsConfigurationSectionDetails[item].operation && skillsConfigurationSectionDetails[item].operation.toLowerCase() === 'delete')).length;

                if (currentItemCnt < subscribedCount) {
                    const { details, sections, updatedDetails, initialDetails, newSection } = addSkillsConfigurationSectionUsingHotKey(skillsConfigurationSectionDetails, skillsConfigurationSections, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails);
                    newState = { ...state,
                        skillsConfigurationSectionDetails:details,
                        skillsConfigurationSections:sections,
                        updatedSkillsConfigurationSectionDetails:updatedDetails,
                        initialskillsConfigurationSectionDetails:initialDetails,
                        loading: false,
                        isNewListItemAdded: true,
                        newlyAddedSectionName: newSection,
                        newlyAddedSectionId: details[newSection].id
                    };
                } else {
                    newState = {
                        ...state
                    };
                }

                break;
            }
            case ACTION_TYPES.UPDATE_ACTIVE_COMPONENT_TAB_FOR_SKILL_TYPES:
                {
                    let { initialskillsConfigurationSectionDetails = {} } = state;
                    const initialDetails = updateActiveTab(initialskillsConfigurationSectionDetails,payload);
                    newState = { ...state,
                        initialskillsConfigurationSectionDetails:{ ...initialDetails }
                    };
                }
                break;
            case ACTION_TYPES.SET_ACTIVE_TAB_FOR_SKILL_TYPE:
                {
                    newState = { ...state,
                        activeTabDetails:payload
                    };
                }
                break;
            case ACTION_TYPES.UPDATE_LEVEL_TYPE:
                {
                    let { initialskillsConfigurationSectionDetails = {},updatedSkillsConfigurationSectionDetails = {} } = state;
                    const { updatedDetails } = updateLevelType(cloneDeep(initialskillsConfigurationSectionDetails),
                        cloneDeep(updatedSkillsConfigurationSectionDetails),payload);
                    newState = { ...state,
                        updatedSkillsConfigurationSectionDetails:{ ...updatedDetails },
                        levelsErrorStatus:determineLevelsError(updatedDetails),
                        levelsGridModified:determineLevelsGridStatus(updatedDetails)
                    };
                }
                break;
            case ACTION_TYPES.UPDATE_LEVEL_NAME:
                {
                    let { initialskillsConfigurationSectionDetails = {},updatedSkillsConfigurationSectionDetails = {} } = state;
                    const { updatedDetails } = updateLevelName(cloneDeep(initialskillsConfigurationSectionDetails),
                        cloneDeep(updatedSkillsConfigurationSectionDetails),payload);
                    newState = { ...state,
                        updatedSkillsConfigurationSectionDetails:{ ...updatedDetails },
                        levelsErrorStatus:determineLevelsError(updatedDetails),
                        levelsGridModified:determineLevelsGridStatus(updatedDetails)
                    };
                }
                break;
            case ACTION_TYPES.UPDATE_USE_LEVELS_FROM:
                {
                    let { initialskillsConfigurationSectionDetails = {},updatedSkillsConfigurationSectionDetails = {} } = state;
                    const { updatedDetails } = updateUseLevelsFrom(cloneDeep(initialskillsConfigurationSectionDetails),
                        cloneDeep(updatedSkillsConfigurationSectionDetails),payload);
                    newState = { ...state,
                        updatedSkillsConfigurationSectionDetails:{ ...updatedDetails },
                        levelsErrorStatus:determineLevelsError(updatedDetails),
                        levelsGridModified:determineLevelsGridStatus(updatedDetails)
                    };
                }
                break;
            case ACTION_TYPES.UPDATE_USE_LEVELS_FROM_USING_API:
                {
                    newState = { ...state,
                        useLevelFromPayload:payload,
                        loading:true
                    };
                }
                break;
            case `${ACTION_TYPES.UPDATE_USE_LEVELS_FROM_USING_API}${API_SUFFIX.SUCCESS}`:
                {
                    let { loading, skillsConfigurationSectionDetails = {}, initialskillsConfigurationSectionDetails = {},useLevelFromPayload = null } = state;
                    skillsConfigurationSectionDetails = getFullSectionDetails(skillsConfigurationSectionDetails, payload);
                    initialskillsConfigurationSectionDetails = getFullSectionDetails(initialskillsConfigurationSectionDetails, payload);
                    loading = false;
                    if (useLevelFromPayload) {
                        let { updatedSkillsConfigurationSectionDetails = {} } = state;
                        const { updatedDetails } = updateUseLevelsFrom(cloneDeep(initialskillsConfigurationSectionDetails),
                            cloneDeep(updatedSkillsConfigurationSectionDetails),useLevelFromPayload);
                        newState = { ...state,
                            updatedSkillsConfigurationSectionDetails:{ ...updatedDetails },
                            skillsConfigurationSectionDetails,
                            initialskillsConfigurationSectionDetails,
                            loading,
                            useLevelFromPayload:null,
                            levelsErrorStatus:determineLevelsError(updatedDetails),
                            levelsGridModified:determineLevelsGridStatus(updatedDetails)
                        };
                    } else {
                        newState = { ...state,
                            skillsConfigurationSectionDetails,
                            initialskillsConfigurationSectionDetails,
                            loading };
                    }
                }
                break;
            case ACTION_TYPES.ADD_LEVELS:
                {
                    let { initialskillsConfigurationSectionDetails = {},updatedSkillsConfigurationSectionDetails = {} } = state;
                    const { updatedDetails } = addLevels(cloneDeep(initialskillsConfigurationSectionDetails),
                        cloneDeep(updatedSkillsConfigurationSectionDetails),payload);
                    newState = { ...state,
                        updatedSkillsConfigurationSectionDetails:{ ...updatedDetails },
                        levelsErrorStatus:determineLevelsError(updatedDetails),
                        levelsGridModified:determineLevelsGridStatus(updatedDetails)
                    };
                }
                break;
            case ACTION_TYPES.UPDATE_LEVELS:
                {
                    let { initialskillsConfigurationSectionDetails = {},updatedSkillsConfigurationSectionDetails = {} } = state;
                    const { updatedDetails } = updateLevels(cloneDeep(initialskillsConfigurationSectionDetails),
                        cloneDeep(updatedSkillsConfigurationSectionDetails),payload);
                    newState = { ...state,
                        updatedSkillsConfigurationSectionDetails:{ ...updatedDetails },
                        levelsErrorStatus:determineLevelsError(updatedDetails),
                        levelsGridModified:determineLevelsGridStatus(updatedDetails)
                    };
                }
                break;
            case `${ACTION_TYPES.LOAD_TABLE_DATA_ADMIN_SETTING}`:
                return { ...state, currentEdit: null, loading: true };
            case `${ACTION_TYPES.LOAD_TABLE_DATA_ADMIN_SETTING}${SUCCESS}`:
            {
                let { customFields = [] } = state;
                customFields = getFilteredCustomFields(action.payload);
                newState = {
                    ...state,
                    customFields
                };
                break;
            }
            case ACTION_TYPES.LOAD_SKILL_TABLE_ACCESS_DATA:
            case ACTION_TYPES.FETCH_ACTIVE_CURRENCY_SKILL:
            case ACTION_TYPES.FETCH_ALL_ENTITIES:
            case ACTION_TYPES.FETCH_ALL_ENTITIYTPES:
            case ACTION_TYPES.FETCH_ALL_CATEGORIES:
            case ACTION_TYPES.FETCH_ALL_SUBCATEGORIES:
            {
                newState = {
                    ...state,
                    loading:true
                };
                break;
            }
            case `${ACTION_TYPES.LOAD_SKILL_TABLE_ACCESS_DATA}${SUCCESS}`:
            {
                newState = {
                    ...state,
                    loading:false,
                    formattingFields: action.payload
                };
                break;
            }
            case `${ACTION_TYPES.FETCH_ACTIVE_CURRENCY_SKILL}${SUCCESS}`:
            {
                newState = {
                    ...state,
                    loading:false,
                    activeCurrency: action.payload.serverResponse
                };
                break;
            }
            case ACTION_TYPES.ADD_SKILL_FIELD:
            {
                let { skillsConfigurationSectionDetails, skillFieldsExpandedKeys, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails } = state;

                const activeData = updatedSkillsConfigurationSectionDetails[payload.skillSectionName] || skillsConfigurationSectionDetails[payload.skillSectionName] || {};
                const activeFields = activeData.skillFields || [];
                let newFieldData = createAddFieldPayload(activeFields.length, payload.type, activeData.id, activeData.name, payload, activeFields);

                let { skillOnFieldsExpandedKeys } = state;
                const keys = skillOnFieldsExpandedKeys[payload.skillSectionName] || [];
                const updatedSkillOnFieldsExpandedKeys = [...keys, newFieldData.key];
                skillOnFieldsExpandedKeys = { ...skillOnFieldsExpandedKeys, [payload.skillSectionName]: updatedSkillOnFieldsExpandedKeys };
                updatedSkillsConfigurationSectionDetails = addSkillFieldFromTable(skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload, newFieldData, skillFieldsExpandedKeys);
                initialskillsConfigurationSectionDetails = addNewSkillFieldInitialDetails(initialskillsConfigurationSectionDetails, payload, newFieldData);

                const loading = false;
                newState = {
                    ...state,
                    skillsConfigurationSectionDetails,
                    updatedSkillsConfigurationSectionDetails,
                    loading,
                    initialskillsConfigurationSectionDetails,
                    skillOnFieldsExpandedKeys
                };
                break;
            }
            case ACTION_TYPES.REMOVE_SKILL_FIELD:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = removeSkillFieldFromTable(skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload);
                initialskillsConfigurationSectionDetails = removeNewlyAddedFieldInitialDetail(initialskillsConfigurationSectionDetails, payload);
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, initialskillsConfigurationSectionDetails };
                break;
            }
            case ACTION_TYPES.CANCEL_REMOVE_SKILL_FIELD:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = cancelRemoveSkillFieldFromTable(updatedSkillsConfigurationSectionDetails, payload);
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails };
                break;
            }

            case ACTION_TYPES.UPDATE_SKILL_FIELD:
            {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                updatedSkillsConfigurationSectionDetails = buildSkillFieldsOnEdit(skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload);
                const hasSectionLevelError = determineRowError(updatedSkillsConfigurationSectionDetails);
                newState = { ...state, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails,
                    hasSectionLevelError
                };
                break;
            }

            case `${ACTION_TYPES.FETCH_ALL_ENTITIES}${SUCCESS}`:
            {
                const getCurrentEntityDetails = getSkillEntityInfo(payload);
                newState = { ...state,currentEntity:getCurrentEntityDetails };
                break;
            }

            case ACTION_TYPES.SKILL_FIELDS_REORDER: {
                let { skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails } = state;
                const { activeSelectionItem } = payload;

                let updatedSectionItem = updatedSkillsConfigurationSectionDetails[activeSelectionItem];

                updatedSectionItem = updatedSectionItem ? updatedSectionItem : { ...skillsConfigurationSectionDetails[activeSelectionItem] };
                const updatedSkillsFieldsReorder = reOrderSkillFields(payload, updatedSectionItem);
                newState = {
                    ...state,
                    updatedSkillsConfigurationSectionDetails: {
                        ...updatedSkillsConfigurationSectionDetails,
                        [activeSelectionItem]: updatedSkillsFieldsReorder
                    }
                };
                break;
            }
            case ACTION_TYPES.SKILL_TYPE_FIELD_ALREADY_EXIST: {
                newState = Object.assign({}, {
                    ...state,
                    loading:false,
                    hasWarning:true
                });
            }
                break;
            case ACTION_TYPES.SKILL_TYPE_CLEAR_FIELD_ALREADY_EXIST_WARNING:
                {
                    newState = Object.assign({}, {
                        ...state,
                        hasWarning:false
                    });
                }
                break;
            case `${ACTION_TYPES.FETCH_ALL_ENTITIYTPES}${SUCCESS}`:
            {
                newState = { ...state,skillEntityTypes:payload };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_ENTITIYTPES}${FAILURE}`:
            {
                newState = { ...state,skillEntityTypes:[] };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_DEPARTMENTS}${SUCCESS}`:
            {
                newState = { ...state,departments:payload };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_DEPARTMENTS}${FAILURE}`:
            {
                newState = { ...state,departments:[] };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_DIVISIONS}${SUCCESS}`:
            {
                newState = { ...state,divisions:payload };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_DIVISIONS}${FAILURE}`:
            {
                newState = { ...state,divisions:[] };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_CATEGORIES}${SUCCESS}`:
            {
                newState = { ...state,categories:payload };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_CATEGORIES}${FAILURE}`:
            {
                newState = { ...state,categories:[] };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_SUBCATEGORIES}${SUCCESS}`:
            {
                newState = { ...state,subCategories:payload };
                break;
            }
            case `${ACTION_TYPES.FETCH_ALL_SUBCATEGORIES}${FAILURE}`:
            {
                newState = { ...state,subCategories:[] };
                break;
            }
            case `${ACTION_TYPES.SET_TYPE_FILTERS}`:
            {
                let currentFilters = state.skillTypeFilters ?? [];
                const item = currentFilters.find(x => x.id === payload.id);
                if (item) {
                    // If filters already present for the item than update
                    let filters = item.value;
                    let filter = filters.indexOf(payload.value);
                    if (filter !== -1) {
                        // If element is found, remove it
                        filters.splice(filter, 1);
                    } else {
                        // If element is not found, add it
                        filters.push(payload.value);
                    }
                } else {
                    // Add new entry for that item
                    currentFilters = [...currentFilters,{ id:payload.id, value:payload.value }];
                }
                newState = { ...state,skillTypeFilters:currentFilters };
                break;
            }
            case `${ACTION_TYPES.CLEAR_TYPE_FILTERS}`:
            {
                newState = { ...state,skillTypeFilters:[] };
                break;
            }
            //#region Import Library skills Reducers
            case IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS : {
                const { librarySections } = state;

                newState = { ...state, librarySections:{ ...librarySections, loading: true } };
                break;
            }
            case `${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS}${SUCCESS}` : {
                const { librarySections } = state;
                // Transform the response by adding default empty skill array to all the subCategories
                const initialSkillSections = payload.map(cat => ({
                    ...cat,
                    subCategories: cat.subCategories.map(sub => ({
                        ...sub,
                        skills: []
                    }))
                }));

                newState = { ...state, librarySections: { ...librarySections, initialSkillSections, loading: false } };
                break;
            }
            case `${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SECTIONS}${FAILURE}` : {
                const { librarySections } = state;

                newState = { ...state, librarySections:{ ...librarySections, loading: false } };
                break;
            }
            case IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS : {
                const { librarySections } = state;

                newState = { ...state, librarySections:{ ...librarySections, childLoading:true } };
                break;
            }
            case `${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS}${SUCCESS}` : {
                const { categoryId, subCategoryId, data, checkSkills } = payload;
                const { librarySections } = state;
                const { selectedSkillRowKeys, initialSkillSections } = librarySections;
                // Map the skills to category and subcategory
                const category = initialSkillSections.find(obj => obj.id === categoryId);
                if (category) {
                    const subcategory = category.subCategories.find(n => n.id === subCategoryId);
                    if (subcategory) {
                        if (!subcategory.skills || !subcategory.skills.length) {
                            //If skills object is not present or no data then insert everything
                            subcategory.skills = data;
                        } else {
                            // only insert non existing values
                            const existingIds = subcategory.skills.map(x => x.id);
                            const newSkills = data.filter(y => !existingIds.includes(y.id));
                            subcategory.skills = [...subcategory.skills, ...newSkills];
                        }
                        //NOTE: The fetched prop will restrict calling the api unnecessarily when all the skills are fetched.
                        subcategory.fetched = true;
                    }
                }
                // Optional way to mark all the fetched skills to be selected
                const newSelectedSkillRowKeys = checkSkills ?
                    [...new Set([...selectedSkillRowKeys , ...data.map(x => x.id)])] : selectedSkillRowKeys;

                newState = { ...state,
                    librarySections:{
                        ...librarySections,
                        initialSkillSections,
                        selectedSkillRowKeys:newSelectedSkillRowKeys,
                        childLoading:false
                    }
                };
                break;
            }
            case IMPORT_LIBRARY_SKILLS.SELECT_SKILL : {
                const { categoryId, subcategoryId, id } = payload;
                const { librarySections } = state;
                const { selectedSkillRowKeys, initialSkillSections } = librarySections;
                // Map the skills to category and subcategory
                const category = initialSkillSections.find(obj => obj.id === categoryId);
                if (category) {
                    const subCategories = category.subCategories.find(n => n.id === subcategoryId);
                    if (subCategories) {
                        if (!subCategories.skills || !subCategories.skills.length) {
                            //If skills object is not present or no data then insert everything
                            subCategories.skills = [payload];
                        } else {
                            // only insert non existing values
                            const existingSkill = subCategories.skills.find(x => x.id === id);
                            if (!existingSkill) {
                                subCategories.skills = [...subCategories.skills, payload];
                            }
                        }
                    }
                }
                // mark fetched skill to be selected
                const newSelectedSkillRowKeys = [...new Set([...selectedSkillRowKeys , id])];
                newState = { ...state,
                    librarySections:{
                        ...librarySections,
                        initialSkillSections,
                        selectedSkillRowKeys:newSelectedSkillRowKeys
                    }
                };
                break;
            }
            case `${IMPORT_LIBRARY_SKILLS.FETCH_LIBRARY_SKILLS}${FAILURE}` : {
                const { librarySections } = state;

                newState = { ...state, librarySections:{ ...librarySections, childLoading: false } };
                break;
            }
            case IMPORT_LIBRARY_SKILLS.UPDATE_SELECTED_SKILLS : {
                const { selectedSkills } = payload;
                const { librarySections } = state;

                newState = { ...state, librarySections:{ ...librarySections, selectedSkillRowKeys:selectedSkills } };
                break;
            }
            case `${IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_STATUS}${SUCCESS}` :
            case `${IMPORT_LIBRARY_SKILLS.IMPORT_LIBRARY_STATUS}${FAILURE}` : {
                const { librarySections } = state;

                newState = { ...state, librarySections:{ ...librarySections, importInProgress: action.payload } };
                break;
            }
            case IMPORT_LIBRARY_SKILLS.SEARCH_SKILL: {
                const { librarySections } = state;
                newState = { ...state, librarySections:{ ...librarySections, skillSearchResult: { loading: true, skills:[] } } };
                break;
            }
            case `${IMPORT_LIBRARY_SKILLS.SEARCH_SKILL}${SUCCESS}` :
            case `${IMPORT_LIBRARY_SKILLS.SEARCH_SKILL}${FAILURE}` : {
                const { librarySections } = state;
                newState = { ...state, librarySections:{ ...librarySections, skillSearchResult: { loading: false, skills: action.payload } } };
                break;
            }
            case IMPORT_LIBRARY_SKILLS.CLEAR_LIBRARY_DATA: {
                newState = {
                    ...state, librarySections: {
                        initialSkillSections: [],
                        selectedSkillRowKeys: [],
                        skillSearchResult: {}
                    }
                };
                break;
            }
            //#endregion
            default:
                newState = state;
        }

        return newState;
    };
};

const getUpdatedSkillTypeFields = (skillTypeFieldsUpdated, skillTypeFieldsInitial) => {
    const skillFields = [];
    skillTypeFieldsUpdated.forEach(item => {
        const { key } = item;
        let skillCategoryGuid = '';
        skillTypeFieldsInitial.forEach(skillTypeItem => {
            if (skillTypeItem.key === key) {
                skillCategoryGuid = skillTypeItem.guid;
            }
        });
        skillFields.push({ ...item, guid: skillCategoryGuid });
    });

    return skillFields;
};

const getDuplicateName = (name, listItems = [], cnt = 1) => {
    let defaultVal = `${name}(${cnt})`;
    while ((listItems).some((entry) => { return entry.toLowerCase() === defaultVal.toLowerCase(); })) {
        cnt++;
        defaultVal = `${name}(${cnt})`;
    }

    return defaultVal;
};


const removeIDOnDuplicate = (fromItem, duplicateName, currentEntity, fromItemName, newId) => {
    const newData = cloneDeep(fromItem);
    newData.id = newId;
    delete newData.editTitleInfo;
    const { skills = [], skillFields = [], skillLevels = [] } = newData;

    newData.skills = skills.filter((item) => !(item.operation && item.operation.toLowerCase() === 'delete')).map((item) => {
        return { ...item, key: uniqueId('newSkill_'), sectionId: null, operation: 'ADD' };
    });
    newData.skillFields = skillFields.filter((item) => !(item.operation && item.operation.toLowerCase() === 'delete')).map((item, index) => {
        return { ...item, guid: index + 1, skillCategoryGuid: index + 1, operation: 'ADD',
            customFieldGuid: null,
            customFieldDetails: {
                ...item.customFieldDetails,
                operation: 'UPDATE'
            }
        };
    });
    newData.skillLevels = skillLevels.filter((item) => !(item.operation && item.operation.toLowerCase() === 'delete')).map((item) => {
        return { ...item, guid: null, operation: 'ADD', skillCategoryGuid: null, populatedUsingUseLevelsFrom: true };
    });

    let levelsData = { initialSelectedLevel: newData.selectedLevel };
    if (!(newData.operation && newData.operation.toLowerCase() === 'add')) {
        levelsData = {
            initialSelectedLevel: USE_LEVELS_FROM,
            selectedLevel: USE_LEVELS_FROM,
            useLevelFrom: fromItemName
        };
    }

    return {
        ...newData,
        name: duplicateName,
        initialName: duplicateName,
        operation: 'ADD',
        ...levelsData,
        initialSkillCategoryLevelName: newData.skillCategoryLevelName,
        skillCount: 0,
        skillLevelCount: 0,
        skillFieldsCount: 0,
        fakeId: uniqueId() + `-${duplicateName}`
    };
};

const createDuplicate = (duplicateName, fromItemName, state, duplicatePayload) => {
    let { skillsConfigurationSectionDetails = {}, updatedSkillsConfigurationSectionDetails = {},
        initialskillsConfigurationSectionDetails = {}, skillsConfigurationSections = [], currentEntity = {} } = state;
    const newId = uniqueId('skillType_');

    if (duplicatePayload) {
        skillsConfigurationSectionDetails = getFullSectionDetails(cloneDeep(skillsConfigurationSectionDetails), duplicatePayload);
        initialskillsConfigurationSectionDetails = getFullSectionDetails(cloneDeep(initialskillsConfigurationSectionDetails), duplicatePayload);
    }

    const skillConfigDetails = removeIDOnDuplicate(skillsConfigurationSectionDetails[fromItemName], duplicateName, currentEntity, fromItemName, newId);
    const initConfigDetails = removeIDOnDuplicate(initialskillsConfigurationSectionDetails[fromItemName], duplicateName, currentEntity, fromItemName, newId);

    let duplicateData = {
        skillsConfigurationSectionDetails: { ...skillsConfigurationSectionDetails, [duplicateName]: skillConfigDetails },
        initialskillsConfigurationSectionDetails: { ...initialskillsConfigurationSectionDetails, [duplicateName]: initConfigDetails }
    };
    if (updatedSkillsConfigurationSectionDetails[fromItemName]) {
        const updatedConfigDetails = removeIDOnDuplicate(updatedSkillsConfigurationSectionDetails[fromItemName], duplicateName, currentEntity, fromItemName, newId);
        duplicateData.updatedSkillsConfigurationSectionDetails = { ...updatedSkillsConfigurationSectionDetails, [duplicateName]: updatedConfigDetails };
    } else {
        duplicateData.updatedSkillsConfigurationSectionDetails = { ...updatedSkillsConfigurationSectionDetails, [duplicateName]: cloneDeep(skillConfigDetails) };
    }

    duplicateData.isNewListItemAdded = true;
    duplicateData.newlyAddedSectionName = duplicateName;
    duplicateData.newlyAddedSectionId = newId;
    duplicateData.skillsConfigurationSections = [...skillsConfigurationSections, duplicateName];

    return duplicateData;
};

const reOrderSkillFields = (payload, data) => {
    const { fromIndex, toIndex } = payload;
    let dataSource = cloneDeep(data.skillFields);
    const item = dataSource.splice(fromIndex, 1)[0];
    dataSource.splice(toIndex, 0, item);
    dataSource = dataSource.map((item, index) => {
        return { ...item, order: index + 1 };
    });

    if (data.operation == null || data.operation == 'UPDATE') {
        data = { ...data, operation: 'UPDATE' };
    }

    return {
        ...data,
        skillFields: dataSource
    };
};


const determineLevelsError = updatedDetails => {
    return Object.keys(updatedDetails).some((val) => updatedDetails[val].skillTypeLevelNameErrorMessage || updatedDetails[val].levelsGridErrorStatus);
};

const determineLevelsGridStatus = updatedDetails => {
    return Object.keys(updatedDetails).some((val) => updatedDetails[val].isLevelsGridModified || updatedDetails[val].orderChangeFlag);
};

const getSkillEntityInfo = (entityDetails) =>{

    let skillEntityDetails = { entityId:entityDetails.guid,entityName:entityDetails.name };

    return skillEntityDetails;
};

const buildSkillFieldsOnEdit = (skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload) => {
    const { data = {}, activeItem = {} } = payload;
    const { fieldName,formItemValue,guid,hasError } = data;
    const { name } = activeItem;
    const currentSkillFieldDetails = cloneDeep(skillsConfigurationSectionDetails[name]);

    updatedSkillsConfigurationSectionDetails = cloneDeep(updatedSkillsConfigurationSectionDetails);

    updatedSkillsConfigurationSectionDetails[name] = updatedSkillsConfigurationSectionDetails[name] ?
        updatedSkillsConfigurationSectionDetails[name] : currentSkillFieldDetails;

    let skillDetails = cloneDeep(updatedSkillsConfigurationSectionDetails[name]);
    let allSkills = skillDetails.skillFields;
    allSkills.forEach((skillItem)=>{
        if (skillItem.guid === guid) {
            const index = allSkills.findIndex(item => guid === item.guid);
            let item = allSkills[index];
            const fieldIndex = fieldName.replace(`${guid}_`, '');
            item.customFieldDetails[fieldIndex] = formItemValue;
            item.customFieldDetails.modified = true;
            if (skillItem.operation === 'UPDATE' || skillItem.operation === null) {
                skillItem.operation = 'UPDATE';
                skillItem.customFieldDetails.operation = 'UPDATE';
            }
        }
    });

    updatedSkillsConfigurationSectionDetails[name] = { ...updatedSkillsConfigurationSectionDetails[name], skillFields: allSkills };
    if (updatedSkillsConfigurationSectionDetails[name].operation == null || updatedSkillsConfigurationSectionDetails[name].operation == 'UPDATE') {
        updatedSkillsConfigurationSectionDetails[name] = { ...updatedSkillsConfigurationSectionDetails[name], operation: 'UPDATE' };
    }

    return { ...updatedSkillsConfigurationSectionDetails };
};



const addSkillFieldFromTable = (skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload, newFieldData, skillFieldsExpandedKeys) =>{
    const { skillSectionName } = payload;
    let activeDataItem = updatedSkillsConfigurationSectionDetails[skillSectionName];
    activeDataItem = activeDataItem ? activeDataItem : { ...skillsConfigurationSectionDetails[skillSectionName] };

    let allSkillFields = [newFieldData].concat(activeDataItem.skillFields || []);
    allSkillFields = allSkillFields.map((item, index) => {
        return { ...item, order: index + 1 };
    });
    updatedSkillsConfigurationSectionDetails = {
        ...updatedSkillsConfigurationSectionDetails,
        [skillSectionName]: {
            ...activeDataItem,
            skillFields: allSkillFields
        }
    };

    return updatedSkillsConfigurationSectionDetails;
};

function addNewSkillFieldInitialDetails(initialskillsConfigurationSectionDetails, payload, newFieldData) {
    const { skillSectionName } = payload;
    let initialSkillDetails = cloneDeep(initialskillsConfigurationSectionDetails[skillSectionName]);
    let allInitialSkillsFields = initialSkillDetails.skillFields;
    let updatedInitialSkillsFields = [newFieldData, ...allInitialSkillsFields];
    // newSkillData.sectionId = initialskillsConfigurationSectionDetails[skillSectionName].id;
    initialskillsConfigurationSectionDetails[skillSectionName] = { ...initialskillsConfigurationSectionDetails[skillSectionName], skillFields: updatedInitialSkillsFields };

    return { ...initialskillsConfigurationSectionDetails };
}

const removeSkillFieldFromTable = (skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload) =>{
    const { record = {}, activeItem = {} } = payload;
    const { name } = activeItem;
    updatedSkillsConfigurationSectionDetails[name] = updatedSkillsConfigurationSectionDetails[name] ?
        updatedSkillsConfigurationSectionDetails[name] : skillsConfigurationSectionDetails[name];

    let allSkillFields = updatedSkillsConfigurationSectionDetails[name].skillFields;
    if (record.operation === 'ADD') {
        allSkillFields = allSkillFields.filter(item => item.key !== record.key);
    } else {
        allSkillFields.map(skill=>{
            if (skill.key === record.key) {
                //skill.key = skillKey;
                skill.operation = 'DELETE';
                skill.customFieldDetails.operation = 'DELETE';
            }
        });
        updatedSkillsConfigurationSectionDetails[name] = { ...updatedSkillsConfigurationSectionDetails[name], operation: 'UPDATE' };
    }
    updatedSkillsConfigurationSectionDetails[name] = { ...updatedSkillsConfigurationSectionDetails[name], skillFields: allSkillFields };

    return { ...updatedSkillsConfigurationSectionDetails };
};

function removeNewlyAddedFieldInitialDetail(initialskillsConfigurationSectionDetails, payload) {
    const { record = {}, activeItem = {} } = payload;
    const { name } = activeItem;
    let initialSkillDetails = cloneDeep(initialskillsConfigurationSectionDetails[name]);
    let allSkillFields = initialSkillDetails.skillFields;
    if (record.customFieldDetails.operation === 'ADD') {
        allSkillFields = initialSkillDetails.skillFields.filter(item => item.key !== record.key);
    }
    initialskillsConfigurationSectionDetails[name] = { ...initialskillsConfigurationSectionDetails[name], skillFields: allSkillFields };

    return { ...initialskillsConfigurationSectionDetails };
}

const cancelRemoveSkillFieldFromTable = (updatedSkillsConfigurationSectionDetails, payload) =>{
    const { key, activeItem = {} } = payload;
    const { name } = activeItem;
    let allSkills = updatedSkillsConfigurationSectionDetails[name].skillFields;

    allSkills.map(skill=>{
        if (skill.key === key) {
            //skill.key = key;
            skill.operation = null;
            skill.customFieldDetails.operation = null;
        }
    });
    const updatedSkills = allSkills.filter(item => item.operation != null);
    if (updatedSkills.length == 0) {
        updatedSkillsConfigurationSectionDetails[name] = { ...updatedSkillsConfigurationSectionDetails[name], operation: null };
    }
    updatedSkillsConfigurationSectionDetails[name] = { ...updatedSkillsConfigurationSectionDetails[name], skillFields: allSkills };

    return { ...updatedSkillsConfigurationSectionDetails };
};

const getFilteredCustomFields = (payload) => {
    let customFields = payload;
    customFields = customFields.filter((item) => item.fieldtype_isdisplayonui);
    customFields.sort((a, b) => {
        if (a.fieldtype_displayorder > b.fieldtype_displayorder) {
            return 1;
        } if (a.fieldtype_displayorder < b.fieldtype_displayorder) {
            return -1;
        }

        return 0;
    });
    customFields = customFields.map((item) => {
        let displayName = item.fieldtype_name;
        const smallCaseName = displayName.toLowerCase();
        let hideInDropdown = false;
        if (smallCaseName === LOOKUP || smallCaseName === HISTORY_LOOKUP ||
            smallCaseName === MULTI_SELECT_LOOKUP || smallCaseName === PLANNING_DATA_LOOKUP) {
            //current scope only static fields;
            hideInDropdown = true;
            displayName = `${item.fieldtype_name}...`;
        }

        return { 'fieldName': item.fieldtype_name, 'fieldGuid': item.fieldtype_guid, 'displayName': displayName,'hideInDropdown':hideInDropdown, ...item };
    }).filter((fieldType) => fieldType.fieldName.toLowerCase() !== TAGS);

    return customFields;
};

function updateLevels(initialDetails,updatedDetails,payload) {
    let updatedItem = updatedDetails[payload.activeSelectionItem];
    if (!updatedItem) updatedDetails[payload.activeSelectionItem] = cloneDeep(initialDetails[payload.activeSelectionItem]);
    updatedItem = updatedDetails[payload.activeSelectionItem];
    updatedItem.selectedLevel = CUSTOM_LEVELS;
    updatedItem.useLevelFrom = '';
    // if(!updatedItem.skillCategoryLevelName.trim()) updatedItem.skillCategoryLevelName = 'Levels';
    updatedItem.skillLevels = [...updatedItem.skillLevels.filter(level => level.hideFromGrid),...payload.levels];
    if (payload.orderChangeFlag)
        updatedItem.orderChangeFlag = payload.orderChangeFlag;
    updatedItem.isLevelsGridModified = payload.isLevelsGridModified;
    updatedItem.levelsGridErrorStatus = payload.levelsGridErrorStatus;

    // to merge the levels error status with skills error
    updatedItem.hasError = determineSkillWithLevelsError(updatedItem);

    updatedItem.operation = updatedItem.operation || 'UPDATE';
    //get active count levels
    updatedItem.activeSkillLevelsCount = payload.activeSkillLevelsCount;

    return { updatedDetails };
}

function addLevels(initialDetails,updatedDetails,payload) {
    let updatedItem = updatedDetails[payload.activeSelectionItem];
    if (!updatedItem) updatedDetails[payload.activeSelectionItem] = cloneDeep(initialDetails[payload.activeSelectionItem]);
    updatedItem = updatedDetails[payload.activeSelectionItem];
    updatedItem.selectedLevel = CUSTOM_LEVELS;
    updatedItem.useLevelFrom = '';
    // if(!updatedItem.skillCategoryLevelName.trim()) updatedItem.skillCategoryLevelName = 'Levels';
    //also update the order column if required
    return { updatedDetails };
}

function updateUseLevelsFrom(initialDetails,updatedDetails,payload) {
    let updatedItem = updatedDetails[payload.activeSelectionItem];
    if (!updatedItem) updatedDetails[payload.activeSelectionItem] = cloneDeep(initialDetails[payload.activeSelectionItem]);
    updatedItem = updatedDetails[payload.activeSelectionItem];
    let initialItem = initialDetails[payload.useLevelFrom];
    const levelTypeName = initialItem.skillLevels.length ? initialItem.skillCategoryLevelName : 'Levels';
    updatedItem.skillLevels = updatedItem.skillLevels.filter(l => l.operation !== 'ADD' || l.populatedUsingUseLevelsFrom).map(level => {
        level.operation = 'DELETE';
        level.hideFromGrid = true;

        return level;
    });
    updatedItem.skillLevels = [...updatedItem.skillLevels,...initialItem.skillLevels];
    updatedItem.skillLevels.map(level => {
        if (level.operation !== 'DELETE') {
            level.operation = 'ADD';
            level.populatedUsingUseLevelsFrom = true;
        }

        return level;
    });
    updatedItem.skillCategoryLevelName = levelTypeName;
    updatedItem.skillTypeLevelNameErrorMessage = '';
    updatedItem.useLevelFrom = payload.useLevelFrom;
    updatedItem.operation = updatedItem.operation || 'UPDATE';

    return { updatedDetails };
}

function updateLevelName(initialDetails,updatedDetails,payload) {
    let updatedItem = updatedDetails[payload.activeSelectionItem];
    if (!updatedItem) updatedDetails[payload.activeSelectionItem] = cloneDeep(initialDetails[payload.activeSelectionItem]);
    updatedItem = updatedDetails[payload.activeSelectionItem];
    updatedItem.skillCategoryLevelName = payload.skillCategoryLevelName;
    updatedItem.useLevelFrom = '';
    updatedItem.selectedLevel = CUSTOM_LEVELS;
    updatedItem.skillTypeLevelNameErrorMessage = payload.skillTypeLevelNameErrorMessage;

    // to merge the levels error status with skills error
    updatedItem.hasError = determineSkillWithLevelsError(updatedItem);

    updatedItem.operation = updatedItem.operation || 'UPDATE';


    return { updatedDetails };
}

function updateLevelType(initialDetails,updatedDetails,payload) {
    if (updatedDetails[payload.activeSelectionItem]) {
        updatedDetails[payload.activeSelectionItem].selectedLevel = payload.selectedLevel;
    } else {
        updatedDetails[payload.activeSelectionItem] = cloneDeep(initialDetails[payload.activeSelectionItem]);
        updatedDetails[payload.activeSelectionItem].selectedLevel = payload.selectedLevel;
    }
    if (payload.selectedLevel === NO_LEVELS) {
        updatedDetails[payload.activeSelectionItem].skillLevels = updatedDetails[payload.activeSelectionItem].skillLevels.filter(level=>level.operation !== 'ADD').map(level=> {
            level.operation = 'DELETE';
            level.hideFromGrid = true;

            return level;
        });
        updatedDetails[payload.activeSelectionItem].skillCategoryLevelName = 'Levels';
        updatedDetails[payload.activeSelectionItem].skillTypeLevelNameErrorMessage = '';
        updatedDetails[payload.activeSelectionItem].useLevelFrom = '';
    }

    updatedDetails[payload.activeSelectionItem].operation = updatedDetails[payload.activeSelectionItem].operation || 'UPDATE';

    return { updatedDetails };
}

function updateActiveTab(initialskillsConfigurationSectionDetails,payload) {
    initialskillsConfigurationSectionDetails[payload.activeSelectionItem].currentTab = payload.currentTab;

    return initialskillsConfigurationSectionDetails;

}

function deleteSkillsConfiguration(details, sections, updatedDetails, payload) {
    let newSections = [];
    switch (payload.menuDetails.operation) {
        case 'ADD' : {
            delete details[payload.selection];
            delete updatedDetails[payload.selection];
            newSections = sections.filter(item=>item != payload.selection);

            return { details,
                sections: newSections,
                updatedDetails
            };
        }
        case 'UPDATE': {
            details[payload.selection].operation = 'DELETE';
            updatedDetails[payload.selection].operation = 'DELETE';

            return { details, sections, updatedDetails };
        }
        case null, undefined: {
            details = { ...details, [payload.selection]: { ...details[payload.selection], operation:'DELETE' } };
            updatedDetails[payload.selection] ?
                updatedDetails = { ...updatedDetails, [payload.selection]: { ...updatedDetails[payload.selection], operation:'DELETE' } } :
                updatedDetails = { ...updatedDetails, [payload.selection]:{ ...details[payload.selection], operation:'DELETE' } };

            return { details, sections, updatedDetails };
        }
        default: {
            return { details, sections, updatedDetails };
        }
    }
}

function determinseStatusOfNewlyAddedSkills(updatedDetails) {
    return Object.keys(updatedDetails).some(listItem => updatedDetails[listItem].operation === 'ADD' || updatedDetails[listItem].operation === 'DELETE');
}

function addSkillsConfiguration(details, sections, updatedDetails, initialDetails, payload) {
    let _sections = cloneDeep(sections);
    _sections.push(payload.sectionName);
    const guid = uniqueId('skillType_');
    const newSkillSectionDataSet = {
        id: guid,
        name: payload.sectionName,
        initialName: payload.sectionName,
        skillCount:0,
        skillLevelCount:0,
        skillFieldsCount:0,
        skills: [],
        skillFields: [],
        skillLevels:[],
        currentTab:'0',
        selectedLevel:NO_LEVELS,
        initialSelectedLevel:NO_LEVELS,
        skillCategoryLevelName:'Levels',
        initialSkillCategoryLevelName:'Levels',
        skillTypeLevelNameErrorMessage:'',
        useLevelFrom:'',
        operation:'ADD',
        isFetchedById: true,
        fetchedDetails: true,
        fakeId: uniqueId() + `-${payload.sectionName}`
    };
    details = { ...details, [payload.sectionName]:newSkillSectionDataSet };
    initialDetails = { ...details, [payload.sectionName]:newSkillSectionDataSet };
    updatedDetails = { ...updatedDetails, [payload.sectionName]:newSkillSectionDataSet };

    return { details, sections: _sections, updatedDetails, initialDetails };
}


function getSections(data) {
    return data.map(item => item.name);
}

function getSectionDetails(skillSectionDetails,activeTabDetails) {
    let sectionDetails = {};
    skillSectionDetails.map((item) => {
        sectionDetails[item.name] = item;
        sectionDetails[item.name].initialName = item.name;
        sectionDetails[item.name].currentTab = activeTabDetails && activeTabDetails.selectionItem && activeTabDetails.selectionItem === item.name ? activeTabDetails.activeTab : '0';
        sectionDetails[item.name].isFetchedById = false;
        sectionDetails[item.name].fetchedDetails = false;
        sectionDetails[item.name].fakeId = item.surrogateId + `-${item.name}`;
    });

    return { ...sectionDetails };
}

function getFullSectionDetails(skillsConfigurationSectionDetails, payload) {
    let sectionIdSkillDetails = [];
    if (payload.skills && payload.skills.length > 0) {
        sectionIdSkillDetails = payload.skills.map((item) => {
            return {
                key: item.id,
                name: item.name,
                initialName: item.name,
                info: item.info,
                tags: item.tags,
                sectionId: item.sectionId,
                entityTypeId: item.entityTypeId,
                categories:item.categories,
                subCategories:item.subCategories,
                departments:item.departments,
                divisions:item.divisions,
                operation: item.operation
            };
        }).sort((previousValue, nextValue) => {
            return IntlCollatorCompare(LOCALE_EN)(previousValue.name, nextValue.name);
        });
    }
    if (skillsConfigurationSectionDetails[payload.name]) {
        skillsConfigurationSectionDetails[payload.name].skills = sectionIdSkillDetails;

        skillsConfigurationSectionDetails[payload.name].skillLevels = payload.skillLevels.map(level => {
            return { ...level,rowNumber:level.order };
        }).sort((previousValue, nextValue) => previousValue.rowNumber - nextValue.rowNumber);
        skillsConfigurationSectionDetails[payload.name].skillCount = payload.skills.length;
        skillsConfigurationSectionDetails[payload.name].skillLevelCount = payload.skillLevels.length;
        skillsConfigurationSectionDetails[payload.name].skillFieldsCount = payload.skillFields.length;
        skillsConfigurationSectionDetails[payload.name].selectedLevel = payload.skillLevels.length ? CUSTOM_LEVELS : NO_LEVELS;
        skillsConfigurationSectionDetails[payload.name].initialSelectedLevel = skillsConfigurationSectionDetails[payload.name].selectedLevel;
        skillsConfigurationSectionDetails[payload.name].skillCategoryLevelName = payload.skillCategoryLevelName ? payload.skillCategoryLevelName : 'Levels',
        skillsConfigurationSectionDetails[payload.name].initialSkillCategoryLevelName = payload.skillCategoryLevelName;
        skillsConfigurationSectionDetails[payload.name].fetchedDetails = true;
        skillsConfigurationSectionDetails[payload.name].isFetchedById = true;
        skillsConfigurationSectionDetails[payload.name].sysMaintained = payload.sysMaintained;
        skillsConfigurationSectionDetails[payload.name].preExpiryNotification = payload.preExpiryNotification;
        skillsConfigurationSectionDetails[payload.name].watcher = payload.watcher;
        skillsConfigurationSectionDetails[payload.name].isExpiryDateMandatory = payload.isExpiryDateMandatory;
        skillsConfigurationSectionDetails[payload.name].isSkillExpiryEnabled = payload.isSkillExpiryEnabled;

        //put payload.skillFields.map to add key
        const skillTypeFieldsUpdated = payload.skillFields.map((item,index)=>{
            return { ...item, key: index + 1, order: index + 1 };
        });
        autoIncKey = skillTypeFieldsUpdated.length;
        skillsConfigurationSectionDetails[payload.name].skillFields = skillTypeFieldsUpdated;
    }

    return { ...skillsConfigurationSectionDetails };
}

const addNewSkillInTable = (skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload, newSkillData) => {
    const { skillSectionName } = payload;

    updatedSkillsConfigurationSectionDetails[skillSectionName] = updatedSkillsConfigurationSectionDetails[skillSectionName] ?
        updatedSkillsConfigurationSectionDetails[skillSectionName] : { ...skillsConfigurationSectionDetails[skillSectionName] };

    newSkillData.sectionId = updatedSkillsConfigurationSectionDetails[skillSectionName].id;

    updatedSkillsConfigurationSectionDetails[skillSectionName].skills = [newSkillData, ...updatedSkillsConfigurationSectionDetails[skillSectionName].skills];
    if (updatedSkillsConfigurationSectionDetails[skillSectionName].operation == null || updatedSkillsConfigurationSectionDetails[skillSectionName].operation == 'UPDATE') {
        updatedSkillsConfigurationSectionDetails[skillSectionName].operation = 'UPDATE';
    }

    return { ...updatedSkillsConfigurationSectionDetails };
};


const createAddFieldPayload = (keyVal, type, entityGuid, entityName, payload = {}, activeFields) => {
    if (autoIncKey === 0) {
        autoIncKey = keyVal;
    }

    return {
        key: autoIncKey + 1,
        guid: Math.floor(new Date().valueOf() * Math.random() * 100).toString(16),
        skillCategoryGuid: entityGuid,
        customFieldGuid:null,
        customFieldDetails: {
            guid: null,
            entityId: payload.currentEntityId,
            name: entityName,
            label: '',
            description: '',
            fieldType: {
                guid: type.fieldGuid,
                name: type.fieldName
            },
            fieldCategory: {
                description: 'Database Field',
                category: 'Custom',
                subCategory: 'Data',
                categoryKey: 'custom'
            },
            isMandatory: false,
            operation: 'ADD',
            length: '',
            decimalPlace: null,
            minimum: null,
            maximum: null,
            default: '',
            hideInUI: false,
            fieldFormat: { guid: null, name: null },
            fieldLookupGuid:null,
            fieldLookupName:null,
            fieldLookupValue:null,
            modified: payload.modified || undefined,
            isReadOnly:false
        },
        order: ++autoIncKey,
        operation: 'ADD'

    };
};

const generateNewSkillData = (payload) => {
    const { updatedSkillSectionName } = payload;

    return {
        key: uniqueId('newSkill_'),
        name: '',
        info: '',
        entityTypeId:null,
        tags: [],
        categories:[updatedSkillSectionName],
        subCategories:[],
        departments:[],
        divisions:[],
        operation: 'ADD'
    };
};

const removeSkillFromTable = (skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload) => {
    const { skillKey, skillSectionName } = payload;

    updatedSkillsConfigurationSectionDetails[skillSectionName] = updatedSkillsConfigurationSectionDetails[skillSectionName] ?
        updatedSkillsConfigurationSectionDetails[skillSectionName] : skillsConfigurationSectionDetails[skillSectionName];

    let allSkills = updatedSkillsConfigurationSectionDetails[skillSectionName].skills;

    if (skillKey.includes('newSkill')) {
        allSkills = allSkills.filter(item => item.key !== skillKey);
    } else {
        allSkills.map(skill=>{
            if (skill.key === skillKey) {
                skill.key = skillKey;
                skill.operation = 'DELETE';
                skill.skillRowError = false;
            }
        });
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], operation: 'UPDATE' };
    }

    updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], skills: allSkills };
    updatedSkillsConfigurationSectionDetails[skillSectionName].skillsError = determineRowWithTitleError(updatedSkillsConfigurationSectionDetails[skillSectionName]);
    updatedSkillsConfigurationSectionDetails[skillSectionName].hasError = determineSkillWithLevelsError(updatedSkillsConfigurationSectionDetails[skillSectionName]);

    return { ...updatedSkillsConfigurationSectionDetails };
};


const cancelRemoveSkillFromTable = (updatedSkillsConfigurationSectionDetails, payload) => {
    const { skillKey, skillSectionName } = payload;
    let allSkills = updatedSkillsConfigurationSectionDetails[skillSectionName].skills;

    allSkills.map(skill=>{
        if (skill.key === skillKey) {
            skill.key = skillKey;
            skill.operation = null;
        }
    });
    const updatedSkills = allSkills.filter(item => item.operation != null);
    if (updatedSkills.length == 0) {
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], operation: null };
    }
    updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], skills: allSkills };
    updatedSkillsConfigurationSectionDetails[skillSectionName].skillsError = determineRowWithTitleError(updatedSkillsConfigurationSectionDetails[skillSectionName]);
    updatedSkillsConfigurationSectionDetails[skillSectionName].hasError = determineSkillWithLevelsError(updatedSkillsConfigurationSectionDetails[skillSectionName]);

    return { ...updatedSkillsConfigurationSectionDetails };
};

const buildSkillRecordsOnEdit = (skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails, payload) => {
    const { key, fieldName, value, skillSectionName, hasError } = payload;
    const currentSkillSectionDetails = cloneDeep(skillsConfigurationSectionDetails[skillSectionName]);

    updatedSkillsConfigurationSectionDetails = cloneDeep(updatedSkillsConfigurationSectionDetails);

    updatedSkillsConfigurationSectionDetails[skillSectionName] = updatedSkillsConfigurationSectionDetails[skillSectionName] ?
        updatedSkillsConfigurationSectionDetails[skillSectionName] : currentSkillSectionDetails;

    let skillDetails = cloneDeep(updatedSkillsConfigurationSectionDetails[skillSectionName]);
    let allSkills = skillDetails.skills;

    allSkills.forEach((skillItem)=>{
        if (skillItem.key === key) {
            skillItem[fieldName] = value;
            skillItem.skillRowError = hasError;
            switch (fieldName) {
                case 'skillName':
                    skillItem['name'] = value;
                    break;
                case 'skillDescription':
                    skillItem['info'] = value;
                    break;
                case 'skillTags':
                    skillItem['tags'] = value;
                    break;
                case 'entityTypeId':
                    skillItem['entityTypeId'] = value;
                    break;
                case 'categories':
                    skillItem['categories'] = value;
                    break;
                case 'subCategories':
                    skillItem['subCategories'] = value;
                    break;
                case 'departments':
                    skillItem['departments'] = value;
                    break;
                case 'divisions':
                    skillItem['divisions'] = value;
                    break;
                default:
                    break;
            }
            if (skillItem.operation === 'UPDATE' || skillItem.operation === null) {
                skillItem.operation = 'UPDATE';
            }
        }
    });


    updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], skills: allSkills, skillsError:hasError };
    if (updatedSkillsConfigurationSectionDetails[skillSectionName].operation == null || updatedSkillsConfigurationSectionDetails[skillSectionName].operation == 'UPDATE') {
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], operation: 'UPDATE' };
    }
    if (updatedSkillsConfigurationSectionDetails[skillSectionName].editTitleInfo && updatedSkillsConfigurationSectionDetails[skillSectionName].editTitleInfo.hasError) {
        // updatedSkillsConfigurationSectionDetails[skillSectionName].hasError = true;
        updatedSkillsConfigurationSectionDetails[skillSectionName].skillsError = true;
    }

    // to merge the levels error status with skills error
    updatedSkillsConfigurationSectionDetails[skillSectionName].hasError = determineSkillWithLevelsError(updatedSkillsConfigurationSectionDetails[skillSectionName]);

    return { ...updatedSkillsConfigurationSectionDetails };
};

function determineRowError(updatedSkillsConfigurationSectionDetails) {
    return Object.keys(updatedSkillsConfigurationSectionDetails).some((val) => updatedSkillsConfigurationSectionDetails[val].skillsError === true);
}

function determineRowWithTitleError(updatedSkillSection) {
    let hasRowError = false;
    if (updatedSkillSection) {
        hasRowError = updatedSkillSection.skills.some((item) => item.skillRowError === true);
        if (!hasRowError) {
            hasRowError = updatedSkillSection.editTitleInfo ? updatedSkillSection.editTitleInfo.hasError : false;
        }
    }

    return hasRowError;
}

function determineSkillWithLevelsError(updatedSkillSection) {
    let hasSkillsWithLevelsError = false;
    if (updatedSkillSection) {
        // to merge the levels error status with skills error
        hasSkillsWithLevelsError = !!(updatedSkillSection.skillsError ||
            updatedSkillSection.levelsGridErrorStatus ||
            updatedSkillSection.skillTypeLevelNameErrorMessage);
    }

    return hasSkillsWithLevelsError;
}

function buildUpdatedSkillsTitles(payload, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails) {
    const { skillSectionName, editTitleInfo } = payload;
    const { hasError } = editTitleInfo;
    updatedSkillsConfigurationSectionDetails = cloneDeep(updatedSkillsConfigurationSectionDetails);
    if (!updatedSkillsConfigurationSectionDetails[skillSectionName]) {
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...skillsConfigurationSectionDetails[skillSectionName] };
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], editTitleInfo, skillsError:hasError, operation: 'UPDATE' };

    } else {
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], editTitleInfo, skillsError:hasError };
        if (!updatedSkillsConfigurationSectionDetails[skillSectionName].operation) {
            updatedSkillsConfigurationSectionDetails[skillSectionName].operation = 'UPDATE';
        }
    }

    // to merge the levels error status with skills error
    updatedSkillsConfigurationSectionDetails[skillSectionName].hasError = determineSkillWithLevelsError(updatedSkillsConfigurationSectionDetails[skillSectionName]);
    // Update the category field in all the skills when the title changes
    const previousValue = updatedSkillsConfigurationSectionDetails[skillSectionName].modifiedName ?? skillSectionName;
    updatedSkillsConfigurationSectionDetails[skillSectionName].skills.forEach(x => {
        if (x.categories.includes(previousValue)) {
            x.categories = [editTitleInfo.name];
        }
    });

    // save the previous value of the title change which can be used to change those on updates
    updatedSkillsConfigurationSectionDetails[skillSectionName].modifiedName = editTitleInfo.name;

    return { ...updatedSkillsConfigurationSectionDetails };
}

const getFieldValue = (fieldName, value) => {
    switch (fieldName) {
        case 'isExpiryDateMandatory':
            return { isExpiryDateMandatory : value };
        case 'isSkillExpiryEnabled':
            return { isSkillExpiryEnabled : value };
        case 'preExpiryNotification':
            return { preExpiryNotification : value };
        case 'watcher':
            return { watcher : value };
        default:
            return;
    }
};

function buildUpdatedSkillCategory(payload, skillsConfigurationSectionDetails, updatedSkillsConfigurationSectionDetails) {
    const { fieldName, value, skillSectionName } = payload;
    updatedSkillsConfigurationSectionDetails = cloneDeep(updatedSkillsConfigurationSectionDetails);

    //Note We are not updating the hasError, since there is no condition with the above changes that will cause a form to error
    if (!updatedSkillsConfigurationSectionDetails[skillSectionName]) {
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...skillsConfigurationSectionDetails[skillSectionName] };
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], ...getFieldValue(fieldName,value), operation: 'UPDATE' };

    } else {
        updatedSkillsConfigurationSectionDetails[skillSectionName] = { ...updatedSkillsConfigurationSectionDetails[skillSectionName], ...getFieldValue(fieldName,value) };
        if (!updatedSkillsConfigurationSectionDetails[skillSectionName].operation) {
            updatedSkillsConfigurationSectionDetails[skillSectionName].operation = 'UPDATE';
        }
    }

    return { ...updatedSkillsConfigurationSectionDetails };
}

function buildUpdatedSkillOnExpandedKeys(payload, skillOnExpandedKeys) {
    const { expandedKeys, skillSectionName } = payload;
    skillOnExpandedKeys[skillSectionName] = expandedKeys;

    return { ...skillOnExpandedKeys };
}


function addNewSkillInitialDetails(initialskillsConfigurationSectionDetails, payload, newSkillData) {
    const { skillSectionName } = payload;
    let initialSkillDetails = cloneDeep(initialskillsConfigurationSectionDetails[skillSectionName]);
    let allInitialSkills = initialSkillDetails.skills;
    let updatedInitialSkills = [newSkillData, ...allInitialSkills];
    newSkillData.sectionId = initialskillsConfigurationSectionDetails[skillSectionName].id;
    initialskillsConfigurationSectionDetails[skillSectionName] = { ...initialskillsConfigurationSectionDetails[skillSectionName], skills: updatedInitialSkills };

    return { ...initialskillsConfigurationSectionDetails };
}

function removeNewlyAddedInitialDetail(initialskillsConfigurationSectionDetails, payload) {
    const { skillKey, skillSectionName } = payload;
    let initialSkillDetails = cloneDeep(initialskillsConfigurationSectionDetails[skillSectionName]);
    let allSkills = initialSkillDetails.skills;
    if (skillKey.includes('newSkill')) {
        allSkills = initialSkillDetails.skills.filter(item => item.key !== skillKey);
    }
    initialskillsConfigurationSectionDetails[skillSectionName] = { ...initialskillsConfigurationSectionDetails[skillSectionName], skills: allSkills };

    return { ...initialskillsConfigurationSectionDetails };
}

function addSkillsConfigurationSectionUsingHotKey(details, sections, updatedDetails, initialDetails) {
    const newSection = calculateDefaultValue(sections, 'Skills', 1);
    let _sections = cloneDeep(sections);
    _sections.push(newSection);
    const guid = uniqueId('skillType_');
    const newSkillSectionDataSet = {
        id: guid,
        name: newSection,
        initialName: newSection,
        skillCount:0,
        skillLevelCount:0,
        skillFieldsCount:0,
        skills: [],
        skillLevels:[],
        currentTab:'0',
        selectedLevel:NO_LEVELS,
        initialSelectedLevel:NO_LEVELS,
        skillCategoryLevelName:'Levels',
        initialSkillCategoryLevelName:'Levels',
        skillTypeLevelNameErrorMessage:'',
        useLevelFrom:'',
        operation:'ADD',
        fakeId: uniqueId() + `-${newSection}`,
        preExpiryNotification:0,
        watcher:false,
        isExpiryDateMandatory:false,
        isSkillExpiryEnabled:false
    };
    details = { ...details, [newSection]:newSkillSectionDataSet };
    initialDetails = { ...details, [newSection]:newSkillSectionDataSet };
    updatedDetails = { ...updatedDetails, [newSection]:newSkillSectionDataSet };

    return { details, sections: _sections, updatedDetails, initialDetails, newSection };
}

function calculateDefaultValue(sections, sectionName, cnt) {
    let defaultVal = `Untitled ${sectionName} ${cnt}`;
    while (sections.some((entry) => { return entry.toLowerCase() === defaultVal.toLowerCase(); })) {
        cnt++;
        defaultVal = `Untitled ${sectionName} ${cnt}`;
    }

    return defaultVal;
}


export default skillsConfigurationReducer;