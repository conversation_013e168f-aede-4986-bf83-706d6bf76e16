import * as actions from '../actions/actionTypes';
import initialState from '../state/initialState';
import { getPlansSectionMenuItem } from '../state/commandBar/plansSection';
import { plansSectionModelCreator, plansSectionModelModify } from '../state/commandBar/plansSection';
import { COMMAND_BAR_MENUS_SECTION_KEYS } from '../constants/commandBarConsts';
import { getWorkspaceStructure, getWorkspacesGuids, getMostRecentlyUsedWorkspaces } from '../selectors/workspaceSelectors';
import { WORKSPACE_ACCESS_TYPES } from '../constants';
import { orderBy } from 'lodash';

const getMostRecentPlansItems = (action) => {
    const { workspaces, selectedWorkspaceGuid } = action.payload;
    const mostRecentlyUsedWorkspacesGuids = getMostRecentlyUsedWorkspaces(workspaces);

    let mostRecentPlans = [];
    let currWS, currWSGuid, menuItemIcon, menuItemStyle = {};

    for (let i = 0; i < mostRecentlyUsedWorkspacesGuids.length; i++) {
        currWSGuid = mostRecentlyUsedWorkspacesGuids[i];
        currWS = getWorkspaceStructure(workspaces, currWSGuid);

        menuItemIcon = {};
        menuItemStyle = {};
        menuItemIcon = currWS.workspace_accesstype === WORKSPACE_ACCESS_TYPES.PRIVATE ? menuItemIcon : 'team';
        let className = '';

        if (currWSGuid === selectedWorkspaceGuid) {
            className = 'selectedPlan';
        }

        const recentPlanProps = {
            label: currWS.workspace_description,
            onClickActionType: actions.SELECT_WORKSPACE,
            plansSectionPlanGuid: currWSGuid,
            style: menuItemStyle,
            icon: menuItemIcon,
            className: className
        };

        mostRecentPlans.push(getPlansSectionMenuItem({ ...recentPlanProps }));
    }

    return mostRecentPlans;
};

// Duplicate of function in commandBarUtils. Pasted here because of jest errors.
const getMenusSectionIndexByKey = (sections, sectionKey) => {
    return sections.findIndex((section) => {
        return section.key === sectionKey;
    });
};

const plansSectionIndex = getMenusSectionIndexByKey(initialState.plannerPage.commandBarConfig.menusSection, COMMAND_BAR_MENUS_SECTION_KEYS.PLANS);
export default function plansSectionReducer(state = initialState.plannerPage.commandBarConfig.menusSection[plansSectionIndex], action) {
    switch (action.type) {
        case actions.COMMAND_BAR_PLANS_SECTION.SELECT_PLAN:
        case actions.COMMAND_BAR_PLANS_SECTION.POPULATE:
        case actions.COMMAND_BAR_PLANS_SECTION.REPOPULATE: {
            const { workspaces, selectedWorkspaceGuid, listPageAndBulkUpdateFeatureFlag } = action.payload;

            const selectedWorkspaceStructure = getWorkspaceStructure(workspaces, selectedWorkspaceGuid);
            const label = selectedWorkspaceStructure.workspace_description;
            const mostRecentPlansItems = getMostRecentPlansItems(action);
            let publicPlansItems = [];
            let privatePlansItems = [];

            let wsGuid, currWS;
            const workspacesGuids = getWorkspacesGuids(workspaces);

            for (let i = 0; i < workspacesGuids.length; i++) {
                wsGuid = workspacesGuids[i];
                currWS = getWorkspaceStructure(workspaces, wsGuid);

                const planItemProps = {
                    label: currWS.workspace_description,
                    onClickActionType: actions.SELECT_WORKSPACE,
                    plansSectionPlanGuid: wsGuid
                };

                if (currWS.workspace_accesstype === WORKSPACE_ACCESS_TYPES.PRIVATE) {
                    privatePlansItems.push(getPlansSectionMenuItem({ ...planItemProps }));
                } else {
                    publicPlansItems.push(getPlansSectionMenuItem({ ...planItemProps }));
                }
            }

            publicPlansItems = orderBy(publicPlansItems, [plan => plan.label.toLowerCase()], ['asc']);
            privatePlansItems = orderBy(privatePlansItems, [plan => plan.label.toLowerCase()], ['asc']);

            const plansMessages = listPageAndBulkUpdateFeatureFlag ? state.workspacesMessages : state.plansMessages;

            return {
                ...state,
                ...plansSectionModelCreator(label, selectedWorkspaceStructure.workspace_accesstype, mostRecentPlansItems, privatePlansItems, publicPlansItems, plansMessages, listPageAndBulkUpdateFeatureFlag)
            };
        }
        case actions.SET_CONFIG: {
            const { config } = action.payload;

            return {
                ...state,
                ...config.menusSection[plansSectionIndex],
                ...plansSectionModelModify(config.menusSection[plansSectionIndex])
            };
        }
        default:
            return state;
    }
}