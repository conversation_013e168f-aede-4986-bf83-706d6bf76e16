import { WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS, TABLE_NAMES, FIELD_DATA_CATEGORIES } from '../constants';
import { BAR_TABLE_NAMES, MANDATORY_FIELDS, PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP, RECORDSLIST_HIDDEN_FIELDS, PLANNER_VIEW_MODES, PLANNER_PAGE_TABLE_TO_GROUP_MAP, PRECONFIGURED_PLANNER_BAR_DISPLAY_FIELDS, MANDATORY_MASTERREC_LOAD_ONLY_FIELD, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS } from '../constants/plannerConsts';
import { unionBy, orderBy } from 'lodash';
import { createSelector } from 'reselect';
import { getMergedSelectionFields } from './tableFieldsSelectors';
import { getTranslationsSelector } from './internationalizationSelectors';
import { getIsValidNotHiddenFieldInfo, getFieldInfoSelector, verifyFieldShouldBeHidden, getTableStructure, getUIFieldInfos } from './tableStructureSelectors';
import { isEmptyObject, isPositiveInteger } from '../utils/commonUtils';
import { FEATURE_FLAGS, ROLE_AND_REQUEST_FEATURE_SWITCH } from '../constants/globalConsts';
import { getFieldPickerDetailsFields } from '../utils/fieldPickerUtils';
import { differenceInCalendarDays, dayValues } from '../utils/dateUtils';
import { getViewModeOptionsByName } from '../utils/dateToggleOptionsUtils';
import { getIsSortableField } from '../utils/fieldUtils';
import { isMinorUnitDay } from '../utils/calendarGridUtils';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import { calcFields } from '../constants/fieldConsts';
import { getFeatureFlagSelector } from './featureManagementSelectors';

const getWorkspacesSettings = state => {
    return state.workspaces || {};
};

const getWorkspacesSettingsStructure = state => {
    return state.workspacesSettings;
};

const getWorkspacesGuids = createSelector(
    workspaces => workspaces.workspacesStructure,
    (workspacesStructure) => {
        if (!workspacesStructure.hasOwnProperty('map')) {
            return [];
        }

        return Object.keys(workspacesStructure.map);
    }
);

const getLoadedWorkspacesGuids = (workspaces) => {
    return Object.keys(workspaces.workspacesSettings.map);
};

const getIsSingleDaySelector = createSelector(
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (workspacesSettings = {}) => {
        const { startDate, endDate } = workspacesSettings;

        return differenceInCalendarDays(endDate, startDate) <= 1;
    }
);

const getWorkspaceSettings = (workspaces, workspaceGuid) => {
    if (!workspaces.workspacesSettings.hasOwnProperty('map')) {
        return {};
    }

    return workspaces.workspacesSettings.map[workspaceGuid];
};

const getSelectedWorkspaceGuid = (workspaces) => {
    return workspaces.selected;
};

const getSelectedWorkspaceSettings = createSelector(
    workspaces => (workspaces || {}).workspacesSettings,
    workspaces => (workspaces || {}).selected,
    (workspacesSettings, selected) => {
        if (!(workspacesSettings || {}).hasOwnProperty('map')) {
            return {};
        }

        return workspacesSettings.map[selected];
    }
);

const getSelectedWorkspace = createSelector(
    workspaces => workspaces.workspacesStructure,
    workspaces => workspaces.selected,
    (workspacesStructure, selected) => {
        if (!workspacesStructure.hasOwnProperty('map')) {
            return {};
        }

        return workspacesStructure.map[selected];
    }
);

const getWorkspacesByAccessType = (workspaces, accessType) => {
    return Object.keys(workspaces).reduce((accumulator, workspaceKey) => {
        if (workspaces[workspaceKey].workspace_accesstype === accessType) {
            accumulator.push(workspaces[workspaceKey]);
        }

        return accumulator;
    }, []);
};

const orderWorkspaces = (workspaces) => {
    return workspaces.length > 0 ? orderBy(workspaces, [plan => plan.workspace_description.toLowerCase()], ['asc']) : workspaces;
};

const getWorkspaces = (workspaces) => {
    return workspaces;
};

const getPublicWorkspaces = createSelector(
    getWorkspaces,
    (workspaces) => { return getWorkspacesByAccessType(workspaces, WORKSPACE_ACCESS_TYPES.PUBLIC); }
);

const getIsPublicWorkspace = createSelector(
    workspace => workspace,
    (workspace) => {
        return (workspace || {}).workspace_accesstype === WORKSPACE_ACCESS_TYPES.PUBLIC;
    }
);

const getPrivateWorkspaces = createSelector(
    getWorkspaces,
    (workspaces) => { return getWorkspacesByAccessType(workspaces, WORKSPACE_ACCESS_TYPES.PRIVATE); }
);

const getOrderedPublicWorkspaces = createSelector(
    getPublicWorkspaces,
    (workspaces) => { return orderWorkspaces(workspaces); }
);

const getOrderedPrivateWorkspaces = createSelector(
    getPrivateWorkspaces,
    (workspaces) => { return orderWorkspaces(workspaces); }
);

const getMostRecentlyUsedWorkspaces = (workspaces) => {
    return workspaces.mostRecentlyUsed;
};

const getWorkspaceStructure = createSelector(
    (workspaces, workspaceGuid) => workspaces.workspacesStructure,
    (workspaces, workspaceGuid) => workspaceGuid,
    (workspacesStructure, workspaceGuid) => {
        if (!workspacesStructure.hasOwnProperty('map')) {
            return {};
        }

        return workspacesStructure.map[workspaceGuid];
    }
);

const getWorkspaceColorSchemeInfo = (workspaces, workspaceGuid) => {
    const { workspace_colourtheme_guid, workspace_custom_colour_field } = getWorkspaceStructure(workspaces, workspaceGuid);

    return {
        workspace_colourtheme_guid,
        workspace_custom_colour_field
    };
};

const getSelectedWorkspaceStructure = createSelector(
    workspaces => workspaces.workspacesStructure,
    workspaces => workspaces.selected,
    (workspacesStructure, selected) => {
        if (!(workspacesStructure || {}).hasOwnProperty('map')) {
            return {};
        }

        return workspacesStructure.map[selected];
    }
);

const getDefaultWorkspaceStructure = (workspaces) => {
    const workspaceStructureMap = workspaces.workspacesStructure.map;
    let currWorkspaceStructure;

    for (let workspaceKey in workspaceStructureMap) {
        currWorkspaceStructure = workspaceStructureMap[workspaceKey];
        if (currWorkspaceStructure.workspace_accesstype === WORKSPACE_ACCESS_TYPES.DEFAULT) {
            return currWorkspaceStructure;
        }
    }

    return {};
};

const getWorkspaceGuid = (workspaceStructure = {}) => {
    return workspaceStructure.workspace_guid;
};

const getWorkspaceStructureFromChangesCollection = (workspaces, collection, workspaceUUID) => {
    let wsStructureChanges = {};

    if (workspaces.workspacesStructureChanges[collection].map[workspaceUUID]) {
        wsStructureChanges = workspaces.workspacesStructureChanges[collection].map[workspaceUUID].change.value;
    }

    return wsStructureChanges;
};

const getWorkspaceSettingsFromChangesCollection = (workspaces, collection, workspaceUUID) => {
    let wsSettingsChanges = {};

    if (workspaces.workspacesSettingsChanges[collection].map[workspaceUUID]) {
        wsSettingsChanges = workspaces.workspacesSettingsChanges[collection].map[workspaceUUID].change.value;
    }

    return wsSettingsChanges;
};

const workspaceSettingsChanged = (workspaces, workspaceGuid) => {
    return workspaces.workspacesSettingsChangesTracker.inserts.map.hasOwnProperty(workspaceGuid);
};

const getWorkspacesForInsert = (workspaces) => {
    return Object.keys(workspaces.workspacesStructureChanges.inserts.map).map((wsUUID) => {
        const workspaceStructure = getWorkspaceStructureFromChangesCollection(workspaces, 'inserts', wsUUID);
        const workspaceStructureUpdates = getWorkspaceStructureFromChangesCollection(workspaces, 'updates', wsUUID);
        const workspaceSettings = getWorkspaceSettingsFromChangesCollection(workspaces, 'inserts', wsUUID);

        return { workspace_guid: wsUUID, ...workspaceStructure, ...workspaceStructureUpdates, ...workspaceSettings };
    });
};

const getWorkspaceMasterRecTableName = workspaceSettings => {
    return workspaceSettings.masterRecTableName;
};

const getWorkspaceSubRecTableName = workspaceSettings => {
    return workspaceSettings.subRecTableName;
};

const getRecordsListRecSelection = (config, mandatoryFields, { order }) => {
    const configFields = config.detailFields.map(({ field }) => ({ fieldName: field }));
    const fields = getMergedSelectionFields(configFields, mandatoryFields);

    return {
        fields,
        order
    };
};

const mapStringFieldsToFieldNames = (fields) => fields.map(field => {
    return { fieldName: field };
});

const getWorkspaceBarsDisplayFieldsSelector = createSelector(
    ({ plannerPage }) => getSelectedWorkspaceSettings(plannerPage.workspaces),
    getFieldInfoSelector,
    (workspaceSettings, getFieldInfo) => {
        const { masterRecTableName, viewsConfig } = workspaceSettings;
        const { barDisplayFields } = viewsConfig[masterRecTableName];

        const allBarDisplayFields = {
            ...barDisplayFields,
            ...PRECONFIGURED_PLANNER_BAR_DISPLAY_FIELDS
        };

        return Object.keys(PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP).reduce((accumulator, tableName) => {
            const displayBarsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[tableName];

            accumulator[displayBarsKey] = (allBarDisplayFields[displayBarsKey] || []).filter(displayField => getIsValidNotHiddenFieldInfo(getFieldInfo(tableName, displayField)));

            return accumulator;
        }, {});
    }
);

const getUIBarsDisplayFieldsSelector = createSelector(
    getWorkspaceBarsDisplayFieldsSelector,
    ({ plannerPage }) => getSelectedWorkspaceSettings(plannerPage.workspaces),
    (displayFields, workspaceSettings) => (tableName) => {
        const { styleSettings } = workspaceSettings;
        const { maxBookingFieldsSelection } = styleSettings;
        const displayBarsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[tableName] || '';

        return (displayFields[displayBarsKey] || []).slice(0, maxBookingFieldsSelection);
    }
);

const getBarDetailsFieldsSelector = createSelector(
    state => getTableStructure(state),
    (tableStructure) => {
        const filterOutTypes = [FIELD_DATA_CATEGORIES.HISTORY];

        return BAR_TABLE_NAMES.reduce((accumulator, tableName) => {
            const fieldInfos = getUIFieldInfos(tableStructure, tableName, filterOutTypes) || {};

            accumulator[tableName] = getFieldPickerDetailsFields(tableName, fieldInfos);

            return accumulator;
        }, {});
    }
);

const getBarSelection = (wsBarFields, mandatoryFields, tableName, order) => {
    const displayBarsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[tableName];

    const wsBarFieldNames = wsBarFields[displayBarsKey].map(field => ({ fieldName: field }));
    const fields = getMergedSelectionFields(wsBarFieldNames, mandatoryFields);

    return {
        fields,
        order
    };
};

const entitySortOrderMap = {
    [TABLE_NAMES.BOOKING]: {
        orderFields: [
            {
                field: 'booking_start',
                order: 'Ascending'
            }
        ]
    },
    [TABLE_NAMES.ROLEREQUEST]: {
        orderFields: [
            {
                field: 'rolerequest_start',
                order: 'Ascending'
            }
        ]
    }
};

const getWorkspaceTableSelections = createSelector(
    workspaceSettings => workspaceSettings.masterRecTableName,
    workspaceSettings => workspaceSettings.subRecTableName,
    workspaceSettings => workspaceSettings.viewsConfig,
    (masterRecTableName, subRecTableName, viewsConfig) => {
        const { recordsListConfig } = viewsConfig[masterRecTableName];
        const { barDisplayFields } = viewsConfig[masterRecTableName];
        const { cellDisplayFields = { bookingByWeekViewCells: [] } } = viewsConfig[masterRecTableName];

        const masterConfig = recordsListConfig.parentDisplayFields;
        const masterMandatoryFields = mapStringFieldsToFieldNames(
            MANDATORY_FIELDS[masterRecTableName].concat(MANDATORY_MASTERREC_LOAD_ONLY_FIELD[masterRecTableName] || [])
        );
        const savedMasterSel = viewsConfig[masterRecTableName].selection;

        const subRecConfig = recordsListConfig.childDisplayFields;
        const subMandatoryFields = mapStringFieldsToFieldNames(MANDATORY_FIELDS[subRecTableName]);
        const savedSubSel = viewsConfig[subRecTableName].selection;

        const masterSel = getRecordsListRecSelection(masterConfig, masterMandatoryFields, savedMasterSel);
        const subRecSel = getRecordsListRecSelection(subRecConfig, subMandatoryFields, savedSubSel);

        // Consider refactor - this to return only WS selection and add Mandatory fields in another function
        return {
            [masterRecTableName]: masterSel,
            [subRecTableName]: subRecSel,
            [TABLE_NAMES.BOOKING]: getBarSelection(barDisplayFields, MANDATORY_FIELDS[TABLE_NAMES.BOOKING], TABLE_NAMES.BOOKING, entitySortOrderMap[TABLE_NAMES.BOOKING]),
            [TABLE_NAMES.ROLEREQUEST]: getBarSelection(barDisplayFields, MANDATORY_FIELDS[TABLE_NAMES.ROLEREQUEST], TABLE_NAMES.ROLEREQUEST, entitySortOrderMap[TABLE_NAMES.ROLEREQUEST]),
            [TABLE_NAMES.BOOKINGBYWEEKVIEW]: getBarSelection(cellDisplayFields, [], TABLE_NAMES.BOOKINGBYWEEKVIEW, { orderFields: [] })
        };
    }
);

const getWorkspaceRecordsListConfig = ({ viewsConfig, styleSettings, masterRecTableName }) => {
    const { primaryHeaderHeight, toolbarHeight } = styleSettings;

    return {
        config: {
            ...((viewsConfig || {})[masterRecTableName] || {}).recordsListConfig,
            primaryHeaderHeight,
            toolbarHeight
        }
    };
};

const getWorkspaceRecordsListSortOrder = (workspaceSettings, tableName) => {
    return workspaceSettings.viewsConfig[tableName].selection.order;
};

const getWorkspaceRecordsListDetailFields = createSelector(
    (workspaceSettings, tableName, getFieldInfo) => workspaceSettings.viewsConfig,
    (workspaceSettings, tableName, getFieldInfo) => workspaceSettings.styleSettings,
    (workspaceSettings, tableName, getFieldInfo) => workspaceSettings.masterRecTableName,
    (workspaceSettings, tableName, getFieldInfo) => tableName,
    (workspaceSettings, tableName, getFieldInfo) => getFieldInfo,
    (workspaceSettings, tableName, getFieldInfo, calcFieldFlagEnabled) => calcFieldFlagEnabled,
    (viewsConfig, styleSettings, masterRecTableName, tableName, getFieldInfo, calcFieldFlagEnabled) => {
        const { config } = getWorkspaceRecordsListConfig({ viewsConfig, styleSettings, masterRecTableName });
        const hiddenFields = RECORDSLIST_HIDDEN_FIELDS[masterRecTableName];
        const mandatoryFields = MANDATORY_FIELDS[tableName];

        const buildFieldInfo = field => {
            return {
                table: masterRecTableName,
                field: field,
                title: field
            };
        };

        const detailFields = masterRecTableName === tableName ? config.parentDisplayFields.detailFields : config.childDisplayFields.detailFields;

        const customMapDetailField = (getFieldInfo) => (fieldObj) => {
            const fieldInfo = getFieldInfo(fieldObj.table, fieldObj.field);
            const isCalcField = calcFields.includes(fieldInfo?.name.toLowerCase());

            return {
                ...fieldObj,
                sortable: isCalcField
                    ? calcFieldFlagEnabled
                    : getIsSortableField(fieldInfo)
            };
        };

        const filteredDetailFields = ROLE_AND_REQUEST_FEATURE_SWITCH ? detailFields.filter((field) => !verifyFieldShouldBeHidden(field.field)) : detailFields;
        const sortableDetailFields = filteredDetailFields.map(customMapDetailField(getFieldInfo));

        const combinedFields = unionBy(sortableDetailFields, mandatoryFields.map(buildFieldInfo), 'field');

        return combinedFields.filter(detailField => getIsValidNotHiddenFieldInfo(getFieldInfo(detailField.table, detailField.field)) && !hiddenFields.includes(detailField.field));
    }
);

const getWorkspaceLoadedFields = createSelector(
    (workspaceSettings, getFieldInfo) => workspaceSettings.masterRecTableName,
    (workspaceSettings, getFieldInfo) => workspaceSettings.subRecTableName,
    (workspaceSettings, getFieldInfo) => workspaceSettings.viewsConfig,
    (workspaceSettings, getFieldInfo) => workspaceSettings.styleSettings,
    (workspaceSettings, getFieldInfo) => getFieldInfo,
    (masterRecTableName, subRecTableName, viewsConfig, styleSettings, getFieldInfo) => {

        const { barDisplayFields = {} } = viewsConfig[masterRecTableName];

        const masterRecFields = getWorkspaceRecordsListDetailFields({ viewsConfig, styleSettings, masterRecTableName }, masterRecTableName, getFieldInfo).map(data => data.field);
        const subRecFields = getWorkspaceRecordsListDetailFields({ viewsConfig, styleSettings, masterRecTableName }, subRecTableName, getFieldInfo).map(data => data.field);

        const loadedBarFields = Object.keys(PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP).reduce((accumulator, tableName) => {
            const displayFieldsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[tableName];
            barDisplayFields[displayFieldsKey] && accumulator.push(...barDisplayFields[displayFieldsKey]);

            return accumulator;
        }, []);

        return [...masterRecFields, ...subRecFields, ...loadedBarFields];
    }
);

const workspaceBelongsToResource = (workspace, resourceId) => {
    const belongsFieldname = 'workspace_author_guid';

    return workspace && resourceId == workspace[belongsFieldname];
};

const getWorkspaceIsEditable = (workspace) => {
    const editRightsFieldname = 'workspace_editrights';

    return workspace[editRightsFieldname] === WORKSPACE_EDIT_RIGHTS.EDIT;
};

const getWorkspaceMasterRecPlannerDataGuid = workspaceSettings => {
    const { pagedMasterRecPlannerDataGuid } = workspaceSettings;

    return pagedMasterRecPlannerDataGuid;
};

const getWorkspaceSubRecPlannerDataGuid = workspaceSettings => {
    const { subRecPlannerDataGuid } = workspaceSettings;

    return subRecPlannerDataGuid;
};

const getWorkspacesStructureMap = workspaces => {
    return workspaces.workspacesStructure.map;
};

const getCurrentWSSettingsSelector = createSelector(
    ({ plannerPage }) => plannerPage.workspaces,

    (workspaces) => getSelectedWorkspaceSettings(workspaces)
);

const getManageMyPlansWindowStaticMessagesSelector = createSelector(
    state => getTranslationsSelector(state, { sectionName: 'plannerPage', idsArray: ['plans'] }),
    state => getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state),
    (messages, listPageAndBulkUpdateFeatureFlag) => {
        const {
            manageMyPlansLabel = 'Manage My Plans',
            newPlanLabel = 'New Plan',
            privatePlansColumnTitle = 'My Plans',
            publicPlansLabel = 'Public Plans',
            moveToPublicLabel = 'Move to public',
            renameLabel = 'Rename',
            makeCopyLabel = 'Make a copy',
            deleteLabel = 'Delete',
            readOnlyLabel = 'Read-only',
            editAccessLabel = 'Edit access',
            makePublicCopyLabel = 'Make a public copy',
            makePrivateCopyLabel = 'Make a private copy',
            moveToPrivateLabel = 'Move to private',
            copyPlanLabel = 'Copy plan',
            editPlanLabel = 'Edit plan',
            manageMyWorkspacesLabel = 'Manage My Workspaces',
            newWorkspaceLabel = 'New Workspace',
            privateWorkspacesColumnTitle = 'My Workspaces',
            publicWorkspacesLabel = 'Public Workspaces',
            copyWorkspaceLabel = 'Copy workspace',
            editWorkspaceLabel = 'Edit workspace'
        } = messages.plans || {};

        if (listPageAndBulkUpdateFeatureFlag) {
            return {
                headerTitle: manageMyWorkspacesLabel,
                newPlanButtonTitle: newWorkspaceLabel,
                newPlanLabel: newWorkspaceLabel,
                privateWorkspacesColumnTitle,
                publicWorkspacesLabel,
                plansColumnMessages: {
                    publicRightsLabels: {
                        readOnlyLabel,
                        editAccessLabel
                    },
                    renameLabel,
                    deleteLabel,
                    makePublicCopyLabel,
                    makePrivateCopyLabel,
                    moveToPrivateLabel,
                    moveToPublicLabel,
                    makeCopyLabel,
                    copyWorkspaceLabel,
                    editWorkspaceLabel
                }
            };
        } else {
            return {
                headerTitle: manageMyPlansLabel,
                newPlanButtonTitle: newPlanLabel,
                newPlanLabel,
                privatePlansColumnTitle,
                publicPlansLabel,
                plansColumnMessages: {
                    publicRightsLabels: {
                        readOnlyLabel,
                        editAccessLabel
                    },
                    renameLabel,
                    deleteLabel,
                    makePublicCopyLabel,
                    makePrivateCopyLabel,
                    moveToPrivateLabel,
                    moveToPublicLabel,
                    makeCopyLabel,
                    copyPlanLabel,
                    editPlanLabel
                }
            };
        }
    }
);

const getWorkspaceCurrentColourSchemeObjSelector = createSelector(
    (state) => (getSelectedWorkspaceStructure(state.plannerPage.workspaces) || {}),
    (structure = {}) => {
        const { workspace_colourtheme_guid, workspace_custom_colour_field = [], isCustomThemeSelected } = structure;
        let result = {
            colourSchemeId: workspace_colourtheme_guid
        };

        if (workspace_custom_colour_field.length > 0) {

            const updatedWorkspaceColourTheme = workspace_custom_colour_field.map(field => {
                const { workspace_colour_field_name = '' } = field;
                const workspace_colour_table_name = workspace_colour_field_name.split('_')[0];
                field.workspace_colour_table_name = workspace_colour_table_name;

                return field;
            });

            result = {
                workspace_custom_colour_field: updatedWorkspaceColourTheme,
                isCustomThemeSelected
            };
        }

        return result;
    }
);

const getWorkspaceFromSurrogateId = ({ plannerPage }, workspaceSurrogateId) =>
    workspaceSurrogateId
        ? Object
            .values(getWorkspacesStructureMap(plannerPage.workspaces))
            .find(({ workspace_surrogate_id }) => workspace_surrogate_id === workspaceSurrogateId) || null
        : null;

const getInitWorkspaceToSelect = (state, surrogateId) => {
    const selectMostRecentlyUsedWorkspace = !surrogateId;
    let workspaceToSelect = null;

    if (selectMostRecentlyUsedWorkspace) {
        const workspaces = getWorkspacesSettings(state.plannerPage);
        workspaceToSelect = getSelectedWorkspace(workspaces);
    } else if (isPositiveInteger(surrogateId)) {
        workspaceToSelect = getWorkspaceFromSurrogateId(state, surrogateId);
    }

    return workspaceToSelect;
};

const getBarGroupsGuidByTable = createSelector(
    (workspaceSettings, tableName) => workspaceSettings.barTableNamesGroupKeys,
    (workspaceSettings, tableName) => workspaceSettings.barGroupsGuids,
    (workspaceSettings, tableName) => tableName,
    (workspaceSettings, tableName) => workspaceSettings.workspace_guid,
    (barTableNamesGroupKeys, barGroupsGuids, tableName, workspaceGuid) => {
        const tableGroupsKey = barTableNamesGroupKeys[tableName] || PLANNER_PAGE_TABLE_TO_GROUP_MAP[tableName];
        let groupsGuid;

        if (tableGroupsKey) {
            const defaultGroupsKeys = `${tableGroupsKey}_${workspaceGuid}`;
            groupsGuid = barGroupsGuids[tableGroupsKey] || defaultGroupsKeys;
        }

        return groupsGuid;
    }
);

const getCellGroupsGuidByTable = createSelector(
    (workspaceSettings, tableName) => workspaceSettings.cellTableNamesGroupKeys,
    (workspaceSettings, tableName) => workspaceSettings.cellGroupsGuids,
    (workspaceSettings, tableName) => tableName,
    (workspaceSettings, tableName) => workspaceSettings.workspace_guid,
    (cellTableNamesGroupKeys, cellGroupsGuids, tableName, workspaceGuid) => {
        const tableGroupsKey = cellTableNamesGroupKeys[tableName] || TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS[tableName];
        let groupsGuid;

        if (tableGroupsKey) {
            const defaultGroupsKeys = `${tableGroupsKey}_${workspaceGuid}`;
            groupsGuid = cellGroupsGuids[tableGroupsKey] || defaultGroupsKeys;
        }

        return groupsGuid;
    }
);

const calcDetailsFieldsheight = (workspaceSettings, getFieldInfo) => {
    const { masterRecTableName, styleSettings: { detailFieldsHeight, detailFieldsPaddingTop } } = workspaceSettings;

    return (detailFieldsHeight + detailFieldsPaddingTop)
        * getWorkspaceRecordsListDetailFields(workspaceSettings, masterRecTableName, getFieldInfo).length;
};

const getDetailFieldsSumHeight = createSelector(
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    state => getFieldInfoSelector(state),
    calcDetailsFieldsheight
);

const getTableViewDetailFieldsSumHeight = createSelector(
    state => getSelectedWorkspaceSettings(state[TABLE_VIEW_PAGE_ALIAS].workspaces),
    state => getFieldInfoSelector(state),
    calcDetailsFieldsheight
);

const getVisiblePlannerDates = createSelector(
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (workspaceSettings) => {
        return {
            visibleDates: {
                startDate: workspaceSettings.startDate,
                endDate: workspaceSettings.endDate
            }
        };
    }
);

const getVisibleEndDateOffset = createSelector(
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (workspaceSettings) => (rangeEnd) => {
        const { masterRecTableName, viewsConfig, viewMode } = workspaceSettings;
        const { hideWeekends = false } = viewsConfig[masterRecTableName];
        const { mode } = viewMode;
        const viewModeOptions = getViewModeOptionsByName(mode);
        const endDateDayOfTheWeek = rangeEnd.get('day');
        const sundayOffset = -2;
        const mondayOffset = -3;
        let endDateOffset = -1;
        const { VIEW_MODE_YEAR, VIEW_MODE_MONTH } = PLANNER_VIEW_MODES;

        if (hideWeekends && isMinorUnitDay(viewModeOptions)) {
            if (endDateDayOfTheWeek == dayValues.monday) {
                endDateOffset = mondayOffset;
            } else if (endDateDayOfTheWeek == dayValues.sunday) {
                endDateOffset = sundayOffset;
            }
        } else if (mode === VIEW_MODE_MONTH || mode === VIEW_MODE_YEAR) {
            endDateOffset = 0;
        }

        return {
            endDateOffset
        };
    }
);

const getDisplayUnassginedCriteriaRolesAsResourcesFlag = workspace =>
    true || workspace.viewsConfig[workspace.masterRecTableName].displayUnassignedCriteriaRolesAsResources;

const getSaveAsNewPlanWorkspaceSelector = createSelector(
    (workspaces) => (getSelectedWorkspaceStructure(workspaces) || {}),
    (workspaces) => (getSelectedWorkspaceGuid(workspaces) || {}),
    (workspaces = {}, newWorkspaceTemplateGuid) => {

        const workspaceColourthemeGuid = workspaces.workspace_colourtheme_guid || null;
        const workspaceCustomColourTheme = workspaces.workspace_custom_colour_field || [];

        return {
            newWorkspaceTemplateGuid,
            workspaceColourthemeGuid,
            workspaceCustomColourTheme
        };
    }
);

const getStoredWorkspaceSettingsSelector = createSelector(
    pageState => getWorkspacesSettings(pageState),
    (workspaces) => (workspaceGuid) => {
        return (workspaces.storedWorkspaceSettings || {})[workspaceGuid] || {};
    }
);

const getRotatedWorkspaceViewSelector = createSelector(
    pageState => getStoredWorkspaceSettingsSelector(pageState),
    (getStoredWorkspaceSettings) => (workspaceGuid, viewTableName) => {
        const storedViewsConfig = (getStoredWorkspaceSettings(workspaceGuid).viewsConfig || {})[viewTableName] || {};

        return !isEmptyObject(storedViewsConfig);
    }
);

export {
    getWorkspacesGuids,
    getLoadedWorkspacesGuids,
    getIsSingleDaySelector,
    getWorkspaceSettings,
    getWorkspacesSettings,
    getSelectedWorkspace,
    getIsPublicWorkspace,
    getOrderedPrivateWorkspaces,
    getOrderedPublicWorkspaces,
    getMostRecentlyUsedWorkspaces,
    getSelectedWorkspaceGuid,
    getSelectedWorkspaceSettings,
    getCurrentWSSettingsSelector,
    getWorkspaceMasterRecTableName,
    getWorkspaceSubRecTableName,
    getWorkspaceTableSelections,
    getWorkspaceRecordsListConfig,
    getWorkspaceRecordsListSortOrder,
    getDefaultWorkspaceStructure,
    getWorkspaceGuid,
    getWorkspaceStructure,
    getWorkspaceColorSchemeInfo,
    getSelectedWorkspaceStructure,
    getWorkspaceRecordsListDetailFields,
    getWorkspaceStructureFromChangesCollection,
    getWorkspaceSettingsFromChangesCollection,
    getWorkspacesForInsert,
    workspaceSettingsChanged,
    getWorkspacesSettingsStructure,
    workspaceBelongsToResource,
    getWorkspaceIsEditable,
    getWorkspaceMasterRecPlannerDataGuid,
    getWorkspaceSubRecPlannerDataGuid,
    getWorkspaceBarsDisplayFieldsSelector,
    getWorkspacesStructureMap,
    getManageMyPlansWindowStaticMessagesSelector,
    getWorkspaceCurrentColourSchemeObjSelector,
    getUIBarsDisplayFieldsSelector,
    getWorkspaceFromSurrogateId,
    getInitWorkspaceToSelect,
    getWorkspaceLoadedFields,
    getBarGroupsGuidByTable,
    getCellGroupsGuidByTable,
    getBarDetailsFieldsSelector,
    getDetailFieldsSumHeight,
    getTableViewDetailFieldsSumHeight,
    getVisiblePlannerDates,
    getVisibleEndDateOffset,
    getDisplayUnassginedCriteriaRolesAsResourcesFlag,
    getSaveAsNewPlanWorkspaceSelector,
    getStoredWorkspaceSettingsSelector,
    getRotatedWorkspaceViewSelector
};