import { createSelector } from 'reselect';

const selectedFeatureFlagsState = (state) => state.featureManagement;

export const getFeaturesSelector = createSelector(
    [selectedFeatureFlagsState],
    (featureManagement) => featureManagement?.features || []
);

export const getFeatureFlagSelector = (featureName) => createSelector(
    [getFeaturesSelector],
    (features) => {
        const feature = features.find((feature) => feature.name.toLowerCase() === featureName.toLowerCase());

        return feature ? feature.enabled : false;
    }
);
