import { createSelector } from 'reselect';
import { EDUCATION } from '../constants/educationSectionConsts';
import { EXPERIENCE } from '../constants/experienceSectionConstants';
import { FEATURE_FLAGS, SKILL_PREFERENCE_TYPES, TABLE_NAMES, URL_PARAMS, FILTER_FIELD_NAMES } from '../constants/globalConsts';
import { getData } from '../utils/commonUtils';
import { getTranslationsSelector } from './internationalizationSelectors';
import { CME } from '../constants/cMeSectionConsts';
import { DATE_TIME_UNITS, differenceInUnits, parseToUtcDate } from '../utils/dateUtils';
import { HIDE_RECOMMENDATION_ALERT_IN_DAYS } from '../constants/talentProfileConsts';
import { RECOMMENDATION_VIEWED } from '../constants/fieldConsts';
import { getApplicationUserId } from './applicationUserSelectors';
import { getResourceSkillsSelector } from './resourceSkillsSelectors';
import { getApplicationAccessSelector } from './userEntityAccessSelectors';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { EDIT_FNAS_PER_TABLENAME } from '../constants/tablesConsts';
import { getFeatureFlagSelector } from './featureManagementSelectors';

export const getTPResourceId = ({ talentProfilePage }) => {
    return (talentProfilePage.pageState.params || {}).resourceId;
};

export const getTPResourceSurrogateId = ({ talentProfilePage }) => {
    if (talentProfilePage.pageState.params) {
        return (talentProfilePage.pageState.params || {})[URL_PARAMS.RESOURCE_SURROGATE_ID];
    }
};

export const getTPEntity = ({ talentProfilePage }) => {
    return talentProfilePage.profileData.entity;
};

export const getTPNavSectionLinks = ({ talentProfilePage }) =>
    talentProfilePage.navSectionLinks;

export const getTPUIEntity = ({ talentProfilePage }) => {
    return talentProfilePage.profileData.uiEntity;
};

export const getTPConfig = ({ talentProfilePage }) => {
    return talentProfilePage.config || {};
};

export const getTPResumeConfig = (state) => {
    return getTPConfig(state).ProfileHeaderSection;
};

export const getQuickRefConfig = (state) => {
    return getTPConfig(state).QuickReferenceArea;
};

export const getProfileSectionsConfig = (state) => {
    return getTPConfig(state).ProfileSections || [];
};

export const getProfileMultyFieldsLists = ({ config }) => {
    return [config.ProfileHeaderSection.HeaderFieldGroup.Fields.map(field => field.FieldName)];
};

export const getEditLinkedFields = (config, name) => {
    const allLinks = getProfileMultyFieldsLists({ config });
    let fieldEditLinks = null;

    for (let i = 0; i < allLinks.length; i++) {
        if (0 <= allLinks[i].indexOf(name)) {
            fieldEditLinks = allLinks[i];
            break;
        }
    }

    return fieldEditLinks;
};

const skillsSectionFilter = section => section.DisplayType === 'Skills';

export const getProfileSkillsSectionConfig = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.find(skillsSectionFilter) || {};
};

const recentWorkSectionFilter = section => section.DisplayType === 'RecentWork';

export const getProfileRecentWorkSectionConfig = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.find(recentWorkSectionFilter) || {};
};

export const getProfileEducationSectionConfig = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.find(item => item.DisplayType === EDUCATION) || {};
};

export const getProfileExperienceSectionConfig = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.find(item => item.DisplayType === EXPERIENCE) || {};
};

export const getProfileCMeSectionConfig = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.find(item => item.DisplayType === CME) || {};
};

export const profileSkillsSectionVisible = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.some(skillsSectionFilter);
};

export const profileRecentWorkSectionVisible = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.some(recentWorkSectionFilter);
};

export const profileEducationSectionVisible = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.some(item => item.DisplayType === EDUCATION);
};

export const profileExperienceSectionVisible = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.some(item => item.DisplayType === EXPERIENCE) || {};
};

export const profileCMeSectionVisible = (state) => {
    const sections = getProfileSectionsConfig(state);

    return sections.some(item => item.DisplayType === CME) || {};
};

export const talentProfileNavSectionLinksSelector = createSelector(
    getTPNavSectionLinks,
    (navSectionLinks) => {
        return Object.keys(navSectionLinks)
            .map(key => ({ id: key, ...navSectionLinks[key] }))
            .sort((a, b) => {
                if (a.index > b.index) {
                    return 1;
                } else if (a.index < b.index) {
                    return -1;
                }

                return 0;
            });
    }
);

export const talentProfileLinkedCollectionsSelector = createSelector(
    ({ talentProfilePage }) => talentProfilePage.profileLinkedData,
    (profileLinkedData) => {
        return [
            ...Object.keys(profileLinkedData).filter(key => key != 'loading').map(tableDataKey => profileLinkedData[tableDataKey])
        ];
    }
);

export const getTalentProfileStaticMessages = createSelector(
    state => state,
    (state) => (translationIds) => {
        const translationSectionName = 'talentProfilePage';
        const translationProps = { sectionName: translationSectionName, idsArray: translationIds };

        return getTranslationsSelector(state, translationProps);
    }
);

export const getProfileCommandBarMessages = createSelector(
    (state) => {
        const translationIds = ['profileTitle', 'shareProfileCaption', 'uploadLabel', 'viewOtherProfile', 'viewMyProfile', 'cMeProfileTitle', 'editDetailsLabel'];

        return getTalentProfileStaticMessages(state)(translationIds);
    },
    (messages) => messages
);

export const getAvatarStaticMessages = createSelector(
    (state) => {
        const translationIds = ['changeProfielPictureText'];

        return getTalentProfileStaticMessages(state)(translationIds);
    },
    (messages) => messages
);

export const getTalentProfileConstanstTranslations = createSelector(
    (state) => {
        const translationIds = ['markDeletedMessage', 'cancelDeletionMessage'];

        return getTalentProfileStaticMessages(state)(translationIds);
    },
    (messages) => messages
);

export const getUpdateAvatarWindowStaticMessages = createSelector(
    (state) => {
        const translationSubsection = 'updateAvatarWindowMessages';
        const translationIds = [translationSubsection];
        const translation = getTalentProfileStaticMessages(state)(translationIds);

        return translation && translation[translationSubsection] ? translation[translationSubsection] : {};
    },
    (messages) => messages
);

export const getProfileSection = createSelector(
    state => state.talentProfilePage,
    (talentProfilePage) =>
        (sectionKey) =>
            getProfileSectionsConfig({ talentProfilePage })
                .find(({ SectionKey }) => SectionKey === sectionKey) || {}
);

export const getProfileSectionActiveMessages = createSelector(
    getProfileSection,
    (getProfileSection) =>
        (sectionKey) => {
            const {
                messages = {}
            } = getProfileSection(sectionKey);

            return Object
                .keys(messages)
                .filter(id => messages[id].active)
                .map(id => messages[id]);
        }
);

export const getTalentProfileMessages = createSelector(
    (state) => {
        const translationIds = ['messages'];

        return getTalentProfileStaticMessages(state)(translationIds);
    },
    (staticMessages) => staticMessages.messages
);

export const getTalentProfileResourceEntity = createSelector(
    getTPEntity,
    state => state.talentProfilePage,
    (state, entityId) => entityId,
    (tPEntity, talentProfilePage, entityId) => {
        const dataCollectionsObj = talentProfilePage.profileLinkedData;
        const dataCollections = Object.keys(dataCollectionsObj).map(tableDataKey => dataCollectionsObj[tableDataKey]);

        return { ...tPEntity, ...getData(dataCollections, TABLE_NAMES.RESOURCE, entityId), resource_description: talentProfilePage.pageState.params.resourceName };
    }
);

export const getTalentProfileFieldAutocompleteValueSelector = createSelector(
    state => state.talentProfilePage.autoComplete || {},
    (autoComplete) => (fieldName) => (autoComplete[fieldName] || {}).enteredText || ''
);

export const getResourceAuditSectionSelector = createSelector(
    state => state.talentProfilePage.resourceUpdateAuditInfo,
    resourceUpdateAuditInfo => auditType => resourceUpdateAuditInfo[auditType]
);

export const getResourceManager = (state) => {
    const tPEntity = getTPEntity(state);

    return tPEntity?.resource_manager_resource_guid ?? null;
};

export const getResourceSkillApprovalPermission = (state) => {
    const tPEntity = getTPEntity(state);

    return tPEntity?.resource_skill_approval_access ?? null;
};


export const getSecurityProfileId = createSelector(
    (state) => state.applicationUser.permissions || {},
    (permissions) => permissions.find(x => x.type === 'ResourceRoleGuid').value

);

export const getRecommendationLastViewed = createSelector(
    getTPEntity,
    (tPEntity) => tPEntity?.[RECOMMENDATION_VIEWED]
);

export const showRecommendationAlert = createSelector(
    getRecommendationLastViewed,
    state => getTPResourceId(state),
    state => getApplicationUserId(state),
    state => getTPEntity(state),
    state => getResourceSkillsSelector(state),
    state => getApplicationAccessSelector(state),
    state => getFeatureFlagSelector(FEATURE_FLAGS.RECOMMENDATIONS)(state),
    (lastViewed, tpResourceId, loggedInResourceId, entity, getResourceSkills, userEntityAccessWrapper, recommendationsEnabled) => {
        const resourceSkills = getResourceSkills(tpResourceId);
        const tableName = TABLE_NAMES.RESOURCE;
        const hasEditResourceAccess = userEntityAccessWrapper(tableName, [tpResourceId], ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName]);
        const hasPrimarySkill = Object.values(resourceSkills.skills.map).some(x => x.skillPreference === SKILL_PREFERENCE_TYPES.PRIMARY_SKILL);
        const hasJobTitle = entity?.resource_rolename;
        const loggedInUser = tpResourceId === loggedInResourceId;
        // Show banner only if one of them exists and is Logged In User's profile and has access to edit profile and recommendation is enabled
        if ((hasPrimarySkill || hasJobTitle) && loggedInUser && hasEditResourceAccess && recommendationsEnabled) {
            if (lastViewed) {
                const currentDay = differenceInUnits(parseToUtcDate(new Date()), parseToUtcDate(lastViewed), DATE_TIME_UNITS.DAY);

                return currentDay > HIDE_RECOMMENDATION_ALERT_IN_DAYS;
            }

            return lastViewed === null;
        }

        return false;


    }
);

export const getManagerIdAndName = createSelector(
    state => state.applicationUser,
    (applicationUser) => {
        const managerId = applicationUser[`${FILTER_FIELD_NAMES.RESOURCE_MANAGER}`];
        const managerName = applicationUser[`${FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME}`];

        return { managerId, managerName };
    }
);

//selector to check if the resource id manager is same as the logged in user
export const isLoggedInUser = createSelector(
    getTPResourceId,
    getApplicationUserId,
    (resourceId, loggedInUserId) => {
        return resourceId === loggedInUserId;
    }
);


//selector to check if the logged in user is a manager
export const isLoggedInUserManager = createSelector(
    getTPResourceId,
    getManagerIdAndName,
    (resourceId, { managerId }) => {
        return resourceId === managerId;
    }
);

//selector to check if the logged in user is allowed to view resource skills approval
export const isAllowedToViewResourceSkillsApproval = createSelector(
    isLoggedInUser,
    isLoggedInUserManager,
    (isUser, isManager) => {
        return isUser || isManager;
    }
);

