import { createSelector } from 'reselect';
import { getTranslationsSelector } from './internationalizationSelectors';
import { getTalentProfileStaticMessages } from './talentProfileSelectors';
import { DEFAULT_MAX_UPLOAD_ALLOWED_WITH_TALENT_PROFILE_PAGE_FEATURE_FLAG_ENABLED } from '../constants/attachmentsConsts';

export const getModuleAttachments = createSelector(
    state => state.attachments.map,
    state => state.moduleName,
    (attachments, moduleName) => attachments[moduleName] || {}
);

export const getEntityAttachments = createSelector(
    getModuleAttachments,
    state => state.entityId,
    (moduleAttachments, entityId) => (moduleAttachments[entityId] || {}).attachments || {}
);

export const getAttachmentsChanges = createSelector(
    getModuleAttachments,
    state => state.entityId,
    (moduleAttachments, entityId) => (moduleAttachments[entityId] || {}).attachmentChanges || {
        updates: {
            map: {},
            orderedKeys: []
        },
        inserts: {
            map: {},
            orderedKeys: []
        },
        deletes: {
            map: {},
            orderedKeys: []
        }
    }
);

export const attachmentsHaveChanges = createSelector(
    getAttachmentsChanges,
    ({ inserts, updates, deletes }) => !!(inserts.orderedKeys.length
        || updates.orderedKeys.length
        || deletes.orderedKeys.length)
);

export const getAttachmentInsertsMap = createSelector(
    getAttachmentsChanges,
    ({ inserts }) => inserts.map
);

export const getAttachmentDeletesMap = createSelector(
    getAttachmentsChanges,
    ({ deletes }) => deletes.map
);

export const getUIEntityAttachmentIds = createSelector(
    getEntityAttachments,
    getAttachmentInsertsMap,
    getAttachmentDeletesMap,
    (entityAttachments, inserts, deletes) => [
        ...Object.keys(entityAttachments).filter(id => !deletes[id]),
        ...Object.keys(inserts)
    ]
);

export const createGetUIEntityAttachmentIds = () => createSelector(
    getEntityAttachments,
    getAttachmentInsertsMap,
    getAttachmentDeletesMap,
    (entityAttachments, inserts, deletes) => [
        ...Object.keys(entityAttachments).filter(id => !deletes[id]),
        ...Object.keys(inserts)
    ]
);

export const getEntityAttachmentData = createSelector(
    state => state.attachmentId,
    getEntityAttachments,
    (attachmentId, entityAttachments) => entityAttachments[attachmentId] || {}
);

export const getEntityAttachmentsData = createSelector(
    getEntityAttachments,
    (entityAttachments) => Object.values(entityAttachments)
);

export const getUIEntityAttachmentsData = createSelector(
    getEntityAttachmentsData,
    getAttachmentInsertsMap,
    getAttachmentDeletesMap,
    (entityAttachments, insertsMap, deletesMap) => [
        ...Object.values(insertsMap),
        ...entityAttachments.filter(({ id }) => !deletesMap[id])
    ]
);

export const getEntityAttachmentSize = createSelector(
    getEntityAttachmentData,
    (entityAttachmentData) => entityAttachmentData.size
);

export const getEntityAttachmentConfig = createSelector(
    state => state.attachments.config,
    state => state.tableName,
    state => state.talentProfilePageTransformedEnabled,
    (config, tableName, talentProfilePageTransformedEnabled) => {
        // Compute updated resource based on feature flag
        const newResource = talentProfilePageTransformedEnabled && config.resource
            ? {
                ...config.resource,
                maxCount: DEFAULT_MAX_UPLOAD_ALLOWED_WITH_TALENT_PROFILE_PAGE_FEATURE_FLAG_ENABLED
            }
            : config.resource;

        // Return updated config with modified resource if applicable
        const updatedConfig = {
            ...config,
            resource: newResource
        };

        return updatedConfig[tableName] || {};
    }
);

export const getEntityAttachmentLoadingState = createSelector(
    state => state.attachments.map,
    state => state.moduleName,
    state => state.entityId,
    (attachmentsMap, moduleName, entityId) => ((attachmentsMap[moduleName] || {})[entityId] || {}).loading
);

export const isUploadButtonDisabled = createSelector(
    getEntityAttachmentConfig,
    getUIEntityAttachmentsData,
    getEntityAttachmentLoadingState,
    (attachmentConfig, UIAttachments, loading) => UIAttachments.length >= attachmentConfig.maxCount || loading
);

export const getAttachmentsStaticMessagesSelector = createSelector(
    state => getTranslationsSelector(state, { sectionName: 'attachmentsMessages' }),
    translations => translations
);

export const isAttachmentDownloadDisabled = createSelector(
    getAttachmentInsertsMap,
    state => state.attachmentId,
    (inserts, attachmentId) => inserts[attachmentId]
);

/**
 * The selector is used to retrieve static messages for the upload documents window.
 */
export const getUploadDocumentsWindowStaticMessagesSelector = createSelector(
    (state) => {
        const translationSubsection = 'uploadDocumentsWindowMessages';
        const translationIds = [translationSubsection];

        const translation = getTalentProfileStaticMessages(state)(translationIds);

        return translation?.[translationSubsection] ?? {};
    },
    (messages) => messages
);
