import { isEqual, omit, unionBy } from 'lodash';
import { createSelector, createSelectorCreator, defaultMemoize } from 'reselect';
import { PlannerPageHideRecordsOptions } from '../components/planner/commandBar/hideRecordsOptions';
import { DateRangePicker } from '../components/planner/dateBar/dateRangePicker';
import { GoToDate } from '../components/planner/dateBar/goToDate';
import { ConnectedBarOptions } from '../connectedComponents/connectedBarOptions';
import { ConnectedTableViewCommandBarSwitch } from '../connectedComponents/connectedCommandBar/connectedBarElements/connectedCommandBarSwitch';
import { SORT_ASCENDING, TABLE_NAMES } from '../constants';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES } from '../constants/commandBarConsts';
import { DEFAULT_TABLE_VIEW_FILTERS_NAME, DEFAULT_TABLE_VIEW_WORKSPACE_NAME, TABLE_VIEW_PAGE_ACTIONS, TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS } from '../constants/tableViewPageConsts';
import { CountBadge } from '../lib';
import RadioGroupElement from '../lib/commandBar/radioGroupElement';
import { VerticalDivider } from '../lib/commandBar/verticalDivider';
import { getData } from '../utils/commonUtils';
import { getAppliedFiltersCount } from '../utils/filtersUtils';
import { getPlannerFieldBlankValueSelector } from './blankValuesSelectors';
import { getTableViewCBConfigItemsAdditionalPropsSelector } from './commandBarSelectors';
import { createDataFn, getPagedDataObjectBase, getRlConfigBase, getSubRecDataBase, getRlDisplayDataBase, getRlDisplayFieldsBase, isPlannerFieldLoaded } from './commonPlannerPageSelectors';
import { getSelectedEntitiesSelector, getPageSelectedViewFiltersSelector } from './commonSelectors';
import { getFilterPaneCollapsed } from './filterPaneSelectors';
import { getTranslationsSelector } from './internationalizationSelectors';
import { getDataRows } from './recordsListSelectors';
import { getFieldInfoSelector } from './tableStructureSelectors';
import { getSelectedWorkspaceGuid, getSelectedWorkspaceSettings, getWorkspaceRecordsListDetailFields, getWorkspaceRecordsListSortOrder } from './workspaceSelectors';
import { isInfiniteScrollAllowed, isUnassignedRow } from '../utils/plannerDataUtils';
import { getTableViewPaginationMode } from '../utils/tableViewDataUtils';
import { MAXIMUM_INFINITE_SCROLL_ROWS_AMOUNT } from '../constants/paginationConsts';
import { getFeatureFlagSelector } from './featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';
import { RESOURCE_DESCRIPTION, calcFields } from '../constants/fieldConsts';
import { populateStringTemplates } from '../utils/translationUtils';

const translationProps = {
    sectionName: 'plannerPage',
    idsArray: [
        'recordsList'
    ]
};

const createDeepEqualSelector = createSelectorCreator(
    defaultMemoize,
    isEqual
);

export const getTableViewCommandBarActionProps = createDeepEqualSelector(
    state => getSelectedWorkspaceGuid(state[TABLE_VIEW_PAGE_ALIAS].workspaces),
    state => getSelectedWorkspaceSettings(state[TABLE_VIEW_PAGE_ALIAS].workspaces),
    state => {
        const { filtersGuid } = getSelectedWorkspaceSettings(state[TABLE_VIEW_PAGE_ALIAS].workspaces);

        return state[TABLE_VIEW_PAGE_ALIAS].filters[filtersGuid];
    },
    state => getSelectedEntitiesSelector(state, TABLE_VIEW_PAGE_ALIAS),
    state => getTableViewCBConfigItemsAdditionalPropsSelector(state),
    state => state[TABLE_VIEW_PAGE_ALIAS].tableName,
    (
        workspaceGuid,
        workspaceSettings,
        filters,
        selectedEntities,
        menuItemsAdditionalProps,
        tableName
    ) => {
        const {
            filtersGuid,
            masterRecTableName,
            subRecTableName,
            plannerDataGuid,
            startDate,
            endDate,
            viewMode,
            detailsPaneGuid,
            viewsConfig
        } = workspaceSettings;

        const {
            hideHistoricRecords = true,
            hideFutureRecords = true,
            hideInactiveResources = true
        } = viewsConfig[masterRecTableName];


        const { selection, hidden: filterHidden = false } = ((filters || {}).views || {})[(filters || {}).selectedView] || {};
        const appliedFiltersCount = getAppliedFiltersCount(selection);

        const badgeComponentsByActionKey = {
            [TABLE_VIEW_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: {
                render: CountBadge,
                props: {
                    count: appliedFiltersCount
                }
            }
        };

        const { tableName: entitiesTableName, ids = [] } = selectedEntities;

        const components = {
            RadioGroupElement: RadioGroupElement,
            [COMMAND_BAR_MENUS_COMPONENT_TYPES.DIVIDER]: VerticalDivider,
            [COMMAND_BAR_MENUS_COMPONENT_TYPES.GO_TO_DATE]: GoToDate,
            [COMMAND_BAR_MENUS_COMPONENT_TYPES.DATE_RANGE_PICKER]: DateRangePicker,
            [COMMAND_BAR_MENUS_COMPONENT_TYPES.BAR_OPTIONS]: ConnectedBarOptions,
            [COMMAND_BAR_MENUS_COMPONENT_TYPES.HIDE_RECORDS_OPTIONS]: PlannerPageHideRecordsOptions,
            [COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH]: ConnectedTableViewCommandBarSwitch
        };

        return {
            workspaceGuid,
            filtersAlias: TABLE_VIEW_PAGE_FILTER_ALIAS,
            filtersGuid,
            plannerDataGuid,
            masterRecTableName,
            subRecTableName,
            entitiesIds: ids,
            tableName,
            entitiesTableName: ids.length && entitiesTableName,
            startDate,
            endDate,
            viewMode,
            components,
            badgeComponentsByActionKey,
            filterHidden,
            detailsPaneGuid,
            menuItemsAdditionalProps,
            hideRecordsOptionsActionProps: {
                workspaceGuid,
                subRecTableName,
                hideHistoricRecords,
                hideFutureRecords,
                hideInactiveResources
            }
        };
    }
);

export const getTableViewPageFiltersGuid = (state) => {
    const workspaceSettings = getSelectedWorkspaceSettings(state.tableViewPage.workspaces);

    return workspaceSettings.filtersGuid;
};

export const getTableViewPageFilters = createSelector(
    state => getPageSelectedViewFiltersSelector(state, TABLE_VIEW_PAGE_ALIAS),
    (filters) => filters || {}
);

export const getTableViewPageFilterPaneCollapsed = (state) => getFilterPaneCollapsed(getTableViewPageFilters(state));

export const getMergedFilterSettingsWorkspaceObject = createSelector(
    state => (state[TABLE_VIEW_PAGE_ALIAS].filters || {})[DEFAULT_TABLE_VIEW_FILTERS_NAME],
    (currentFilterSettings) => (defaultState) => {
        const { guid, title, views, selectedView } = currentFilterSettings || {};
        const { filterSettings: defaultFilterSettings } = defaultState[DEFAULT_TABLE_VIEW_WORKSPACE_NAME];

        return {
            guid: guid || (defaultFilterSettings || {}).guid || '',
            title: title || (defaultFilterSettings || {}).title || '',
            views: views || (defaultFilterSettings || {}).views || {},
            selectedView: selectedView || (defaultFilterSettings || {}).selectedView || 'resource'
        };
    }
);

export const getCurrentTableViewData = createSelector(
    state => state[TABLE_VIEW_PAGE_ALIAS].tableViewData,
    state => getSelectedWorkspaceSettings(state[TABLE_VIEW_PAGE_ALIAS].workspaces),
    (tableViewData, workspaceSettings) => {
        return {
            ...omit(tableViewData[workspaceSettings.tableViewDataGuid], ['rows', 'rowsById']),
            rows: getDataRows(tableViewData[workspaceSettings.tableViewDataGuid])
        };
    }
);

export const getCurrentTableViewGridData = createSelector(
    state => state[TABLE_VIEW_PAGE_ALIAS].tableViewData,
    state => getSelectedWorkspaceSettings(state[TABLE_VIEW_PAGE_ALIAS].workspaces),
    (tableViewData, workspaceSettings) => {
        return {
            ...tableViewData[workspaceSettings.tableViewDataGuid]
        };
    }
);

export const getTableViewRlConfig = createSelector(
    state => getSelectedWorkspaceSettings(state.tableViewPage.workspaces),
    getFieldInfoSelector,
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    getRlConfigBase
);

export const getCurrentTableViewSubRecDataObject = createSelector(
    tableViewPage => tableViewPage.subRecTableViewData,
    tableViewPage => (getSelectedWorkspaceSettings(tableViewPage.workspaces) || {}).subRecTableViewDataGuid,
    getSubRecDataBase
);

export const getCurrentTableViewSubRecData = createSelector(
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).data,
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).byId,
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).loading,
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).tableName,
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).tableNames,
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).count,
    createDataFn
);

export const getCurrentTableViewPagedDataObject = createSelector(
    tableViewPage => tableViewPage.pagedMasterRecTableViewData,
    tableViewPage => (getSelectedWorkspaceSettings(tableViewPage.workspaces) || {}).pagedMasterRecTableViewDataGuid || '',
    getPagedDataObjectBase
);

export const getCurrentTableViewPagedData = createSelector(
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).data,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).byId,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).loading,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).tableName,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).tableNames,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).count,

    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).guid,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).startElement,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).scrollKey,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).loadedPages,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).loadedPagesMap,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).pageSize,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).rowCount,
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).key,
    createDataFn
);

export const getTableViewPageDataCollections = createSelector(
    //state => getCurrentPlannerBarGroups(state.tableViewPage),
    state => getCurrentTableViewPagedData(state.tableViewPage),
    state => getCurrentTableViewSubRecData(state.tableViewPage),
    state => state.tableViewPage.tableViewTableDatas,
    (pagedData, subRecData, tableViewTableDatas = {}) => {
        return [
            // ...Object.values(barGroups),
            pagedData,
            subRecData,
            ...Object.keys(tableViewTableDatas).map(tableDataKey => tableViewTableDatas[tableDataKey])
        ];
    }
);

export const getRlTableViewDataSelector = createSelector(
    getTableViewPageDataCollections,
    (dataCollections) => (tableName, id) => getData(dataCollections, tableName, id)
);

const getStaticMessagesSelector = createSelector(
    state => state.internationalization,
    (internationalization) => {
        const messages = getTranslationsSelector({ internationalization }, { sectionName: 'tableViewPage', idsArray: ['recordsList'] });

        return messages.recordsList;
    }
);

export const getTableViewRlDisplayFieldsSelector = createSelector(
    state => getSelectedWorkspaceSettings(state.tableViewPage.workspaces),
    state => state.applicationSettings,
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    getRlDisplayFieldsBase
);

export const getTableViewRlDisplayDataSelector = createSelector(
    getRlTableViewDataSelector,
    getFieldInfoSelector,
    getPlannerFieldBlankValueSelector,
    getStaticMessagesSelector,
    getRlDisplayDataBase
);

export const getTableViewRlIsFieldLoadedSelector = createSelector(
    state => state.tableViewPage.fieldOptions,
    state => getSelectedWorkspaceGuid(state.tableViewPage.workspaces),
    (fieldOptions, workspaceGuid) => (fieldName) => isPlannerFieldLoaded(fieldOptions, workspaceGuid, fieldName)
);

export const getWaitTableView = createSelector(
    state => state.tableViewPage.loadingState,
    (loadingStates) => Object.keys(loadingStates).reduce((accumulator, currentValue) => accumulator = accumulator || loadingStates[currentValue], false)
);

export const getTableViewRlOrder = createSelector(
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.tableViewPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.masterRecTableName);
    },
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.tableViewPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.subRecTableName);
    },
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    state => getSelectedWorkspaceSettings(state.tableViewPage.workspaces),
    (masterRecSortOrder, subRecSortOrder, calcFieldsEnabled, wsSettings) => {

        if (masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE) {
            if (!calcFieldsEnabled) {
                const masterHasCalcFields = masterRecSortOrder.orderFields.some(field => calcFields.includes(field.field));

                if (masterHasCalcFields) {
                    masterRecSortOrder.orderFields = masterRecSortOrder.orderFields.filter(field => !calcFields.includes(field.field));
                    masterRecSortOrder.orderFields = [{ field: RESOURCE_DESCRIPTION, order: SORT_ASCENDING }];
                }
            }
        }

        return {
            masterRecSortOrder,
            subRecSortOrder
        };
    }
);

export const getTableViewParentRecordConfig = createSelector(
    state => getTableViewRlConfig(state).config,
    (config) => config.parentDisplayFields
);

export const getTableViewChildRecordConfig = createSelector(
    state => getTableViewRlConfig(state).config,
    (config) => config.childDisplayFields
);

export const getTableViewRlSelectedRecords = createSelector(
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage).currentEdits,
    tableViewPage => getCurrentTableViewSubRecDataObject(tableViewPage).currentEdits,
    //tableViewPage => getCurrentPlannerBarSelection({tableViewPage}),
    (parent, child/*, bars*/) => {
        return {
            parent,
            child,
            bars: {}
        };
    }
);

export const getTableViewPageFieldOptions = (tableViewPage, workspaceGuid) => {
    return tableViewPage.fieldOptions[workspaceGuid] || {};
};

export const getTableViewMasterRecCollectionAlias = (workspaceSettings) => {
    return workspaceSettings.pagedMasterRecTableViewDataGuid;
};

export const getTableViewSubRecCollectionAlias = (workspaceSettings) => {
    return workspaceSettings.subRecTableViewDataGuid;
};

export const getTableViewWorkspaceLoadedFields = createSelector(
    (workspaceSettings, getFieldInfo) => workspaceSettings.masterRecTableName,
    (workspaceSettings, getFieldInfo) => workspaceSettings.subRecTableName,
    (workspaceSettings, getFieldInfo) => workspaceSettings.viewsConfig,
    (workspaceSettings, getFieldInfo) => workspaceSettings.styleSettings,
    (workspaceSettings, getFieldInfo) => getFieldInfo,
    (masterRecTableName, subRecTableName, viewsConfig, styleSettings, getFieldInfo) => {

        const masterRecFields = getWorkspaceRecordsListDetailFields({ viewsConfig, styleSettings, masterRecTableName }, masterRecTableName, getFieldInfo).map(data => data.field);
        const subRecFields = getWorkspaceRecordsListDetailFields({ viewsConfig, styleSettings, masterRecTableName }, subRecTableName, getFieldInfo).map(data => data.field);


        return [...masterRecFields, ...subRecFields];
    }
);

export const getUnassginedParentRowsCount = createSelector(
    (state, tableViewPageDataGuid) => state.tableViewPage.tableViewData[tableViewPageDataGuid].rows,
    (rows) => rows.filter(({ tableName, id }) => isUnassignedRow(tableName, id) || tableName === TABLE_NAMES.ROLEREQUEST).length
);

export const getTableViewPageData = createSelector(
    state => state.tableViewPage,
    (tableViewPage) => tableViewPage.tableViewData
);

export const getTableViewPageScrollSettings = createSelector(
    state => getTableViewPageData(state),
    (tableViewPageData) => (tableViewPageDataGuid) => tableViewPageData[tableViewPageDataGuid].scrollSettings
);

export const getWaitTableViewPage = createSelector(
    state => state.tableViewPage.loadingState,
    (loadingStates) => Object.keys(loadingStates).reduce((accumulator, currentValue) => accumulator = accumulator || loadingStates[currentValue], false)
);

export const getCurrentTableViewPageNumber = createSelector(
    tableViewPage => tableViewPage.uiOptions,
    (uiOptions) => uiOptions.pageNumber
);

export const getCurrentPagePlannerData = createSelector(
    tableViewPage => getCurrentTableViewPagedDataObject(tableViewPage),
    tableViewPage => getCurrentTableViewPageNumber(tableViewPage),
    (currPagedMasterRecTableViewData, currentPageNumber) => {
        const { from, to } = currPagedMasterRecTableViewData.loadedPagesMap[currentPageNumber - 1] || {};

        return currPagedMasterRecTableViewData.data.slice(from, to);
    }
);

export const isTableViewPageGuidDataLoading = createSelector(
    tableViewPage => tableViewPage.pagedMasterRecTableViewData,
    tableViewPage => tableViewPage.subRecTableViewData,
    tableViewPage => tableViewPage.filters,
    tableViewPage => tableViewPage.tableViewData,
    tableViewPage => getSelectedWorkspaceSettings(tableViewPage.workspaces),
    (pagedMasterRecTableViewData = {}, subRecTableViewData = {}, filters = {}, tableViewData = {}, workspaceSettings = {}) => {
        const { subRecTableViewDataGuid, tableViewDataGuid, pagedMasterRecTableViewDataGuid } = workspaceSettings;

        return (pagedMasterRecTableViewData[pagedMasterRecTableViewDataGuid] || {}).loading
        || (subRecTableViewData[subRecTableViewDataGuid] || {}).loading
        || filters.loading
        || (tableViewData[tableViewDataGuid] || {}).loading;
        /*|| tableViewPage.detailsPane.loading*/ // Uncomment once details pane is implemented
    }
);

export const getExpandedRowsSelector = createSelector(
    state => getCurrentTableViewData(state).rowsExpanded,
    (rowsExpanded) => {
        return Object.entries(rowsExpanded)
            .filter(([_, value]) => value)
            .map(([key, _]) => key);
    }
);

export const getShouldHideTableViewPaginationSelector = createSelector(
    state => isTableViewPageGuidDataLoading(state.tableViewPage),
    (isTableViewLoading) => (rowCount, pageSize) => {
        return isTableViewLoading
        || rowCount <= pageSize
        || isInfiniteScrollAllowed(rowCount, MAXIMUM_INFINITE_SCROLL_ROWS_AMOUNT, getTableViewPaginationMode);
    }
);

export const getJobTableViewData = createSelector(
    tableViewPage => (getSelectedWorkspaceSettings(tableViewPage.workspaces) || {}).pagedMasterRecTableViewDataGuid,
    tableViewPage => (getSelectedWorkspaceSettings(tableViewPage.workspaces) || {}).subRecTableViewDataGuid,
    tableViewPage => (getSelectedWorkspaceSettings(tableViewPage.workspaces) || {}).masterRecTableName,
    tableViewPage => tableViewPage.pagedMasterRecTableViewData,
    tableViewPage => tableViewPage.subRecTableViewData,
    (pagedMasterRecTableViewDataGuid, subRecTableViewDataGuid, masterRecTableName, pagedMasterRecTableViewData, subRecTableViewData) => {
        let jobDataCollection = {};

        if (masterRecTableName === TABLE_NAMES.JOB) {
            const tableViewDataGuid = pagedMasterRecTableViewDataGuid;
            jobDataCollection = pagedMasterRecTableViewData[tableViewDataGuid];
        } else {
            const tableViewDataGuid = subRecTableViewDataGuid;
            jobDataCollection = subRecTableViewData[tableViewDataGuid];
        }

        return jobDataCollection;
    }
);

export const getTableViewShouldShowFloatingActionBarSelector = createSelector(
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.tableViewPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.masterRecTableName);
    },
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.tableViewPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.subRecTableName);
    },
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    state => state.tableViewPage.workspaces,
    state => getSelectedWorkspaceSettings(state.tableViewPage.workspaces),
    (masterRecSortOrder, subRecSortOrder, calcFieldsEnabled, workspaces, wsSettings) => {

        if (!workspaces.workspacesSettings.showSortFloatingActionBar) {
            return false;
        }
        if (!calcFieldsEnabled) {
            return false;
        }

        const masterHasCalcFields = masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE &&
            masterRecSortOrder.orderFields.some(field => calcFields.includes(field.field));

        const subHasCalcFields = subRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.JOB &&
            subRecSortOrder.orderFields.some(field => calcFields.includes(field.field));

        return masterHasCalcFields || subHasCalcFields;
    }
);

export const getTableViewFloatingActionBarLabelSelector = createSelector(
    state => getTranslationsSelector(state, translationProps).recordsList,
    state => getTranslationsSelector(state, { sectionName: 'common' }),
    state => getTableViewRlOrder(state),
    state => getTableViewRlConfig(state).config,
    state => getSelectedWorkspaceSettings(state.tableViewPage.workspaces),
    (recordsList, commonMessages, recSortOrder, rlConfig, wsSettings) => {
        const { floatingActionBarLabel } = commonMessages;
        const { resourceLabel } = recordsList;
        const { parentDisplayFields, childDisplayFields } = rlConfig;
        const controlFields = unionBy(childDisplayFields.detailFields, parentDisplayFields.detailFields, 'field');

        const masterHasCalcFields = recSortOrder.masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE &&
            recSortOrder.masterRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const subHasCalcFields = recSortOrder.subRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.JOB &&
            recSortOrder.subRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const calculatedFields = unionBy(masterHasCalcFields, subHasCalcFields);
        const currentOrderFieldDetails = calculatedFields.length > 0 && controlFields.filter(field => calculatedFields[0].field === field.field);
        const currentOrderFieldTitle = currentOrderFieldDetails[0]?.title || currentOrderFieldDetails[0]?.field;

        const { floatingActionBarLabel: floatingActionBarLabelText } = populateStringTemplates({ floatingActionBarLabel }, { fieldName: currentOrderFieldTitle, entity: resourceLabel });

        return floatingActionBarLabelText;
    }
);

export const getTableViewIsCalcFieldSortedSelector = createSelector(
    state => getTableViewRlOrder(state),
    state => getTableViewRlConfig(state).config,
    state => getSelectedWorkspaceSettings(state.tableViewPage.workspaces),
    (recSortOrder, rlConfig, wsSettings) => {
        const { parentDisplayFields, childDisplayFields } = rlConfig;
        const controlFields = unionBy(childDisplayFields.detailFields, parentDisplayFields.detailFields, 'field');

        const masterHasCalcFields = recSortOrder.masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE &&
            recSortOrder.masterRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const subHasCalcFields = recSortOrder.subRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.JOB &&
            recSortOrder.subRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const calculatedFields = unionBy(masterHasCalcFields, subHasCalcFields);
        const currentOrderFieldDetails = calculatedFields.length > 0 && controlFields.filter(field => calculatedFields[0].field === field.field)[0];

        return currentOrderFieldDetails;
    }
);