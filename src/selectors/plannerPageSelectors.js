import {
    getSelectedWorkspaceSettings, getSelectedWorkspaceGuid, getWorkspaceR<PERSON>ordsListSortOrder,
    getWorkspaceMasterRecTableName, getWorkspaceSubRecPlannerDataGuid, getWorkspaceMasterRecPlannerDataGuid, getCurrentWSSettingsSelector, getBarGroupsGuidByTable, getDetailFieldsSumHeight, getRotatedWorkspaceViewSelector
} from './workspaceSelectors';
import { getTranslationsSelector } from './internationalizationSelectors';
import { PLANNER_MASTER_REC_ALIAS, PLANNER_SUB_REC_ALIAS, PLANNER_BOOKING_GROUPS_ALIAS, PLANNER_BAR_ACTIONS, PLANNER_ACTIONS, UNASSIGNED_BOOKINGS_RESOURCE, PLANNER_PAGE_ALIAS, PLANNER_PAGE_TABLE_TO_GROUP_MAP, PLANNER_ROLEREQUESTS_ALIAS, BAR_TABLE_NAMES, ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, DEFAULT_RENDER_FROM_VERTICE, PLANNER_ROLEREQUESTSRESOURCE_ALIAS, PLANNER_ROLEREQUESTGROUP_ALIAS, HIDE_BARS_FIELDS_LABEL_DEFAULT_VALUE, PLANNER_VIEW_MODES } from '../constants/plannerConsts';
import { getFilterPaneCollapsed } from './filterPaneSelectors';
import { isEmpty, isEqual, union, unionBy } from 'lodash';
import { createSelector, createSelectorCreator, defaultMemoize } from 'reselect';
import { getPlannerPaginationMode, isInfiniteScrollAllowed, isUnassignedResourceGuid } from '../utils/plannerDataUtils';
import { getFieldInfoSelector, tempGetTableSystemFields } from './tableStructureSelectors';
import { getPlannerFieldBlankValueSelector } from './blankValuesSelectors';
import { getFieldTableName, isTableFieldLinked, isCustomLookupOrPlanningDataField } from '../utils/tableStructureUtils';
import { GoToDate } from '../components/planner/dateBar/goToDate';
import { DateRangePicker } from '../components/planner/dateBar/dateRangePicker';
import { ConnectedBarOptions } from '../connectedComponents/connectedBarOptions';
import { getAvatarAltValueSelector, getAvatarImageURL } from './avatarSelectors';
import { AVATAR_SIZES, UNASSIGNED_AVATAR_ICON } from '../constants/avatarConsts';
import { TABLE_NAMES, DETAILS_PANE_TAB_KEYS } from '../constants';
import { getIsCustomLookupField } from '../utils/fieldUtils';
import * as CGUtils from '../utils/calendarGridUtils';
import { mapByFieldValue, omit, getData, isEmptyObject } from '../utils/commonUtils';
import { getWorkingDaysRanges, createDiarySectionsData, updateDiarySectionsData, getNoDiariesInRange, resourceLoaded, isNoDiaryGroup, getDefaultDiaryName, getDiaryGroup } from '../utils/diaryUtils';
import { DIARY_GROUP, resourceGuidsFieldByTableName, ROLEREQUESTSTATUS_FIELDS, ROLEREQUEST_FIELDS, RESOURCE_DESCRIPTION, calcFields } from '../constants/fieldConsts';
import { isWithinRange, isBefore, isAfter, startOfDay, endOfDay, subDays, addDays, formatDate, parseToUtcDate, DATE_TIME_UNITS, parseUtcToLocalDate } from '../utils/dateUtils';
import { mergeOverlappingRanges, getRangesInRange } from '../utils/rangeUtils';
import { STYLE_SETTINGS_KEYS } from '../constants/dataGridConsts';
import { getDataRows, getStaticMessagesSelector } from './recordsListSelectors';
import { PlannerPageHideRecordsOptions } from '../components/planner/commandBar/hideRecordsOptions';
import { VerticalDivider } from '../lib/commandBar/verticalDivider';
import { message } from 'antd';
import { ConnectedCommandBarSwitch } from '../connectedComponents/connectedCommandBar/connectedBarElements/connectedCommandBarSwitch';
import { getPageIsUserActiveSelector, getPageSelectedViewFiltersSelector, getPlannerSelectedEntitiesSelector } from './commonSelectors';
import { getIsCriteriaRole } from '../utils/roleRequestsUtils';
import { getMenuItemActionAccessibleIdsSelector, getPlannerCBConfigItemsAdditionalPropsSelector } from './commandBarSelectors';
import { evalCommandBarCustomHideActionConditions } from '../utils/commandBarCustomConditions';
import { ConnectedRoleFromTemplate } from '../connectedComponents/connectedRoleFromTemplate';
import { createDataFn, getPagedDataObjectBase, getRlConfigBase, getRlDisplayDataBase, getRlDisplayFieldsBase, getRoleGroupsObjectBase, getSubRecDataBase, isPlannerFieldLoaded } from './commonPlannerPageSelectors';
import { getCurrentPageAliasSelector } from './navigationSelectors';
import { CustomDateRangePicker } from '../components/planner/dateBar/customDateRangePicker';
import { getAdvancedAppliedFiltersSelection, getAppliedFiltersSelection } from '../utils/filterSelectionUtils';
import { ConnectedCountBadge } from '../connectedComponents/connectedCountBadge';
import { getLicenseValuesByKeySelector } from '../selectors/commonSelectors';
import { FEATURE_FLAGS, LICENSE_KEYS_ADMIN_SETTINGS } from '../constants/globalConsts';
import { hasCMeColours } from '../utils/entityWindowUtils';
import { getFeatureFlagSelector } from './featureManagementSelectors';
import { populateStringTemplates } from '../utils/translationUtils';

const { licensePlannerPageMaxInfiniteScrollRows, licenseDiaryYearsPersistedInHistory } = LICENSE_KEYS_ADMIN_SETTINGS;

const translationProps = {
    sectionName: 'plannerPage',
    idsArray: [
        'recordsList'
    ]
};

const getCurrentPlannerFilters = (workspaceSettings, plannerPage) => {
    return plannerPage.filters[workspaceSettings.filtersGuid];
};

const getCurrentViewHideHistoricRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideHistoricRecords = true } = viewsConfig[masterRecTableName];

    return hideHistoricRecords;
};

const getCurrentViewHideFutureRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideFutureRecords = true } = viewsConfig[masterRecTableName];

    return hideFutureRecords;
};

const getCurrentViewHideUnassignedResourceRows = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideUnassignedResourceRows = true } = viewsConfig[masterRecTableName];

    return hideUnassignedResourceRows;
};

const getCurrentViewHideJobTimeline = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideJobTimeline = false } = viewsConfig[masterRecTableName];

    return hideJobTimeline;
};

const getCurrentViewHideJobMilestones = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideJobMilestones = false } = viewsConfig[masterRecTableName];

    return hideJobMilestones;
};

const getCurrentViewHideRolesRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideRolesRecords = true } = viewsConfig[masterRecTableName];

    return hideRolesRecords;
};

const getCurrentViewHideRoleBarsFieldLabels = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideRolesBarsLabels = HIDE_BARS_FIELDS_LABEL_DEFAULT_VALUE } = viewsConfig[masterRecTableName];

    return hideRolesBarsLabels;
};

const getCurrentViewHideBookingBarsFieldLabels = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideBookingBarsLabels = HIDE_BARS_FIELDS_LABEL_DEFAULT_VALUE } = viewsConfig[masterRecTableName];

    return hideBookingBarsLabels;
};

const getCurrentViewHideUnassignedRolesRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideUnassignedRolesRecords = true } = viewsConfig[masterRecTableName];

    return hideUnassignedRolesRecords;
};

const getCurrentViewHideRolesByNameRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideRolesByNameRecords = true } = viewsConfig[masterRecTableName];

    return hideRolesByNameRecords;
};

const getCurrentViewHideRolesByRequirementsRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideRolesByCriteriaRecords = true } = viewsConfig[masterRecTableName];

    return hideRolesByCriteriaRecords;
};

const getCurrentViewHideDraftRolesRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideDraftRolesRecords = true } = viewsConfig[masterRecTableName];

    return hideDraftRolesRecords;
};

const getCurrentViewHideRequestedRolesRecords = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideRequestedRolesRecords = true } = viewsConfig[masterRecTableName];

    return hideRequestedRolesRecords;
};

const getCurrentViewHideLiveBars = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideLiveBars = true } = viewsConfig[masterRecTableName];

    return hideLiveBars;
};

const getCurrentViewHideWeekends = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideWeekends = false } = viewsConfig[masterRecTableName];

    return hideWeekends;
};

const getCurrentViewHidePotentialConflicts = (wsSettings) => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hidePotentialConflicts = true } = viewsConfig[masterRecTableName];

    return hidePotentialConflicts;
};

const getCurrentViewHideInactiveResources = wsSettings => {
    const { masterRecTableName, viewsConfig } = wsSettings;
    const { hideInactiveResources = true } = viewsConfig[masterRecTableName] || {};

    return hideInactiveResources;
};

const getCurrentPlannerData = createSelector(
    state => state.plannerPage.plannerData,
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (plannerData, workspaceSettings) => {
        return {
            ...omit(plannerData[workspaceSettings.plannerDataGuid], ['rows', 'rowsById']),
            rows: getDataRows(plannerData[workspaceSettings.plannerDataGuid])
        };
    }
);

const isResourceView = (tableName) => {
    return tableName === TABLE_NAMES.RESOURCE;
};

const buildResViewSelection = (data, hasDiary, resourceActive, resourceGuidsField, queryTableName) => {
    const { parentId, childId, parentTableName, childTableName } = data;

    return {
        [`${queryTableName}_${TABLE_NAMES.RESOURCE}_guid`]: (hasDiary && resourceActive && parentId != UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID) ? parentId : null,
        [`${queryTableName}_${TABLE_NAMES.JOB}_guid`]: childId,
        [resourceGuidsField]: [hasDiary && resourceActive ? parentId : UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID],
        parentTableName,
        childTableName
    };
};

const buildJobViewSelection = (data, hasDiary, resourceActive, resourceGuidsField, queryTableName) => {
    const { parentId, childId, childIds, parentTableName, childTableName } = data;

    return {
        [`${queryTableName}_${TABLE_NAMES.JOB}_guid`]: parentId,
        [`${queryTableName}_${TABLE_NAMES.RESOURCE}_guid`]: hasDiary && resourceActive && childId != UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ? childId : null,
        [resourceGuidsField]: childIds,
        parentTableName,
        childTableName
    };
};

const buildSelection = (data, hasDiary, resourceActive, queryTableName, expanded) => {
    const { startDate, endDate, masterRecTableName } = data;
    const resourceGuidsField = resourceGuidsFieldByTableName[queryTableName];

    return {
        [`${queryTableName}_start`]: formatDate(parseUtcToLocalDate(startDate)),
        [`${queryTableName}_end`]: formatDate(parseUtcToLocalDate(endDate)),
        ...(isResourceView(masterRecTableName) ?
            buildResViewSelection(data, hasDiary, resourceActive, resourceGuidsField, queryTableName) :
            buildJobViewSelection(data, hasDiary, resourceActive, resourceGuidsField, queryTableName, expanded))
    };
};

const getChildRowIndicesFromSelectionSelector = createSelector(
    state => getFlattenedRowsInfoSelector(state),
    state => getDetailFieldsSumHeight(state),
    (getFlattenedRowsInfo, detailFieldsSumHeight) => (positionData, renderedFrom) => {
        const rowsData = getFlattenedRowsInfo(detailFieldsSumHeight);

        const { startYPos, endYPos } = positionData;
        const startPosition = startYPos - renderedFrom.y;
        const endPosition = endYPos - renderedFrom.y;

        let childRowIndices = [];
        let position = 0;

        for (let row of rowsData) {
            position += row.height;

            if (position > startPosition && position <= endPosition) {
                childRowIndices.push(row.childRowIndex);
            } else if (position > endPosition) {
                break;
            }
        }

        return childRowIndices;
    }
);

const getCurrentPlannerSelectionSelector = createSelector(
    state => getCurrentPlannerData(state),
    state => getResourceDateHasNoDiarySelector(state),
    state => getPageIsUserActiveSelector(state),
    state => getChildRowIndicesFromSelectionSelector(state),
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (currentPlannerData, getResourceDateHasNoDiary, isResourceActive, getChildRowIndicesFromSelection, { masterRecTableName }) => (queryTableName) => {
        if (!Object.keys(currentPlannerData.selection).length) {
            return {};
        }

        const { rowIndex, childRowIndex, startDate, endDate, startYPos, endYPos } = (currentPlannerData.selection.positionData || {});
        const currentRow = currentPlannerData.rows[rowIndex] || {};

        const { tableName: parentTableName, id: parentId, subRows, expanded } = currentRow;
        const { id: childId, tableName: childTableName } = (expanded && childRowIndex > 0) ? (subRows[childRowIndex - 1] || {}) : {};

        const resourceId = parentTableName == TABLE_NAMES.RESOURCE ? parentId : childId;
        const hasDiary = !getResourceDateHasNoDiary(resourceId, startDate);
        const resourceActive = isResourceActive(resourceId);

        let childIds = [];

        if (expanded && parentTableName === TABLE_NAMES.JOB) {
            const parentRowIndex = 0;
            const childRowIndices = getChildRowIndicesFromSelection({ startYPos, endYPos }, { x: DEFAULT_RENDER_FROM_VERTICE, y: DEFAULT_RENDER_FROM_VERTICE });

            childIds = childRowIndices.reduce((accumulator, rowIndex) => {
                const isValidRow = rowIndex !== parentRowIndex;
                const subRowId = (subRows[rowIndex - 1] || {}).id;

                if (isValidRow && !getResourceDateHasNoDiary(subRowId, startDate) && isResourceActive(subRowId)) {
                    accumulator.push(subRowId);
                }

                return accumulator;
            }, []);
        }

        const data = {
            startDate,
            endDate,
            parentId,
            childId,
            parentTableName,
            childTableName,
            startYPos,
            endYPos,
            childIds,
            masterRecTableName
        };

        return buildSelection(data, hasDiary, resourceActive, queryTableName, expanded);
    }
);

const getCurrentPlannerBarTypesIds = createSelector(
    plannerPage => getCurrentPlannerBarGroupsObject(plannerPage),
    (barGroups) => BAR_TABLE_NAMES.reduce((accumulator, tableName) => {

        return {
            ...accumulator,
            [tableName]: Object.keys((barGroups[tableName]).byId[tableName])
        };
    }, {})
);

const getCurrentPlannerBookingIds = createSelector(
    plannerPage => (getCurrentPlannerBarGroupsObject(plannerPage)[TABLE_NAMES.BOOKING]).byId,
    bookingGroupsDataById => bookingGroupsDataById.booking
);

const getCurrentPlannerRoleIds = createSelector(
    plannerPage => (getCurrentPlannerBarGroupsObject(plannerPage)[TABLE_NAMES.ROLEREQUEST]).byId,
    roleGroupsDataById => roleGroupsDataById.rolerequest
);

const getCurrentPlannerCriteriaRoleIds = createSelector(
    plannerPage => (getCurrentPlannerBarGroupsObject(plannerPage)[TABLE_NAMES.ROLEREQUEST]).byId[TABLE_NAMES.ROLEREQUEST],
    plannerPage => (getCurrentPlannerBarGroupsObject(plannerPage)[TABLE_NAMES.ROLEREQUEST]).data,
    (rolesById, roles) => {
        return Object.keys(rolesById).filter(roleId => {
            const index = rolesById[roleId];

            return getIsCriteriaRole(roles[index]);
        });
    }
);

const getPlannerGroups = (plannerPage) => {
    let result = {};

    Object.keys(PLANNER_PAGE_TABLE_TO_GROUP_MAP).forEach((key) => {
        result = {
            ...result,
            [key]: plannerPage[PLANNER_PAGE_TABLE_TO_GROUP_MAP[key]]
        };
    });

    return result;
};

const getCurrentPlannerBarGroupsObject = createSelector(
    plannerPage => plannerPage.bookingGroups,
    plannerPage => plannerPage.roleGroups,
    plannerPage => plannerPage.rolerequestResourceGroups,
    plannerPage => getSelectedWorkspaceSettings(plannerPage.workspaces),
    (bookingGroups, roleGroups, rolerequestResourceGroups, workspaceSettings) => {
        let result = [];

        if (!isEmptyObject(workspaceSettings)) {
            const barGroups = getPlannerGroups({ bookingGroups, roleGroups, rolerequestResourceGroups });

            Object.keys(barGroups).forEach((key) => {
                const barGroupsGuid = getBarGroupsGuidByTable(workspaceSettings, key);

                result = {
                    ...result,
                    [key]: barGroups[key][barGroupsGuid]
                };
            });
        }

        return result;
    }
);

const getActivePlannerEntitySelector = createSelector(
    state => getCurrentPlannerBarGroupsObject(state.plannerPage),
    state => state.window || {},
    (plannerBarGroupsObject, window) => (tableName, moduleName) => {
        const currentBarGroup = plannerBarGroupsObject[tableName];
        const { byId, currentEdits, data } = currentBarGroup || {};
        let currentEdit = currentEdits[0];
        const ewWindow = window[moduleName];
        const { visible, activeEntity, tableName: ewTableName } = ewWindow || {};

        // Check if have opened batch EW for the same tableName and use its activeEntity
        if (visible && activeEntity && ewTableName === tableName) {
            currentEdit = activeEntity;
        }

        const currentIndex = byId[tableName][currentEdit];

        return data[currentIndex] || {};
    }
);

const getCurrentPlannerBarGroupDataMappedSelector = createSelector(
    state => getCurrentPlannerBarsMap(state.plannerPage),
    (barsDataMap = {}) => (barTableName) => {
        const barsGroupData = barsDataMap[barTableName] || [];

        return mapByFieldValue(`${barTableName}_guid`, barsGroupData);
    }
);

const getFilteredPlannerRoleRequestsByStatusDescriptionSelector = createSelector(
    state => getCurrentPlannerBarGroupDataMappedSelector(state),
    state => getPlannerRoleRequestStatusGuidSelector(state),
    (getBarGroupMappedData, getRoleRequestStatusGuid) => (roleGuids = [], statusDescription) => {
        const rolesDataMap = getBarGroupMappedData(TABLE_NAMES.ROLEREQUEST);
        const statusGuid = getRoleRequestStatusGuid(statusDescription);

        return roleGuids.reduce((accumulator, roleGuid) => {
            const roleEntity = rolesDataMap[roleGuid] || {};

            if (roleEntity[ROLEREQUEST_FIELDS.STATUS_GUID] === statusGuid) {
                accumulator.push(roleEntity);
            }

            return accumulator;
        }, []);
    }
);

const getCurrentPlannerBarsMap = createSelector(
    getCurrentPlannerBarGroupsObject,
    (barGroups) => Object.entries(barGroups)
        .reduce((accumulator, [tableName, group = {}]) => {
            accumulator[tableName] = group.data || [];

            return accumulator;
        }, {})
);

const getCurrentPlannerBarSelection = createSelector(
    state => getCurrentPlannerBarGroupsObject(state.plannerPage),
    (barGroups) => Object.entries(barGroups).reduce((accumulator, [tableName, group]) => {
        return {
            ...accumulator,
            [tableName]: group.currentEdits
        };
    }, {})
);

const getMergedBarSelection = createSelector(
    getCurrentPlannerBarSelection,
    (selectedBars) => {
        return Object.entries(selectedBars).reduce((accumulator, [tableName, ids]) => {
            const bars = ids.map(id => { return { id, barTableName: tableName }; });
            accumulator.push(...bars);

            return accumulator;
        }, []);
    }
);

const getSelectedBarsIntervalSelector = createSelector(
    state => getCurrentPlannerBarSelection(state),
    state => getSelectedBarsCount(state),
    state => getCgGetDataWrappedSelector(state),
    (selectedBars, selectedBarsCount, getBarData) => {
        let result = [];

        if (selectedBarsCount > 0) {
            result = Object.entries(selectedBars).reduce((selectedIntervals, [tableName, bars]) => {
                bars.forEach((barGuid) => {
                    const barData = getBarData(barGuid, tableName);

                    selectedIntervals.push({
                        startDate: parseToUtcDate(barData[`${tableName}_start`]),
                        endDate: parseToUtcDate(barData[`${tableName}_end`])
                    });
                });

                return selectedIntervals;
            }, []);
        }

        return result;
    }
);

// Selector should be used after knowing that only one type of bars are selected
const getBarActiveSelectionTableName = createSelector(
    getCurrentPlannerBarSelection,
    (selectedBars) => Object.keys(selectedBars).find(tableName => selectedBars[tableName].length > 0)
);

const getBarMultipleTableSelectionActive = createSelector(
    getCurrentPlannerBarSelection,
    (selectedBars) => Object.entries(selectedBars).filter(([tableName, ids]) => ids.length > 0 && tableName !== TABLE_NAMES.ROLEREQUESTRESOURCE).length > 1
);

const getSelectedBarsCount = createSelector(
    state => getCurrentPlannerBarGroupsObject(state.plannerPage),
    (barGroups) => Object.values(barGroups).reduce((accumulator, group) => accumulator + group.currentEdits.length, 0)
);

const getCurrentPlannerBarGroups = createSelector(
    getCurrentPlannerBarGroupsObject,
    (barGroupsObject) => {
        let result = {};

        Object.keys(barGroupsObject).forEach((key) => {
            const { data, byId, loading, tableName, tableNames, count } = barGroupsObject[key];
            result = {
                ...result,
                [key]: createDataFn(data, byId, loading, tableName, tableNames, count)
            };
        });

        return result;
    }
);

const getCurrentPlannerPageNumber = createSelector(
    plannerPage => plannerPage.uiOptions,
    (uiOptions) => uiOptions.pageNumber
);

const getCurrentPagePlannerData = createSelector(
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage),
    plannerPage => getCurrentPlannerPageNumber(plannerPage),
    (currPagedMasterRecPlannerData, currentPageNumber) => {
        const { from, to } = currPagedMasterRecPlannerData.loadedPagesMap[currentPageNumber - 1] || {};

        return currPagedMasterRecPlannerData.data.slice(from, to);
    }
);

const getCurrentPageSubRecPlannerData = createSelector(
    plannerPage => getCurrentPagePlannerData(plannerPage),
    plannerPage => getCurrentPlannerSubRecData(plannerPage),
    plannerPage => getCurrentPlannerBarGroups(plannerPage),
    plannerPage => (getSelectedWorkspaceSettings(plannerPage.workspaces) || {}),
    (currPageData, subRecDataCollection, barGroups, currWS) => {
        const { masterRecTableName, subRecTableName } = currWS;
        const barTableNames = Object.keys(barGroups);
        const currPageById = mapByFieldValue(`${masterRecTableName}_guid`, currPageData);

        let dataIds = [];

        barTableNames.forEach(
            barTableName => {
                let extractedSubRecIds = [];

                const masterLink = `${barTableName}_${masterRecTableName}_guid`;
                const subRecLink = `${barTableName}_${subRecTableName}_guid`;
                const { data: groupData = [] } = barGroups[barTableName] || {};

                (groupData || []).forEach(
                    row => {

                        const matchingMasterRec = currPageById[row[masterLink]];

                        if (matchingMasterRec !== undefined) {
                            const subRecRow = getData([subRecDataCollection], subRecTableName, row[subRecLink]);
                            extractedSubRecIds.push(subRecRow[`${subRecTableName}_guid`]);
                        }
                    }
                );

                dataIds = union(dataIds, extractedSubRecIds);
            }
        );


        return dataIds.map(id => getData([subRecDataCollection], subRecTableName, id));
    }
);

const getCurrentPlannerPagedDataObject = createSelector(
    plannerPage => plannerPage.pagedMasterRecPlannerData,
    plannerPage => (getSelectedWorkspaceSettings(plannerPage.workspaces) || {}).pagedMasterRecPlannerDataGuid || '',
    getPagedDataObjectBase
);

export const getCurrentPlannerRoleGroupsDataObject = createSelector(
    plannerPage => plannerPage.roleGroups,
    plannerPage => ((getSelectedWorkspaceSettings(plannerPage.workspaces) || {}).barGroupsGuids || {}).roleGroupsGuid || '',
    getRoleGroupsObjectBase
);

const getCurrentPlannerPagedData = createSelector(
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).data,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).byId,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).loading,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).tableName,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).tableNames,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).count,

    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).guid,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).startElement,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).scrollKey,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).loadedPages,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).loadedPagesMap,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).pageSize,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).rowCount,
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).key,
    createDataFn
);

const getCurrentPlannerSubRecDataObject = createSelector(
    plannerPage => plannerPage.subRecPlannerData,
    plannerPage => (getSelectedWorkspaceSettings(plannerPage.workspaces) || {}).subRecPlannerDataGuid,
    getSubRecDataBase
);

const getCurrentPlannerSubRecData = createSelector(
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).data,
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).byId,
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).loading,
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).tableName,
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).tableNames,
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).count,
    createDataFn
);

const getPlannerGroupedTableNames = (workspaceSettings, plannerPage) => {
    const bookingGroups = getCurrentPlannerBarGroupsObject(plannerPage)[TABLE_NAMES.BOOKING]; //Temporary solution until intractions for roles are implemented

    return bookingGroups.tableNames;
};

const getCurrentFiltersSelection = (workspaceSettings, page, getFieldInfo, pageAlias) => {
    const { views = {}, selectedView = '' } = page.filters[workspaceSettings.filtersGuid] || {};

    return pageAlias === PLANNER_PAGE_ALIAS
        ? getAdvancedAppliedFiltersSelection(views[selectedView]?.filterSectionsData, getFieldInfo)
        : getAppliedFiltersSelection(views[selectedView].filterGroupsData, getFieldInfo);
};

const getCurrentPlannerTableDatas = createSelector(
    plannerPage => plannerPage.plannerTableDatas,
    (plannerTableDatas) => {
        return Object.keys(plannerTableDatas)
            .filter((currentDataKey) => typeof (plannerTableDatas[currentDataKey]) === 'object')
            .reduce((accumulator, currentDataKey) => {
                return [
                    ...accumulator,
                    plannerTableDatas[currentDataKey]
                ];
            }, []);
    }
);

const getCurrentPlannerDetailsPane = createSelector(
    plannerPage => plannerPage.detailsPane,
    plannerPage => getSelectedWorkspaceSettings(plannerPage.workspaces),
    (detailsPane, workspaceSettings) => detailsPane[(workspaceSettings || {}).detailsPaneGuid]
);

const getCollectionAliasForTableName = (workspaceSettings, tableName) => {
    // These strings have to be defined in one place only for the whole app
    switch (tableName) {
        case TABLE_NAMES.BOOKING:
            return PLANNER_BOOKING_GROUPS_ALIAS;
        case workspaceSettings.masterRecTableName:
            return PLANNER_MASTER_REC_ALIAS;
        case workspaceSettings.subRecTableName:
            return PLANNER_SUB_REC_ALIAS;
        case TABLE_NAMES.ROLEREQUEST:
            return PLANNER_ROLEREQUESTS_ALIAS;
        case TABLE_NAMES.ROLEREQUESTGROUP:
            return PLANNER_ROLEREQUESTGROUP_ALIAS;
        case TABLE_NAMES.CLIENT:
            return ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS;
        case TABLE_NAMES.ROLEREQUESTRESOURCE:
            return PLANNER_ROLEREQUESTSRESOURCE_ALIAS;
        default:
            throw new Error(`No collection found for table ${tableName}`);
    }
};

const getCurrentEditsForTableName = createSelector(
    state => getCurrentPlannerBarSelection(state),
    state => state.plannerPage.pagedMasterRecPlannerData,
    state => state.plannerPage.subRecPlannerData,
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (barGroupsSelection, pagedMasterRecPlannerData, subRecPlannerData, workspaceSettings) =>
        (tableName) => {
            const { pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid } = workspaceSettings;
            const currentPagedMasterRecPlannerData = pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid];
            const currentSubRecPlannerData = subRecPlannerData[subRecPlannerDataGuid];

            let result = [];

            if (barGroupsSelection[tableName]) {
                result = barGroupsSelection[tableName];
            } else if (currentPagedMasterRecPlannerData.tableName === tableName) {
                result = currentPagedMasterRecPlannerData.currentEdits;
            } else if (currentSubRecPlannerData.tableName === tableName) {
                result = currentSubRecPlannerData.currentEdits;
            }

            return result;
        }
);

const isPlannerPageGuidDataLoading = (plannerPage) => {
    return plannerPage.bookingGroups.loading || plannerPage.pagedMasterRecPlannerData.loading ||
        plannerPage.subRecPlannerData.loading || plannerPage.filters.loading || plannerPage.detailsPane.loading || plannerPage.plannerData.loading;
};

const getPlannerPageFilterPaneCollapsed = (page, filtersGuid) => {
    const { views, selectedView } = page.filters[filtersGuid] || {};

    return getFilterPaneCollapsed(((views || {})[selectedView] || {}));
};

const getPlannerPageFilters = createSelector(
    state => getPageSelectedViewFiltersSelector(state, PLANNER_PAGE_ALIAS),
    (filters) => filters || {}
);

const getVisiblePageDates = createSelector(
    (state, pageAlias) => getSelectedWorkspaceSettings(state[pageAlias].workspaces),
    (workspaceSettings) => {
        return {
            visibleDates: {
                startDate: workspaceSettings.startDate,
                endDate: workspaceSettings.endDate
            }
        };
    }
);

const VIEW_MODE_TO_SUBRACT_DAYS = [
    PLANNER_VIEW_MODES.VIEW_MODE_WEEK,
    PLANNER_VIEW_MODES.VIEW_MODE_DAY
];

const getFilterVisibleDates = createSelector(
    (state) => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (workspaceSettings) => {
        const toSubtractDays = VIEW_MODE_TO_SUBRACT_DAYS.includes(workspaceSettings.viewMode.mode);

        return {
            visibleDates: {
                startDate: workspaceSettings.startDate,
                endDate: toSubtractDays ? subDays(workspaceSettings.endDate, 1) : workspaceSettings.endDate
            }
        };
    }
);

const getPlannerPageFiltersGuid = (state) => {
    const workspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);

    return workspaceSettings.filtersGuid;
};

const getBookingsCollectionAlias = (workspaceSettings) => {
    return getBarGroupsGuidByTable(workspaceSettings, TABLE_NAMES.BOOKING);
};

const getMasterRecCollectionAlias = (workspaceSettings) => {
    return workspaceSettings.pagedMasterRecPlannerDataGuid;
};

const getSubRecCollectionAlias = (workspaceSettings) => {
    return workspaceSettings.subRecPlannerDataGuid;
};

const getCutBar = (clipboard) => {
    if (
        clipboard &&
        clipboard.bar &&
        clipboard.bar.clipboardAction === PLANNER_BAR_ACTIONS.CUT // refactor? when implementing role actions
    ) {
        return clipboard.bar.barData;
    }
};

const getPlannerPageFieldOptions = (plannerPage, workspaceGuid) => {
    return plannerPage.fieldOptions[workspaceGuid] || {};
};

const getPlannerPageDataCollections = createSelector(
    state => getCurrentPlannerBarGroups(state.plannerPage),
    state => getCurrentPlannerPagedData(state.plannerPage),
    state => getCurrentPlannerSubRecData(state.plannerPage),
    state => state.plannerPage.plannerTableDatas,
    (barGroups, pagedData, subRecData, plannerTableDatas) => {
        return [
            ...Object.values(barGroups),
            pagedData,
            subRecData,
            ...Object.keys(plannerTableDatas).map(tableDataKey => plannerTableDatas[tableDataKey])
        ];
    }
);

const getPlannerTableDatasByTableName = createSelector(
    plannerPage => plannerPage.plannerTableDatas,
    (plannerTableDatas) => (tableName) => plannerTableDatas[tableName] || {}
);

const getRlSelectedRecords = createSelector(
    plannerPage => getCurrentPlannerPagedDataObject(plannerPage).currentEdits,
    plannerPage => getCurrentPlannerSubRecDataObject(plannerPage).currentEdits,
    plannerPage => getCurrentPlannerBarSelection({ plannerPage }),
    (parent, child, bars) => {
        return {
            parent,
            child,
            bars
        };
    }
);

const getPlannerHasActiveSelection = createSelector(
    state => getCurrentPlannerPagedDataObject(state.plannerPage).currentEdits,
    state => getCurrentPlannerSubRecDataObject(state.plannerPage).currentEdits,
    state => getCurrentPlannerData(state).selection,
    state => getCurrentPlannerRoleGroupsDataObject(state.plannerPage).currentEdits,
    (parent, child, gridSelection, roleGroups) => parent.length > 0 || child.length > 0 || !isEmptyObject(gridSelection) || roleGroups.length > 0
);

const getPlannerPageEntityWindowAdditionalPayloadData = (state) => {
    const selectedWorkspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);
    const { plannerDataGuid, masterRecTableName, workspace_guid } = selectedWorkspaceSettings;
    const bookingGroupsGuid = getBarGroupsGuidByTable(selectedWorkspaceSettings, TABLE_NAMES.BOOKING);

    return {
        workspaceSettingsGuid: workspace_guid,
        bookingGroupsGuid,
        plannerDataGuid,
        masterRecTableName
    };
};

const getPlannerDataSelector = createSelector(
    getPlannerPageDataCollections,
    (dataCollections) => (tableName, id) => getData(dataCollections, tableName, id)
);

const getRlPlannerDataSelector = createSelector(
    getPlannerPageDataCollections,
    (dataCollections) => (tableName, id) => getData(dataCollections, tableName, id)
);

const getRlDisplayFieldsSelector = createSelector(
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    state => state.applicationSettings,
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    getRlDisplayFieldsBase
);

const getRlDisplayDataSelector = createSelector(
    getRlPlannerDataSelector,
    getFieldInfoSelector,
    getPlannerFieldBlankValueSelector,
    getStaticMessagesSelector,
    getRlDisplayDataBase
);

const getRlIsFieldLoadedSelector = createSelector(
    state => state.plannerPage.fieldOptions,
    state => getSelectedWorkspaceGuid(state.plannerPage.workspaces),
    (fieldOptions, workspaceGuid) => (fieldName) => isPlannerFieldLoaded(fieldOptions, workspaceGuid, fieldName)
);

export const getCMeColourValuesSelector = createSelector(
    (state) => getPlannerDataSelector(state),
    plannerData => (tableName, id) => {
        const resourceData = plannerData(tableName, id);
        const hasCMeData = hasCMeColours(resourceData);

        return {
            resourceData,
            hasCMeData
        };
    }
);

const getCgGetDataWrappedSelector = createSelector(
    state => getCurrentPlannerBarGroups(state.plannerPage),
    (barGroups) => (id, tableName = TABLE_NAMES.BOOKING) => getData(Object.values(barGroups), tableName, id)
);

const getGroupBarsDataSelector = createSelector(
    plannerPage => getCurrentPlannerBarGroups(plannerPage),
    (barGroups) => {
        const groupBarsDataRes = {};

        BAR_TABLE_NAMES.forEach(barTable => {
            groupBarsDataRes[barTable] = barGroups[barTable].data;
        });

        return groupBarsDataRes;
    }
);

export const getPlannerPageCommandBarSpecificAliases = createSelector(
    (state) => getCurrentWSSettingsSelector(state).subRecTableName,
    (subRecTableName) => (commonEntityAliases) => {
        const alias = subRecTableName === TABLE_NAMES.JOB ? commonEntityAliases.jobPluralLowerAlias : commonEntityAliases.resourcePluralLowerAlias;

        return {
            pluralViewNameAlias: alias
        };
    }
);

const getGetBarDisplayDataSelector = createSelector(
    getFieldInfoSelector,
    getPlannerDataSelector,
    (wrappedGetFieldInfo, getBarDataWrapped) => (record, barTableName) => {
        const result = {};

        Object.keys(record)
            .forEach((dataKey) => {
                const fieldInfo = wrappedGetFieldInfo(barTableName, dataKey);
                const tableName = getFieldTableName(fieldInfo, barTableName);

                if ((isTableFieldLinked(barTableName, fieldInfo) || isCustomLookupOrPlanningDataField(fieldInfo)) && record[dataKey]) {
                    const linkedRecordId = record[dataKey];
                    const linkedRecord = getBarDataWrapped(tableName, linkedRecordId);

                    result[dataKey] = getIsCustomLookupField(fieldInfo)
                        ? linkedRecord[`${tableName}_value`]
                        : linkedRecord[`${tableName}_description`];
                } else {
                    result[dataKey] = record[dataKey];
                }
            });

        return result;
    }
);

const createDeepEqualSelector = createSelectorCreator(
    defaultMemoize,
    isEqual
);

const getCommandBarActionProps = createDeepEqualSelector(
    state => getSelectedWorkspaceGuid(state.plannerPage.workspaces),
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    state => getCurrentPlannerData(state).selection,
    state => getCurrentPlannerData(state).rows,
    state => {
        const { filtersGuid } = getSelectedWorkspaceSettings(state.plannerPage.workspaces);

        return state.plannerPage.filters[filtersGuid];
    },
    state => getPlannerSelectedEntitiesSelector(state.plannerPage),
    state => getFieldInfoSelector(state),
    state => getBarMultipleTableSelectionActive(state),
    state => getBarActiveSelectionTableName(state),
    state => getPlannerCBConfigItemsAdditionalPropsSelector(state),
    state => getPlannerSelectionByBarTableSelector(state),
    state => getRotatedWorkspaceViewSelector(state.plannerPage),
    (
        workspaceGuid,
        workspaceSettings,
        plannerSelection,
        plannerRows,
        filters,
        getPlannerSelectedEntities,
        getFieldInfo,
        multipleTableSelectionActive,
        activeSelectionTableName,
        menuItemsAdditionalProps,
        selectionByBarTable,
        rotatedWorkspaceViewSelector
    ) => {
        const {
            filtersGuid,
            masterRecTableName,
            subRecTableName,
            plannerDataGuid,
            startDate,
            endDate,
            viewMode,
            detailsPaneGuid,
            viewsConfig
        } = workspaceSettings;

        const bookingGroupsGuid = getBarGroupsGuidByTable(workspaceSettings, TABLE_NAMES.BOOKING);
        const {
            hideHistoricRecords = true,
            hideFutureRecords = true,
            hideUnassignedResourceRows = true,
            hideWeekends = false,
            hideRolesRecords = true,
            hideUnassignedRolesRecords = true,
            hideRolesByNameRecords = true,
            hideRolesByCriteriaRecords = true,
            hideDraftRolesRecords = true,
            hideRequestedRolesRecords = true,
            hidePotentialConflicts = true,
            hideLiveBars = true,
            hideJobTimeline = false,
            hideJobMilestones = false,
            hideInactiveResources = true
        } = viewsConfig[masterRecTableName];

        const components = {
            'CustomDateRangePicker': CustomDateRangePicker,
            'GoToDate': GoToDate,
            'DateRangePicker': DateRangePicker,
            'BarOptions': ConnectedBarOptions,
            'HideRecordsOptions': PlannerPageHideRecordsOptions,
            'Switch': ConnectedCommandBarSwitch,
            'Divider': VerticalDivider,
            'RoleFromTemplate': ConnectedRoleFromTemplate
        };

        const { hidden: filterHidden } = filters.views[filters.selectedView];

        const badgeComponentsByActionKey = {
            [PLANNER_ACTIONS.TOGGLE_FILTER_PANE]: {
                render: ConnectedCountBadge,
                props: {}
            }
        };

        let singleTableSelectedEntities = { ids: [], entities: [], tableName: null, entitiesGuid: null, collectionAlias: null };

        if (!multipleTableSelectionActive) {
            singleTableSelectedEntities = getPlannerSelectedEntities(activeSelectionTableName);
        }

        const isRotatedWorkspaceView = rotatedWorkspaceViewSelector(workspaceGuid, masterRecTableName);

        return {
            workspaceGuid,
            bookingGroupsGuid,
            filtersGuid,
            plannerDataGuid,
            masterRecTableName,
            subRecTableName,
            entitiesIds: singleTableSelectedEntities.ids,
            entitiesTableName: singleTableSelectedEntities.tableName,
            selectedEntitiesByTable: selectionByBarTable,
            startDate,
            endDate,
            viewMode,
            components,
            badgeComponentsByActionKey,
            filterHidden,
            plannerSelection,
            plannerRows,
            detailsPaneGuid,
            hideWeekends,
            hidePotentialConflicts,
            multipleTableSelectionActive,
            menuItemsAdditionalProps,
            hideRecordsOptionsActionProps: {
                workspaceGuid,
                subRecTableName,
                hideHistoricRecords,
                hideFutureRecords,
                hideUnassignedResourceRows,
                hideRolesRecords,
                hideUnassignedRolesRecords,
                hideRolesByNameRecords,
                hideRolesByCriteriaRecords,
                hideDraftRolesRecords,
                hideRequestedRolesRecords,
                hideLiveBars,
                hideJobTimeline,
                hideJobMilestones,
                hideInactiveResources
            },
            isRotatedWorkspaceView
        };
    }
);

const isUnassignedResource = (resData) => {
    return (
        resData[`${TABLE_NAMES.RESOURCE}_guid`] === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ||
        resData[`${TABLE_NAMES.RESOURCE}_description`] === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_DESCRIPTION
    );
};

const getRlAvatarConfig = createSelector(
    state => state.avatars,
    state => getAvatarAltValueSelector(state),
    (avatars, getAvatarAltValue) => {
        return {
            getAvatarImageURL: (resourceID) => getAvatarImageURL(avatars, resourceID, AVATAR_SIZES.TINY.label),
            getAvatarIcon: (resData) => isUnassignedResource(resData) ? UNASSIGNED_AVATAR_ICON : null,
            getAvatarAltText: (resData) => getAvatarAltValue(resData[RESOURCE_DESCRIPTION] || UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_DESCRIPTION),
            avatarProps: {
                size: AVATAR_SIZES.REGULAR.value
            }
        };
    }
);

const getRlOrder = createSelector(
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.masterRecTableName);
    },
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.subRecTableName);
    },
    (masterRecSortOrder, subRecSortOrder) => {

        return {
            masterRecSortOrder,
            subRecSortOrder
        };
    }
);

const getRlConfig = createSelector(
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    getFieldInfoSelector,
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    getRlConfigBase
);

export const getChildRecordConfig = createSelector(
    state => getRlConfig(state).config,
    (config) => config.childDisplayFields
);

export const getParentRecordConfig = createSelector(
    state => getRlConfig(state).config,
    (config) => config.parentDisplayFields
);

const getJobPlannerData = createSelector(
    plannerPage => getSelectedWorkspaceSettings(plannerPage.workspaces),
    plannerPage => plannerPage.pagedMasterRecPlannerData,
    plannerPage => plannerPage.subRecPlannerData,
    (wsSettings, pagedMasterRecPlannerData, subRecPlannerData) => {
        const masterRecTableName = getWorkspaceMasterRecTableName(wsSettings);
        let jobDataCollection = {};

        if (masterRecTableName === TABLE_NAMES.JOB) {
            const plannerDataGuid = getWorkspaceMasterRecPlannerDataGuid(wsSettings);
            jobDataCollection = pagedMasterRecPlannerData[plannerDataGuid];
        } else {
            const plannerDataGuid = getWorkspaceSubRecPlannerDataGuid(wsSettings);
            jobDataCollection = subRecPlannerData[plannerDataGuid];
        }

        return jobDataCollection;
    }
);

const getWaitPlanner = createSelector(
    state => state.plannerPage.plannerLoadingState,
    (loadingStates) => Object.keys(loadingStates).reduce((accumulator, currentValue) => accumulator = accumulator || loadingStates[currentValue], false)
);

const hasOnlySubRecsSelected = createSelector(
    (state) => state.plannerPage.workspaces,
    getCurrentEditsForTableName,
    (workspaces, getCurrentEditsForTableName) => {
        const selectedWorkspaceSettings = getSelectedWorkspaceSettings(workspaces);
        const { masterRecTableName, subRecTableName } = selectedWorkspaceSettings;
        const selectedBars = getCurrentEditsForTableName('booking');
        const selectedSubRecs = getCurrentEditsForTableName(subRecTableName);
        const selectedMasterRecs = getCurrentEditsForTableName(masterRecTableName);

        return selectedSubRecs.length > 0 && selectedBars.length === 0 && selectedMasterRecs.length === 0;
    }
);

const shouldDisableTab = (key, getPlannerSelectedEntities, currentEditsForTab) => {
    let enabled = currentEditsForTab && currentEditsForTab.length > 0 && currentEditsForTab.some((id) => id !== null);

    if (key == DETAILS_PANE_TAB_KEYS.SUGGESTIONS_KEY) {
        const { ROLEREQUEST: roleRequestTableName } = TABLE_NAMES;
        const { entities = [] } = getPlannerSelectedEntities(roleRequestTableName);
        let criteriaRolesCount = 0;

        entities.forEach(entity => {
            if (getIsCriteriaRole(entity)) {
                criteriaRolesCount++;
            }
        });

        enabled = criteriaRolesCount >= 1;
    }

    if (key === DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY) {
        const entity = getPlannerSelectedEntities(TABLE_NAMES.ROLEREQUEST);
        const activeEntities = (entity || {}).entities || [];

        enabled = activeEntities.some(entity => entity[ROLEREQUEST_FIELDS.ROLE_GROUP_GUID] !== null);
    }

    return !enabled;
};

const getPlannerDetailsPaneTabsCreateSelector = createSelector(
    (state) => getCurrentPlannerDetailsPane(state.plannerPage),
    getCurrentEditsForTableName,
    (state) => getPlannerSelectedEntitiesSelector(state.plannerPage),
    (detailsPane, getCurrentEditsForTableName, getPlannerSelectedEntities) => {
        const tabKeys = Object.keys(detailsPane.tabs);

        const tabs = tabKeys.reduce((acc, curTabKey) => {
            const { table } = detailsPane.tabs[curTabKey];
            const currentEditsForTab = getCurrentEditsForTableName(table);

            return {
                ...acc,
                [curTabKey]: {
                    ...detailsPane.tabs[curTabKey],
                    disabled: shouldDisableTab(curTabKey, getPlannerSelectedEntities, currentEditsForTab)
                }
            };
        }, {});

        return tabs;
    }
);

const getDetailsPaneSelectionIsValid = createSelector(
    (state) => getCurrentPlannerDetailsPane(state.plannerPage),
    getCurrentEditsForTableName,
    (detailsPane, getCurrentEditsForTableName) => {
        const tabKeys = Object.keys(detailsPane.tabs);

        let selectionIsValid = false;
        for (let i = 0; i < tabKeys.length; i++) {
            const { table } = detailsPane.tabs[tabKeys[i]];
            const currentEditsForTab = getCurrentEditsForTableName(table);
            if (currentEditsForTab && currentEditsForTab.length !== 0) {
                selectionIsValid = true;
                break;
            }
        }

        return selectionIsValid;
    }
);

const getFlattenedRowsInfoSelector = createSelector(
    (state) => getCurrentPlannerData(state).rows,
    (state) => getCurrentPlannerData(state).rowsExpanded,
    (state) => getCurrentWSSettingsSelector(state).styleSettings,
    (state) => getCurrentWSSettingsSelector(state).masterRecTableName,
    (rows, rowsExpanded, styleSettings, masterRecTableName) => (detailFieldsSumHeight) => {
        const { ROW_HEIGHT, BARS_ROWS_MARGIN } = STYLE_SETTINGS_KEYS;
        const { density } = styleSettings;

        const rowHeight = CGUtils.getRowsDensityData(ROW_HEIGHT, density);
        const barsRowsMargin = CGUtils.getRowsDensityData(BARS_ROWS_MARGIN, density);

        const rowsData = [];

        for (let i = 0; i < rows.length; i++) {
            const rowData = CGUtils.getRowRenderingData(rows[i], detailFieldsSumHeight, rowHeight, barsRowsMargin, masterRecTableName, rowsExpanded);

            rowData.heightIndex.forEach((height, childRowIndex) => rowsData.push({ height, rowIndex: i, childRowIndex }));
        }

        return rowsData;
    }
);

const getDiaryWorkingDaysRangesSelector = createSelector(
    state => state.applicationSettings.plannerDiaries,
    (diaries) => {
        let cache = {};

        return (diaryName = getDefaultDiaryName(diaries), startDate, endDate, diaryYearsPersistedInHistory) => {
            if (isEmpty(diaryName) || isEmpty(diaries)) {
                return [];
            }

            const diary = diaries.map[diaryName];

            if (diaryName in cache) {
                cache[diaryName] = updateDiarySectionsData(diary, cache[diaryName], startDate, endDate, diaryYearsPersistedInHistory);

                return getWorkingDaysRanges(cache[diaryName], startDate, endDate);
            }

            cache[diaryName] = createDiarySectionsData(diary, startDate, endDate, diaryYearsPersistedInHistory);

            return getWorkingDaysRanges(cache[diaryName], startDate, endDate);
        };
    }
);

const getCurrentResourcePlannerData = createSelector(
    state => ((state.plannerPage || {}).pagedMasterRecPlannerData || {}),
    state => ((state.plannerPage || {}).subRecPlannerData || {}),
    state => getSelectedWorkspaceSettings((state.plannerPage || {}).workspaces || {}).masterRecTableName,
    state => getSelectedWorkspaceSettings((state.plannerPage || {}).workspaces || {}).pagedMasterRecPlannerDataGuid,
    state => getSelectedWorkspaceSettings((state.plannerPage || {}).workspaces || {}).subRecPlannerDataGuid,
    (pagedMasterRecPlannerData, subRecPlannerData, masterRecTableName, pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid) => {
        return masterRecTableName === TABLE_NAMES.RESOURCE
            ? pagedMasterRecPlannerData[pagedMasterRecPlannerDataGuid]
            : subRecPlannerData[subRecPlannerDataGuid];
    }
);

const noDiaryCacheSizeModifier = 50;
const getResourceNoDiaryRangesSelector = createSelector(
    getCurrentResourcePlannerData,
    getFieldInfoSelector,
    (resourceData, getFieldInfo) => {
        const cache = {};

        return (resourceGuid, startDate, endDate) => {
            if (!(resourceGuid in cache)) {
                const cacheStartDate = subDays(startDate, noDiaryCacheSizeModifier);
                const cacheEndDate = addDays(endDate, noDiaryCacheSizeModifier);
                const ranges = getNoDiariesInRange(resourceGuid, cacheStartDate, cacheEndDate, resourceData, getFieldInfo);

                cache[resourceGuid] = {
                    ranges,
                    cacheStartDate,
                    cacheEndDate
                };
            } else {
                if (isBefore(startDate, cache[resourceGuid].cacheStartDate, DATE_TIME_UNITS.DAY)) {
                    const newCacheStartDate = subDays(startDate, noDiaryCacheSizeModifier);
                    const rangesBeforeStartDate = getNoDiariesInRange(
                        resourceGuid,
                        newCacheStartDate,
                        cache[resourceGuid].cacheStartDate,
                        resourceData,
                        getFieldInfo
                    );

                    cache[resourceGuid].ranges = mergeOverlappingRanges(cache[resourceGuid].ranges, rangesBeforeStartDate);
                    cache[resourceGuid].cacheStartDate = newCacheStartDate;
                }

                if (isAfter(endDate, cache[resourceGuid].cacheEndDate, DATE_TIME_UNITS.DAY)) {
                    const newCacheEndDate = addDays(endDate, noDiaryCacheSizeModifier);
                    const rangesAfterEndDate = getNoDiariesInRange(
                        resourceGuid,
                        cache[resourceGuid].cacheEndDate,
                        newCacheEndDate,
                        resourceData,
                        getFieldInfo
                    );

                    cache[resourceGuid].ranges = mergeOverlappingRanges(cache[resourceGuid].ranges, rangesAfterEndDate);
                    cache[resourceGuid].cacheEndDate = newCacheEndDate;
                }
            }

            return getRangesInRange(cache[resourceGuid].ranges, startDate, endDate);
        };
    }
);

const getResourcesDiariesSelector = createSelector(
    state => getCurrentPlannerData(state),
    state => getDiaryGroupSelector(state),
    (plannerData, getDiaryGroup) => {
        const { rows } = plannerData;

        return rows.reduce((accumulator, currentRow) => {
            const resourceCurrentDiary = getDiaryGroup(currentRow.id);

            accumulator.push(resourceCurrentDiary);

            return accumulator;
        }, []);
    }
);

const getDiaryGroupSelector = createSelector(
    getCurrentResourcePlannerData,
    getFieldInfoSelector,
    (resourceData, getFieldInfo) => {
        return (resourceGuid) => {
            let diaryGroup = [];

            if (resourceLoaded(resourceData, resourceGuid)) {
                diaryGroup = getDiaryGroup(resourceData.data, resourceData.byId[resourceGuid], getFieldInfo);
            }

            return diaryGroup;
        };
    }
);

const getResourceWorkingDaysRangesSelector = createSelector(
    getDiaryGroupSelector,
    getDiaryWorkingDaysRangesSelector,
    state => getLicenseValuesByKeySelector(state)(licenseDiaryYearsPersistedInHistory)?.subscribedCount || 1,
    (getDiaryGroup, getDiaryWorkingDaysRanges, diaryYearsPersistedInHistory) => {
        return (resourceGuid, startDate, endDate, respectNWD = true) => {
            let workingRanges = [];
            const diaryGroup = getDiaryGroup(resourceGuid);
            const isUnassignedResource = resourceGuid === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID || resourceGuid === null;

            if (diaryGroup.length > 0) {
                diaryGroup
                    .filter(group => !isNoDiaryGroup(group) && isBefore(group.startDate, endDate) && (group.endDate === null || isAfter(group.endDate, startDate, 'day', true)))
                    .forEach(group => {
                        const start = isBefore(group.startDate, startDate) ? startDate : startOfDay(group.startDate);
                        const end = group.endDate === null || isAfter(group.endDate, endDate) ? endDate : endOfDay(group.endDate);
                        let diaryWorkingDaysRanges = [{
                            startDate: start,
                            endDate: end
                        }];

                        if (respectNWD) {
                            diaryWorkingDaysRanges = getDiaryWorkingDaysRanges(group.label, start, end, diaryYearsPersistedInHistory);
                        }

                        workingRanges = mergeOverlappingRanges(workingRanges, diaryWorkingDaysRanges);
                    });
            } else if (isUnassignedResource) {
                workingRanges = getDiaryWorkingDaysRanges(undefined, startDate, endDate, diaryYearsPersistedInHistory);
            }

            return workingRanges;
        };
    }
);

const getResourceDateHasNoDiarySelector = createSelector(
    getDiaryGroupSelector,
    (getDiaryGroup) => {
        return (resourceGuid, date) => {
            if (isUnassignedResourceGuid(resourceGuid) || !resourceGuid) {
                return false;
            }

            const diaryGroup = getDiaryGroup(resourceGuid);

            return diaryGroup.length === 0
                || isBefore(date, diaryGroup[0].startDate)
                || diaryGroup
                    .filter(isNoDiaryGroup)
                    .some(group => group.endDate != null
                        ? isWithinRange(date, parseToUtcDate(group.startDate), parseToUtcDate(group.endDate), DATE_TIME_UNITS.DAY, '[]')
                        : isAfter(date, parseToUtcDate(group.startDate), DATE_TIME_UNITS.DAY, true));
        };
    }
);

const getDetailsPaneTooltipMessages = createSelector(
    state => {
        const translationIds = ['detailsPaneTooltipText', 'detailsPaneTooltipTitle', 'closeButtonLabel'];
        const selectorProps = { sectionName: 'entityWindow', idsArray: translationIds };

        return getTranslationsSelector(state, selectorProps);
    },
    (messages) => messages
);

const getDateBarMessages = createSelector(
    state => {
        const pageAlias = getCurrentPageAliasSelector(state);
        const selectorProps = { sectionName: pageAlias, idsArray: ['dateBar'] };

        return getTranslationsSelector(state, selectorProps);
    },
    (messages) => messages.dateBar
);

const getPlannerToasterMessageProps = createSelector(
    state => state.toasterMessage,
    (toasterState) => {
        const { showSuccess, showError, showWarning, top, duration, displayMessage } = toasterState;

        return {
            messageComponent: message,
            isSuccess: showSuccess,
            isError: showError,
            isWarning: showWarning,
            top,
            duration,
            displayMessage
        };
    }
);

const resourceDiaryFieldLoaded = createSelector(
    getCurrentResourcePlannerData,
    (resourceData) => {
        const { data } = resourceData;
        const diaryGroup = tempGetTableSystemFields(TABLE_NAMES.RESOURCE)[DIARY_GROUP];
        let result = false;

        if (diaryGroup && diaryGroup.name) {
            result = Object.keys(data).some((index) => {
                return (data[index].hasOwnProperty(diaryGroup.name.toLowerCase()));
            });
        }

        return result;
    }
);

const getPlannerRoleRequestStatusGuidSelector = createSelector(
    (state) => state[PLANNER_PAGE_ALIAS].plannerTableDatas[TABLE_NAMES.ROLEREQUESTSTATUS],
    (tableData) => {
        return (statusDescription) => tableData.data.filter(field => field[ROLEREQUESTSTATUS_FIELDS.DESCRIPTION] == statusDescription)[0][ROLEREQUESTSTATUS_FIELDS.GUID];
    }
);

const getClipboardCriteriasSelector = createSelector(
    (state) => state.clipboard,
    (clipboard) => {
        let criterias = [];

        if (
            clipboard
            && clipboard.bar
            && clipboard.bar.criterias
            && clipboard.bar.criterias.length > 0
        ) {
            criterias = clipboard.bar.criterias;
        }

        return criterias;
    }
);

const getPlannerSelectionByBarTableSelector = createSelector(
    state => getPlannerSelectedEntitiesSelector(state.plannerPage),
    (getSelectedEntities) => {
        let selectionByBarTable = {};

        BAR_TABLE_NAMES.forEach(table => {
            selectionByBarTable[table] = getSelectedEntities(table);
        });

        return selectionByBarTable;
    }
);

export const getCurrentCellSelectionDataSelector = createSelector(
    state => getCurrentPlannerData(state),
    (currentPlannerData) => {
        if (!Object.keys(currentPlannerData.selection).length) {
            return {};
        }

        const { rowIndex, childRowIndex } = (currentPlannerData.selection.positionData || {});
        const { actor } = currentPlannerData.selection || {};
        const currentRow = currentPlannerData.rows[rowIndex] || {};

        const { tableName: parentTableName, subRows, expanded, id: parentRowId } = currentRow;
        const subRow = (expanded && childRowIndex > 0) ? subRows[childRowIndex - 1] : {};
        const { tableName: childTableName, id: childRowId } = subRow || {};

        return {
            parentTableName,
            parentRowId,
            childTableName,
            childRowId,
            parentRowExpanded: expanded,
            actor
        };
    }
);

const getIsPlannerMenuItemActionAllowedSelector = createSelector(
    state => getPlannerSelectionByBarTableSelector(state),
    state => getMenuItemActionAccessibleIdsSelector(state),
    state => getCurrentCellSelectionDataSelector(state),
    (selectionByBarTable, getMenuItemActionAccessibleIds, plannerSelection) => (menuItemActionConfig = {}) => {
        const { customHideActionConditions = [], options = {} } = menuItemActionConfig;
        const { tableName: actionTableName = '' } = options || {};

        let result = true;

        if (customHideActionConditions.length > 0) {
            const { ids: actionRelatedEntitiesIds = [] } = selectionByBarTable[actionTableName] || {};

            const context = {
                actionAccessibleIds: getMenuItemActionAccessibleIds(actionRelatedEntitiesIds, menuItemActionConfig),
                selectedRoles: (selectionByBarTable[TABLE_NAMES.ROLEREQUEST] || {}).entities,
                selectedBookings: (selectionByBarTable[TABLE_NAMES.BOOKING] || {}).entities,
                plannerSelection
            };

            result = !evalCommandBarCustomHideActionConditions(customHideActionConditions, context, PLANNER_PAGE_ALIAS);
        }

        return result;
    }
);

const getPlannerPaginationModeSelector = createSelector(
    state => getCurrentPlannerPagedDataObject(state[PLANNER_PAGE_ALIAS]).rowCount,
    state => getLicenseValuesByKeySelector(state)(licensePlannerPageMaxInfiniteScrollRows).subscribedCount,
    (rowCount, subscribedCount) => getPlannerPaginationMode(rowCount, subscribedCount)
);

const getShouldHidePlannerPagePaginationSelector = createSelector(
    state => isPlannerPageGuidDataLoading(state.plannerPage),
    state => getLicenseValuesByKeySelector(state)(licensePlannerPageMaxInfiniteScrollRows)?.subscribedCount,
    (isPlannerLoading, subscribedCount) => (rowCount, pageSize) => {
        return isPlannerLoading
            || rowCount <= pageSize
            || isInfiniteScrollAllowed(rowCount, subscribedCount);
    }
);

const getShouldShowFloatingActionBarSelector = createSelector(
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.masterRecTableName);
    },
    state => {
        const workspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);

        return getWorkspaceRecordsListSortOrder(workspaceSettings, workspaceSettings.subRecTableName);
    },
    state => getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state),
    state => state.plannerPage.workspaces,
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (masterRecSortOrder, subRecSortOrder, calcFieldsEnabled, workspaces, wsSettings) => {

        if (!workspaces.workspacesSettings.showSortFloatingActionBar) {
            return false;
        }
        if (!calcFieldsEnabled) {
            return false;
        }

        const masterHasCalcFields = masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE &&
            masterRecSortOrder.orderFields.some(field => calcFields.includes(field.field));

        const subHasCalcFields = subRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.JOB &&
            subRecSortOrder.orderFields.some(field => calcFields.includes(field.field));

        return masterHasCalcFields || subHasCalcFields;
    }
);

const getFloatingActionBarLabelSelector = createSelector(
    state => getTranslationsSelector(state, translationProps).recordsList,
    state => getTranslationsSelector(state, { sectionName: 'common' }),
    state => getRlOrder(state),
    state => getRlConfig(state).config,
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (recordsList, commonMessages, recSortOrder, rlConfig, wsSettings) => {
        const { floatingActionBarLabel } = commonMessages;
        const { resourceLabel } = recordsList;
        const { parentDisplayFields, childDisplayFields } = rlConfig;
        const controlFields = unionBy(childDisplayFields.detailFields, parentDisplayFields.detailFields, 'field');

        const masterHasCalcFields = recSortOrder.masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE &&
            recSortOrder.masterRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const subHasCalcFields = recSortOrder.subRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.JOB &&
            recSortOrder.subRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const calculatedFields = unionBy(masterHasCalcFields, subHasCalcFields);
        const currentOrderFieldDetails = calculatedFields.length > 0 && controlFields.filter(field => calculatedFields[0].field === field.field);
        const currentOrderFieldTitle = currentOrderFieldDetails[0]?.title || currentOrderFieldDetails[0]?.field;

        const { floatingActionBarLabel: floatingActionBarLabelText } = populateStringTemplates({ floatingActionBarLabel }, { fieldName: currentOrderFieldTitle, entity: resourceLabel });

        return floatingActionBarLabelText;
    }
);

const getIsCalcFieldSortedSelector = createSelector(
    state => getRlOrder(state),
    state => getRlConfig(state).config,
    state => getSelectedWorkspaceSettings(state.plannerPage.workspaces),
    (recSortOrder, rlConfig, wsSettings) => {
        const { parentDisplayFields, childDisplayFields } = rlConfig;
        const controlFields = unionBy(childDisplayFields.detailFields, parentDisplayFields.detailFields, 'field');

        const masterHasCalcFields = recSortOrder.masterRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.RESOURCE &&
            recSortOrder.masterRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const subHasCalcFields = recSortOrder.subRecSortOrder && wsSettings.masterRecTableName === TABLE_NAMES.JOB &&
            recSortOrder.subRecSortOrder.orderFields.filter(field => calcFields.includes(field.field));

        const calculatedFields = unionBy(masterHasCalcFields, subHasCalcFields);
        const currentOrderFieldDetails = calculatedFields.length > 0 && controlFields.filter(field => calculatedFields[0].field === field.field)[0];

        return currentOrderFieldDetails;
    }
);

export {
    getCurrentPlannerFilters,
    getCurrentPlannerData,
    getCurrentPlannerBarGroups,
    getCurrentPlannerPagedData,
    getCurrentPlannerBarGroupsObject,
    getActivePlannerEntitySelector,
    getCurrentPlannerBarsMap,
    getCurrentPlannerBarSelection,
    getSelectedBarsCount,
    getCurrentPlannerBookingIds,
    getCurrentPlannerRoleIds,
    getCurrentPlannerBarTypesIds,
    getSubRecDataBase,
    getCurrentPlannerSubRecDataObject,
    getPagedDataObjectBase,
    getCurrentPlannerPageNumber,
    getCurrentPagePlannerData,
    getCurrentPageSubRecPlannerData,
    getCurrentPlannerPagedDataObject,
    getCurrentPlannerSubRecData,
    getCurrentPlannerDetailsPane,
    getCurrentResourcePlannerData,
    getCollectionAliasForTableName,
    getCurrentEditsForTableName,
    isPlannerPageGuidDataLoading,
    getPlannerPageFiltersGuid,
    getBookingsCollectionAlias,
    getMasterRecCollectionAlias,
    getSubRecCollectionAlias,
    getCurrentPlannerTableDatas,
    getCutBar,
    getCurrentFiltersSelection,
    getPlannerGroupedTableNames,
    getPlannerPageFilters,
    getVisiblePageDates,
    getFilterVisibleDates,
    getPlannerPageFilterPaneCollapsed,
    getPlannerPageFieldOptions,
    getJobPlannerData,
    getPlannerDataSelector,
    getRlDisplayDataSelector,
    getRlIsFieldLoadedSelector,
    getCgGetDataWrappedSelector,
    getGroupBarsDataSelector,
    getGetBarDisplayDataSelector,
    getCommandBarActionProps,
    getRlAvatarConfig,
    getRlOrder,
    getRlDisplayFieldsSelector,
    getRlConfig,
    getWaitPlanner,
    isUnassignedResource,
    hasOnlySubRecsSelected,
    getPlannerDetailsPaneTabsCreateSelector,
    getDetailsPaneSelectionIsValid,
    getRlPlannerDataSelector,
    getFlattenedRowsInfoSelector,
    getRlSelectedRecords,
    getPlannerHasActiveSelection,
    getCurrentViewHideHistoricRecords,
    getCurrentViewHideFutureRecords,
    getCurrentViewHideUnassignedResourceRows,
    getCurrentViewHideJobTimeline,
    getCurrentViewHideJobMilestones,
    getCurrentViewHideRolesRecords,
    getCurrentViewHideBookingBarsFieldLabels,
    getCurrentViewHideRoleBarsFieldLabels,
    getCurrentViewHideWeekends,
    getCurrentViewHidePotentialConflicts,
    getDiaryWorkingDaysRangesSelector,
    getDiaryGroupSelector,
    getResourcesDiariesSelector,
    getResourceWorkingDaysRangesSelector,
    getResourceNoDiaryRangesSelector,
    getResourceDateHasNoDiarySelector,
    getDetailsPaneTooltipMessages,
    getPlannerPageDataCollections,
    getPlannerPageEntityWindowAdditionalPayloadData,
    getDateBarMessages,
    getPlannerToasterMessageProps,
    resourceDiaryFieldLoaded,
    getPlannerRoleRequestStatusGuidSelector,
    getCurrentPlannerBarGroupDataMappedSelector,
    getFilteredPlannerRoleRequestsByStatusDescriptionSelector,
    getCurrentViewHideDraftRolesRecords,
    getCurrentViewHideRolesByRequirementsRecords,
    getCurrentViewHideRequestedRolesRecords,
    getCurrentViewHideLiveBars,
    getCurrentViewHideUnassignedRolesRecords,
    getCurrentViewHideRolesByNameRecords,
    getPlannerTableDatasByTableName,
    getMergedBarSelection,
    getSelectedBarsIntervalSelector,
    getBarMultipleTableSelectionActive,
    getBarActiveSelectionTableName,
    getCurrentPlannerSelectionSelector,
    getChildRowIndicesFromSelectionSelector,
    getClipboardCriteriasSelector,
    getIsPlannerMenuItemActionAllowedSelector,
    getPlannerSelectionByBarTableSelector,
    getCurrentPlannerCriteriaRoleIds,
    getPlannerPaginationModeSelector,
    getShouldHidePlannerPagePaginationSelector,
    getCurrentViewHideInactiveResources,
    getShouldShowFloatingActionBarSelector,
    getFloatingActionBarLabelSelector,
    getIsCalcFieldSortedSelector
};