import { createSelector } from 'reselect';
import { camelCase } from 'lodash';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { JOBS_PAGE_ALIAS, PAGE_NAMES, PLANNER_PAGE_ALIAS } from '../constants';
import { getTranslationsSelector } from './internationalizationSelectors';
import { buildBrowserHistoryUrl } from '../history';
import { getApplicationUserName, getApplicationUserSurrogateId } from './applicationUserSelectors';
import { APPLICATION_NAME, FEATURE_FLAGS, TABLE_NAMES, URL_PARAMS } from '../constants/globalConsts';
import { getNotificationBadgeStatus } from './notificationsPageSelectors';
import { NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { ADMIN_SETTINGS_PAGE, JOBS_PAGE, LIST_PAGE } from '../pages/pages';
import { SECTION_CONSTANTS } from '../constants/adminSettingConsts';
import { MASS_DUPLICATE_JOBS_ALIAS, MASS_DUPLICATE_JOBS_PAGE } from '../constants/massDuplicateJobsConsts';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';
import { getFeatureFlagSelector } from './featureManagementSelectors';

export const getApplicationPages = createSelector(
    pages => pages,
    (pages) => {
        const { map, orderedKeys, loaded } = pages;

        return loaded ? orderedKeys
            .filter((pageKey) => (map[pageKey].visible && map[pageKey].hasAccess) || map[pageKey].alwaysVisible)
            .map((pageKey) => map[pageKey]) : [];
    }
);

export const getAllPages = createSelector(
    pages => pages,
    (pages) => {
        const { map, orderedKeys, loaded } = pages;

        return loaded ? orderedKeys.map((pageKey) => map[pageKey]) : [];
    }
);

export const getErrorPageGoToPage = createSelector(
    getAllPages,
    (pages = []) => {
        let goToPage = null;
        const plannerPage = pages.find(page => page.internalName === PAGE_NAMES.PLANNER);

        if (plannerPage && plannerPage.visible && plannerPage.hasAccess)
            goToPage = plannerPage;
        else
            goToPage = pages.find(page => page.visible && page.hasAccess);

        return goToPage;
    }
);

export const getNavigationState = createSelector(
    state => state.navigation,
    state => state.applicationSettings.pages,
    state => state.adminSetting,
    (navigation, appPages, adminSetting) => {
        const { map = null } = appPages;
        const pages = getApplicationPages(appPages);
        const page = pages.find((p) => p.name == navigation.page);
        let subPage = null;

        if (map)
            subPage = navigation.page === map[PAGE_NAMES.ADMIN_SETTINGS].name ? adminSetting.subnavigation.activePage : null;

        return { page, subPage };
    }
);

export const getStaticMessagesSelector = createSelector(
    state => getTranslationsSelector(state, { sectionName: 'navigation' }),
    (messages) => messages
);

const getPageTitle = (applicationPages = [], pageName, defaultTitle, listPageAndBulkUpdateFeatureFlag = false) => {
    if (listPageAndBulkUpdateFeatureFlag) {
        if (pageName === JOBS_PAGE_ALIAS || pageName === RESOURCES_PAGE_ALIAS) {
            const { title } = applicationPages.find(page => page.name === LIST_PAGE_ALIAS) || {};
            return title ? title : defaultTitle;
        }
    }

    const { title } = applicationPages.find(page => page.name === pageName) || {};

    return title ? title : defaultTitle;
};

const getApplicatonSettings = (state) => (state.applicationSettings || {}).pages || {};

export const getNavPageTitleSelector = createSelector(
    getApplicatonSettings,
    state => getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state),
    (getApplicatonSettings, listPageAndBulkUpdateFeatureFlag) => (pageName, defaultTitle) => {
        const applicationPages = getApplicationPages(getApplicatonSettings);

        return getPageTitle(applicationPages, pageName, defaultTitle, listPageAndBulkUpdateFeatureFlag);
    }
);

export const getPageStateParamsSelector = createSelector(
    state => state,
    (state) => (pageName) => {
        let result = {};

        if ((state[pageName] || {}).pageState) {
            result = state[pageName].pageState.params || {};
        }

        return result;
    }
);

export const getPageLinkSelector = createSelector(
    getPageStateParamsSelector,
    (getPageStateParams) => (pageName) => {
        const pageStateParams = getPageStateParams(pageName);

        return buildBrowserHistoryUrl(pageStateParams, pageName);
    }
);

export const getIsShowBadgeOnPageIconSelector = createSelector(
    state => state,
    (state) => (pageName) => {
        let isShowBadge = false;

        switch (pageName) {
            case NOTIFICATIONS_PAGE_ALIAS:
                isShowBadge = getNotificationBadgeStatus(state);
                break;
            default:
                break;
        }

        return isShowBadge;
    }
);

export const getNavigationPageState = createSelector(
    state => state.navigation.page,
    (page) => (page)
);

export const buildNavPagesSelector = createSelector(
    getPageStateParamsSelector,
    getIsShowBadgeOnPageIconSelector,
    state => state.applicationUser,
    state => state.applicationSettings.pages,
    state => getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state),
    (getPageParams, getIsShowBadgeOnPageIcon, applicationUser, pagesState, listPageAndBulkUpdateFeatureFlag) => {
        const pages = getApplicationPages(pagesState) || [];
        const navPages = pages.filter(page => {
            if (listPageAndBulkUpdateFeatureFlag) {
                if (page.name === JOBS_PAGE_ALIAS) {
                    return false;
                }
            } else {
                if (page.name === LIST_PAGE_ALIAS) {
                    return false;
                }
            }
            return true;
        })
        .map(page => {
            let pageParams = getPageParams(page.name);

            if (page.name == PROFILE_PAGE_ALIAS) {
                pageParams = {
                    ...pageParams,
                    [URL_PARAMS.RESOURCE_SURROGATE_ID]: getApplicationUserSurrogateId({ applicationUser }),
                    [URL_PARAMS.RESOURCE_NAME]: getApplicationUserName({ applicationUser })
                };
            }

            const navLink = buildBrowserHistoryUrl(pageParams, page.name);

            return {
                ...page,
                builtNavLink: navLink,
                showBadgeOnPageIcon: getIsShowBadgeOnPageIcon(page.name)
            };
        });

        return navPages;
    }
);

export const getCurrentPageAliasSelector = createSelector(
    state => state.navigation || {},
    state => state.adminSetting?.subnavigation?.activePage || '',
    (navigation, activePage) => {
        const { page = PLANNER_PAGE_ALIAS, subPage = '' } = navigation;

        if (page === ADMIN_SETTINGS_PAGE.name && activePage === MASS_DUPLICATE_JOBS_ALIAS.toLowerCase()) {
            return camelCase(MASS_DUPLICATE_JOBS_PAGE);
        }

        return subPage !== '' ? subPage : page;
    }
);

export const getIsCurrentPageSubPage = createSelector(
    state => state.navigation || {},
    state => getCurrentPageAliasSelector(state),
    (navigation, pageAlias) => {
        const { subPage = '' } = navigation;

        return subPage === pageAlias;
    }
);

export const getCurrentPageTitleSelector = createSelector(
    getNavPageTitleSelector,
    (navPageTitle) => (pageAlias) => `${APPLICATION_NAME} - ${navPageTitle(pageAlias)}`
);

export const getCompanyInformationSelector = createSelector(
    state => state.companyInformation.companyInformation.supportEmail || '',
    state => state.companyInformation.companyInformation.supportPhoneNumber || '',
    (supportEmail, supportPhoneNumber) => ({
        supportEmail,
        supportPhoneNumber
    })
);