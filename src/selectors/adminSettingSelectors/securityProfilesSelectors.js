import { createSelector } from 'reselect';
import { TABLE_NAMES } from '../../constants';
import { ENTITY_ACCESS_CONDITION_TYPES, FNAS_KEY, FUNCTIONAL_ACCESS_LEVEL_TYPES, REMAP_ENTITY_TITLES, SECTION_CONSTANTS } from '../../constants/adminSettingConsts';
import { isEmptyObject } from '../../utils/commonUtils';
import { areJSONObjectsEqual } from '../../utils/adminSettings/securityProfilesEpicsUtils';
import { getFeatureFlagSelector } from '../featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

export const getSecurityProfilesState = state => state.adminSetting.securityProfiles || {};
const getFunctionalAccessesState = state => getSecurityProfilesState(state).functionalAccesses || {};
const getEntityAccessesState = state => getSecurityProfilesState(state).entityAccesses || {};

const getProfilesListSelector = createSelector(
    state => getSecurityProfilesState(state).selectionListData || {},
    (selectionListData) => {
        return selectionListData;
    }
);

const getGeneralTabContentPropsSelector = createSelector(
    state => getFunctionalAccessesState(state).generalTabFNARulesConfig || [],
    state => getFunctionalAccessesState(state).valuesById || {},
    state => getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state),
    (generalTabRulesConfig, valuesById, listPageAndBulkUpdateFeatureFlag) => {
        const rulesWithValues = generalTabRulesConfig.map(ruleConfig => {
            const { id, name, canEdit, subRules = [], key } = ruleConfig;

            const subRulesWithValues = subRules.map(subRule => {
                const { id, name, parentId, canEdit } = subRule;

                return {
                    id,
                    name,
                    parentId,
                    canEdit,
                    value: (valuesById[id] || {}).accessValue
                };
            });

            return {
                id,
                key,
                name,
                canEdit,
                value: (valuesById[id] || {}).accessValue,
                subRules: subRulesWithValues
            };
        });

        return {
            rulesConfig: rulesWithValues,
            listPageAndBulkUpdateFeatureFlag: listPageAndBulkUpdateFeatureFlag
        };
    }
);


/**
 * Get Entity access card configuration based on entityName
 * @param {Object} entityFnas Object we need to build config from
 * @param {Object} accessLevelIdByNameMap contains access level details(CRUD)
 * @param {Object} fnaValuesById contains fna values
 * @param {string} entityName Table or entity name
 * @returns {import('../../types/entityAccessCardConfig.jsdoc').EntityAccessCardConfig} Entity access card configuration object
 */
const getEntityAccessCardsConfig = (entityFnas, accessLevelIdByNameMap, fnaValuesById, entityName) =>{
    if (isEmptyObject(accessLevelIdByNameMap)) {
        return [];
    }

    const entityTableName = REMAP_ENTITY_TITLES[entityName].toLowerCase();
    const entityFNARules = entityFnas[entityTableName] || [];

    const result = entityFNARules.map(fnaRule => {
        const { name: fnaRuleName , key = '' } = fnaRule || {};
        let accessType = '';

        const fnaRuleMatchingAccessLevelName = Object.keys(accessLevelIdByNameMap).find(accessLevelName => {
            accessType = accessLevelName.split(' ')[0];

            return fnaRuleName.includes(accessType);
        });

        const accessLevelId = accessLevelIdByNameMap[fnaRuleMatchingAccessLevelName];
        const isReadAccessType = accessType === FUNCTIONAL_ACCESS_LEVEL_TYPES.READ;

        return {
            isParentCard: isReadAccessType,
            fnaName: fnaRuleName,
            accessType,
            isReadAccessType,
            entityAlias: entityName,
            order: fnaRule.order,
            fnaValueProps: fnaValuesById[fnaRule.id],
            accessLevelId,
            isEntityAccess:true,
            hideEntityAccessConditions: false,
            key
        };
    });

    return result;
};

const getEntityAccessCardsConfigSelector = createSelector(
    state => getFunctionalAccessesState(state).entityFnasByTableName || {},
    state => getSecurityProfilesState(state).accessLevelIdByNameMap || {},
    state => getFunctionalAccessesState(state).valuesById || {},
    (entityFnasByTableName, accessLevelIdByNameMap, fnaValuesById) => (entityName) => {
        return getEntityAccessCardsConfig(entityFnasByTableName, accessLevelIdByNameMap, fnaValuesById, entityName);
    }
);

const getEntityAccessCardsConfigSecuritySelector = createSelector(
    state => getFunctionalAccessesState(state).entityFnasSecurityInfo || {},
    state => getSecurityProfilesState(state).accessLevelIdByNameMap || {},
    state => getFunctionalAccessesState(state).valuesById || {},
    getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL),
    (entityFnasSecurityInfo, accessLevelIdByNameMap, fnaValuesById, skillApprovalEnabled) => (entityName) => {
        // TODO: Remove once we remove feature Flag
        if (entityName === SECTION_CONSTANTS.SKILL && !skillApprovalEnabled) return [];

        const cards = getEntityAccessCardsConfig(entityFnasSecurityInfo, accessLevelIdByNameMap, fnaValuesById, entityName);

        return cards.map(card =>{
            if (card.key === FNAS_KEY.VIEW_BUDGET_DETAILS) {
                // The below parameter overrides if it needs to show in entityAccess as well and if it needs to hide the RadioButtons
                return { ...card , hideEntityAccessConditions:true, isEntityAccess:false };
            }

            return card;
        });
    }
);

const getEntityEditFnaUIValueSelector = createSelector(
    state => getFunctionalAccessesState(state).valuesById || {},
    state => getFunctionalAccessesState(state).entityFnasByTableName || {},
    (fnaValuesById, fnaRulesByEntityTableName) => (entityName) => {
        const entityTableName = `${REMAP_ENTITY_TITLES[entityName]}`.toLowerCase();

        const entityFNARules = (fnaRulesByEntityTableName || {})[entityTableName] || [];
        const { id: editFNARuleId } = entityFNARules.find(rule => (rule || {}).name.includes(FUNCTIONAL_ACCESS_LEVEL_TYPES.EDIT)) || {};

        let result = {};

        if (entityTableName !== TABLE_NAMES.ROLEREQUEST && editFNARuleId) {
            result = (fnaValuesById || {})[editFNARuleId];
        }

        return result;
    }
);

const getEntityAccessTableNameSelector = createSelector(
    state => getSecurityProfilesState(state).entityTableName || {},
    (entityTableName) => entityTableName
);

const getEntityAccessConditionTypeSelector = createSelector(
    state => getEntityAccessesState(state).conditionsValuesByAccessId || {},
    (conditionsValuesByAccessId) => (accessLevelId) => {
        const { conditionType } = conditionsValuesByAccessId[accessLevelId] || {};

        return conditionType;
    }
);

const getEntityAccessCustomConditionSelector = createSelector(
    state => getEntityAccessesState(state).conditionsValuesByAccessId || {},
    (conditionsValuesByAccessId) => (accessLevelId) => {
        const { customConditions } = conditionsValuesByAccessId[accessLevelId] || {};
        const { conditionValue , hasError = false } = customConditions || {};

        return {
            conditionValue,
            hasError
        };
    }
);

const getEntityAccessJsonCustomConditionSelector = createSelector(
    state => getEntityAccessesState(state).conditionsValuesByAccessId || {},
    (conditionsValuesByAccessId) => (accessLevelId) => {
        const { jsonCustomCondition } = conditionsValuesByAccessId[accessLevelId] || {};
        const { condition, hasError = false, conditionValue } = jsonCustomCondition || {};

        return {
            conditionValue,
            condition,
            hasError
        };
    }
);

const getAccessLevelCustomConditionsLists = (state, accessLevelId) => {
    const conditionsByAccessLevel = getEntityAccessesState(state).conditionsValuesByAccessId || {};
    const { customConditions } = conditionsByAccessLevel[accessLevelId] || {};
    const { conditionsLists } = customConditions || {};

    return conditionsLists || [];
};

const getEntityAccessCustomConditionsSelector = createSelector(
    (state, accessLevelId) => getAccessLevelCustomConditionsLists(state, accessLevelId),
    (customConditionsLists) => {
        return customConditionsLists || [];
    }
);

const getEntityAccessPredefinedConditionsSelector = createSelector(
    state => getSecurityProfilesState(state).configs || {},
    (configs) => (entityId, accessLevelId) => {
        const { predefinedConditionsMap = {} } = configs;
        const predefinedConditionsByAccessLevelId = predefinedConditionsMap[entityId] || {};

        return predefinedConditionsByAccessLevelId[accessLevelId] || [];
    }
);

const getEntityAccessPredefinedConditionValueSelector = createSelector(
    state => getEntityAccessesState(state).conditionsValuesByAccessId || {},
    (conditionsValuesByAccessId) => (accessLevelId) => {
        const { predefinedCondition } = conditionsValuesByAccessId[accessLevelId] || {};
        const { conditionId: predefinedConditionId } = predefinedCondition || {};

        return predefinedConditionId;
    }
);

const getHasProfileNameChanges = (nameChangesState = {}) => !isEmptyObject(nameChangesState) && nameChangesState.initialName !== nameChangesState.newName;

const getEntityAccessFNAToggleValueSelector = createSelector(
    state => getFunctionalAccessesState(state).valuesById || {},
    state => getFunctionalAccessesState(state).entityFnasByTableName || {},
    state => getSecurityProfilesState(state).accessLevelsById || {},
    (fnaValuesById, fnaRulesByEntityTableName, accessLevelsById) => (entityTableName, accessLevelId) => {
        const entityFNARules = (fnaRulesByEntityTableName || {})[entityTableName] || [];

        const accessLevelName = accessLevelsById[accessLevelId];
        const accessType = accessLevelName.split(' ')[0];
        const { id: fnaRuleId } = entityFNARules.find(rule => (rule || {}).name.includes(accessType)) || {};

        const { accessValue: fnaToggleValue } = (fnaValuesById || {})[fnaRuleId] || {};

        return fnaToggleValue;
    }
);

const getTabToggledOnEntityAccessLevelsSelector = createSelector(
    state => getEntityAccessesState(state),
    state => getEntityAccessTableNameSelector(state),
    state => getEntityAccessFNAToggleValueSelector(state),
    (entityAccessesState, entityTableName, getEntityAccessFNAToggleValue) => {
        const { conditionsValuesByAccessId } = entityAccessesState || {};

        return Object.values(conditionsValuesByAccessId || {}).filter(entityAccessLevelValues => {
            const { accessLevelId } = entityAccessLevelValues;
            const toggleValue = getEntityAccessFNAToggleValue(entityTableName, accessLevelId);

            return toggleValue === true;
        });
    }
);

const getTabHasErrorsSelector = createSelector(
    state => getSecurityProfilesState(state).hasError,
    state => getTabToggledOnEntityAccessLevelsSelector(state),
    (profileNameHasError, visibleEntityAccessLevels) => {
        const entityAccessesConditionsHasErrors = visibleEntityAccessLevels.some(accessConditionsValues => {
            const { customConditions, jsonCustomCondition, conditionType } = accessConditionsValues || {};
            let hasErrors = false;

            if (conditionType == ENTITY_ACCESS_CONDITION_TYPES.CUSTOM) {
                const { conditionsLists = [], hasError = false } = customConditions || {};

                hasErrors = conditionsLists.some(list => list.some(row => row.hasError)) || hasError;
            } else if (conditionType == ENTITY_ACCESS_CONDITION_TYPES.JSON) {
                const { hasError = false } = jsonCustomCondition || {};

                hasErrors = hasError;
            }

            return hasErrors;
        });

        return profileNameHasError || entityAccessesConditionsHasErrors;
    }
);

const getTabHasEntityAccessUIChangesSelector = createSelector(
    state => getTabToggledOnEntityAccessLevelsSelector(state),
    (visibleEntityAccessLevels) => {
        const result = visibleEntityAccessLevels.some(entityAccessLevelState => {
            const { conditionType, initialConditionType, predefinedCondition = {}, customConditions, jsonCustomCondition } = entityAccessLevelState || {};

            let hasChanges = false;

            if (conditionType !== initialConditionType) {
                hasChanges = true;
            } else {
                if (conditionType === ENTITY_ACCESS_CONDITION_TYPES.PREDEFINED) {
                    hasChanges = predefinedCondition.conditionId !== predefinedCondition.initialConditionId;
                } else if (conditionType === ENTITY_ACCESS_CONDITION_TYPES.CUSTOM) {
                    const { changedListsIndexes = [], conditionValue, initialConditionValue } = customConditions || {};

                    hasChanges = changedListsIndexes.length > 0 || !areJSONObjectsEqual(conditionValue, initialConditionValue);
                } else if (conditionType === ENTITY_ACCESS_CONDITION_TYPES.JSON) {
                    const { condition, initialCondition, conditionValue, initialConditionValue } = jsonCustomCondition || {};

                    hasChanges = initialCondition !== condition || !areJSONObjectsEqual(conditionValue, initialConditionValue);
                }
            }

            return hasChanges;
        });

        return result;
    }
);

const getTabHasChangesSelector = createSelector(
    state => getTabHasEntityAccessUIChangesSelector(state) || false,
    state => getSecurityProfilesState(state).functionalAccesses || {},
    state => getSecurityProfilesState(state).fieldSecurity || {},
    state => getSecurityProfilesState(state).profileNameChanges || {},
    (hasEntityAccessChanges, functionalAccessesState, fieldSecurityState, profileNameChanges) => {
        const hasFNAChanges = (functionalAccessesState.changedIds || []).length > 0;
        const { isModified: hasUpdatedFields = false } = fieldSecurityState;
        const hasProfileNameChanges = getHasProfileNameChanges(profileNameChanges);

        return hasFNAChanges || hasEntityAccessChanges || hasUpdatedFields || hasProfileNameChanges;
    }
);

const getCustomConditionsAreaCollapsedStateSelector = createSelector(
    state => getSecurityProfilesState(state).activeSecurityProfileId,
    state => getSecurityProfilesState(state).customConditionsAreaCollapsedStateMap || {},
    (activeSecurityProfileId, customConditionsAreaCollapsedStateMap) => (accessLevelId, entityId) => {
        return ((customConditionsAreaCollapsedStateMap[activeSecurityProfileId] || {})[entityId] || {})[accessLevelId] || false;
    }
);

export {
    getProfilesListSelector,
    getGeneralTabContentPropsSelector,
    getEntityAccessCardsConfigSelector,
    getEntityEditFnaUIValueSelector,
    getHasProfileNameChanges,
    getEntityAccessPredefinedConditionsSelector,
    getEntityAccessPredefinedConditionValueSelector,
    getTabHasChangesSelector,
    getTabHasErrorsSelector,
    getEntityAccessTableNameSelector,
    getEntityAccessConditionTypeSelector,
    getEntityAccessCustomConditionsSelector,
    getEntityAccessJsonCustomConditionSelector,
    getCustomConditionsAreaCollapsedStateSelector,
    getEntityAccessCustomConditionSelector,
    getEntityAccessCardsConfigSecuritySelector
};