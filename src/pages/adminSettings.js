import React from 'react';
import SubNavigation from '../components/adminSetting/subNavigation/subNavigation.Connect';
import AdminPageContent from '../components/adminSetting/adminPageContent/adminPageContent.Connect';
import { ConnectedHotKeysHelpWindow } from '../connectedComponents/connectedHotKeysHelpWindow';
import '../components/prompts/styles.less';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { ADMIN_SETTING_ALIAS } from '../constants/adminSettingConsts';

const ConnectedPageLayout = React.lazy(() => import('../connectedComponents/connectedPageLayout').then((module) =>
    ({ default: module.ConnectedPageLayout })));

const pageNavigation = {
    component: SubNavigation,
    props: {},
    containerStyle: { background: 'white', width: '230px' }
};

const connectedComponents = (
    <React.Fragment>
        <ConnectedHotKeysHelpWindow />
        <ConnectedPromptModal pageAlias={ADMIN_SETTING_ALIAS} />
    </React.Fragment>
);

const pageContent = {
    component: AdminPageContent,
    props: { connectedComponents },
    containerStyle: {}
};

const pageObj = { commandBar: undefined, pageNavigation, pageContent, statusBar: undefined, connectedComponents };

class AdminSettings extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        const allProps = { ...this.props, ...pageObj };

        return (
            <React.Suspense fallback={<div />}>
                <ConnectedPageLayout {...allProps} />
            </React.Suspense>
        );
    }
}

export default AdminSettings;