import React from 'react';
import { connect } from 'react-redux';
import BasePage from './basePage';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import { ConnectedRoleGroupEntityWindow, ConnectedGlobalCreateModalEntityWindow, ConnectedJobsPageModalEntityWindow } from '../connectedComponents/connectedEntityWindow';
import ConnectedProgressRolesWindow from '../connectedComponents/connectedRoleTransitionDialogs/connectedProgressRoles';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import { ConnectedJobsDetailsPane, ConnectedJobsPaneTabsContent } from '../connectedComponents/connectedRoleGroupListPageDetailsPane';
import ConnectedBreadcrumb from '../connectedComponents/connectedBreadCrumb';
import { HOT_KEYS_HELP_WINDOW_SECTIONS } from '../constants';
import { ConnectedRoleGroupsListDataGrid } from '../connectedComponents/connectedDataGrid/connectedRoleGroupListDataGrid';
import { getPageState } from '../selectors/pagesSelectors';
import { rolegroupListPageConnectedCommandBar } from '../connectedComponents/connectedCommandBar/rolegroupListPageConnectedCommandBar';
import { ConnectedCreateRolegroupModal } from '../connectedComponents/connectedRolegroupListPageComponents/connectedCreateRolegroupModal';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { ConnectedRoleGroupDuplicateDialog } from '../connectedComponents/connectedRoleGroupDuplicateDialog';

const ConnectedRolegroupListLayout = React.lazy(() =>
    import('../connectedComponents/ConnectedRolegroupListLayout').then((module) =>
        ({ default: module.ConnectedRolegroupListLayout })));

const commandBar = {
    component: rolegroupListPageConnectedCommandBar,
    containerStyle: { background: 'white', display: 'flex', alignItems: 'center', padding: '0px' }
};

const dataGrid = {
    component: ConnectedRoleGroupsListDataGrid,
    props: {},
    containerStyle: {
        overflow: 'hidden',
        backgroundColor: '#fff'
    }
};

const detailsPane = {
    component: ConnectedJobsDetailsPane,
    props: {
        layoutProps: {
            style: {
                background: 'white',
                overflow: 'hidden',
                position: 'fixed',
                right: '0',
                height: '100%'
            },
            width: '550px'
        },
        classNameVisible: 'PLANNER_CLEAR_SELECTION_IGNORE_CLASS',
        classNameHidden: 'details-pane-container-hidden',
        contentComponent: ConnectedJobsPaneTabsContent,
        contentComponentProps: {},
        prefix: 'dp'
    }
};

const connectedComponents = (
    <React.Fragment>
        <ConnectedJobsPageModalEntityWindow />
        <ConnectedRoleGroupDuplicateDialog />
        <ConnectedProgressRolesWindow />
        <ConnectedCreateRolegroupModal pageAlias={ROLE_GROUP_LIST_PAGE} />
        <ConnectedPromptModal pageAlias={ROLE_GROUP_LIST_PAGE}/>
        <ConnectedGlobalCreateModalEntityWindow />
        <ConnectedRoleGroupEntityWindow />
    </React.Fragment>
);

const masterPageOptions = {
    masterPageAlias: JOBS_PAGE_ALIAS,
    masterPageDisplayName :HOT_KEYS_HELP_WINDOW_SECTIONS.JOBS
};
const breadcrumb = {
    component: ConnectedBreadcrumb,
    props: { pageAlias: ROLE_GROUP_LIST_PAGE , masterPageOptions, getPageState }
};

const pageObj = { commandBar, detailsPane, connectedComponents, breadcrumb, dataGrid };

class RolegroupListPage extends BasePage {
    constructor(props) {
        super(props);
    }

    internalRender() {
        const allProps = { ...this.props, ...pageObj };

        return (
            <React.Suspense fallback={<div />}>
                <ConnectedRolegroupListLayout {...allProps} />
            </React.Suspense>
        );
    }
}

const mapStateToProps = (state) => {
    const { parked } = state.rolegroupListPage.tableDatas;

    return {
        parked
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onPageMount: (params) => dispatch(PAGE_ACTIONS.OPEN[ROLE_GROUP_LIST_PAGE](params.location)),
        onPageUnmount: () => dispatch(PAGE_ACTIONS.CLOSE[ROLE_GROUP_LIST_PAGE](true)),
        dispatchAction: (action) => dispatch(action)
    };
};

const ConnectedRolegroupListPage = connect(
    mapStateToProps,
    mapDispatchToProps
)(RolegroupListPage);

export default ConnectedRolegroupListPage;