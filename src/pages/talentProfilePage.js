import React from 'react';
import { connect } from 'react-redux';
import ConnectedTalentProfile from '../connectedComponents/talentProfile';
import ConnectedProfileCommandBar from '../connectedComponents/talentProfile/connectedProfileCommandBar';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import BasePage from './basePage';
import { ConnectedUpdateAvatarWindow } from '../connectedComponents/talentProfile/connectedUpdateAvatarWindow';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { PROFILE_PAGE_ALIAS, TALENT_PROFILE_ALIAS } from '../constants/talentProfileConsts';
import { ConnectedEditResourceSkillsWindow } from '../connectedComponents/connectedEditResourceSkillsWindow';
import ConnectedPeopleFinderDialog from '../connectedComponents/ConnectedPeopleFinder';
import { ConnectedPlannerToasterMessage } from '../connectedComponents/connectedPlannerToasterMessage';
import { ConnectedGlobalCreateModalEntityWindow, ConnectedProfilePageModalEntityWindow } from '../connectedComponents/connectedEntityWindow';
import ConnectedExperienceModal from '../connectedComponents/connectedExperience/connectedModal';
import ConnectedEducationExperienceModal from '../connectedComponents/connectedEducation/connectedModal';
import { getCurrentPageTitleSelector } from '../selectors/navigationSelectors';
import { talentProfileNavSectionLinksSelector } from '../selectors/talentProfileSelectors';
import { removeNavSectionLink, updateNavSectionLinks } from '../actions/talentProfileActions';
import { FEATURE_FLAGS } from '../constants/globalConsts.js';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors.js';

const TalentProfileLayout = React.lazy(() => import('../layouts/talent-profile-layout'));

const commandBar = {
    component: ConnectedProfileCommandBar,
    containerStyle: {}
};

const content = {
    component: ConnectedTalentProfile
};

const connectedComponents = (
    <React.Fragment>
        <ConnectedUpdateAvatarWindow />
        <ConnectedEditResourceSkillsWindow pageAlias={PROFILE_PAGE_ALIAS}/>
        <ConnectedPromptModal pageAlias={PROFILE_PAGE_ALIAS} />
        <ConnectedPeopleFinderDialog />
        <ConnectedPlannerToasterMessage />
        <ConnectedProfilePageModalEntityWindow />
        <ConnectedExperienceModal alias={TALENT_PROFILE_ALIAS} />
        <ConnectedEducationExperienceModal alias={TALENT_PROFILE_ALIAS} />
        <ConnectedGlobalCreateModalEntityWindow />
    </React.Fragment>
);

const pageObj = { content, connectedComponents, commandBar };

class TalentProfilePage extends BasePage {
    constructor(props) {
        super(props);
    }

    internalRender() {
        const allProps = { ...this.props, ...pageObj };
        allProps.commandBar.props = this.props;

        return (
            <React.Suspense fallback={<div />}>
                <TalentProfileLayout {...allProps} />
            </React.Suspense>
        );
    }
}

const mapStateToProps = (state) => {
    const { pageState } = state.plannerPage;
    const pageTitle = getCurrentPageTitleSelector(state)(PROFILE_PAGE_ALIAS);
    const talentProfileNavSectionLinks = talentProfileNavSectionLinksSelector(state);
    const talentProfilePageTransformedEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED)(state);

    return {
        ...pageState,
        pageTitle,
        talentProfileNavSectionLinks,
        talentProfilePageTransformedEnabled
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onPageMount: (routerProps) => dispatch(PAGE_ACTIONS.OPEN[PROFILE_PAGE_ALIAS](routerProps.location)),
        onRegisterLinks: (data) => dispatch(updateNavSectionLinks(data)),
        onRemoveRegisteredLink: (id) => dispatch(removeNavSectionLink(id))
    };
};

const ConnectedTalentProfilePage = connect(
    mapStateToProps,
    mapDispatchToProps
)(TalentProfilePage);


export default ConnectedTalentProfilePage;