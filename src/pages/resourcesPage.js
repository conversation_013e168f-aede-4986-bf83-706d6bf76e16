import React from 'react';
import { connect } from 'react-redux';
import { JobsPageConnectedCommandBar } from '../connectedComponents/connectedCommandBar/jobsPageConnectedCommandBar';
import { ConnectedEntityListLayout } from '../connectedComponents/connectedEntityListLayout';
import { ConnectedJobsGrid } from '../connectedComponents/connectedDataGrid';
import { ConnectedJobsPageFiltersPane } from '../connectedComponents/connectedFilters';
import BasePage from './basePage';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import { ConnectedJobsPageModalEntityWindow, ConnectedJobsPageBatchModalEntityWindow, ConnectedGlobalCreateModalEntityWindow, ConnectedRoleGroupEntityWindow } from '../connectedComponents/connectedEntityWindow';
import { ConnectedClientLookupEntityWindowJobsPage, ConnectedJobsLookupEntityWindowJobsPage } from '../connectedComponents/connectedEntityLookupWindow';
import { ConnectedHotKeysHelpWindow } from '../connectedComponents/connectedHotKeysHelpWindow';
import { createConnectedTooltipContextualMenu } from '../connectedComponents/connectedTooltipContextualMenu';
import { createConnectedTooltip } from '../connectedComponents/connectedTooltip';
import { JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { getSelectedEntitiesSelector } from '../selectors/commonSelectors';
import { ConnectedDetailsPane, ConnectedPaneTabsContent} from '../connectedComponents/connectedJobDetailsPane';
import { ConnectedCreateRolegroupModal } from '../connectedComponents/connectedRolegroupListPageComponents/connectedCreateRolegroupModal';
import { ConnectedPlannerToasterMessage } from '../connectedComponents/connectedPlannerToasterMessage';
import { ConnectedLongRunningBanners } from "../connectedComponents/longRunningTaskBanner";
import { LONG_RUNNING_TASK_TYPES } from "../reducers/longRunningTasksReducer/state";
import { ConnectedJobDuplicateDialog } from "../connectedComponents/connectedJobDuplicateDialog";
import { DuplicateJobLongRunningBannerContents, DuplicateJobLongRunningBannerTitles } from './commonPageComponents';
import { getCurrentPageTitleSelector } from '../selectors/navigationSelectors';
import { ConnectedJobFilterDialogInstance } from '../connectedComponents/connectedJobFilterDialog';
import { ResourcesPageConnectedCommandBar } from '../connectedComponents/connectedCommandBar/resourcesPageConnectedCommandBar';

const commandBar = {
    component: ResourcesPageConnectedCommandBar,
    containerStyle: { background: "white", display: 'flex', alignItems: 'center', padding: '0px'}
};

const dataGrid = {
    component: ConnectedJobsGrid,
    props: {},
    containerStyle: {
        overflow: 'hidden',
        margin: '15px 15px 0 15px',
        borderRadius: '8px 8px 0 0',
        boxShadow: '0 -1px 6px rgba(0,0,0,0.08)',
        backgroundColor: '#fff'
    }
};

const filterPane = {
    component: ConnectedJobsPageFiltersPane,
    containerClassName: "filter-pane-container",
    props: {}
};


const detailsPane = {
    component: ConnectedDetailsPane,
    props: {
        layoutProps: {
            style: {
                background: 'white',
                overflow: 'hidden',
                position: 'fixed',
                right: '0px',
                height: '100%'
            },
            width: '550px'
        },
        classNameVisible: 'JOBS_CLEAR_SELECTION_IGNORE_CLASS display-above-ant-notification',
        classNameHidden: 'details-pane-container-hidden',
        contentComponent: ConnectedPaneTabsContent,
        contentComponentProps: {},
        prefix: 'dp'
    }
};


const ConnectedTooltipContextualMenu = createConnectedTooltipContextualMenu(JOBS_PAGE_ALIAS);
const ConnectedTooltip = createConnectedTooltip(JOBS_PAGE_ALIAS);

const connectedComponents = (
    <React.Fragment>
        <ConnectedTooltip>
            <ConnectedTooltipContextualMenu getSelectedEntitiesSelector={getSelectedEntitiesSelector}/>
        </ConnectedTooltip>

        <ConnectedJobsPageModalEntityWindow />
        <ConnectedJobsPageBatchModalEntityWindow />
        <ConnectedJobsLookupEntityWindowJobsPage />
        <ConnectedClientLookupEntityWindowJobsPage />
        <ConnectedHotKeysHelpWindow />
        <ConnectedJobDuplicateDialog />
        <ConnectedPromptModal pageAlias={JOBS_PAGE_ALIAS}/>
        <ConnectedCreateRolegroupModal pageAlias={JOBS_PAGE_ALIAS}/>
        <ConnectedPlannerToasterMessage />
        <ConnectedGlobalCreateModalEntityWindow />
        <ConnectedLongRunningBanners 
            type={LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB} 
            pageAlias={JOBS_PAGE_ALIAS} 
            contents={DuplicateJobLongRunningBannerContents}
            titles={DuplicateJobLongRunningBannerTitles}
        />
        <ConnectedRoleGroupEntityWindow />
        {ConnectedJobFilterDialogInstance}
    </React.Fragment>
);

const pageObj = {commandBar, filterPane, dataGrid: null, detailsPane: null, connectedComponents };

class JobsPage extends BasePage {
    constructor(props){
        super(props);
    }

    internalRender(){
        const allProps = {...this.props, ...pageObj};
        return <ConnectedEntityListLayout {...allProps} />;
    }
}

const mapStateToProps = (state) => {
    const {jobsPage} = state.jobsPage;
    const pageTitle = getCurrentPageTitleSelector(state)(JOBS_PAGE_ALIAS);

    return {
        ...jobsPage,
        pageTitle
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onPageMount: (routerProps) => dispatch(PAGE_ACTIONS.OPEN[JOBS_PAGE_ALIAS](routerProps.location))
    };
};

const ConnectedResourcesPage = connect(
    mapStateToProps,
    mapDispatchToProps
)(JobsPage);



export default ConnectedResourcesPage;
