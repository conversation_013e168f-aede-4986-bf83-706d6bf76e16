import React from 'react';
import { connect } from 'react-redux';
import { BaseLayoutComponent } from '../layouts/base-layout';
import { ConnectedWebPlanner, ConnectedCommandBar, ConnectedRecordsList, ConnectedCalendarGrid, ConnectedDateBar } from '../connectedComponents/connectedPlanner';
import { ConnectedDetailsPane, ConnectedPaneTabsContent } from '../connectedComponents/connectedDetailsPane';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import BasePage from './basePage';
import { ConnectedModalEntityWindow, ConnectedBatchModalEntityWindow, ConnectedSimplifiedModalEntityWindow, ConnectedPlannerMoveRoleModalEntityWindow, ConnectedPlannerPageAssigneesBudgetModalEntityWindow, ConnectedManageRoleTemplatesModalEntityWindow, ConnectedCreateRoleTemplateModalEntityWindow, ConnectedGlobalCreateModalEntityWindow, ConnectedRoleGroupEntityWindow } from '../connectedComponents/connectedEntityWindow';
import { ConnectedManageMyPlansWindow } from '../connectedComponents/connectedManageMyPlansWindow';
import { ConnectedClientLookupEntityWindowPlannerPage, ConnectedJobsLookupEntityWindowPlannerPage } from '../connectedComponents/connectedEntityLookupWindow';
import { ConnectedHotKeysHelpWindow } from '../connectedComponents/connectedHotKeysHelpWindow';
import { PLANNER_CLEAR_SELECTION_IGNORE_CLASS, PLANNER_PAGE_ALIAS } from '../constants/plannerConsts';
import { ConnectedColourSchemeLegend } from '../connectedComponents/ConnectedColourSchemeLegend';
import ConnectedRejectRolesWindow from '../connectedComponents/connectedRoleTransitionDialogs/connectedRejectRoles';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { ConnectedSelectionBar } from '../connectedComponents/connectedSelectionBar';
import { ConnectedPlannerToasterMessage } from '../connectedComponents/connectedPlannerToasterMessage';
import ConnectedProgressRolesWindow from '../connectedComponents/connectedRoleTransitionDialogs/connectedProgressRoles';
import { ConnectedBarTypeTab } from '../connectedComponents/connectedLegendTabs/connectedBarTypeTab';
import { ConnectedColourSchemeTab } from '../connectedComponents/connectedLegendTabs/connectedColourSchemeTab';
import ConnectedRollForwardWindow from '../connectedComponents/connectedRollForwardWindow/connectedRollForwardBooking';
import ConnectedPeopleFinderDialog from '../connectedComponents/ConnectedPeopleFinder';
import { ConnectedMilestonesTab } from '../connectedComponents/connectedLegendTabs/connectedMilestonesTab';
import { ConnectedSaveAsTemplateModal } from '../connectedComponents/connectedTemplateModal';
import { ConnectedPlannerPagination } from '../connectedComponents/connectedPlannerPagination/connectedPlannerPagination';
import { ConnectedJobFilterDialogInstance } from '../connectedComponents/connectedJobFilterDialog';
import { usePlannerIsSplitting, withInactiveClass, withPlannerMode } from '../utils/plannerLayoutUtils';
import { ConnectedRoleGroupDuplicateDialog } from '../connectedComponents/connectedRoleGroupDuplicateDialog';
import { ConnectedJobDuplicateDialog } from '../connectedComponents/connectedJobDuplicateDialog';
import { ConnectedLongRunningBanners } from '../connectedComponents/longRunningTaskBanner';
import { LONG_RUNNING_TASK_TYPES } from '../reducers/longRunningTasksReducer/state';
import { DuplicateJobLongRunningBannerContents, DuplicateJobLongRunningBannerTitles } from './commonPageComponents';
import { ConnectedPlannerFilterActions } from '../connectedComponents/connectedFilters/connectedPlannerFilterActions';
import { ConnectedPlannerFilterSections } from '../connectedComponents/connectedFilters/connectedPlannerFilterSections';
import { ConnectedPlannerFiltersPane } from '../connectedComponents/connectedFilters/connectedPlannerFiltersPane';
import { ConnectedPlannerFilterGroups } from '../connectedComponents/connectedFilters/connectedPlannerFilterGroups';
import { PlannerGridResizeObservable, PlannerVerticalScrollBarResizeObservable } from '../observables/planner/plannerGridResizeObservable';
import { ConnectedPlannerFilterMessage } from '../connectedComponents/connectedFilters/connectedAdvancedFilterMessage';
import { ConnectedRepeatBookingDialog } from '../connectedComponents/connectedRepeatBookingDialog';
import { ConnectedUpdateSeriesContent } from '../connectedComponents/connectedPrompt/connectedPrompts/connectedUpdateSeriesContent';
import ConnectedFloatingBar from '../connectedComponents/connectedFloatingBar';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';
import { getLocalStorageItem, populateStorageItem } from '../localStorage';
import { USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY } from '../constants/localStorageConsts';
import { fromEvent } from 'rxjs';
import { take } from 'rxjs/operators';

const ConnectedPlannerLayout = React.lazy(() =>
    import('../connectedComponents/../connectedComponents/connectedPlannerLayout').then((module) =>
        ({ default: module.ConnectedPlannerLayout })));

const commandBar = {
    component: ConnectedCommandBar,
    props: {},
    containerStyle: { background: 'white', display: 'flex', alignItems: 'center', padding: '0px' }
};

const onFiltersSectionResize = () => {
    PlannerGridResizeObservable.resize();
    PlannerVerticalScrollBarResizeObservable.resize();
};

export const PlannerPageFiltersPane = React.memo(
    () => (
        <ConnectedPlannerFiltersPane >
            <ConnectedPlannerFilterGroups />
            <ConnectedPlannerFilterActions />
            <ConnectedPlannerFilterMessage />
            <ConnectedPlannerFilterSections onFiltersSectionResize={onFiltersSectionResize} />
        </ConnectedPlannerFiltersPane>
    )
);

const filterPane = {
    component: PlannerPageFiltersPane,
    props: { },
    className: 'filter-pane-container'
};

const planner = {
    component: withPlannerMode(ConnectedWebPlanner),
    props: {
        componentChildren: [
            {
                component: withInactiveClass(ConnectedRecordsList, usePlannerIsSplitting)
            },
            {
                component: withPlannerMode(ConnectedCalendarGrid),
                dateBarComponent: withInactiveClass(ConnectedDateBar, usePlannerIsSplitting)
            }
        ]
    },
    calendarGridChildIndex: 1,
    containerStyle: { height: '75%' /*borderTop: "1px solid #d8d8d8"*/ } // set border on planner pane
};

const detailsPane = {
    component: ConnectedDetailsPane,
    props: {
        layoutProps: {
            style: {
                background: 'white',
                overflow: 'hidden',
                position: 'fixed',
                right: '0',
                height: '100%'
            },
            width: '550px'
        },
        classNameVisible: `${PLANNER_CLEAR_SELECTION_IGNORE_CLASS} display-above-ant-notification`,
        classNameHidden: 'details-pane-container-hidden',
        contentComponent: ConnectedPaneTabsContent,
        contentComponentProps: {},
        prefix: 'dp'
    }
};

const listPane = {
    component: BaseLayoutComponent,
    props: { text: 'This is List Pane placeholder' },
    containerStyle: { height: '22%', background: 'whitesmoke' },
    hidden: true
};

const statusBar = {
    component: BaseLayoutComponent,
    props: { text: 'This is Status Bar placeholder' },
    hidden: true,
    containerStyle: { background: 'lightgrey' }
};

const colourLegendTabs = [
    {
        component: ConnectedColourSchemeTab,
        titleKey: 'colourTypesTabTitle'
    },
    {
        component: ConnectedBarTypeTab,
        titleKey: 'barTypesTabTitle'
    },
    {
        component: ConnectedMilestonesTab,
        titleKey: 'milestonesTabTitle'
    }
];

const paginationBar = {
    component: ConnectedPlannerPagination,
    props: {},
    containerStyle: {
        height: '50px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start'
    }
};

const connectedComponents = (isSortCalcFieldsEnabled) => (
    <React.Fragment>
        <ConnectedModalEntityWindow />
        <ConnectedBatchModalEntityWindow />
        <ConnectedSimplifiedModalEntityWindow />
        <ConnectedManageMyPlansWindow />
        <ConnectedPlannerPageAssigneesBudgetModalEntityWindow />
        <ConnectedHotKeysHelpWindow />
        <ConnectedJobsLookupEntityWindowPlannerPage />
        <ConnectedClientLookupEntityWindowPlannerPage />
        <ConnectedColourSchemeLegend tabs={colourLegendTabs} />
        <ConnectedRejectRolesWindow />
        <ConnectedProgressRolesWindow />
        <ConnectedPromptModal pageAlias={PLANNER_PAGE_ALIAS}/>
        <ConnectedSelectionBar />
        {isSortCalcFieldsEnabled && <ConnectedFloatingBar />}
        <ConnectedPlannerToasterMessage />
        <ConnectedPlannerMoveRoleModalEntityWindow />
        <ConnectedRollForwardWindow />
        <ConnectedPeopleFinderDialog />
        <ConnectedJobDuplicateDialog />
        <ConnectedRepeatBookingDialog />
        <ConnectedUpdateSeriesContent />
        {ConnectedJobFilterDialogInstance}
        <ConnectedSaveAsTemplateModal pageAlias={PLANNER_PAGE_ALIAS} />
        <ConnectedManageRoleTemplatesModalEntityWindow />
        <ConnectedCreateRoleTemplateModalEntityWindow />
        <ConnectedGlobalCreateModalEntityWindow />
        <ConnectedRoleGroupEntityWindow />
        <ConnectedRoleGroupDuplicateDialog />
        <ConnectedLongRunningBanners
            type={LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB}
            pageAlias={PLANNER_PAGE_ALIAS}
            contents={DuplicateJobLongRunningBannerContents}
            titles={DuplicateJobLongRunningBannerTitles}
        />
    </React.Fragment>
);

class SchedulerPage extends BasePage {
    constructor(props) {
        super(props);
        this.hideTooltip = this.hideTooltip.bind(this);
    }

    componentDidMount() {
        this.onPageMount(this.props);
        if (!getLocalStorageItem(USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY)) {
            fromEvent(document, 'click')
                .pipe(take(1))
                .subscribe(this.hideTooltip);
        }
    }

    hideTooltip() {
        populateStorageItem(USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY, true);
        window.dispatchEvent(new Event('storage'));
    }

    internalRender() {
        const isSortCalcFieldsEnabled = this.props.isSortCalcFieldsEnabled;
        const pageObj = { commandBar, filterPane, planner, detailsPane, listPane, paginationBar, statusBar, connectedComponents: connectedComponents(isSortCalcFieldsEnabled) };
        const allProps = { ...this.props, ...pageObj };

        return (
            <React.Suspense fallback={<div />}>
                <ConnectedPlannerLayout {...allProps} />
            </React.Suspense>
        );
    }
}

const mapStateToProps = (state) => {
    const { pageState } = state.plannerPage;
    const isSortCalcFieldsEnabled = getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state);

    return {
        ...pageState,
        isSortCalcFieldsEnabled
    };
};

const mapDispatchToPropsCG = (dispatch) => {
    return {
        onPageMount: (routerProps) => dispatch(PAGE_ACTIONS.OPEN[PLANNER_PAGE_ALIAS](routerProps.location))
    };
};

const ConnectedScheduler = connect(
    mapStateToProps,
    mapDispatchToPropsCG
)(SchedulerPage);


export default ConnectedScheduler;