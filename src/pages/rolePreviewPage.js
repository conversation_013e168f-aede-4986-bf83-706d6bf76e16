import React from 'react';
import { connect } from 'react-redux';
import BasePage from './basePage';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import { ConnectedPreviewEntityPageModalEntityWindow } from '../connectedComponents/connectedEntityWindow';
import { PREVIEW_ENTITY_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';

const bodyComponent = ConnectedPreviewEntityPageModalEntityWindow;
const ConnectedPreviewEntityLayout = React.lazy(() =>
    import('../connectedComponents/connectedPreviewEntityLayout').then((module) =>
        ({ default: module.ConnectedPreviewEntityLayout })));

const connectedComponents = (
    <React.Fragment>
        <ConnectedPromptModal pageAlias={PREVIEW_ENTITY_PAGE_ALIAS} />
    </React.Fragment>
);

const pageObj = { connectedComponents };
class PreviewRolePage extends BasePage {
    constructor(props) {
        super(props);
    }

    internalRender() {
        const allProps = { ...this.props, bodyComponent, ...pageObj };

        return (
            <React.Suspense fallback={<div />}>
                <ConnectedPreviewEntityLayout {...allProps} />
            </React.Suspense>
        );
    }
}

const mapStateToProps = (state) => {
    return {};
};

const mapDispatchToProps = (dispatch) => {
    return {
        onPageMount: (params) => dispatch(PAGE_ACTIONS.OPEN[PREVIEW_ENTITY_PAGE_ALIAS](params.location))
    };
};

const ConnectedPreviewRolePage = connect(
    mapStateToProps,
    mapDispatchToProps
)(PreviewRolePage);

export default ConnectedPreviewRolePage;