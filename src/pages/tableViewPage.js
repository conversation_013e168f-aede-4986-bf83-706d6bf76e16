import React from 'react';
import { connect } from 'react-redux';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import ConnectedTableViewPageLayout from '../connectedComponents/tableView/ConnectedTableViewPageLayout';
import { ConnectedTableViewPageCommandBar } from '../connectedComponents/connectedCommandBar/connectedTableViewPageCommandBar';
import { ConnectedGlobalCreateModalEntityWindow, ConnectedSimplifiedTableViewEntityWindow, ConnectedTableViewEntityWindow } from '../connectedComponents/connectedEntityWindow';
import { ConnectedTableViewPageFiltersPane } from '../connectedComponents/connectedFilters';
import { createConnectedTooltip } from '../connectedComponents/connectedTooltip';
import { createConnectedTooltipContextualMenu } from '../connectedComponents/connectedTooltipContextualMenu';
import { getSelectedEntitiesSelector } from '../selectors/commonSelectors';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';

import ConnectedTableViewLayoutContent from '../connectedComponents/tableView/connectedTableViewContentLayout';
import ConnectedTableViewRecordsList from '../connectedComponents/tableView/connectedTableViewRecordsList';
import ConnectedTableViewContainer from '../connectedComponents/tableView/connectedTableViewContainer';
import ConnectedTableViewDateBar from '../connectedComponents/tableView/connectedTableViewDateBar';
import ConnectedTableViewGrid from '../connectedComponents/tableView/connectedTableViewGrid';

import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import BasePage from './basePage';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { ConnectedTableViewPagination } from '../connectedComponents/connectedTableViewPagination/connectedTableViewPagination';
import { ConnectedJobFilterDialogInstance } from '../connectedComponents/connectedJobFilterDialog';
import { ConnectedRepeatBookingDialog } from '../connectedComponents/connectedRepeatBookingDialog';
import ConnectedTableViewFloatingBar from '../connectedComponents/tableView/connectedTableViewFloatingBar';

const commandBar = {
    component: ConnectedTableViewPageCommandBar,
    containerStyle: { background: 'white', display: 'flex', alignItems: 'center', padding: '0px' }
};

const filterPane = {
    component: ConnectedTableViewPageFiltersPane,
    containerClassName: 'filter-pane-container',
    props: {}
};

const tableViewContent = {
    component: ConnectedTableViewLayoutContent,
    props: {
        componentChildren: [
            {
                component: ConnectedTableViewRecordsList
            },
            {
                component: ConnectedTableViewContainer,
                gridComponent: ConnectedTableViewGrid,
                dateBarComponent: ConnectedTableViewDateBar
            }
        ]
    },
    tableViewDataGridChildIndex: 1
};

const ConnectedTooltip = createConnectedTooltip(TABLE_VIEW_PAGE_ALIAS);
const ConnectedTooltipContextualMenu = createConnectedTooltipContextualMenu(TABLE_VIEW_PAGE_ALIAS);

const connectedComponents = (isSortCalcFieldsEnabled) => (
    <React.Fragment>
        <ConnectedTooltip>
            <ConnectedTooltipContextualMenu getSelectedEntitiesSelector={getSelectedEntitiesSelector} />
        </ConnectedTooltip>
        <ConnectedTableViewEntityWindow />
        <ConnectedSimplifiedTableViewEntityWindow />
        <ConnectedRepeatBookingDialog />
        {isSortCalcFieldsEnabled && <ConnectedTableViewFloatingBar />}
        {ConnectedJobFilterDialogInstance}
        <ConnectedPromptModal pageAlias={TABLE_VIEW_PAGE_ALIAS}/>
        <ConnectedGlobalCreateModalEntityWindow />
    </React.Fragment>
);

const paginationBar = {
    component: ConnectedTableViewPagination,
    props: {},
    containerStyle: {
        height: '50px',
        minHeight: '50px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start'
    }
};

class TableViewPage extends BasePage {
    constructor(props) {
        super(props);
    }

    internalRender() {
        const isSortCalcFieldsEnabled = this.props.isSortCalcFieldsEnabled;
        const pageObj = { commandBar, filterPane, tableViewContent, connectedComponents: connectedComponents(isSortCalcFieldsEnabled), paginationBar };
        const allProps = { ...this.props, ...pageObj };

        return <ConnectedTableViewPageLayout {...allProps} />;
    }
}

const mapStateToProps = (state) => {
    const { pageState = {} } = state[TABLE_VIEW_PAGE_ALIAS] || {};
    const isSortCalcFieldsEnabled = getFeatureFlagSelector(FEATURE_FLAGS.SORT_CALC_FIEDLS)(state);

    return {
        ...pageState,
        isSortCalcFieldsEnabled
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onPageMount: (routerProps) => dispatch(PAGE_ACTIONS.OPEN[TABLE_VIEW_PAGE_ALIAS](routerProps.location))
    };
};

const ConnectedTableViewPage = connect(
    mapStateToProps,
    mapDispatchToProps
)(TableViewPage);

export default ConnectedTableViewPage;