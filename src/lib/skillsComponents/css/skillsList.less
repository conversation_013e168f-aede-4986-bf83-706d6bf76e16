:local(.icon) {
    margin: 0 12px;
    color: @text-color;
}

:local(.iconInfo) {
    font-size: 13px;
    color: @text-color;// #C5C5C5;
}

:local(.listHeading) {
    margin-top: 13px;
    border-radius: 1em;
    width: inherit;
}

:local(.listHeadingCollapsed) .ant-card-head {
    border-bottom: none !important;
}

:local(.skillCardStyle) {
    border-radius: 18px;
    margin: 8px;
    background-color: #F4F4F4;
    width: fit-content;
    display: inline-block;
    max-width: 98%;
    border: 1px solid #E6E6E6;
}

:local(.PrimarySkill) {
    border: 2px solid var(--primary-color);
}

:local(.SecondarySkill) {
    border: 2px solid var(--dark-grey-color);
}

:local(.cardBodyStyle){
    border-radius: 32px;
    background-color: #ffffff9f;
}

:local(.skillCardStyle) .ant-card-head-title {
    padding: 0;
    padding-right: 16px;
    height: 40px;
}

:local(.skillCardStyle) .ant-card-extra {
    padding: 0;
    padding-right: 4px;
}
.PrimarySkill.new-tp .ant-card-head:hover {
  cursor: pointer;
}

// On hover, change text and thumb icon color to green if the feature flag is enabled
.PrimarySkill.new-tp .ant-card-head:hover :local(.cardTitleText),
.PrimarySkill.new-tp .ant-card-head:hover .ant-card-extra {
  color: @primary-color;
}


:local(.skillCardStyle) .ant-card-head {
    min-height: 0;
    border-bottom: none;
    padding: 0;
}

:local(.iconCollapse) {
    margin: 0 4px 0;
    font-size: @heading-5-size;
}

:local(.iconCollapseExpanded) {
    transform: rotate(180deg);
}

:local(.collapseButtonStyle) {
    display: flex;
    align-items: center;
}

:local(.doubleArrows) {
    transform: rotate(90deg);
    margin: 6px 0px 5px 0px;
    font-size: 12px;
}

:local(.doubleDownArrows) {
    transform: rotate(270deg);
}

:local(.skillContainerTitle) {
    overflow: auto;
    display: flex;
}

:local(.editableSkillContainerTitle) {
    cursor: pointer;
}

:local(.levelLegentSection){
    background-color: #F4F4F4;
    border-top: 1px solid #E6E6E6;
    border-bottom: 1px solid #E6E6E6;
    padding: 8px;
}

:local(.cardTitleText){
    margin-left: 12px;
    margin-top: 8px;
    text-overflow: ellipsis;
    overflow: hidden;
}

:local(.skillLvlLegendWrapper){
    background-color: rgb(255, 255, 255);
    border-radius: 30px;
    display: inline-flex;
    margin-right: 8px;
    margin-top: 4px;
}

:local(.skillLvlLegendName){
    margin-top: 8px;
    font-size: initial;
    padding-right: 8px;
}

:local(.lineThroughText) {
    text-decoration: line-through;
}

:local(.skillsSectionContainer) {
    width: inherit;
}

:local(.headerTitleSpan) {
    font-weight: @regular-weight;
}

:local(.fieldValue){
    padding-right: 16px;
    max-width: 80%;
}

:local(.fieldWrapper){
    border-top: 1px solid #E6E6E6;
    display: flex;
    padding: 8px;
    justify-content: space-between;

    .no-result {
        color: @light-grey-color-2;
        font-weight: @bold-weight;
    }
}

:local(.levelPopup) .ant-popover-content .ant-popover-inner > div .ant-popover-inner-content {
    background-color: #000000;
    border-radius: 5px;
    color: white;
    max-width: 280px;
}

:local(.levelPopup) .ant-popover-content .ant-popover-arrow {
    background-color: #000000;
    border: #000000;
}

.ant-legacy-form-item {
    .ant-legacy-form-item-required {
        span {
            color: @black-color;
        }
        &::before {
            color: @black-color;
        }
    }
    &.has-error {
        .ant-legacy-form-item-required {
            span {
                color: @error-color !important;
            }
            &::before {
                color: @error-color !important;
            }
        }
    }
}