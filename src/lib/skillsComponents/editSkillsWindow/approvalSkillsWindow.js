import React from 'react';
import { Tabs, Empty, Typography } from 'antd';
import PropTypes from 'prop-types';
import { ResendRequestSection } from './resendRequestSection';
import './approvalStyles.less';

const { TabPane } = Tabs;

const ApprovalSkillsWindow = ({
    resourceId,
    managerName,
    staticMessages,
    approvalRequestsData,
    onResendRequest
}) => {

    const { approvalRequestsLabel, noSkillPendingRequestsLabel, noSkillApprovalHistoryLabel } = staticMessages;

    return (
        <div className="approval-requests-tab">
            <Tabs defaultActiveKey="pending" className="nested-approval-tabs" tabPosition="top">
                <TabPane tab="Pending" key="pending">
                    {approvalRequestsData && approvalRequestsData.pendingRequests?.length > 0 ? (
                        <>
                            <ResendRequestSection managerName={managerName} onResend={() => onResendRequest(resourceId)} />
                            <p>{approvalRequestsLabel} pending content goes here.</p>
                        </>
                    ) : (
                        <Empty description={noSkillPendingRequestsLabel} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                </TabPane>
                <TabPane tab="Historic" key="historic">
                    {approvalRequestsData && approvalRequestsData.historicRequests?.length > 0 ? (
                        <>
                            <Typography.Text strong>{noSkillApprovalHistoryLabel}</Typography.Text>
                            <p>{approvalRequestsLabel} historic content goes here.</p>
                        </>
                    ) : (
                        <Empty description={noSkillApprovalHistoryLabel} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                </TabPane>
            </Tabs>
        </div>
    );
};

export default ApprovalSkillsWindow;

ApprovalSkillsWindow.propTypes = {
    approvalRequestsData: PropTypes.object,
    managerName: PropTypes.string,
    staticMessages: PropTypes.object,
    resourceId: PropTypes.string,
    onResendRequest: PropTypes.func
};

