import React from 'react';
import { string } from 'prop-types';
import { SkillsLookup } from '../skillsLookup';
import { Divider } from 'antd';

export const EditSkillsWindowHeader = ({
    title,
    ...props
}) => (
    <div className="skills-window-header">
        <span className="skills-window-header-title">
            {title}
        </span>
        <SkillsLookup
            {...props}
        />
        <Divider className="divider" />
    </div>
);

EditSkillsWindowHeader.propTypes = {
    title: string.isRequired
};