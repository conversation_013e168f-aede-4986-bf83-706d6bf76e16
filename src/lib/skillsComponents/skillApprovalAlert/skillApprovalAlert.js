import React from 'react';
import { object } from 'prop-types';
import { Alert } from 'antd';
import { useSelector } from 'react-redux';
import { getTranslationsSelector } from '../../../selectors/internationalizationSelectors';

/**
 * SkillApprovalAlert component
 *
 * @param {{ style: any; }}  style - Style object to apply to the alert
 * @returns skill Approval alert component
 */
const SkillApprovalAlert = ({ style }) => {
    const { skillApproval = '' } = useSelector(state => getTranslationsSelector(state, { sectionName: 'talentProfilePage' }));

    return (
        <Alert
            style={{ marginTop: '10px', ...style }}
            message={skillApproval}
            showIcon
            type="info"
        />
    );
};

SkillApprovalAlert.propTypes = {
    style: object
};

SkillApprovalAlert.defaultProps = {
    style: {}
};

export default SkillApprovalAlert;