import React from 'react';
import PropTypes from 'prop-types';
import { Card, Button, Popover, Tooltip } from 'antd';
import styles from './css/skillsList.less';
import { EXPAND_ALL_BUTTON_TEXT, COLLAPSE_ALL_BUTTON_TEXT, SKILL_LEVEL_DELETED_TOOLTIP_MESSAGE } from '../../constants/skillsSectionConsts';
import { CircleProgressBar } from '../circleProgressBar/circleProgressBar';
import { formatFieldValue, isBlankFieldValue } from '../../utils/fieldControlUtils';
import Icon from '../icon';
import { FIELD_ACCESS_LEVEL_CONSTS } from '../../constants/adminSettingConsts';
import { boolToYesOrNoValue } from '../../utils/commonUtils';
import { LikeFilled, ExclamationCircleFilled } from '@ant-design/icons';
import { FEATURE_FLAGS, SKILL_PREFERENCE_TYPES } from '../../constants/globalConsts';
import { humanizeIt } from '../../utils/helperFunctions';
export class CardSkill extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            markedForDeletion: false,
            progressBarHovered: false
        };

        this.onLevelTooltipVisiblityChange = this.onLevelTooltipVisiblityChange.bind(this);
        this.onMarkSkillForDeletion = this.onMarkSkillForDeletion.bind(this);
    }

    onLevelTooltipVisiblityChange(visible) {
        this.setState({
            progressBarHovered: visible
        });
    }

    onMarkSkillForDeletion(marked) {
        this.setState(prevState => {
            return {
                ...prevState,
                ...(prevState.markedForDeletion !== marked && { markedForDeletion: marked })
            };
        });
    }

    getFieldValue(value, fieldInfo) {
        switch (fieldInfo.dataType) {
            case 'Bool': {
                return boolToYesOrNoValue(value);
            }
            default: {
                return isBlankFieldValue(value)
                    ? <span className="no-result">-</span>
                    : formatFieldValue(value, fieldInfo, { keepLocalTime: true });
            }
        }
    }

    renderCardContent(field, index) {
        const { name, value, fieldInfo } = field;
        const displayValue = this.getFieldValue(value, fieldInfo);

        return (
            <div key={`card-content-${index}`} className={styles.fieldWrapper}>
                <div className={styles.fieldValue}>
                    {fieldInfo.alias || name}
                </div>
                <div>
                    {displayValue}
                </div>
            </div>
        );
    }

    /**
     * Get the car head style based on the skill preference
     * @param {*} preference
     * @returns {icon: JSX.Element, border: string, borderRadius: string}
     */
    getSkillPreferenceStyle = (preference) => {
        switch (preference) {
            case SKILL_PREFERENCE_TYPES.PRIMARY_SKILL:
                return {
                    icon: <LikeFilled />,
                    toolTip:'Primary Skill'
                };
            case SKILL_PREFERENCE_TYPES.SECONDARY_SKILL:
                return {
                    icon: <LikeFilled />,
                    toolTip:'Secondary Skill'
                };
            case SKILL_PREFERENCE_TYPES.PREFERRED:
                return {
                    icon: <LikeFilled />,
                    toolTip:'Prefers work with this skill'
                };
            case SKILL_PREFERENCE_TYPES.LESS_PREFERRED:
                return {
                    icon: <ExclamationCircleFilled />,
                    toolTip:'Prefers less work with this skill'
                };
            default:
                return {
                    icon: null,
                    border: null,
                    borderRadius: null,
                    toolTip:null
                };
        }
    }

    renderCard() {
        const { skill, editable, levelTotalSteps, skillsStaticLabels = {}, isSectionSysMaintained, talentProfilePageTransformedEnabled } = this.props;
        const { name, fields = {}, level = {} } = skill;
        const { value } = level;
        const { markedForDeletion } = this.state;
        const filteredFields = (fields ?? []).filter(x => x?.fieldInfo?.accessLevel !== FIELD_ACCESS_LEVEL_CONSTS.HIDDEN);
        // Get skill preference styling
        const skillPreferenceStyle = this.getSkillPreferenceStyle(skill.skillPreference);
        // Construct card class; add "new-tp" if feature is enabled
        const cardClassName = talentProfilePageTransformedEnabled ? `${styles.skillCardStyle} ${skill.skillPreference} new-tp` : `${styles.skillCardStyle} ${skill.skillPreference} `;
        const titleClassName = `${styles.skillContainerTitle} ${markedForDeletion ? styles.lineThroughText : ''} ${editable ? styles.editableSkillContainerTitle : ''}`;

        return (
            <Tooltip title={this.getSkillPreferenceStyle(skill.skillPreference).toolTip}>
                <Card
                    title={<div className={titleClassName}>
                        {!isSectionSysMaintained && <Popover
                            placement="bottom"
                            content={skillsStaticLabels.skillLevelDeletedMessage || SKILL_LEVEL_DELETED_TOOLTIP_MESSAGE}
                            trigger="hover"
                            overlayClassName={styles.levelPopup}
                            open={value === -1 && this.state.progressBarHovered}
                            onOpenChange={this.onLevelTooltipVisiblityChange}
                        >
                            <div>
                                {value != undefined && <CircleProgressBar
                                    totalSteps={levelTotalSteps}
                                    radius={16}
                                    step={value} />}
                            </div>
                        </Popover>}

                        <div className={styles.cardTitleText}>
                            {name}
                        </div>

                    </div>}
                    bodyStyle={{
                        backgroundColor: '#ffffff',
                        borderBottomLeftRadius: '16px',
                        borderBottomRightRadius: '16px',
                        padding: '0px'
                    }}
                    className={cardClassName}
                    extra={skillPreferenceStyle.icon}>
                    {Object.values(filteredFields).map((field, index) => this.renderCardContent(field, index))}
                </Card>
            </Tooltip>);
    }

    render() {
        const { skill, editable, skillWindowPopover: SkillWindowPopover } = this.props;
        const { markedForDeletion } = this.state;

        return (<>
            {editable ? (
                <SkillWindowPopover skill={skill} markedForDeletion={markedForDeletion} setSkillMarkedForDeletion={this.onMarkSkillForDeletion} >
                    {
                        editable && this.renderCard()
                    }
                </SkillWindowPopover>
            ) : this.renderCard()}
        </>
        );
    }
}

CardSkill.propTypes = {
    skill: PropTypes.object.isRequired,
    className: PropTypes.string,
    editable: PropTypes.bool,
    skillWindowPopover: PropTypes.func,
    levelTotalSteps: PropTypes.number,
    skillsStaticLabels: PropTypes.object,
    isSectionSysMaintained: PropTypes.bool,
    talentProfilePageTransformedEnabled: PropTypes.bool
};

export class SkillsList extends React.Component {
    constructor(props) {
        super(props);

        this.onSectionCollapse = this.onSectionCollapse.bind(this);
        this.onCollapseAllSections = this.onCollapseAllSections.bind(this);

        this.state = {
            collapsedSections: []
        };
    }

    getIsSectionCollapsed(sectionId) {
        return this.state.collapsedSections.findIndex(id => id === sectionId) > -1;
    }

    getSectionsIds() {
        return Object.keys(this.props.listItems || {});
    }

    onSectionCollapse(sectionId, collapsed) {
        let newCollapsedSections = [...this.state.collapsedSections];

        if (collapsed) {
            newCollapsedSections.push(sectionId);
        } else {
            newCollapsedSections = newCollapsedSections.filter(id => id !== sectionId);
        }

        this.setState({
            collapsedSections: newCollapsedSections
        });
    }

    onCollapseAllSections(collapsed) {
        this.setState({
            collapsedSections: collapsed ? this.getSectionsIds() : []
        });
    }

    render() {
        const { listItems, allowCollapseAllSections = false, editable, skillWindowPopover, skillsStaticLabels, getAriaLabelDeleteSkillButton, getIsSectionSysMaintained, talentProfilePageTransformedEnabled } = this.props;
        const sectionsCount = this.getSectionsIds().length;
        const allSectionsCollapsed = this.state.collapsedSections.length === sectionsCount;
        const showCollapseAllButton = allowCollapseAllSections && sectionsCount > 0;

        return (
            <>
                {
                    showCollapseAllButton && (
                        <ExpandCollapseAllButton
                            collapsed={allSectionsCollapsed}
                            onClick={this.onCollapseAllSections}
                            skillsStaticLabels={skillsStaticLabels}
                        />)
                }
                {
                    Object
                        .keys(listItems)
                        .map(sectionId => (
                            <SkillsSection
                                key={sectionId}
                                section={listItems[sectionId]}
                                collapsed={this.getIsSectionCollapsed(sectionId)}
                                onCollapse={this.onSectionCollapse}
                                editable={editable}
                                skillWindowPopover={skillWindowPopover}
                                skillsStaticLabels={skillsStaticLabels}
                                getAriaLabelDeleteSkillButton={getAriaLabelDeleteSkillButton}
                                getIsSectionSysMaintained={getIsSectionSysMaintained}
                                talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled}
                            />))
                }
            </>
        );
    }
}

const SkillsLevelLegend = (props) => {
    const { levels = [] } = props.level;
    const { levelTotalSteps } = props;

    return (
        <div className={styles.levelLegentSection}>
            {levels.map((level) => (
                <div
                    key={level.id}
                    className={styles.skillLvlLegendWrapper}
                >
                    <CircleProgressBar
                        totalSteps={levelTotalSteps}
                        radius={16}
                        step={level.value}
                    />
                    <div className={styles.skillLvlLegendName}>
                        {level.name}
                    </div>
                </div>
            ))}
        </div>
    );
};

SkillsLevelLegend.propTypes = {
    level: PropTypes.object.isRequired,
    levelTotalSteps: PropTypes.number
};

SkillsList.propTypes = {
    listItems: PropTypes.object.isRequired,
    allowCollapseAllSections: PropTypes.bool,
    editable: PropTypes.bool,
    skillsStaticLabels: PropTypes.object,
    skillWindowPopover: PropTypes.func,
    getAriaLabelDeleteSkillButton: PropTypes.func,
    getIsSectionSysMaintained: PropTypes.func,
    talentProfilePageTransformedEnabled: PropTypes.bool
};

const SkillsSection = (props) => {
    const { section, collapsed, onCollapse, editable, skillWindowPopover, skillsStaticLabels = {}, getAriaLabelDeleteSkillButton, getIsSectionSysMaintained, talentProfilePageTransformedEnabled } = props;
    const { sectionName, sectionId, skills = [], levelDefinition = {} } = section;
    const { levels = [], name } = levelDefinition;
    const levelTotalSteps = levels.filter(level => level.value > -1).length;
    const { singularSkillString = 'skill', pluralSkillsString = 'skills' } = skillsStaticLabels;
    const skillCount = skills.length > 1 ? skills.length + ' ' + pluralSkillsString : skills.length + ' ' + singularSkillString;
    const isSectionSysMaintained = getIsSectionSysMaintained(sectionId);
    const isSectionEditable = !isSectionSysMaintained ? editable : false;

    const title = (
        <>
            <Icon type="down"
                className={`${styles.iconCollapse} ${!collapsed && styles.iconCollapseExpanded}`}
                onClick={() => onCollapse(sectionId, !collapsed)} />
            {sectionName} {name && !isSectionSysMaintained && <span className={styles.headerTitleSpan}>({name})</span>}
        </>
    );

    return (
        <div key={sectionName} className={styles.skillsSectionContainer}>
            <Card
                title={title}
                className={`${styles.listHeading} ${collapsed ? `${styles.listHeadingCollapsed} hidden-body` : ''}`}
                extra={<p style={{
                    fontWeight: 'bold',
                    margin: 0
                }}>{skillCount}</p>}
                bodyStyle={
                    { padding: 0 }
                }
                headStyle={
                    { paddingLeft: '16px' }
                }
            >
                {levels.length > 0 && !isSectionSysMaintained && <SkillsLevelLegend level={levelDefinition} levelTotalSteps={levelTotalSteps} />}
                {
                    !collapsed && skills.map((skill, index) => (
                        <CardSkill
                            key={`${sectionName}-${skill.name}-${index}`}
                            skill={skill}
                            editable={isSectionEditable}
                            skillWindowPopover={skillWindowPopover}
                            levelTotalSteps={levelTotalSteps}
                            skillsStaticLabels={skillsStaticLabels}
                            getAriaLabelDeleteSkillButton={getAriaLabelDeleteSkillButton}
                            isSectionSysMaintained={isSectionSysMaintained}
                            skillCount={skillCount}
                            talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled} >
                        </CardSkill>
                    ))
                }
            </Card>
        </div>
    );
};

SkillsSection.propTypes = {
    section: PropTypes.object.isRequired,
    collapsed: PropTypes.bool.isRequired,
    onCollapse: PropTypes.func.isRequired,
    skillsStaticLabels: PropTypes.object,
    editable: PropTypes.bool,
    skillWindowPopover: PropTypes.func,
    getAriaLabelDeleteSkillButton: PropTypes.func,
    getIsSectionSysMaintained: PropTypes.func,
    talentProfilePageTransformedEnabled: PropTypes.bool
};

const ExpandCollapseAllButton = ({ collapsed, onClick, skillsStaticLabels = {} }) => {
    const expandAllCaption = skillsStaticLabels.expandAllCaption || EXPAND_ALL_BUTTON_TEXT;
    const collapseAllCaption = skillsStaticLabels.collapseAllCaption || COLLAPSE_ALL_BUTTON_TEXT;
    const buttonLabel = collapsed ? expandAllCaption : collapseAllCaption;

    return (
        <Button
            className={styles.collapseButtonStyle}
            type="secondary"
            onClick={() => onClick(!collapsed)}
        >
            <Icon type="double-right"
                className={`${styles.doubleArrows} ${!collapsed && styles.doubleDownArrows}`} />
            {buttonLabel}
        </Button>
    );
};

ExpandCollapseAllButton.propTypes = {
    collapsed: PropTypes.bool.isRequired,
    onClick: PropTypes.func.isRequired,
    skillsStaticLabels: PropTypes.object
};