import React from 'react';
import { Button, Space, Divider, Typography, Tag, Row, Col ,Empty } from 'antd';
import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import './recommendations.less';
import { EditSkillsWindowBody } from '../editSkillsWindow/body';
const { Title } = Typography;

export const RecommendationsTab = ({
    bodyProps,
    handleAcceptSkills,
    handleIgnoreSkills,
    entityId,
    onSkillFieldChange,
    onSkillsFieldsChange
}) => {
    const { recommendationSectionData, getActionedRecommendedSkillIds, staticMessages } = bodyProps;
    const { noRecommendations } = staticMessages;
    const { recommendationSections, totalRecommendations, selectedIds } = recommendationSectionData;
    const skillIds = recommendationSections.flatMap(x => x.skills).map(x=> x.id);
    const actionedSkillIds = getActionedRecommendedSkillIds(entityId);

    //#region Handle Accept and Ignore Skills
    const onAcceptSkill = (skillId) => {
        handleAcceptSkills([skillId]);
    };

    const handleAcceptAll = () => {
        handleAcceptSkills(skillIds);
    };

    const onIgnoreSkill = (skillId) => {
        handleIgnoreSkills([skillId]);
    };

    const handleIgnoreAll = () => {
        handleIgnoreSkills(skillIds);
    };
    //#endregion

    const selectedCount = selectedIds.length;
    const hasActionedRecommendedSkills = !!actionedSkillIds.length;
    const transformedSections = recommendationSections.map(section => ({
        sectionName: section.displaySectionName,
        skills: section.skills
            .map(skill => ({
                ...skill,
                removed: !selectedIds.includes(skill.id),
                resourceSkillPreference: skill.skillPreference || 'NoPreference'
            }))
    }));

    const config = {
        ...bodyProps.config,
        markDeletedMessage: 'Ignore',
        cancelDeletionMessage: 'Accept',
        isRecommendationsView: true,
        onAcceptSkill,
        onIgnoreSkill,
        actionedSkillIds
    };

    const newBodyProps = {
        ...bodyProps,
        skillSections:transformedSections,
        config,
        onSkillFieldChange,
        onSkillsFieldsChange
    };

    return (
        <>
            {!!totalRecommendations && <div className="recommendations-header">
                <Row justify="space-between" align="middle">
                    <Col>
                        <Space>
                            <Title level={5} style={{ margin: 0 }}>
                                {totalRecommendations} skills recommended for you
                            </Title>
                            {selectedCount > 0 && (
                                <Tag color="processing" className="selected-count">
                                    {selectedCount} selected
                                </Tag>
                            )}
                        </Space>
                    </Col>
                    <Col>
                        <Space>
                            <Button disabled={hasActionedRecommendedSkills} icon={<CloseOutlined />} onClick={handleIgnoreAll}>
                                Ignore All
                            </Button>
                            <Button disabled={hasActionedRecommendedSkills} type="primary" icon={<CheckOutlined />} onClick={handleAcceptAll}>
                                Accept All
                            </Button>
                        </Space>
                    </Col>
                </Row>

                <Divider />
                <EditSkillsWindowBody
                    {...newBodyProps}
                />
            </div>}
            {!totalRecommendations && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={
                <Typography.Text>
                    {noRecommendations}
                </Typography.Text>
            } />}
        </>
    );
};

RecommendationsTab.propTypes = {
    entityId: PropTypes.string,
    bodyProps: PropTypes.object,
    handleAcceptSkills: PropTypes.func,
    handleIgnoreSkills: PropTypes.func,
    onSkillFieldChange: PropTypes.func,
    onSkillsFieldsChange: PropTypes.func
};