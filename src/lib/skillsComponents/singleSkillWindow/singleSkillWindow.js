import React, { Component } from 'react';
import { <PERSON><PERSON>, Divider } from 'antd';
import { Icon } from '../../';
import { string, bool, func, object, array } from 'prop-types';
import styles from './styles.less';
import { SKILL_WINDOW_LABELS } from '../../../constants/skillWindowConsts';
import { FIELD_ACCESS_LEVEL_CONSTS } from '../../../constants/adminSettingConsts';
import SkillPreference from '../../../components/skillPreference/skillPreference';
import { SKILL_PREFERENCE } from '../../../constants/editSkillsWindowConsts';
import SkillApprovalAlert from '../skillApprovalAlert/skillApprovalAlert';
export class SingleSkillWindow extends Component {
    constructor(props) {
        super(props);

        this.onSaveClick = this.onSaveClick.bind(this);
        this.onCancelClick = this.onCancelClick.bind(this);
    }

    onSaveClick() {
        const {
            entityId,
            deleteSkill,
            updateSkill,
            skill,
            markedForDeletion,
            disposeWindow,
            updateSkillEnabled
        } = this.props;

        if (markedForDeletion) {
            deleteSkill(entityId, skill.id);
        } else if (updateSkillEnabled) {
            updateSkill(entityId, skill.id);
        }

        disposeWindow();
    }

    onCancelClick() {
        this.props.disposeWindow();
    }

    render() {
        const {
            entityId,
            skill,
            staticMessages = {},
            markedForDeletion,
            onMarkForDeletion,
            fieldsForm,
            updateSkillEnabled,
            onMouseEnter,
            onMouseLeave,
            bodyRef,
            popupControlsInBody,
            skillPreferences,
            getResourceSkillPreference,
            skillApprovalFeatureFlagEnabled,
            resourceSkillApprovalPermission
        } = this.props;

        const resourceSkillPreference = getResourceSkillPreference(entityId,skill.id);

        const onSkillPreferenceChange = (value) => {
            const { entityId , skill, getSkillInfo, onSkillFieldChange } = this.props;
            const { id:skillId } = skill;
            const skillInfo = getSkillInfo(skillId);
            onSkillFieldChange(entityId, skillId, { id: SKILL_PREFERENCE }, value, skillInfo);
        };

        return (
            <div
                className={styles.skillWindowContent}
                onMouseEnter = {onMouseEnter}
                onMouseLeave = {onMouseLeave}
            >
                <Header
                    title={skill.name}
                    markedForDeletion={markedForDeletion}
                    onDelete={onMarkForDeletion}
                    skillPreferences={skillPreferences}
                    resourceSkillPreference={resourceSkillPreference}
                    onSkillPreferenceChange={onSkillPreferenceChange}
                />
                <Body
                    markedForDeletion={markedForDeletion}
                    skill={skill}
                    fieldsForm={fieldsForm}
                    bodyRef={bodyRef}
                    staticMessages={staticMessages}
                    popupControlsInBody={popupControlsInBody}
                    skillApprovalFeatureFlagEnabled={skillApprovalFeatureFlagEnabled}
                    resourceSkillApprovalPermission={resourceSkillApprovalPermission}
                />
                <Footer
                    staticMessages={staticMessages}
                    saveButtonEnabled={markedForDeletion || updateSkillEnabled}
                    onCancel={this.onCancelClick}
                    onSave={this.onSaveClick}
                />
            </div>
        );
    }
}

SingleSkillWindow.propTypes = {
    skill: object.isRequired,
    staticMessages: object,
    deleteSkill: func.isRequired,
    updateSkill: func.isRequired,
    entityId: string.isRequired,
    markedForDeletion: bool.isRequired,
    onMarkForDeletion: func.isRequired,
    disposeWindow: func.isRequired,
    fieldsForm: func.isRequired,
    updateSkillEnabled: bool.isRequired,
    onMouseEnter: func.isRequired,
    onMouseLeave: func.isRequired,
    bodyRef: object,
    skillPreferences:array.isRequired,
    getResourceSkillPreference:func.isRequired,
    getSkillInfo:func.isRequired,
    onSkillFieldChange:func.isRequired,
    skillApprovalFeatureFlagEnabled: bool.isRequired,
    resourceSkillApprovalPermission: bool.isRequired
};

const Header = ({
    title,
    markedForDeletion,
    onDelete,
    skillPreferences,
    resourceSkillPreference,
    onSkillPreferenceChange
}) => {

    const skillPreferenceProps = {
        skillPreferences,
        resourceSkillPreference,
        onSkillPreferenceChange
    };

    return (
        <div className={`${styles.headerContainer}`}>
            <span className={`${styles.headerTitle} ${markedForDeletion ? styles.lineThroughText : ''} popupTitle skillName`}>
                {title}
            </span>
            {!markedForDeletion && <SkillPreference {...skillPreferenceProps}></SkillPreference>}
            {
                !markedForDeletion &&
                <Icon
                    type="delete"
                    className={`${styles.headerDeleteButton}`}
                    onClick={onDelete}
                />
            }
        </div>
    );
};

Header.propTypes = {
    title: string,
    markedForDeletion: bool.isRequired,
    onDelete: func.isRequired,
    skillPreferences:array.isRequired,
    resourceSkillPreference:string.isRequired,
    onSkillPreferenceChange:func.isRequired
};

const Body = (props) => {
    const {
        markedForDeletion,
        skill = {},
        fieldsForm: SkillFieldsForm,
        bodyRef,
        staticMessages,
        popupControlsInBody,
        skillApprovalFeatureFlagEnabled,
        resourceSkillApprovalPermission
    } = props;

    const {
        info: description,
        tags = [],
        fields = [],
        level = {}
    } = skill;

    const { tagsPrefixText, markedForDeletionMessage } = staticMessages;
    const filteredFields = (fields ?? []).filter(x => x?.fieldInfo?.accessLevel !== FIELD_ACCESS_LEVEL_CONSTS.HIDDEN);

    return (
        <>
            {
                markedForDeletion ? (
                    <>
                        <Divider />
                        <div className={styles.markedForDeletionContainer}>
                            <Icon type="info-circle" />
                            <span className={styles.markForDeletionMessage}>
                                {markedForDeletionMessage}
                            </span>
                        </div>
                    </>
                ) : (
                    <>
                        {
                            description ? <p className={styles.innerContainer}>{description}</p> : null
                        }
                        {
                            tags.length > 0 ? <Tags tags={tags} prefix={tagsPrefixText} /> : null
                        }
                        {
                            filteredFields.length > 0 || (level.levels || []).length > 0 ?
                                (
                                    <>
                                        <Divider />
                                        {skillApprovalFeatureFlagEnabled && !!resourceSkillApprovalPermission && (
                                            <SkillApprovalAlert style={{ marginBottom: '10px' }} />
                                        )}
                                        <SkillFieldsForm className={styles.innerContainer} bodyRef={bodyRef} staticMessages={staticMessages} popupControlsInBody={popupControlsInBody}/>
                                    </>
                                )
                                : null
                        }
                    </>
                )
            }
        </>
    );
};

Body.propTypes = {
    skill: object.isRequired,
    markedForDeletion: bool.isRequired,
    staticMessages: object.isRequired,
    skillApprovalFeatureFlagEnabled: bool.isRequired,
    resourceSkillApprovalPermission: bool.isRequired
};

const Footer = ({
    staticMessages,
    saveButtonEnabled,
    onSave,
    onCancel
}) => (
    <>
        <Divider/>
        <div className={styles.footerContainer}>
            <Button
                id="single-skill-window-save-button"
                type="primary"
                htmlType="submit"
                className={styles.buttonSave}
                disabled={!saveButtonEnabled}
                onClick={onSave}
            >
                {staticMessages.primarySaveButtonLabel || 'Save' }
            </Button>
            <Button onClick={onCancel} id="single-skill-window-cancel-button">
                {staticMessages.cancelButtonLabel || 'Cancel' }
            </Button>
        </div>
    </>
);

Footer.propTypes = {
    staticMessages: object.isRequired,
    saveButtonEnabled: bool.isRequired,
    onSave: func.isRequired,
    onCancel: func.isRequired
};

const Tags = ({
    tags = [],
    prefix = SKILL_WINDOW_LABELS.TAGS_PREFIX
}) => (
    <>
        <Divider />
        <div className={styles.tagsContainer}>
            <p>
                <span className={styles.tagsPrefix}>{prefix} </span>{tags.join(', ')}
            </p>
        </div>
    </>
);

Tags.propTypes = {
    tags: array,
    prefix: string
};