import React, { Component } from 'react';
import { Popover } from 'antd';
import { string, bool, func, object, oneOfType, array } from 'prop-types';
import styles from './styles.less';
import { SingleSkillWindow } from './singleSkillWindow';

export class SingleSkillWindowPopover extends Component {
    constructor(props) {
        super(props);

        this.onVisibleChange = this.onVisibleChange.bind(this);
        this.handleScroll = this.handleScroll.bind(this);
        this.disposeWindow = this.disposeWindow.bind(this);
        this.handleMouseEnter = this.handleMouseEnter.bind(this);
        this.handleMouseLeave = this.handleMouseLeave.bind(this);
        this.childBodyRef = React.createRef();
        this.state = {
            visible: false,
            popoverHovered: false
        };
    }

    componentDidMount() {
        window.addEventListener('wheel', this.handleScroll, { passive: false, capture: true });
    }

    componentWillUnmount() {
        window.removeEventListener('wheel', this.handleScroll);
    }

    isScrollMovingUp(deltaY) {
        return deltaY < 0;
    }

    isScrollMaxDown(element) {
        const { scrollTop, scrollHeight, clientHeight } = element;

        return scrollTop + clientHeight === scrollHeight;
    }

    isScrollMaxUp(element) {
        const { scrollTop } = element;

        return scrollTop === 0;
    }

    hasScroll(scrollHeight, clientHeight) {
        return scrollHeight != clientHeight;
    }

    conditionalLockScroll(e) {
        const element = this.childBodyRef.current;
        const { scrollHeight, clientHeight } = element;
        const scrollMovingUp = this.isScrollMovingUp(e.deltaY);
        const hasScroll = this.hasScroll(scrollHeight, clientHeight);

        if (hasScroll && this.isScrollMaxUp(element) && scrollMovingUp) {
            e.preventDefault();
        } else if (hasScroll && this.isScrollMaxDown(element) && !scrollMovingUp) {
            e.preventDefault();
        }
    }

    hasBody() {
        return this.childBodyRef.current;
    }

    handleScroll(e) {
        const { visible, popoverHovered } = this.state;

        if (popoverHovered && this.hasBody()) {
            this.conditionalLockScroll(e);
        }

        if (visible && !popoverHovered) {
            this.disposeWindow();
        }
    }

    onVisibleChange(visible) {
        if (visible) {
            this.setState({
                visible: visible
            });

            const { skill, initSkillFieldsForm, entityId } = this.props;
            initSkillFieldsForm(entityId, skill);
        } else {
            this.disposeWindow();
        }
    }

    handleMouseEnter() {
        this.setState({
            popoverHovered: true
        });
    }

    handleMouseLeave() {
        this.setState({
            popoverHovered: false
        });
    }

    disposeWindow() {
        this.setState(() => {
            if (this.props.markedForDeletion) {
                this.props.setSkillMarkedForDeletion(false);
            }

            return {
                visible: false,
                popoverHovered: false
            };
        });

        this.props.onWindowClose(this.props.entityId);
    }

    render() {
        const {
            children,
            staticMessages,
            skill,
            entityId,
            markedForDeletion,
            setSkillMarkedForDeletion,
            deleteSkill,
            updateSkill,
            fieldsForm,
            updateSkillEnabled,
            skillPreferences,
            getResourceSkillPreference,
            getSkillInfo,
            onSkillFieldChange,
            skillApprovalFeatureFlagEnabled,
            resourceSkillApprovalPermission
        } = this.props;

        const skillPreferenceProps = {
            skillPreferences,
            getResourceSkillPreference,
            getSkillInfo,
            onSkillFieldChange
        };

        const {
            visible
        } = this.state;

        const skillWindow = (
            <SingleSkillWindow
                {...skillPreferenceProps}
                bodyRef={this.childBodyRef}
                staticMessages={staticMessages}
                skill={skill}
                markedForDeletion={markedForDeletion}
                onMarkForDeletion={() => setSkillMarkedForDeletion(true)}
                deleteSkill={deleteSkill}
                updateSkill={updateSkill}
                disposeWindow={this.disposeWindow}
                entityId={entityId}
                fieldsForm={fieldsForm}
                updateSkillEnabled={updateSkillEnabled}
                onMouseEnter={this.handleMouseEnter}
                onMouseLeave={this.handleMouseLeave}
                popupControlsInBody={true}
                skillApprovalFeatureFlagEnabled={skillApprovalFeatureFlagEnabled}
                resourceSkillApprovalPermission={resourceSkillApprovalPermission}
            />
        );

        return (
            <Popover
                overlayClassName={styles.skillWindowPopover}
                arrow={{ pointAtCenter: true }}
                trigger="click"
                content={skillWindow}
                placement={'bottom'}
                getPopupContainer={(trigger) => trigger.parentNode}
                open={visible}
                onOpenChange={this.onVisibleChange}
            >
                {children}
            </Popover>
        );
    }
}

SingleSkillWindowPopover.propTypes = {
    skill: object.isRequired,
    children: object.isRequired,
    staticMessages: object,
    deleteSkill: func.isRequired,
    updateSkill: func.isRequired,
    initSkillFieldsForm: func.isRequired,
    entityId: string.isRequired,
    markedForDeletion: bool.isRequired,
    setSkillMarkedForDeletion: func.isRequired,
    fieldsForm: oneOfType([func, object]).isRequired,
    updateSkillEnabled: bool.isRequired,
    onWindowClose: func.isRequired,
    skillPreferences:array.isRequired,
    getResourceSkillPreference:func.isRequired,
    getSkillInfo:func.isRequired,
    onSkillFieldChange:func.isRequired,
    skillApprovalFeatureFlagEnabled: bool.isRequired,
    resourceSkillApprovalPermission: bool.isRequired
};