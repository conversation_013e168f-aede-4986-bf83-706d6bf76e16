import React from 'react';
import { ConnectedTableOptions } from '../../../connectedComponents/connectedTableOptions';
import { getWorkHistoryPageDataForEntityWindow } from '../../../selectors/workHistorySelector';
import { densityFields } from '../../../utils/commonDataGridUtils';
import { EntityFormSection } from './entityFormSection';
import PropTypes from 'prop-types';

class WorkHistoryEntityFormSection extends React.Component {
    render() {
        const {
            omitFields,
            fixedFields,
            tableName,
            density,
            profilePageMaxFieldsSelection,
            configAlias
        } = this.props;

        const sectionProps = {
            ...this.props,
            additionalTitleComponent: (
                <div className={'header-cog-icon'}>
                    <React.Suspense fallback={<div />}>
                        <ConnectedTableOptions
                        densityFields={densityFields}
                        triggerSubMenuAction={'click'}
                        tableName={tableName}
                        density={density}
                        fixedColumns={fixedFields}
                        omitFields={omitFields}
                        maxFieldsSelection={profilePageMaxFieldsSelection}
                        getDataGridPageState={getWorkHistoryPageDataForEntityWindow}
                        configAlias={configAlias}
                        menuContainer={(trigger) => trigger.closest('.entityDetailsWindow')}
                        dropdownContainer={(trigger) => trigger.parentNode}
                    /></React.Suspense>
                </div>
            )
        };

        return <EntityFormSection {...sectionProps} />;
    }
}

WorkHistoryEntityFormSection.propTypes = {
    registerLink: PropTypes.func,
    recentWorkConfig: PropTypes.object,
    visibleRecentWork: PropTypes.bool
};

export { WorkHistoryEntityFormSection };
