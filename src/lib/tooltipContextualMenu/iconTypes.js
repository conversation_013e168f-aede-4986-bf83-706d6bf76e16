/* eslint-disable react/display-name */
import React from 'react';
import Icon from '../icon';

export const iconTypes = {
    "close": (className) => {
        return (<Icon className={className} type='close' />)
    },
    "arrow-right": (className) => {
        return (<Icon className={className} type='arrow-right' />)
    },
    "date": (className) => {
        return (<Icon className={className} type='calendar' />)
    },
    "date-range": (className) => {
        return (<Icon className={className} type='calendar-range' />)
    },
    "set-date-range": (className) => {
        return (<Icon className={className} type='calendar-range' />)
    },
    "resource": (className) => {
        return (<Icon className={className} type='user' />)
    },
    "job": (className) => {
        return (<Icon className={className} type='job' />)
    },
    "list": (className) => {
        return (<Icon className={className} type='list' />)
    },
    "client": (className) => {
        return (<Icon className={className} type='client' />)
    },
    "booking": (className) => {
        return (<Icon className={className} type='booking' />)
    },
    "create-bar": (className) => {
        return (<Icon className={className} type='plus' />)
    },
    "delete-bar": (className) => {
        return (<Icon className={className} type='delete' />)
    },
    "delete": (className) => {
        return (<Icon className={className} type='delete' />)
    },
    "edit-bar": (className) => {
        return (<Icon className={className} type='edit' />)
    },
    "copy-bar": (className) => {
        return (<Icon className={className} type='copy' />)
    },
    "rollForward-bar": (className) => {
        return (<Icon className={className} type='rollForward' />)
    },
    "duplicate-bar": (className) => {
        return (<Icon className={className} type='duplicateBar' />)
    },
    "cut-bar": (className) => {
        return (<Icon className={className} type='cut' />)
    },
    "paste-bar": (className) => {
        return (<Icon className={className} type='paste' />)
    },
    "warning": (className) => {
        return (<Icon className={className} type='warning' />)
    },
    "text": (className) => {
        return
    },
    "private": (className) => {
        return (<Icon className={className} type='plans-page' />)
    },
    "public": (className) => {
        return (<Icon className={className} type='user-manager' />)
    },
    "information": (className) => {
        return (<Icon className={className} type='info' />)
    },
    "right": (className) => {
        return (<Icon className={className} type="right" />)
    },
    "left": (className) => {
        return (<Icon className={className} type="left" />)
    },
    "time": (className) => {
        return (<Icon className={className} type="time" />)
    },
    "role-group": (className) => {
        return (<Icon className={className} type="role-group" />)
    },
    "restart": (className) => {
        return (<Icon className={className} type="history" />)
    },
    "archive": (className) => {
        return (<Icon className={className} type="archive" />)
    },
    "reject": (className) => {
        return (<Icon className={className} type="close-circle" />)
    },
    "role": (className) => {
        return (<Icon className={className} type="role" />)
    },
    "criteria-role": (className) => {
        return (<Icon className={className} type="criteria-role"/>)
    },
    "missing-user": (className) => {
        return (<Icon className={className} type="missing-user"/>)
    },
    "user-slashed": (className) => {
        return (<Icon className={className} type="user-slashed" />)
    },
    "info-circle": (className) => {
        return (<Icon className={className} type="info-circle" />)
    },
    "refresh-icon": (className) => {
        return (<Icon className={className} type="refresh-icon" />)
    },
    "fte": (className) => {
        return (<Icon className={className} type="clock-icon" />)
    },
    "role-group-without-fill": (className) => {
        return (<Icon className={className} type="role-group-without-fill" />)
    },
    "empty-state-resources": (className) => {
        return (<Icon className={className} type="empty-state-resources" />);
    },
    "empty-state-hidden-suggestions": (className) => {
        return (<Icon className={className} type="empty-state-hidden-suggestions" />);
    },
    "megaphone": (className) => {
        return (<Icon className={className} type="megaphone" />)
    },
    "table": (className) => {
        return (<Icon className={className} type='table' />)
    },
    "completed-milestone": (className) => (<Icon className={className} type="completed-milestone" />),
    "unfinished-milestone": (className) => (<Icon className={className} type="unfinished-milestone" />),
    "overdue-milestone": (className) => (<Icon className={className} type="overdue-milestone" />),
    "external-link-goToProfile": (className) => {
        return (<Icon className={className} type="external-link-goToProfile" />)
    },
    'save-as-template': (className) => {
        return (<Icon className={className} type='save-as-template' />)
    },
    'project-health': (className) => {
        return (<Icon className={className} type='project-health' />)
    }
};