import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';


import { Table, Button, Spin, Empty, Modal, Checkbox } from 'antd';
import {Icon} from '../';
import React from 'react';
import ReactDragListView from 'react-drag-listview';
import PropTypes from 'prop-types';
import bindAll from 'lodash/bindAll';
import isEqual from 'lodash/isEqual';
import {FormattedMessage} from 'react-intl';
import {messageService} from '../../components/common-components/helpers';
import EditableFormRow from './editableFormRow';
import EditableCell from './editableCell';
import {uniqueId} from 'lodash';
import {ConnectedTableOptions} from '../../connectedComponents/connectedTableOptions';
import styles from './styles/styles.less';
import {SettingColumnsFragment} from './gridSubComponents/settingColumnsFragment';
import {adminSettingConsts} from '../../constants';

const {REUSABLE_GRID_CONSTANTS, SECTION_INTERNAL_NAME_CONSTANTS} = adminSettingConsts;
const {rowFormatStyles, DIRECT_EDIT, DELETE_OP, UPDATE_OP, formItemLayout} = REUSABLE_GRID_CONSTANTS;
const dragHandleColumn = [
    {
        title: '',
        dataIndex: 'dragHandle_operation',
        width:'5%',
        key: 'operate',
        render: (text, record, index) =>
            <a className="drag-handle" ><Icon type="menu" /></a>
    }
];

const EmptyStateView = (props) => {
    const {emptyView, entityType, handleAdd} = props;
    const {imageIcon, description, btnText} = emptyView;

    const getEmptyStateIcon = (entityType = '') => {

        switch(entityType.toLowerCase()) {
            case SECTION_INTERNAL_NAME_CONSTANTS.JOB: return <Icon type="job" theme="filled" />;
            case SECTION_INTERNAL_NAME_CONSTANTS.RESOURCE: return <Icon type="user-manager" theme="filled" />;
            case SECTION_INTERNAL_NAME_CONSTANTS.CLIENT: return <Icon type="client" theme="filled" />;

            default: return Empty.PRESENTED_IMAGE_SIMPLE;
        }
    };

    const getEmptyStateBtn = () => {

        if (!btnText) {
            return <></>;
        } else {
            return (
                <Button type="primary" onClick={handleAdd}>
                    {btnText}
                </Button>
            );
        }
    };

    return(
        <div className="ant-empty ant-empty-normal">
            <Empty className="ant-empty-description"
                image={ imageIcon ? imageIcon : getEmptyStateIcon(entityType)}
                aria-live="polite"
                aria-atomic="true"
                role="alert"
                description={description}>
                {getEmptyStateBtn()}
            </Empty>
        </div>
    );
};

class ResuableGrid extends React.Component {
    constructor(props) {
        super(props);
        const {dataSource = [], columns, idAttribute = 'id', additionalRequiredEntries = []} = props;

        this.state = {
            undoEditData: {},
            gridLevelUndoEditData:{},
            dataSource: [...this.getData(dataSource)],
            count: dataSource.length || 0,
            currentRecord: undefined,
            deleteConfirmed:false,
            defaultDataSrcEntries: columns.map((item) => item.dataIndex).concat(['operation']).concat(idAttribute).concat(additionalRequiredEntries),
            dataReorderedState: false,
            showEditConfirmation:false,
            showDeleteConfirmation:false,
            editRecord:undefined
        };

        bindAll(this, ['resetRowNumber', 'deleteInvalidItems', 'handleAdd', 'handleEdit', 'cancelEdit', 'handleCheck',
            'handleCellUpdate', 'onCancelDeletion', 'handleNewItemDelete', 'getRowClassName', 'resetGrid', 'setCurrentRecord',
            'updateDataSource', 'getTableOptionsColumn', 'isTableAltered', 'toggleExternalButton', 'handleExternalAdd','renderGridView',
            'showTable', 'getSettingColumns', 'handleMarkForDelete','selectReorderMenuOption','getDragProps','onDragEnd','getMappedColumn',
            'determineGridLevelMofificationStatus','handleSaveCallback','handleCancelCallback','handleDeleteCancelCallback','handleDeleteCallback','renderDeleteIcon']);

    }

    determineGridLevelMofificationStatus(gridLevelResetRow = this.state.gridLevelUndoEditData, dataSource = this.state.dataSource) {
        const uniqueKey = this.props.uniqueGridId || this.props.id;
        const isAnyElementChanged = dataSource.filter((item) => (item.markForDelete || (item.newItem && item.newItemVisited)));
        return (gridLevelResetRow[uniqueKey] === undefined || Object.keys(gridLevelResetRow[uniqueKey]).length == 0) && isAnyElementChanged.length == 0 ? true : false;
    }

    selectReorderMenuOption(event, sourceKeyReorderOption){
        const sourceKey = sourceKeyReorderOption;
        const targetKey = event.key;
        const dataSource = this.state.dataSource;
        const item = dataSource.splice(sourceKey, 1)[0];
        dataSource.splice(targetKey, 0, item);
        this.setState({
            dataSource,
            dataReorderedState: true,
            sourceKeyReorderOption: null
        });

    }


    renderDeleteIcon(warningOnDelete, record, handleMarkForDelete, setCurrentRecord) {
        if(this.props.deleteConfirmationCallback) {
            setCurrentRecord(record);
            this.setState({showDeleteConfirmation:true});
        } else {
            if(warningOnDelete) {
                setCurrentRecord(record);
            } else {
                handleMarkForDelete(record.key);
            }
        }
    }

    toggleExternalButton(isGridModified, newDataSource = this.state.dataSource,gridLevelIsGridModified,isReorderingDone = false, licenseCnt) {
        const {toggleExternalBtnStatus, id} = this.props;
        const errorList = newDataSource.filter((item) => (item.error && !item.markForDelete));
        let hasError = false;
        if(errorList.length > 0) {
            hasError = true;
        }
        if(!licenseCnt) {
            licenseCnt = newDataSource.length;
        }
        messageService.sendMessage({id, errorStatus: hasError, isGridModified: !isGridModified});
        toggleExternalBtnStatus && toggleExternalBtnStatus(isGridModified, hasError, id, newDataSource,gridLevelIsGridModified,isReorderingDone, licenseCnt); //isReorderingDone is reset once sent to the outside component
    }

    onCancelDeletion(key) {
        const dataSource = [...this.state.dataSource];
        const newDataSource = dataSource.map(item => item.key === key ? {...item, markForDelete: false, operation: UPDATE_OP} : item);
        this.isTableAltered(undefined, newDataSource);
        this.setState({dataSource: newDataSource});
    }

    handleNewItemDelete(key) {
        const dataSource = [...this.state.dataSource];
        const newDataSource = dataSource.filter(item => !(item.key === key));
        /******Remove if present from undoEditList ****/
        let resetRow = {...this.state.undoEditData};
        let gridLevelResetRow = {...this.state.gridLevelUndoEditData};
        const uniqueKey = this.props.uniqueGridId || this.props.id;
        if(resetRow[key]) {
            delete resetRow[key];
        }
        if(gridLevelResetRow[uniqueKey] && gridLevelResetRow[uniqueKey][key]) {
            delete gridLevelResetRow[uniqueKey][key];
        }
        this.isTableAltered(resetRow, newDataSource);
        this.setState({dataSource: newDataSource, undoEditData: resetRow, count: newDataSource.length, gridLevelUndoEditData:gridLevelResetRow}, this.resetRowNumber);
    }

    //function added to call it from outside
    handleNewItemDeleteAll() {
        const dataSource = [...this.state.dataSource];
        let resetRow = {...this.state.undoEditData};
        let gridLevelResetRow = {...this.state.gridLevelUndoEditData};
        const uniqueKey = this.props.uniqueGridId || this.props.id;

        const newDataSource = dataSource.filter(item => {
            if(item.newItem) { //also can check for newItemVIsited flag
                /******Remove if present from undoEditList ****/
                if(resetRow[item.key]) {
                    delete resetRow[item.key];
                }
                if(gridLevelResetRow[uniqueKey] && gridLevelResetRow[uniqueKey][item.key]) {
                    delete gridLevelResetRow[uniqueKey][item.key];
                }
                return false;
            }
            return true;
        });
        this.setState({dataSource: newDataSource, undoEditData: resetRow, count: newDataSource.length, gridLevelUndoEditData:gridLevelResetRow}, this.resetRowNumber);
    }

    getCurrentGridRecords() {
        return this.state.dataSource;
    }

    setCurrentRecord(record) {
        this.setState({currentRecord: record, deleteConfirmed: false});
    }

    handleCheck() {
        this.setState({
            deleteConfirmed:!this.state.deleteConfirmed
        });
    }

    renderConfirmModal(record) {
        let recordName = record.name ?
            <FormattedMessage id="markDeleteWarningTitle" values={{recordName: record.name}} /> :
            <FormattedMessage id="markDeleteWarningTitleDefault" />;

        const {warningOnDelete} = this.props;
        const {warningMessage, checkMessage} = warningOnDelete;
        return (
            (<Modal
                title={recordName}
                footer={null}
                open={true}
                onCancel={this.setCurrentRecord}>
                <div className="confirm-delete-modal">
                    {warningMessage()}
                    <Form>
                        <Form.Item>
                            <Checkbox onChange={this.handleCheck} checked={this.state.deleteConfirmed}>
                                <FormattedMessage {...checkMessage}/>
                            </Checkbox>
                        </Form.Item>
                        <hr/>
                        <Form.Item {...formItemLayout} className="buttonGroup">
                            <Button type="danger" className="mark_delete_btn" onClick={()=>this.handleMarkForDelete(record.key)} disabled={!this.state.deleteConfirmed}>
                                <FormattedMessage id="userManagementTooltipMarkDelete"/>
                            </Button>
                            <Button type="link" className="cancel_delete_btn" onClick={() => this.setCurrentRecord(undefined)}>
                                <FormattedMessage id="cancelButtonLabel" />
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
            </Modal>)
        );
    }

    handleSaveCallback(record) {
        this.setState({showEditConfirmation:false});
        const dataSource = [...this.state.dataSource];
        this.setState({dataSource: dataSource.map(item => item.key === record.key ? {...item, markForEdit: !item.markForEdit} : item)});
    }

    handleCancelCallback() {
        this.setState({showEditConfirmation:false});
    }

    handleDeleteCallback(record) {
        this.setState({showDeleteConfirmation:false});
        this.handleMarkForDelete(record.key);
    }

    handleDeleteCancelCallback() {
        this.setState({showDeleteConfirmation:false});
        this.setCurrentRecord();
    }

    renderEditConfirmModal() {
        return this.props.editConfirmationCallback(this.state.editRecord,this.handleSaveCallback,this.handleCancelCallback);
    }

    renderDeleteConfirmModal() {
        return this.props.deleteConfirmationCallback(this.state.currentRecord,this.handleDeleteCallback,this.handleDeleteCancelCallback);
    }

    handleMarkForDelete(key) {
        const dataSource = [...this.state.dataSource];
        const updateDataSource = dataSource.map(item => item.key === key ? {...item, markForDelete: true, operation: DELETE_OP} : item);
        this.toggleExternalButton(false, updateDataSource,false);
        this.setState({dataSource: updateDataSource, currentRecord: undefined, deleteConfirmed: false});
    }

    handleEdit(key,record) {
        if(this.props.editConfirmationCallback && record.showConfirmation) {
            this.setState({showEditConfirmation:true,editRecord:record});
        } else{
            const dataSource = [...this.state.dataSource];
            this.setState({dataSource: dataSource.map(item => item.key === key ? {...item, markForEdit: !item.markForEdit} : item)});
        }
    }

    cancelEdit(key) {
        const dataSource = [...this.state.dataSource];
        const uniqueKey = this.props.uniqueGridId || this.props.id;
        if(!this.state.undoEditData[key] || key === undefined) {
            this.setState({dataSource: dataSource.map(item => item.key === key ? {...item, markForEdit: false} : item)});
            return;
        } else {
            const originalRow = {...this.state.undoEditData[key]};
            let resetRow = {...this.state.undoEditData};
            let gridLevelResetRow = {...this.state.gridLevelUndoEditData};
            delete resetRow[key];
            delete gridLevelResetRow[uniqueKey][key];
            const updateDataSource = dataSource.map(item => item.key === key ? {...originalRow, markForEdit: false} : item);
            this.isTableAltered(resetRow, updateDataSource,gridLevelResetRow);
            this.setState({dataSource: updateDataSource, undoEditData: resetRow,gridLevelUndoEditData:gridLevelResetRow});
        }
    }

    handleAdd() {
        const {count, dataSource} = this.state;
        const {defaultNewRow, idAttribute = 'id', addPosition = 'UP', id, emptyView = {}} = this.props;
        let newData = {
            key: `${uniqueId(`${id}_`)}${count + 1}`,
            [idAttribute]: count,
            newItem: true,
            rowNumber: count + 1
        };
        if (defaultNewRow) {
            newData = {...newData, ...defaultNewRow};
        }

        let newDataSource = [newData, ...dataSource];
        if(addPosition !== 'UP') {
            newDataSource = [...dataSource, newData];
        }
        //trigger toggle external button
        this.toggleExternalButton(false, newDataSource, false, undefined, newDataSource.length);

        this.setState({
            dataSource: newDataSource,
            count: count + 1
        }, this.resetRowNumber);
        emptyView.btnClickEvent && emptyView.btnClickEvent();
    }

    //adding record in grid from external source.
    handleExternalAdd(externalData) {
        const {count, dataSource} = this.state;
        const {idAttribute = 'id', customHandler} = this.props;
        //customHandler for checking duplicate record in datasource when added from external source
        const updatedDataSrc = customHandler ? customHandler(dataSource, externalData) : dataSource;
        let newData = {
            key: count + 1,
            [idAttribute]: count,
            newItem: true
        };
        newData = {...newData, ...externalData};

        this.setState({
            dataSource: [newData, ...updatedDataSrc],
            count: count + 1
        });
    }

    deleteInvalidItems() {
        const {allowErrorFields = false} = this.props;
        let newDataSource = this.state.dataSource.filter((item) => (!item.error || item.markForDelete));
        if(allowErrorFields) {
            newDataSource = this.state.dataSource;
        }
        // Delete Unvisited new added rows
        newDataSource = newDataSource.filter((item) => !(item.newItem && !item.newItemVisited));
        newDataSource = newDataSource.map(function (itemX) {
            return {...itemX, newItem: false};
        });
        return newDataSource;
    }

    isTableAltered(resetRow = this.state.undoEditData, dataSource = this.state.dataSource,gridLevelResetRow = this.state.gridLevelUndoEditData) {
        const uniqueKey = this.props.uniqueGridId || this.props.id;
        const isAnyElementChanged = dataSource.filter((item) => (item.markForDelete || (item.newItem && item.newItemVisited)));
        const gridLevelStatus = (gridLevelResetRow[uniqueKey] === undefined || Object.keys(gridLevelResetRow[uniqueKey]).length == 0) && isAnyElementChanged.length == 0 ? true : false;
        if(Object.keys(resetRow).length == 0 && isAnyElementChanged.length == 0) {
            this.toggleExternalButton(true, dataSource,gridLevelStatus);
            return false;
        } else {
            this.toggleExternalButton(false, dataSource,gridLevelStatus);
        }
        return true;
    }

    compareValues(originalData, rowData, itemIndex) {
        if(originalData[itemIndex] !== undefined &&
            !isEqual(originalData[itemIndex], rowData[itemIndex]) &&
            !(this.isEmptyContent(originalData[itemIndex]) && this.isEmptyContent(rowData[itemIndex]))) {
            return false;
        }
        return true;
    }
    isEmptyContent(value) {
        if(value === '' || value === null || value === undefined) {
            return true;
        }
        return false;
    }

    handleCellUpdate(row) {
        const {dataSource} = this.props;
        const {undoEditData} = this.state;
        const idAttrib = this.props.idAttribute || 'id';
        let originalData = dataSource.find(item => row[idAttrib] === item[idAttrib]);

        const newData = [...this.state.dataSource];
        const index = newData.findIndex(item => row.key === item.key);
        const item = newData[index];
        const result = newData.find(item => item.key === row.key);
        const uniqueKey = this.props.uniqueGridId || this.props.id;

        if(row.newItem) {
            row.newItemVisited = true;
        }

        newData.splice(index, 1, {...item, ...row});

        /******  To handle Undo functionality below undoEditData is set  ***********/
        let equalProps = true;
        if(undoEditData[row.key]) {
            originalData = undoEditData[row.key];
        }
        !row.newItem && this.props.columns.forEach((item) => {
            if(item.formField == 'RangePicker') {
                ['startDate', 'endDate'].forEach((rangeIndex) => {
                    if(!this.compareValues(originalData, row, rangeIndex)) {
                        equalProps = false;
                    }
                });
            } else if(!this.compareValues(originalData, row, item.dataIndex)) {
                equalProps = false;
            }
        });
        let gridLevelResetRow = {...this.state.gridLevelUndoEditData};
        if(equalProps && !row.newItem) {
            let resetRow = {...this.state.undoEditData};
            // let gridLevelResetRow = {...this.state.gridLevelUndoEditData};
            delete resetRow[row.key];
            delete gridLevelResetRow[uniqueKey][row.key];
            this.isTableAltered(resetRow, newData,gridLevelResetRow);
            this.setState({undoEditData: resetRow,gridLevelUndoEditData:gridLevelResetRow});
        } else if(!this.state.undoEditData[row.key]) {
            const resultFinal = {[row.key]: result};
            this.setState({undoEditData: {...this.state.undoEditData, ...resultFinal},gridLevelUndoEditData:{...this.state.gridLevelUndoEditData,[uniqueKey]:{...this.state.gridLevelUndoEditData[uniqueKey],...resultFinal}}});
        }
        /***************************************************************************/

        if(!equalProps || row.newItem) {
            // const gridLevelStatus = this.determineGridLevelMofificationStatus(gridLevelResetRow,newData);
            this.toggleExternalButton(false, newData,false);
        }
        this.setState({dataSource: newData});
    }

    getData(dataSource) {
        if(!dataSource) {
            return [];
        }
        return dataSource.map((data, index) => {
            return {
                key: data.key || index,
                ...data
            };
        });
    }

    getRowClassName(record, index) {
        const {gridFormat = 'default', className} = this.props;
        let concatclassName = styles.dataGridRow;
        if(className) {
            concatclassName += ` ${className}`;
        }
        concatclassName += ` ${styles[rowFormatStyles[gridFormat]]}`;
        if(record) {
            concatclassName += ` ${record.markForEdit ? 'row-color-edit' : ''}`;
            concatclassName += ` ${record.newItem ? 'row-color-new' : ''}`;
            concatclassName += ` ${!record.newItem && index % 2 === 0 ? 'row-color-even' : 'row-color-odd'}`;
            if(record.error) {
                concatclassName += ' row-has-error';
            }
        }
        return concatclassName;
    }

    resetGrid() {
        this.setState({count: 0, undoEditData: {},gridLevelUndoEditData:{}, dataSource: []});
    }

    updateDataSource() {
        const {updateDataSource, id} = this.props;
        let {defaultDataSrcEntries} = this.state;
        let dataSource = this.deleteInvalidItems(dataSource);
        this.toggleExternalButton(true,undefined,true);
        if(updateDataSource) {
            dataSource = dataSource.map((item) => {
                for(var propt in item) {
                    if(!defaultDataSrcEntries.includes(propt)) {
                        delete item[propt];
                    }
                }
                return item;
            });
            updateDataSource(dataSource, id);
            this.setState({count: 0, undoEditData: {},gridLevelUndoEditData:{}});
        }
    }

    getTableOptionsColumn() {
        const tableOptionsColumnWidth = 10;
        return {
            title: <React.Suspense fallback={<div />}>
                <ConnectedTableOptions />
            </React.Suspense>,
            fixed: 'right',
            key: 'tableOptions',
            width: tableOptionsColumnWidth,
            onHeaderCell: () => {
                return {
                    isDataCell: false
                };
            },
            onRow: () => ({
                hideDeleteCancelationRow: true
            })
        };
    }

    getSettingColumns(operationColFixed, settingColumnProps){
        const {messages = {}} = this.props;
        return{
            title: settingColumnProps.showAdditionalCols ? <Icon type="setting" /> : <></>, //send showAdditionalCols as true to display settings icon
            dataIndex: 'grid_operation',
            className: 'operations resuable_grid_op',
            width: '20%',
            align: 'center',
            fixed: operationColFixed ? 'right' : '',
            render: (text, record) =>
                <SettingColumnsFragment messages={messages} text={text} record={record} settingColumnProps ={settingColumnProps} />
        };
    }


    componentDidUpdate(prevProps){
        let {dataSource} = this.props;
        if(!isEqual(prevProps.dataSource, dataSource) || (this.state.count === 0 && dataSource && dataSource.length > 0)){
            if(!dataSource) { dataSource = [];}
            this.setState({dataSource: [...this.getData(dataSource)], count: dataSource.length});
        }
        if(this.state.dataReorderedState === true){
            this.toggleExternalButton(false,undefined,false,true);
            this.setState({dataReorderedState: false});
        }
    }

    resetRowNumber() {
        this.setState({dataSource:this.state.dataSource.map((row, index) => {
            return {...row, rowNumber:index + 1};
        })});
    }

    showTable(columns,dragProps) {
        const {tableSpecificProps,
            emptyView,
            bordered,
            tableScrollSize = {},
            hasPagination,
            paginationProps
        } = this.props;

        const components = {
            body: {
                row: EditableFormRow,
                cell: EditableCell
            }
        };


        const defaultClass = this.getRowClassName;

        return (
            <ReactDragListView {...dragProps}>
                <Table
                    scroll={{x: tableScrollSize.width, y: tableScrollSize.height}}
                    className={this.props.tableClassName || this.props.id}
                    components={components}
                    rowClassName={defaultClass}
                    bordered={bordered}
                    dataSource={this.state.dataSource}
                    columns={columns}
                    onRow={(rowData) => ({
                        rowData,
                        onCancelDeletion: this.onCancelDeletion
                    })}
                    locale={{emptyText: <EmptyStateView emptyView={emptyView} entityType={this.props.entityType} handleAdd={this.handleAdd}/>}}
                    {...tableSpecificProps}
                    pagination={hasPagination ? paginationProps : false} />
            </ReactDragListView>
        );
    }

    renderGridView(columns,dragProps) {
        const {showEmptyGridView} = this.props;
        if(!showEmptyGridView || this.state.dataSource.length) {
            return this.showTable(columns,dragProps);
        }else {
            //show empty view for grid when no data.
            return(
                <React.Fragment></React.Fragment>
            );
        }
    }

    onDragEnd(fromIndex, toIndex) {
        if(fromIndex >= 0 && toIndex >= 0) {
            const dataSource = this.state.dataSource;
            const item = dataSource.splice(fromIndex, 1)[0];
            dataSource.splice(toIndex, 0, item);
            this.setState({
                dataSource,
                dataReorderedState: true
            });
        }
    }

    getDragProps(){
        const {onDragEnd} = this;
        return {
            onDragEnd,
            handleSelector: 'a',
            lineClassName: 'reorder-row',
            nodeSelector: 'tr.drag-drop-selector',
            ignoreSelector: 'tr.mark-for-delete'
        };
    }

    getMappedColumn(columns) {
        const mappedColumn = columns.map(col => {
            switch(col.dataIndex){
                case 'grid_operation' :
                case 'dragHandle_operation' :
                    col.editable = false;
                    break;
                default :
                    col.editable = col.editable === false ? false : true;
                    break;
            }

            return {
                ...col,
                onCell: record => ({
                    record,
                    editable: col.editable,
                    dataindex: col.dataIndex,
                    placeholder: col.placeHolder,
                    ariaLabel: col.ariaLabel,
                    customvalidator: col.customValidator,
                    id: this.props.id,
                    ignoreMessageUpdate: this.props.ignoreMessageUpdate,
                    ruleoptions: col.ruleOptions,
                    formField: col.formField,
                    customProps: col.customProps,
                    metaData:col.metaData,
                    colTitle: col.title,
                    datasource: this.state.dataSource,
                    tableType: this.props.tableType,
                    formatterSymbol: col.formatterSymbol,
                    togglebutton: this.toggleExternalButton,
                    markedit: (record.newItem || this.props.tableType == DIRECT_EDIT) ? true : record.markForEdit,
                    handleCellUpdate: this.handleCellUpdate
                })
            };
        });

        return mappedColumn;
    }

    render() {
        const dragProps = this.getDragProps();
        const {loading,
            addButton,
            showReadOnlyFeature,
            tableType,
            defaultAddBtnHide,
            reorderingRowRequired,
            customOperations,
            warningOnDelete = false,
            operationColFixed,
            showAdditionalCols
        } = this.props;

        const settingColumnProps = {
            tableType,
            cancelEdit: this.cancelEdit,
            handleEdit: this.handleEdit,
            renderDeleteIcon: this.renderDeleteIcon,
            setCurrentRecord: this.setCurrentRecord,
            handleNewItemDelete: this.handleNewItemDelete,
            customOperations: customOperations,
            warningOnDelete: warningOnDelete,
            handleMarkForDelete: this.handleMarkForDelete,
            undoEditData : this.state.undoEditData,
            reorderingRowRequired: reorderingRowRequired,
            dataSource: this.state.dataSource,
            selectReorderMenuOption: this.selectReorderMenuOption,
            showAdditionalCols
        };

        const settingColumns = this.getSettingColumns(operationColFixed,settingColumnProps);
        let {columns} = this.props;

        if (loading || columns === undefined) {
            return (
                <Spin/>
            );
        }

        columns = reorderingRowRequired ? dragHandleColumn.concat(columns.concat(settingColumns)) : columns.concat(settingColumns);
        columns = this.getMappedColumn(columns);
        columns = showReadOnlyFeature ? columns.concat(this.getTableOptionsColumn()) : columns;


        return (
            <div className="resuable-data-grid">
                <span onClick={this.handleAdd}>
                    {addButton ? addButton :
                        (!defaultAddBtnHide && <Button type="primary" style={{marginBottom: 16}}>
                            <FormattedMessage id="addButtonReusableGrid"/>
                        </Button>)
                    }
                </span>
                {this.state.currentRecord && (!this.props.deleteConfirmationCallback) && this.renderConfirmModal(this.state.currentRecord)}
                {this.state.showEditConfirmation && this.renderEditConfirmModal(this.state.currentRecord)}
                {this.state.currentRecord && this.state.showDeleteConfirmation && this.renderDeleteConfirmModal(this.state.currentRecord)}
                {this.renderGridView(columns,dragProps)}
            </div>
        );
    }
}

ResuableGrid.propTypes = {
    loading: PropTypes.bool,
    dataSource: PropTypes.array.isRequired,
    gridFormat: PropTypes.string,
    updateDataSource: PropTypes.func,
    tableType: PropTypes.string,
    className: PropTypes.string,
    pagination: PropTypes.any,
    showReadOnlyFeature: PropTypes.bool,
    columns: PropTypes.array,
    defaultNewRow: PropTypes.object,
    toggleExternalBtnStatus: PropTypes.func,
    addButton: PropTypes.any,
    idAttribute: PropTypes.string,
    hasPagination:PropTypes.bool,
    showEmptyGridView: PropTypes.bool,
    reorderingRowRequired: PropTypes.bool,
    entityType: PropTypes.string
};

ResuableGrid.defaultProps = {
    toggleExternalBtnStatus: undefined,
    updateDataSource: () => {},
    addButton: undefined,
    dataSource: [],
    loading: false,
    gridFormat: 'default',
    tableType: undefined,
    className: '',
    pagination: true,
    showReadOnlyFeature: false,
    columns: [],
    defaultNewRow: {operation: 'Add'},
    idAttribute: 'guid',
    tableClassName: '',
    showEmptyGridView: false,
    reorderingRowRequired: false,
    uniqueGridId:undefined, //new property used to set maintain individual grid level modification status
    entityType: undefined
};

EmptyStateView.propTypes = {
    emptyView: PropTypes.object,
    handleAdd: PropTypes.func,
    btnText: PropTypes.string,
    entityType: PropTypes.any
};

EmptyStateView.defaultProps = {
    emptyView:{
        imageIcon: Empty.PRESENTED_IMAGE_SIMPLE,
        description: <FormattedMessage id="noResultsText" />,
        btnText: '',
        btnClickEvent: () => {}
    },
    handleAdd: ()=>{}
};

export default ResuableGrid;
