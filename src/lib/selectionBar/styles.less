.selectionBar {
    left: 0 !important;
    position: fixed !important;
    bottom: 4%;
    top: auto !important;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0;
    z-index: 2;
    .ant-popover-content {
        display: block;
        position: relative;
        bottom: 0;
        z-index: 2;
    }

    .ant-popover-inner {
        padding: 0px;
        height: 50px;

        .ant-popover-inner-content {
            height: inherit;

            > div {
                height: inherit;
            }
        }
    }

    .selectedLabel {
        margin-right: 70px;
        padding: 12px 0;
        min-height: 49px;
        display: flex;
        align-items: center;
    }
    .optionLabel {
        padding-left: 10px;

        .ant-btn {
            padding: 0 5px 0 10px; 
        }
    }
}

.selectionBar .ant-popover-content {
    .ant-popover-arrow {
        display: none;
    }

    .ant-btn-link:not(:disabled):not(.ant-btn-disabled):hover {
        color: @drag-item-color;
        background-color: transparent;
    }
}

.selectionBar .ant-popover-inner-content {
    background-color: @heading-color !important; 
    color: @white-color !important;
    border-radius: 5px;
    padding: 0 16px;
    > div {
        display: flex;
        align-items: center;
    }

    .primaryButton span, .dangerButton span {
        text-decoration: none;
    }

    .ant-btn-link {
        color: @white-color;
    }

    .itemsCounter {
        margin-right: 15px;
        margin-left: -10px;
    }
}

.selectionBar {
    .ant-tooltip {
        top: auto !important;
        bottom: 100%;
        min-width: 200px;
    }
}

.combinedSelectionBar {
    display: inline !important;
    color: @white-color!important;
    margin-right: 10px;
    text-align: center;
    &.__drop-down-open {
        background-color: @item-active-bg-dark;
    }
    .ant-dropdown-trigger {
        cursor: pointer;
        .ant-tag {
            cursor: pointer;
        }
    }
    .ant-dropdown-trigger {
        min-width: 150px;
        display: inline-block !important;
        margin: 0;
        display: flex !important;
        align-items: center;
        justify-content: center;
        padding: 12px 0 !important;
        min-height: 49px;
    }
    .ant-dropdown {
        margin: 0;
        left: 0 !important;
        box-shadow: none;
        border: none;
        min-width: 150px;
        &:before {
            display: none;
        }
        .ant-dropdown-menu {
            border: none;
            margin: 0;
            .ant-dropdown-menu-item {
                .menuItemRow {
                    span + span {
                        margin: 0;
                    }
                }
            }
        }
    }
}

.selectionBarDropdownOpen {
    display: inline !important;
    color: @heading-color !important;
    padding: 6px 2px;
}

.selectionBarDropdownClosed {
    display: inline !important;
    padding: 6px 2px;
    color: @white-color !important;
}

.dropdownItemWrapper {
    display: inline-flex !important
}