.floatingBar {
    left: 0 !important;
    position: fixed !important;
    bottom: 4%;
    top: auto !important;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0;
    z-index: 1;

    &.ant-popover-hidden {
        display: none !important;
    }

    .ant-popover-content {
        display: block;
        position: relative;
        bottom: 0;
        z-index: 1;
    }

    .ant-popover-inner {
        padding: 0px;
        height: 50px;

        .ant-popover-inner-content {
            height: inherit;

            > div {
                height: inherit;
            }
        }
    }

    :local(.selectedLabel) {
        margin-right: 70px;
        padding: 12px 0;
        min-height: 49px;
        display: flex;
        align-items: center;
    }
    
    .anticon-sort-up-down {
        width: 17px;
    }
}

.floatingBar {
    .ant-popover-arrow {
        display: none;
    }

    .ant-btn-link:not(:disabled):not(.ant-btn-disabled):hover {
        color: @drag-item-color;
        background-color: transparent;
    }
}

.floatingBar .ant-popover-inner-content {
    background-color: @heading-color !important; 
    color: @white-color !important;
    border-radius: 5px;
    padding: 0 16px;
    > div {
        display: flex;
        align-items: center;
    }

    .primaryButton span, .dangerButton span {
        text-decoration: none;
    }

    .ant-btn-link {
        color: @white-color;
    }

    .itemsCounter {
        margin-right: 15px;
        margin-left: -10px;
    }
}