import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Popover } from 'antd';
import styles from './styles.less';
import { ActionElement } from '../selectionBar';

const FloatingBar = ({ showFloatingActionBar, floatingActionBarLabel, floatingActionBarButtonLabel, selectedField, closeSortFloatingBar, workspaceGuid, settings: workspaceSettings, onSortChanged }) => {
    const [visible, setVisible] = useState(false);

    useEffect(() => {
        setVisible(showFloatingActionBar);
    }, [showFloatingActionBar]);

    const changeSort = useCallback((sortField, sortSubRecords) => {
        if (onSortChanged) {
            onSortChanged(workspaceGuid, workspaceSettings, sortField, sortSubRecords);
        }
    }, [onSortChanged, workspaceGuid, workspaceSettings]);

    const buttonProps = useMemo(() => ({
        componentType: 'Button',
        label: floatingActionBarButtonLabel,
        onButtonClick: selectedField ? () => changeSort(selectedField, false) : null,
        additionalProps: {
            className: 'primaryButton',
            hasDynamicIcon: true,
            hasDynamicLabel: true,
            type: 'link',
            iconType: 'sort-up-down',
            showIconOnBar: true
        }
    }), [floatingActionBarButtonLabel, selectedField, changeSort]);

    const closeButton = useMemo(() => ({
        label: null,
        onButtonClick: () => closeSortFloatingBar(),
        hotKeyDescription: null,
        componentType: 'Icon',
        additionalProps: { className: 'closeButton', type: 'close' }
    }), [closeSortFloatingBar]);

    const content = (
        <div className={styles.floatingBarContent}>
            <p className={styles.selectedLabel}>{floatingActionBarLabel}</p>
            <ActionElement {...buttonProps} />
            <ActionElement {...closeButton} />
        </div>
    );

    return (
        <Popover
            content={content}
            trigger="click"
            overlayClassName="floatingBar"
            open={visible}
        />
    );
};

export default FloatingBar;
