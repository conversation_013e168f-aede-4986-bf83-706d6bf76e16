.attachment-message {
    margin: 8px 0;

    &.default {
        background: #F4F4F4;
        border: 1px solid gray;
    }
}

.attachment-date-legacy {
    color: gray;
    font-size: 12px;
}

.attachment-date {
    color: black;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.attachment-link-disabled {
    display: inline-block;
}

.attachment-link-legacy {
    color: @primary-10;

    &:hover {
        color: @primary-10
    }
}

.attachment-link {
    color: black;
    display: inline-block;
    width: 100%;

    &:hover {
        color: @primary-10
    }
}

.attachment-legacy {
    margin: 14px 0;
}

.attachment {
    background: #F5F5F5;
    width: 150px;
    padding: 8px;
    display: grid;
    gap: 8px !important;
    margin-right: 12px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 14px;
}

.attachment-section {
    display: flex;
    flex-wrap: wrap;
}

.attachment-button {
    margin-bottom: 20px;
}

.attachment-expiry-tag-and-action-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attachment .actions-menu-legacy {
    cursor: pointer;
    margin: 0 8px;
    vertical-align: -6px;
    font-size: x-large;
}

.attachment .actions-menu:hover {
    background-color: @primary-color;
    color: white;
    border-radius: 8px;
}

.attachment .actions-menu {
    font-size: x-large;
}

.actions-menu-item-delete-legacy {
    min-width: 100px;
}

.actions-menu-item-delete {
    min-width: 100px;
    color: red !important;
}

.attachment-certificates-and-others {
    display: flex;
    flex-wrap: wrap;
}

.document-icon{
    color: @primary-color;
    font-size: x-large;
}

.delete-icon{
    color: red;
    font-size: large;
}

.document-type-radio-button {
    display: flex;
    gap: 8px;
    flex-direction: column;
    margin-top: 6px;
}

.move-to {
    margin-left: 19px !important;
}

.attachment-tooltip-content {
    color: black;
    border-radius: 8px;
}

.attachment-hover-container:hover .attachment-link,
.attachment-hover-container:hover {
    cursor: pointer;
    color: @primary-color;
}

.certification-and-other {
    font-weight: 400;
    margin: 0;
    margin-bottom: 12px;
}