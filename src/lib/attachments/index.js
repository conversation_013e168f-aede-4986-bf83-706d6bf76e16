import React, { Component } from 'react';
import PropTypes, { string, array, func, any, bool, object } from 'prop-types';
import { MessageArea } from '../messageArea';
import './styles.less';
import { ActionsMenu } from '../actionsMenu';
import { ATTACHMENT_LABELS, ATTACHMENT_MESSAGES, ATTACHMENT_TRUNCATION_LENGTH, attachmentExpiryStatus, attachmentTypes, DEFAULT_FILE_EXPIRY_DAYS } from '../../constants/attachmentsConsts';
import { Skeleton, Tooltip, Upload } from 'antd';
import { parseToUtcDate, parseUtcToLocalDate, formatLocalDate, getDisplayLongDateTimeFormat } from '../../utils/dateUtils';
import Icon from '../icon';
import { FileFilled, InboxOutlined } from '@ant-design/icons';
import { convertBytesToMB, isTalentProfilePageTransformedAndIsResourceTable } from '../../utils/attachments';
import { getTruncatedText } from '../../utils/commonUtils';
import { DISPLAY_DATE_TIME_FORMATS } from '../../constants/globalConsts';

export class AttachmentsSection extends Component {
    constructor(props) {
        super(props);

        this.onDownload = this.onDownload.bind(this);
        this.onDelete = this.onDelete.bind(this);
        this.moveDocumentTo = this.moveDocumentTo.bind(this);
    }

    onDownload(attachmentId) {
        const {
            tableName,
            entityId,
            onDownloadAttachment
        } = this.props;

        onDownloadAttachment(tableName, entityId, attachmentId);
    }

    onDelete(attachmentId, attachmentUid) {
        const {
            moduleName,
            tableName,
            entityId,
            onDeleteAttachment
        } = this.props;

        if (onDeleteAttachment) {
            onDeleteAttachment(moduleName, tableName, entityId, attachmentId, attachmentUid);
        }
    }

    moveDocumentTo(attachmentId, moveDocumentTo) {
        const {
            moduleName,
            tableName,
            entityId,
            onMoveAttachmentTo
        } = this.props;

        if (onMoveAttachmentTo) {
            onMoveAttachmentTo(moduleName, tableName, entityId, attachmentId, moveDocumentTo);
        }
    }

    render() {
        const {
            className,
            messageAreaClassName,
            description,
            readonly,
            files = [],
            messages = [],
            formatDate,
            uploadButton,
            attachmentsLabels = {},
            isDownloadDisabled,
            loading = false,
            talentProfilePageTransformedEnabled,
            tableName
        } = this.props;

        // Group the attachments by documentType into 'Certification' and 'Other'
        const groupedFiles = files.reduce((acc, file) => {
            const group = file.documentType || attachmentTypes.OTHER;

            if (!acc[group]) acc[group] = [];
            acc[group].push(file);

            return acc;
        }, {
            Certification: [],
            Other: []
        });

        // Helper to render attachment component
        const renderAttachment = ({
            id,
            uid,
            createdOn,
            documentType,
            expiryDate,
            size,
            ...file
        }) => (
            <Attachment
                key={`${id}-${createdOn}`}
                {...file}
                id={id}
                uid={uid}
                date={createdOn}
                readonly={readonly}
                formatDate={formatDate}
                onDelete={this.onDelete}
                onDownload={this.onDownload}
                attachmentsLabels={attachmentsLabels}
                isDownloadDisabled={isDownloadDisabled}
                documentType={documentType}
                moveDocumentTo={this.moveDocumentTo}
                talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled}
                expiryDate={expiryDate}
                size={size}
                tableName={tableName}
            />
        );

        // Group the attachments by documentType into 'Certification' and 'Other'
        const renderAttachmentsSection = (title, files) => (
            <div>
                <h3 className="certification-and-other">{files.length > 0 && `${title}s`}</h3>
                <div className="attachment-certificates-and-others">
                    {files.map(renderAttachment)}
                </div>
            </div>
        );

        return loading ? (
            <Skeleton loading={true} active={true} />
        ) : (
            isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName) ? (
                <>
                    {uploadButton && <div className="attachment-button">{uploadButton}</div>}
                    {groupedFiles.Certification.length === 0 && groupedFiles.Other.length === 0 ? (
                        <Upload.Dragger disabled={true}>
                            <p className="ant-upload-drag-icon"><InboxOutlined /></p>
                            <p className="ant-upload-text">{ATTACHMENT_MESSAGES.NO_ATTACHMENT_UPLOADED}</p>
                        </Upload.Dragger>
                    ) : (
                        <>
                            {renderAttachmentsSection(attachmentTypes.CERTIFICATION, groupedFiles.Certification)}
                            {renderAttachmentsSection(attachmentTypes.OTHER, groupedFiles.Other)}
                        </>
                    )}
                </>
            ) : (
                <div className={className}>
                    {!readonly && description}
                    {!readonly && !!messages.length && (
                        <MessageArea className={messageAreaClassName} messages={messages} />
                    )}
                    {uploadButton}
                    {files.map(renderAttachment)}
                </div>
            )
        );
    }
}

export class Attachment extends Component {
    constructor(props) {
        super(props);

        this.onDownload = this.onDownload.bind(this);
        this.onDelete = this.onDelete.bind(this);
        this.moveDocumentTo = this.moveDocumentTo.bind(this);
    }

    onDownload() {
        this.props.onDownload(this.props.id);
    }

    onDelete() {
        const {
            id,
            uid,
            onDelete
        } = this.props;

        if (onDelete) {
            onDelete(id, uid || id);
        }
    }

    moveDocumentTo() {
        const {
            id,
            uid,
            moveDocumentTo,
            documentType
        } = this.props;
        if (this.moveDocumentTo) {

            // If the document is a certification, move it to 'Other', otherwise move it to 'Certification'
            const moveType = documentType === attachmentTypes.CERTIFICATION ? attachmentTypes.OTHER : attachmentTypes.CERTIFICATION;
            this.props.moveDocumentTo(id, moveType);
        }
    }

    /**
     * Method to get the expiry status based on the attachment's expiry date.
     * @param {Date} expiryDate
     * @returns {string} returns the expiry status tag 'EXPIRED', 'EXPIRING_SOON' or ''.
     */
    getExpiryTag = (expiryDateString) => {
        const expiryDate = new Date(expiryDateString);
        const currentDate = new Date();

        const timeDiff = expiryDate - currentDate;
        const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

        if (daysDiff < 0) {
            return attachmentExpiryStatus.EXPIRED;
        } else if (daysDiff <= DEFAULT_FILE_EXPIRY_DAYS) {
            return attachmentExpiryStatus.EXPIRING_SOON;
        } else {
            return '';
        }
    };

    render() {
        const {
            id,
            readonly,
            formatDate = date => date,
            attachmentsLabels,
            isDownloadDisabled,
            talentProfilePageTransformedEnabled,
            documentType,
            expiryDate,
            size,
            tableName,
            ...props
        } = this.props;

        // Change the move to label based on the document type
        const moveAttachmentTo = ATTACHMENT_MESSAGES.CHANGE_TYPE(
            documentType === attachmentTypes.CERTIFICATION ? attachmentTypes.OTHER : `${attachmentTypes.CERTIFICATION}s`
        );

        const { deleteButtonLabel = ATTACHMENT_LABELS.DELETE_ATTACHMENT_BUTTON, downloadAttachmentLabel = ATTACHMENT_LABELS.DOWNLOAD_ATTACHMENT_BUTTON } = attachmentsLabels;

        // Get the expiry status
        const expiryStatus = expiryDate && this.getExpiryTag(expiryDate);

        // Check if the feature flag is enabled and we are on the profile page
        const isFeatureFlagEnabled = isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName);

        // Check if downloading is disabled for this attachment
        const downloadDisabled = isDownloadDisabled(id);

        // Build action array based on the feature flag.
        // If it is enabled then include 'Download', 'Move to 'Certification'', 'Move to 'Other'', 'Delete'
        // Else just 'Download'
        let actions = [];
        if (!readonly) {
            if (isFeatureFlagEnabled) {
                // Push the download and move to other functionality only when the file is saved.
                if (!downloadDisabled) {
                    actions.push(
                        {
                            key: 'download',
                            label: (<><Icon type="download" /> {downloadAttachmentLabel}</>),
                            onClick: this.onDownload
                        },
                        {
                            key: 'moveTo',
                            className: 'move-to',
                            label: moveAttachmentTo,
                            onClick: this.moveDocumentTo
                        }
                    );
                }
                actions.push(
                    {
                        key: 'divider'
                    },
                    {
                        key: 'delete',
                        className: 'actions-menu-item-delete',
                        label: (<><Icon type="delete" className="delete-icon" /> {deleteButtonLabel}</>),
                        onClick: this.onDelete
                    }
                );
            } else {
                actions.push({
                    key: 'delete',
                    className: 'actions-menu-item-delete-legacy',
                    label: deleteButtonLabel,
                    onClick: this.onDelete
                });
            }
        }

        // Attachment content
        const attachmentContent = (
            <AttachmentContent
                {...props}
                formatDate={formatDate}
                onDownload={this.onDownload}
                talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled}
                expiryDate={expiryDate}
                size={size}
                downloadDisabled={downloadDisabled}
                tableName={tableName}
            />
        );

        // Define props based on talent profile page transformed feature flag
        const actionsMenuProps = talentProfilePageTransformedEnabled
            ? {
                placement: 'bottomRight',
                iconClassName: 'actions-menu',
                useArrow: true
            }
            : {
                placement: 'bottomLeft',
                iconClassName: 'actions-menu-legacy',
                useArrow: false
            };

        // Pass props to the action menu
        const actionsMenu = !readonly ? (
            <ActionsMenu
                {...actionsMenuProps}
                actions={actions}
            />
        ) : null;

        return isFeatureFlagEnabled ? (
            <div className="attachment">
                <div className="attachment-expiry-tag-and-action-menu">
                    {expiryStatus && (
                        <span
                            style={{
                                color: expiryStatus === attachmentExpiryStatus.EXPIRED ? 'red' : 'orange'
                            }}
                        >
                            {expiryStatus}
                        </span>
                    )}

                    {!expiryStatus && <FileFilled className="document-icon" />}

                    {actionsMenu}
                </div>

                {attachmentContent}
            </div>
        ) : (
            <div className="attachment-legacy">
                {attachmentContent}
                {actionsMenu}
            </div>
        );
    }
}

const AttachmentContent = ({
    date,
    name,
    downloadDisabled,
    formatDate,
    onDownload,
    talentProfilePageTransformedEnabled,
    expiryDate,
    size,
    tableName
}) => {
    // Truncate text after 15 characters
    const truncatedText = getTruncatedText(name, ATTACHMENT_TRUNCATION_LENGTH);

    // Tooltip content which includes attachment 'name' and uploaded date.
    const tooltipContent = (
        <div className="attachment-tooltip-content">
            <div>{name}</div>
            <div>Uploaded {formatLocalDate(date, getDisplayLongDateTimeFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR))} </div>
        </div>
    );

    return (
        isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName) ? (
            <div className="attachment-hover-container">
                <Tooltip title={tooltipContent} color="white" placement="top">
                    <a className="attachment-link" onClick={onDownload}>{truncatedText}</a>
                    <div>
                        {expiryDate && (
                            <>
                                {/* Formats the date in local date */}
                                E: {formatLocalDate(expiryDate, getDisplayLongDateTimeFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR, null))}{' '}
                                <span className="icon-bullet"> &bull; </span>{' '}
                            </>
                        )}
                        {size && `${Math.ceil(convertBytesToMB(size))} MB`}
                    </div>
                </Tooltip>
            </div>
        ) : (
            <>
                <div className="attachment-date-legacy">
                    {formatDate(parseUtcToLocalDate(parseToUtcDate(date)))}
                </div>
                {downloadDisabled
                    ? <div className="attachment-link-disabled">{name}</div>
                    : <a className="attachment-link-legacy" onClick={onDownload}>{name}</a>}
            </>
        )
    );
};

AttachmentsSection.propTypes = {
    moduleName: string,
    tableName: string,
    entityId: string,
    description: any,
    readonly: bool,
    files: array,
    messages: array,
    messageAreaClassName: string,
    uploadButton: any,
    formatDate: func,
    onDeleteAttachment: func,
    onDownloadAttachment: func.isRequired,
    isDownloadDisabled: func.isRequired,
    attachmentsLabels: object,
    talentProfilePageTransformedEnabled: bool,
    documentType: string,
    onMoveAttachmentTo: func.isRequired,
    className: string,
    loading: bool
};

Attachment.propTypes = {
    id: string.isRequired,
    name: string.isRequired,
    date: any.isRequired,
    readonly: bool,
    attachmentsLabels: object,
    formatDate: func,
    onDelete: func,
    onDownload: func.isRequired,
    isDownloadDisabled: func.isRequired,
    documentType: string,
    talentProfilePageTransformedEnabled: bool,
    moveDocumentTo: func.isRequired,
    expiryDate: PropTypes.date,
    size: PropTypes.number,
    tableName: string
};

AttachmentContent.propTypes = {
    name: string.isRequired,
    date: any.isRequired,
    downloadDisabled: bool,
    formatDate: func,
    onDownload: func.isRequired,
    talentProfilePageTransformedEnabled: bool,
    expiryDate: PropTypes.date,
    size: PropTypes.number,
    tableName: string
};