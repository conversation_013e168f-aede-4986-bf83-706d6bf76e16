import React from 'react';
import { array, string } from 'prop-types';
import { Menu, Dropdown } from 'antd';
import Icon from '../icon';

export const ActionsMenu = ({
    actions = [],
    iconClassName,
    placement = 'bottomRight',
    useArrow = false
}) => {
    const menu = (
        <Menu>
            {actions.map((action) => {
                // If the key is 'divider' then return divider
                if (action.key === 'divider') {
                    return <Menu.Divider key="divider" />;
                }

                const {
                    key,
                    className,
                    disabled = false,
                    label,
                    onClick
                } = action;

                return (
                    <Menu.Item
                        key={key}
                        className={className}
                        disabled={disabled}
                        onClick={onClick}
                    >
                        {label}
                    </Menu.Item>
                );
            })}
        </Menu>
    );

    return (
        <Dropdown
            placement={placement}
            overlay={menu}
            trigger={['click']}
            arrow={useArrow}
        >
            <Icon type="ellipsis" className={iconClassName} />
        </Dropdown>
    );
};

ActionsMenu.propTypes = {
    actions: array.isRequired,
    iconClassName: string,
    placement: string,
    useArrow: Boolean
};