import React, { useState, useRef } from 'react';
import { Form, message, Radio, Upload } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import AntdModal from '../antdModal';
import DatePickerFieldControl from '../datePickerControl';
import { ATTACHMENT_MESSAGES, attachmentTypes, DEFAULT_MAX_FILE_SIZE_MB } from '../../constants/attachmentsConsts';
import PropTypes from 'prop-types';
import '../attachments/styles.less';
import { validateFileSize } from '../../utils/attachments';

/**
 * The component is used to show the attachment modal
 * @param {{ uploadModeProps: Object; headerProps: Object; footerProps: Object; onSubmit: function; trigger: function; }} param0
 * @param {Object} param0.uploadModeProps Defines the body props of the modal pop-up
 * @param {Object} param0.headerProps Defines header props which includes 'title'
 * @param {Object} param0.footerProps Footer props of the modal which includes 'Cancel' and 'Upload' button
 * @param {function} param0.onSubmit Callback function which is called on click of 'Upload'
 * @param {function} param0.trigger Callback function which is used to open the modal pop-up.
 * @returns
 */
export const UploadFilesModal = ({
    uploadModeProps,
    headerProps,
    footerProps,
    onSubmit,
    trigger
}) => {
    const [open, setOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState(null);
    const formRef = useRef(null);

    const {
        allowedFileTypes,
        uploadAreaText,
        documentType,
        expiryDate,
        uploadAreaHint,
        maxFileSize,
        customProps
    } = uploadModeProps;

    const { title } = headerProps;
    const { cancelBtnTitle, uploadBtnTitle } = footerProps;

    /**
     * Validates the file before upload.
     * @param {*} file
     */
    const handleBeforeUpload = (file) => {
        const isValid = validateFileSize(file.size, DEFAULT_MAX_FILE_SIZE_MB);
        if (!isValid) {
            // Show an error message if the file size exceeds the limit
            message.error(ATTACHMENT_MESSAGES.FILE_TOO_LARGE);

            // IF the file size is not valid, we return LIST_IGNORE to prevent the file from being added to the list
            return Upload.LIST_IGNORE;
        }

        // Set the state with the selected file
        setSelectedFile(file);

        return false;
    };

    /** The method is used to handle the upload confirmation */
    const handleOk = () => {
        if (!selectedFile) return;
        formRef.current
            .validateFields()
            .then((values) => {
                const formData = {
                    ...values,
                    expiryDate: values.expiryDate ?? '',
                    file: selectedFile
                };
                onSubmit?.({ file: formData });
                handleClose();
            });
    };

    /** The method is used to close the upload modal */
    const handleClose = () => {
        setOpen(false);
        setSelectedFile(null);
    };

    /** The method is used to handle the remove file functionality */
    const handleRemove = () => {
        setSelectedFile(null);
    };

    return (
        <>
            <span onClick={() => setOpen(true)}>
                {trigger}
            </span>

            <AntdModal
                open={open}
                onCancel={handleClose}
                onOk={handleOk}
                title={title}
                okText={uploadBtnTitle}
                cancelText={cancelBtnTitle}
                okButtonProps={{ disabled: !selectedFile }}
                cancelButtonProps={{ className: 'ant-btn-tertiary' }}
            >
                <p>{ATTACHMENT_MESSAGES.MAX_UPLOAD_ALLOWED_NEW}</p>
                <Form
                    ref={formRef}
                    layout="horizontal"
                    initialValues={{
                        documentType: attachmentTypes.CERTIFICATION
                    }}
                >
                    <Form.Item
                        label={documentType}
                        name="documentType"
                    >
                        <Radio.Group className="document-type-radio-button">
                            <Radio value={attachmentTypes.CERTIFICATION}>{attachmentTypes.CERTIFICATION}</Radio>
                            <Radio value={attachmentTypes.OTHER}>{attachmentTypes.OTHER}</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <Form.Item
                        label={expiryDate}
                        name="expiryDate"
                        style={{ marginLeft: '47px' }}
                        rules={[]}
                    >
                        <DatePickerFieldControl customProps={customProps} />
                    </Form.Item>
                </Form>

                <Upload.Dragger
                    accept={allowedFileTypes}
                    beforeUpload={handleBeforeUpload}
                    showUploadList={
                        selectedFile ? { showRemoveIcon: true, showDownloadIcon: false } : false
                    }
                    fileList={
                        selectedFile ? [{ uid: '-1', name: selectedFile.name, status: 'done' }] : []
                    }
                    customRequest={() => null}
                    onRemove={handleRemove}
                >
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">{uploadAreaText}</p>
                    <p className="ant-upload-hint">
                        {uploadAreaHint}
                        <br />
                        {maxFileSize}
                    </p>
                </Upload.Dragger>
            </AntdModal>
        </>
    );
};

UploadFilesModal.propTypes = {
    uploadModeProps: PropTypes.object.isRequired,
    headerProps: PropTypes.object.isRequired,
    footerProps: PropTypes.object.isRequired,
    onSubmit: PropTypes.func.isRequired,
    trigger: PropTypes.node.isRequired
};
