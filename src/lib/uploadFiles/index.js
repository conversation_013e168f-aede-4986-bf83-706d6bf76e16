import React, { Component } from 'react';
import { Upload, Button } from 'antd';
import PropTypes, { string, bool, func, array, number } from 'prop-types';
import Icon from '../icon';
import { DATE_TIME_UNITS, getStartOfDay } from '../../utils/dateUtils';
import { isBefore } from 'date-fns';
import { UploadFilesModal } from './uploadFilesModal';
import { isTalentProfilePageTransformedAndIsResourceTable } from '../../utils/attachments';
import { UploadOutlined } from '@ant-design/icons';

export class UploadFiles extends Component {
    constructor(props) {
        super(props);

        this.validateFile = this.validateFile.bind(this);
        this.onSuccess = this.onSuccess.bind(this);
    }

    validateFile({ name, size, url }) {
        const {
            moduleName,
            tableName,
            entityId,
            validateFile,
            onUploadFail
        } = this.props;

        const isValid = validateFile(size, name);

        if (!isValid) {
            onUploadFail(moduleName, tableName, entityId, { name, size, url });
        }

        // eslint-disable-next-line no-undef
        return isValid || Promise.reject();
    }

    onSuccess({ file }) {
        const {
            moduleName,
            tableName,
            entityId,
            onUpload,
            talentProfilePageTransformedEnabled
        } = this.props;

        onUpload(moduleName, tableName, entityId, file, talentProfilePageTransformedEnabled);
    }

    render() {
        const {
            uploadButtonLabel,
            uploadButtonDisabled,
            className,
            allowedFileTypes = [],
            uploadButtonType,
            headerProps,
            footerProps,
            uploadModeProps,
            talentProfilePageTransformedEnabled,
            tableName
        } = this.props;

        // Conditionally pass the props based on the talent profile page feature flag
        const conditionalUploadProps = isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName)
            ? {
                headerProps,
                footerProps,
                uploadModeProps
            }
            : {
                allowedFileTypes: allowedFileTypes.join(',')
            };

        return (
            <>
                <UploadFilesButton
                    className={className}
                    label={uploadButtonLabel}
                    disabled={uploadButtonDisabled}
                    type={uploadButtonType}
                    validateFile={this.validateFile}
                    onSuccess={this.onSuccess}
                    talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled}
                    tableName={tableName}
                    {...conditionalUploadProps}
                />
            </>
        );
    }
}

export class UploadFilesButton extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        const {
            label,
            className,
            allowedFileTypes,
            disabled = false,
            validateFile,
            onSuccess,
            type,
            headerProps,
            footerProps,
            uploadModeProps,
            talentProfilePageTransformedEnabled,
            tableName
        } = this.props;

        const customProps = { shouldDisableDates: (currentDate) => isBefore(currentDate.utc(true), getStartOfDay().utc(true), DATE_TIME_UNITS.DAY) };

        return (
            <>{isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName) ? (<UploadFilesModal
                headerProps={headerProps}
                footerProps={footerProps}
                uploadModeProps={{ ...uploadModeProps, customProps }}
                onSubmit={onSuccess}
                trigger={
                    <Button
                        className={className}
                        disabled={disabled}
                        type={type}
                    >
                        <UploadOutlined />
                        {label}
                    </Button>
                }
            />) : <Upload
                accept={allowedFileTypes}
                showUploadList={false}
                beforeUpload={validateFile}
                customRequest={onSuccess}
            >
                <Button className={className} disabled={disabled} type={type} >
                    <Icon type="paper-clip" />
                    {label}
                </Button>
            </Upload>
            }</>
        );
    }
}

UploadFiles.propTypes = {
    className: string,
    uploadButtonDisabled: bool,
    uploadButtonLabel: string,
    uploadButtonType: string,
    allowedFileTypes: array,
    maxFileSizeMB: number,
    moduleName: string,
    tableName: string.isRequired,
    entityId: string,
    validateFile: func.isRequired,
    onUpload: func.isRequired,
    onUploadFail: func.isRequired,
    headerProps: PropTypes.object,
    footerProps: PropTypes.object,
    uploadModeProps: PropTypes.object,
    talentProfilePageTransformedEnabled: bool
};

UploadFilesButton.propTypes = {
    label: string.isRequired,
    className: string,
    disabled: bool,
    type: string,
    allowedFileTypes: string,
    maxFileSizeMB: number,
    url: string,
    validateFile: func.isRequired,
    onSuccess: func.isRequired,
    uploadModeProps: PropTypes.object,
    headerProps: PropTypes.object,
    footerProps: PropTypes.object,
    talentProfilePageTransformedEnabled: bool,
    tableName: string
};