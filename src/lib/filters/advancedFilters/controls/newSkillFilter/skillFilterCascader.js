import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Cascader, Tag, Tooltip, Button, Space, Empty } from 'antd';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'react-intl';
import { FILTER_ALIAS_BY_PAGE_NAME, SKILL_FILTER_CASCADER_CHILD_TYPES } from '../../../../../constants/filterConsts';
import { TABLE_NAMES, MAX_LABEL_LENGTH } from '../../../../../constants';
import { CRITERIA_SKILLS_OPERATOR } from '../../../../../constants/pickerConsts';
import './filterCascader.less';
import { EDIT_RESOURCE_SKILLS_WINDOW } from '../../../../../actions/actionTypes';
import { getSkillsStaticMessages } from '../../../../../selectors/editSkillsWindowSelectors';
import { useSelector } from 'react-redux';

const { CheckableTag } = Tag;

const { SHOW_CHILD } = Cascader;

/**
 * SkillsFilterCascader component for selecting skills and their levels.
 *
 * @param {object} props - The component props.
 * @param {object} props.skillsData - The skills data.
 * @param {boolean} props.showButtons - Whether to show apply and cancel buttons.
 * @param {number} props.maxSelection - Maximum number of selections allowed.
 * @param {string} props.filterKey - The filter key.
 * @param {string} props.filtersGuid - The filters GUID.
 * @param {string} props.pageAlias - The page alias.
 * @param {string} props.className - The class name.
 * @param {function} props.changeFilterValue - Function to change filter value.
 * @param {function} props.onSelectionChange - Function to handle selection change.
 * @param {function} props.onDropdownButtonClick - Function to handle dropdown button click.
 * @returns {JSX.Element} The SkillsFilterCascader component.
 */
const SkillsFilterCascader = ({
    getSkillEntityTypes = () => [],
    skillsData = {},
    showButtons = false,
    filterKey,
    filtersGuid,
    pageAlias,
    className = '',
    changeFilterValue = () => { },
    maxSelection: maxSkillSelection = Number.MAX_SAFE_INTEGER,
    onSelectionChange = () => { },
    onFilterApply = () => { },
    onFilterValueChange = () => { },
    selectedSkillsOptions = [],
    selectedValues = [],
    isDropdownChild = SKILL_FILTER_CASCADER_CHILD_TYPES.DEFAULT,
    onDropdownVisibleChange = () => { },
    cascaderOpenState }) => {

    const [options, setOptions] = useState([]);
    const [internalSelectedOptions, setInternalSelectedOptions] = useState([]);
    const [internalValues, setInternalValues] = useState([]);
    const [filterType, setFilterType] = useState('all');
    const [entityTypes, setEntityTypes] = useState([]);
    const isInitialLoad = useRef(true);
    const [getCascaderOpenState, setCascaderOpenState] = useState(false);

    // Check if the skillFilterCascader is opened from edit skills(talent profile page)
    const isAddSkillsMode = isDropdownChild === EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS;

    /**
     * Updates the internal open state of the cascader only if the cascader is opened from edit skills(talent profile page).
     * Prevents unintended visibility control from parent components in other modes.
     * @param {Boolean} visible for setting the cascader visible state
     */
    const handleDropdownVisibleChange = (visible) => {
        if (isAddSkillsMode) {
            setCascaderOpenState(visible);
        }
    };

    useEffect(() => {
        const types = getSkillEntityTypes();
        const sortedTypes = types.sort((a, b) => a.order - b.order);
        setEntityTypes(sortedTypes);
    }, [getSkillEntityTypes]);

    const filterDataByType = useCallback((data, type) => {
        if (type === 'all' || entityTypes.length === 0) {
            return data;
        }

        const entityType = entityTypes.find(et => et.id === type);
        if (!entityType) {
            return data; // Fallback to all data if entity type not found
        }

        const filteredData = {};
        Object.keys(data).forEach(categoryKey => {
            const category = data[categoryKey];
            const filteredSkills = (category.skills || []).filter(skill =>
                skill.entityTypeId != null && skill.entityTypeId === type);

            if (filteredSkills.length > 0) {
                filteredData[categoryKey] = {
                    ...category,
                    skills: filteredSkills
                };
            }
        });

        return filteredData;
    }, [entityTypes]);

    /**
   * Truncates text if it exceeds the max length
   * @param {string} text - The text to truncate
   * @returns {string} Truncated text with ellipsis or original text
   */
    const truncateText = (text) => {
        if (!text) return '';
        if (text.length <= MAX_LABEL_LENGTH) return text;

        return `${text.substring(0, MAX_LABEL_LENGTH)}...`;
    };

    const sectionIdToNameMap = React.useMemo(() => {
        const map = new Map();
        Object.values(skillsData || {}).forEach(category => {
            if (category.id && category.name) {
                const trimmedName = category.name.trim();
                map.set(category.id, trimmedName);
            }
        });

        return map;
    }, [skillsData]);

    /**
     * Transforms the initial data for categories and subcategories.
     *
     * @param {object} data - The skills data.
     * @returns {Array} The transformed initial data.
     */
    const transformInitialData = useCallback((data) => {
        if (!data || Object.keys(data).length === 0) {
            return [];
        }

        const sortAlphabetically = (arr, key) => arr.sort((a, b) => a[key].localeCompare(b[key]));
        const sortedCategories = sortAlphabetically(Object.values(data), 'name');

        return sortedCategories
            .filter(category => category.skills && category.skills.length > 0) // Ensure category has skills
            .map(category => {
                // Map subcategories and their skills
                const subCategories = (category.subCategories || [])
                    .map(sub => {
                        // Filter skills for this subcategory
                        const skillsForSubCategory = sortAlphabetically(category.skills || [], 'name')
                            .filter(skill => skill.subCategories.includes(sub.subCategoryDescription))
                            .map(skill => {
                                const hasLevels = category.skillLevels && category.skillLevels.length > 0;

                                return {
                                    value: skill.key,
                                    label: truncateText(skill.name),
                                    fullLabel: skill.name,
                                    parentValue: sub.subCategoryId,
                                    parentLabel: sub.subCategoryDescription,
                                    isLeaf:
                                        isDropdownChild ===
                                            EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS
                                            ? true
                                            : !hasLevels,
                                    ...(isDropdownChild !==
                                        EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS &&
                                        hasLevels && {
                                        children:
                                            category.skillLevels.map(
                                                (level) => ({
                                                    value: `${level.value}`,
                                                    label: truncateText(
                                                        level.skillLevelDescription
                                                    ),
                                                    fullLabel:
                                                        level.skillLevelDescription,
                                                    guid: level.guid,
                                                    parentValue:
                                                        skill.key,
                                                    parentLabel:
                                                        skill.name,
                                                    isLeaf: true
                                                })
                                            )
                                    })
                                };
                            });

                        // Only return the subcategory if it has skills
                        if (skillsForSubCategory.length === 0) {
                            return null; // Skip empty subcategories
                        }

                        return {
                            value: sub.subCategoryId,
                            label: truncateText(sub.subCategoryDescription),
                            disableCheckbox: true,
                            fullLabel: sub.subCategoryDescription,
                            parentValue: category.id,
                            parentLabel: category.name,
                            isLeaf: false,
                            children: skillsForSubCategory
                        };
                    })
                    .filter(sub => sub !== null); // Remove null entries (empty subcategories)

                // Only return the category if it has subcategories with skills
                if (subCategories.length === 0) {
                    return null; // Skip categories with no valid subcategories
                }

                return {
                    value: category.id,
                    label: truncateText(category.name),
                    disableCheckbox: true,
                    fullLabel: category.name,
                    skillLevels: category.skillLevels,
                    children: subCategories
                };
            })
            .filter(category => category !== null); // Remove null entries (empty categories)
    }, [maxSkillSelection]);

    useEffect(() => {
        if (!internalSelectedOptions.length || !internalValues.length) {
            setInternalSelectedOptions(selectedSkillsOptions || []);
            setInternalValues(selectedValues || []);
        }
    }, [selectedSkillsOptions, selectedValues]);

    // Set options based on skillsData and filterType
    useEffect(() => {
        const filteredData = filterDataByType(skillsData, filterType);
        const initialOptions = transformInitialData(filteredData);
        setOptions(initialOptions);
        // Clear selections only when filterType changes and not on initial load
        if (!isInitialLoad.current && filterType !== 'all') { // Exclude 'all' if initial state
            setInternalValues([]);
            setInternalSelectedOptions([]);
        }
        isInitialLoad.current = false;
    }, [skillsData, filterType, filterDataByType, transformInitialData, isDropdownChild]);

    /**
    * Handles the change event for the Cascader component.
    * @param {Array} value - The selected values.
    * @param {Array} selectedOptions - The selected options.
    */
    const onChange = (value, selectedOptions) => {

        const distinctSkillsCount = new Set(value.map(value => value[2]).filter(skillId => skillId != null && typeof skillId === 'string')).size;
        if (distinctSkillsCount > maxSkillSelection + 1) {
            return;
        }

        setInternalValues(value);
        setInternalSelectedOptions(selectedOptions);

        if (isDropdownChild !== SKILL_FILTER_CASCADER_CHILD_TYPES.ROLE_REQUIREMENTS) {
            const selectedSkillFilterValues = transformSelectedOptions(selectedOptions);
            changeFilterValue(FILTER_ALIAS_BY_PAGE_NAME[pageAlias], filtersGuid, TABLE_NAMES.RESOURCE, filterKey, selectedSkillFilterValues, selectedOptions, value);
            onFilterValueChange(filterKey, selectedSkillFilterValues);
        }
    };

    /**
    * Transforms the selected options to the required format.
    * @param {Array} selectedOptions - The selected options.
    * @returns {object} The transformed selected options.
    */
    function transformSelectedOptions(selectedOptions) {
        // Initialize parameters object to store transformed data
        const parameters = {};

        if (selectedOptions && selectedOptions.length > 0) {
            selectedOptions.forEach(selection => {
                // Ensure we have at least the category level
                if (selection.length < 2) return;

                // Get the top-level category GUID (first element)
                const categoryGuid = selection[0].value;

                // Ensure the category exists in our parameters
                if (!parameters[categoryGuid]) {
                    parameters[categoryGuid] = [];
                }

                const skillGuid = selection[2].value;

                // We need at least 4 levels: category, subcategory, skill, skill level
                if (selection.length >= 4) {
                    // Case when levels exist
                    const skillLevel = selection[3];

                    // Check if this skill is already processed for this category
                    const existingSkillEntry = parameters[categoryGuid].find(
                        entry => entry.skillGuid === skillGuid
                    );

                    if (existingSkillEntry) {
                        // Add the level if not already present
                        if (!existingSkillEntry.levels.includes(Number(skillLevel.value))) {
                            existingSkillEntry.levels.push(Number(skillLevel.value));
                            // Sort levels
                            existingSkillEntry.levels.sort((a, b) => a - b);
                        }
                    } else {
                        // Create a new skill entry
                        parameters[categoryGuid].push({
                            skillGuid,
                            levels: [Number(skillLevel.value)]
                        });
                    }
                } else {
                    // Case when no levels exist - just add the skillGuid
                    const existingSkillEntry = parameters[categoryGuid].find(
                        entry => entry.skillGuid === skillGuid
                    );

                    if (!existingSkillEntry) {
                        parameters[categoryGuid].push({ skillGuid });
                    }
                }
            });
        }

        return { calcValue: 1, parameters };
    }

    /**
    * Finds the option path for a given value.
    *
    * @param {string} value - The value to find the path for.
    * @param {Array} options - The options to search within.
    * @returns {Array|null} The option path or null if not found.
    */
    const findOptionPath = useCallback((targetValue, optionsToSearch) => {
        // Split the target value into individual values using the Cascader separator
        const targetValues = targetValue.split('__RC_CASCADER_SPLIT__');

        // Helper function to recursively search through options
        const searchInOptions = (opts, targets, currentPath = []) => {
            // If we've found all target values, return the path
            if (targets.length === 0) {
                return currentPath;
            }

            const currentTarget = targets[0];

            for (let i = 0; i < opts.length; i++) {
                const currentOption = opts[i];

                // Check if current option matches the current target
                if (currentOption.value === currentTarget) {
                    const newPath = [...currentPath, currentOption];

                    // If this is the last target, return the path
                    if (targets.length === 1) {
                        return newPath;
                    }

                    // If there are more targets and we have children, search in children
                    if (currentOption.children && currentOption.children.length > 0) {
                        const foundInChildren = searchInOptions(
                            currentOption.children,
                            targets.slice(1),
                            newPath
                        );
                        if (foundInChildren.length > 0) {
                            return foundInChildren;
                        }
                    }
                }

                // If current option has children but didn't match, search in children
                if (currentOption.children && currentOption.children.length > 0) {
                    const foundInChildren = searchInOptions(
                        currentOption.children,
                        targets,
                        currentPath
                    );
                    if (foundInChildren.length > 0) {
                        return foundInChildren;
                    }
                }
            }

            return [];
        };

        return searchInOptions(optionsToSearch, targetValues);
    }, []);

    // get display label for the tag
    const getDisplayLabel = useCallback((path) => {
        if (!path || path.length === 0) return '';

        // For a 4-level cascader (category/subcategory/skill/level)
        // We only want to show: skill - level
        if (path.length >= 4) {
            // path[2] is the skill (3rd level)
            // path[3] is the level (4th level)
            // Use fullLabel if available, otherwise fallback to label
            const skillLabel = path[2].fullLabel || path[2].label;
            const levelLabel = path[3].fullLabel || path[3].label;

            return `${skillLabel} - ${levelLabel}`;
        } else if (path.length === 3) {
            // If only skill is selected without level
            return path[2].fullLabel || path[2].label;
        }

        // Fallback if path is incomplete
        const lastNode = path[path.length - 1];

        return lastNode.fullLabel || lastNode.label;
    }, []);

    // get full path label for tooltip
    const getFullPathLabel = useCallback((path) => {
        if (!path || path.length === 0) return '';

        return path.map(item => item.fullLabel || item.label || '').filter(Boolean).join(' /\n');
    }, []);

    /**
    * Renders the tag for the selected option.
    * @param {object} tagProps - The tag properties.
    * @param {string} tagProps.label - The label.
    * @param {string} tagProps.value - The value.
    * @param {boolean} tagProps.closable - Whether the tag is closable.
    * @param {function} tagProps.onClose - The function to call on tag close.
    * @returns {JSX.Element|null} The rendered tag or null.
    */
    const tagRender = ({ label, value, closable, onClose }) => {
        if (!value) return null;

        const path = findOptionPath(value, options);

        if (!path || path.length === 0) {
            return null;
        }

        const displayLabel = getDisplayLabel(path);
        const truncatedLabel = truncateText(displayLabel);
        const fullPathLabel = getFullPathLabel(path);

        return (
            <Tooltip title={fullPathLabel} placement="top" overlayStyle={{ whiteSpace: 'pre-line' }}>
                <Tag
                    closable={closable}
                    onClose={onClose}
                    className="mr-1"
                >
                    {truncatedLabel}
                </Tag>
            </Tooltip>
        );
    };

    // Handles the click event for the apply button
    const onApplyClick = () => {
        const skillMap = new Map();

        const findSectionAndSkillName = (sectionId, skillId) => {
            const sectionName = sectionIdToNameMap.get(sectionId);
            const category = skillsData[sectionName];

            let skillName = 'Unknown Skill';
            if (category.skills && Array.isArray(category.skills)) {
                const skill = category.skills.find(skill => skill.key === skillId);
                skillName = skill ? skill.name : 'Unknown Skill';
            }

            const skillLevels = category?.skillLevels || [];

            return { sectionName, skillName, skillLevels };
        };

        // Aggregate all selected levels per skill
        internalValues.forEach(value => {
            const sectionId = value[0];
            const skillId = value[2];
            const skillLevelOptions = value.slice(3);

            if (skillId == null) {
                return;
            }

            const { sectionName, skillName, skillLevels } = findSectionAndSkillName(sectionId, skillId);

            // Initialize skill entry if not exists
            if (!skillMap.has(skillId)) {
                skillMap.set(skillId, {
                    sectionName: sectionName,
                    sectionId,
                    skillId,
                    skillsData: {
                        id: skillId,
                        name: skillName,
                        sectionId
                    },
                    skillLevels: new Set(),
                    hasSkillLevels: false,
                    operator: CRITERIA_SKILLS_OPERATOR.ONE_OF
                });
            }

            // TODO: remove prop selectedskillsOptions and use selectedValues instead
            // and construct selectedSkillsOptions from selectedValues

            // Collect all unique level GUIDs
            skillLevelOptions.forEach(level => {
                if (isGuid(level)) {
                    // If level is a GUID, add it directly
                    skillMap.get(skillId).skillLevels.add(level);
                } else {
                    // If level is a number, convert it to GUID using getGuidByValue
                    const guid = getGuidByValue(skillLevels, level);
                    if (guid) {
                        skillMap.get(skillId).skillLevels.add(guid);
                    }
                }
            });
        });

        // Determine operator and finalize skillLevels
        Array.from(skillMap.values()).forEach(skill => {
            const category = Object.values(skillsData).find(cat => cat.id === skill.sectionId);
            const skillLevelsData = category?.skillLevels || [];
            const totalLevels = category?.skillLevels?.length || 0;
            const selectedLevels = Array.from(skill.skillLevels);

            if (totalLevels === 0) {
                skill.operator = CRITERIA_SKILLS_OPERATOR.ANY;
                skill.skillLevels = undefined;
                skill.hasSkillLevels = false;
            } else {
                const isAllLevelsSelected = totalLevels > 0 &&
                    selectedLevels.length === totalLevels &&
                    skillLevelsData.every(l => selectedLevels.includes(l.guid));
                const noLevelsSelected = selectedLevels.length === 0;
                const treatAsAny = noLevelsSelected || isAllLevelsSelected;
                skill.operator = treatAsAny
                    ? CRITERIA_SKILLS_OPERATOR.ANY
                    : CRITERIA_SKILLS_OPERATOR.ONE_OF;

                skill.skillLevels = treatAsAny ? undefined : selectedLevels;
                skill.hasSkillLevels = true;
            }
        });

        const selectedData = Array.from(skillMap.values());
        onSelectionChange(
            selectedData,
            internalSelectedOptions,
            internalValues
        );

        if (isAddSkillsMode) {
            setInternalValues([]);
            setCascaderOpenState(false);
        }
        onFilterApply(filterKey);
    };

    function getGuidByValue(skillLevels, targetValue) {
        if (!Array.isArray(skillLevels)) return null;

        const level = skillLevels.find(level => level.value === Number(targetValue));

        return level?.guid || null;
    }

    const isGuid = (value) => {
        const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

        return typeof value === 'string' && guidRegex.test(value);
    };

    /**
    * Renders the display text for selected items in the cascader dropdown
    * @param {string[]} labels - Array of selected label texts
    * @param {Option[]} selectedOptions - Array of selected option objects
    * @returns {JSX.Element} Tooltip wrapped display text
    */
    const displayRender = (labels, selectedOptions) => {
        if (!selectedOptions || selectedOptions.length === 0) {
            return labels.join(' - ');
        }

        const fullLabels = selectedOptions.map(option => {
            if (!option || typeof option !== 'object') {
                return '';
            }

            return option.fullLabel || option.label || '';
        }).filter(Boolean); // Remove empty strings

        if (fullLabels.length > 2) {
            const displayText = fullLabels.slice(-2).join(' - ');

            return truncateText(displayText);
        }

        return truncateText(fullLabels.join(' - '));
    };

    /**
    * Renders the dropdown menu.
    *
    * @param {JSX.Element} menus - The dropdown menus.
    * @returns {JSX.Element} The rendered dropdown menu.
    */
    const dropdownRender = (menus) => {
        const distinctSkillsCount = new Set(internalValues.map(value => value[2]).filter(skillId => skillId != null && typeof skillId === 'string')).size;
        const isNoSelection = internalValues.length === 0;
        const isMaxSelectionExceeded = distinctSkillsCount > maxSkillSelection;
        const isApplyDisabled = isNoSelection || isMaxSelectionExceeded;
        const isResetDisabled = isNoSelection;

        return (
            <div style={{ display: 'flex', flexDirection: 'column' }}>
                {/* Header: Always show skill types */}
                <div style={{ padding: '8px', borderBottom: '1px solid #e8e8e8' }}>
                    <Space>
                        <CheckableTag
                            checked={filterType === 'all'}
                            onChange={() => setFilterType('all')}
                        >
                            All
                        </CheckableTag>
                        {entityTypes.map(entityType => (
                            <CheckableTag
                                key={entityType.id}
                                checked={filterType === entityType.id}
                                onChange={() => setFilterType(entityType.id)}
                            >
                                {entityType.description}
                            </CheckableTag>
                        ))}
                    </Space>
                </div>
                {/* Body: Show the menu */}
                <div style={{ padding: '8px 0' }}>
                    {menus}
                </div>
                {/* Footer: Conditionally show Accept and Ignore buttons */}
                {showButtons && (
                    <div style={{
                        padding: '8px',
                        borderTop: '1px solid #e8e8e8',
                        display: 'flex',
                        justifyContent: 'flex-end'
                    }}>
                        <Space>
                            <Tooltip title={isResetDisabled ? <FormattedMessage id="skillFilterResetButtonDisabledMessage" /> : ''}>
                                <Button
                                    size="small"
                                    onClick={() => {
                                        setInternalValues([]);
                                        setInternalSelectedOptions([]);
                                        if (isDropdownChild === SKILL_FILTER_CASCADER_CHILD_TYPES.ROLE_REQUIREMENTS) {
                                            onSelectionChange([], [], []);
                                            onFilterApply(filterKey);
                                            onDropdownVisibleChange(false);
                                        }
                                    }}
                                    disabled={isResetDisabled}
                                >
                                    Reset
                                </Button>
                            </Tooltip>
                            <Tooltip
                                title={
                                    isApplyDisabled
                                        ? isNoSelection
                                            ? <FormattedMessage id="skillFilterApplyButtonDisabledMessage" />
                                            : <FormattedMessage id="skillFilterApplyButtonDisabledForMaxCountMessage" values={{ maxSkillSelection: maxSkillSelection }} />
                                        : ''
                                }
                            >
                                <Button
                                    type="primary"
                                    size="small"
                                    onClick={onApplyClick}
                                    disabled={isApplyDisabled}
                                >
                                    Apply
                                </Button>
                            </Tooltip>
                        </Space>
                    </div>
                )}
            </div>
        );
    };

    // Custom filter option renderer to show tooltips in dropdown
    const filterOption = (inputValue, path) => {
        return path.some(option => option?.fullLabel?.toLowerCase()?.includes(inputValue.toLowerCase()));
    };

    // Custom search render function with tooltips
    const searchRender = (inputValue, path) => {
        // Create a full path string for tooltip
        const fullPath = path.filter(Boolean).map(item => item.fullLabel || item.label || '').join(' /\n');

        // Create display path for the dropdown item (truncated)
        const displayPath = path.filter(Boolean).map(item => truncateText(item.fullLabel || item.label || '')).join(' / ');

        return (
            <Tooltip title={fullPath} placement="right" overlayStyle={{ whiteSpace: 'pre-line' }}>
                <div>{displayPath}</div>
            </Tooltip>
        );
    };

    const { searchPlaceholder = '', searchSkillFilterCascaderPlaceholder = '' } = useSelector(state => getSkillsStaticMessages(state));

    const getCascaderProps = () => {
        const baseProps = {
            options: options,
            onChange: onChange,
            multiple: true,
            allowClear: false,
            changeOnSelect: true,
            expandTrigger: 'click',
            showSearch: {
                filter: filterOption,
                matchInputWidth: true,
                render: searchRender
            },
            className: className,
            popupClassName: 'skill-filter-cascader-popup',
            placeholder: isDropdownChild === EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS ? searchPlaceholder : searchSkillFilterCascaderPlaceholder,
            value: internalValues,
            dropdownRender: dropdownRender,
            notFoundContent: <Empty description={<FormattedMessage id="noDataAvailableText" />} image={Empty.PRESENTED_IMAGE_SIMPLE} />,
            tagRender: tagRender,
            displayRender: displayRender,
            showCheckedStrategy: SHOW_CHILD,
            checkStrictly: true,
            checkable: true,
            dropdownStyle: {
                minWidth: '250px',
                width: 'auto',
                position: 'fixed',
                zIndex: 1001
            }
        };

        return baseProps;
    };

    return (
        <div className="skills-filter-cascader">
            <Cascader
                {...getCascaderProps()}
                open={isAddSkillsMode ? getCascaderOpenState : undefined}
                onDropdownVisibleChange={handleDropdownVisibleChange}
            />
        </div>
    );
};

export default SkillsFilterCascader;

SkillsFilterCascader.propTypes = {
    skillsData: PropTypes.object,
    showButtons: PropTypes.bool,
    onSelectionChange: PropTypes.func,
    className: PropTypes.string,
    filterKey: PropTypes.string,
    filtersGuid: PropTypes.string,
    pageAlias: PropTypes.string,
    changeFilterValue: PropTypes.func,
    onDropdownButtonClick: PropTypes.func,
    maxSelection: PropTypes.number,
    onFilterApply: PropTypes.func,
    onFilterValueChange: PropTypes.func,
    selectedSkillsOptions: PropTypes.array,
    selectedValues: PropTypes.array,
    isDropdownChild: PropTypes.string,
    cascaderOpenState: PropTypes.bool
};