import React, {Component} from 'react';
import {Prompt} from 'react-router-dom';
import {bindAll} from 'lodash';
import PropTypes from 'prop-types';
import {Button, message, Modal} from 'antd';
import {navigationService} from '../../../components/common-components/helpers';
import {FormattedMessage} from 'react-intl';
import AntdModal from '../../antdModal';

class NotifyUnsavedChanges extends Component {
    constructor(props) {
        super(props);
        this.state = {
            confirmedNavigation: false,
            visible: false
        };
        bindAll(this, [
            'handleBlockedNavigation', 'showModal', 'closeModal', 'saveChanges', 'handleCancel',
            'renderFooter', 'discardChanges'
        ]);
    }

    componentDidMount() {
        this.subscription = navigationService.getNavigationStatus().subscribe(navigationStatus => {
            this.setState({confirmedNavigation: navigationStatus});
        });
    }

    componentWillUnmount() {
        this.subscription.unsubscribe();
    }

    handleBlockedNavigation(nextLocation) {
        const {confirmedNavigation} = this.state;
        const {location: currentLocation} = this.props;
        const isSelectionListItem = this.isSelectionListItem(nextLocation, currentLocation);
        if (!confirmedNavigation && this.shouldBlockNavigation(nextLocation) && !isSelectionListItem){
            this.showModal(nextLocation);
            return false;
        }
        return true;
    }

    shouldBlockNavigation() {
        return this.props.when;
    }

    showModal(location) {
        this.setState({
            modalVisible: true,
            lastLocation: location
        });
    }

    closeModal(callback) {
        this.setState({
            modalVisible: false
        }, callback);
        Modal.destroyAll();
    }

    isSelectionListItem(nextLocation, currentLocation) {
        const {state: currentLocationState = {}} = currentLocation;
        const {state: lastLocationState = {}} = nextLocation;
        /**
         * Multiple permutations combinations
         * Either 1 can have area and section or neither can or 1 of them can
         */
        if ((currentLocationState.areaName && lastLocationState.areaName) &&
            (currentLocationState.sectionName === lastLocationState.sectionName)) { // NULL check
            return !!(currentLocationState.areaName === lastLocationState.areaName && currentLocationState.sectionName === lastLocationState.sectionName); // Equality check
        }
        return false;
    }

    saveChanges(event) {
        this.closeModal(() => {
            const {navigate, saveChanges: handleSubmit, checkIfErrors, gridStatus, getUserConfirmation, confirmSave,messages = {}} = this.props;
            const {toasterDefaultUnsavedChangesMessage} = messages;
            const {lastLocation} = this.state;
            if (lastLocation) {
                this.setState({confirmedNavigation: true}, () => {
                    const forceSubmit = true;
                    const errorStatus = handleSubmit(event, forceSubmit);
                    if (!confirmSave()) {
                        if (!checkIfErrors(errorStatus) && !gridStatus.hasError) {
                            // Navigate to the previous blocked location with your navigate function
                            navigate(lastLocation.pathname, lastLocation.state);
                        } else {
                            message.error(toasterDefaultUnsavedChangesMessage);
                            this.setState({confirmedNavigation: false});
                        }
                    } else {
                        getUserConfirmation(true, lastLocation);
                    }
                });
            }
        });
    }

    handleCancel() {
        this.closeModal();
    }

    discardChanges() {
        const {form, onCancel} = this.props;
        const discardAndNavigate = true;
        form.resetFields();
        onCancel(discardAndNavigate);
        this.closeModal(() => {
            const {navigate} = this.props;
            const {lastLocation} = this.state;
            if (lastLocation) {
                this.setState({
                    confirmedNavigation: true
                }, () => {
                    // Navigate to the previous blocked location with your navigate function
                    navigate(lastLocation.pathname, lastLocation.state);
                });
            }
        });
    }

    renderFooter() {
        return (
            <div className="action-bar-modal-buttons">
                <Button
                    key="submit"
                    type="primary"
                    className="ant-btn-primary"
                    onClick={this.saveChanges}
                >
                    <span><FormattedMessage id="actionBarSaveButtonLabel"/></span>
                </Button>
                <Button className="ant-btn-secondary" key="discard" type="secondary-button" onClick={this.discardChanges}>
                    <span><FormattedMessage id="actionBarDiscardChangesLabel"/></span>
                </Button>
                <Button className="ant-btn-tertiary" key="cancel" type="link secondary-button" onClick={this.handleCancel}>
                    <span><FormattedMessage id="actionBarCancelButtonLabel"/></span>
                </Button>
            </div>
        );
    }
    render() {
        const {when} = this.props;
        return (<>
            <Prompt
                when={when}
                message={this.handleBlockedNavigation} />
            <AntdModal
                open={this.state.modalVisible}
                footer={this.renderFooter()}
                className="action-bar-modal"
                closable={false}
                onCancel={this.handleCancel}
            >
                <p tabIndex="0"><FormattedMessage id="actionBarUnsavedChanges"/></p>
            </AntdModal>
        </>);
    }
}

NotifyUnsavedChanges.propTypes = {
    when: PropTypes.bool.isRequired,
    navigate: PropTypes.func.isRequired,
    saveChanges: PropTypes.func.isRequired,
    gridStatus: PropTypes.object,
    form: PropTypes.object,
    location: PropTypes.object,
    checkIfErrors: PropTypes.func.isRequired,
    confirmSave: PropTypes.func
};

NotifyUnsavedChanges.defaultProps = {
    confirmSave: () => {return false;},
    getUserConfirmation: () => {}
};

export default NotifyUnsavedChanges;