import React from 'react';
import PropTypes from 'prop-types';
import { Button, Modal, message } from 'antd';
import { FormattedMessage } from 'react-intl';
import { bindAll } from 'lodash';
import { messageService } from '../../../components/common-components/helpers';

class NotifyUnsavedChangesWithoutPrompt extends React.Component {
    constructor(props) {
        super(props);

        bindAll(this, [
            'closeModal', 'saveChanges', 'handleCancel', 'renderFooter', 'discardChanges'
        ]);
    }

    closeModal(callback) {
        if (callback) {
            callback();
        }
        Modal.destroyAll();
    }

    handleCancel() {
        this.closeModal(() => {
            this.props.onCancel();
        });
    }

    discardChanges() {
        const { form, onDiscardChanges, messageServiceId, onCancel} = this.props;
        form.resetFields();
        this.closeModal(() => {
            onCancel();
        });
        onDiscardChanges();

        messageService.sendMessage({ id: messageServiceId, isGridModified: false, errorStatus: false });
    }

    saveChanges() {
        this.closeModal(() => {
            const { onSaveChanges, messages, hasError, messageServiceId, onCancel } = this.props;
            const { toasterDefaultUnsavedChangesMessage } = messages;

            if (!hasError) {
                onSaveChanges();
                messageService.sendMessage({ id: messageServiceId, isGridModified: false, errorStatus: false });
                onCancel();
            } else {
                onCancel();
                message.error(toasterDefaultUnsavedChangesMessage);
            }
        });
    }

    renderFooter() {
        return (
            <div className="action-bar-modal-buttons">
                <Button
                    key="submit"
                    type="primary"
                    className="ant-btn-primary"
                    onClick={this.saveChanges}
                >
                    <span><FormattedMessage id="actionBarSaveButtonLabel" /></span>
                </Button>
                <Button className="ant-btn-secondary" key="discard" type="secondary-button" onClick={this.discardChanges}>
                    <span><FormattedMessage id="actionBarDiscardChangesLabel" /></span>
                </Button>
                <Button className="ant-btn-tertiary" key="cancel" type="link secondary-button" onClick={this.handleCancel}>
                    <span><FormattedMessage id="actionBarCancelButtonLabel" /></span>
                </Button>
            </div>
        );
    }

    render() {
        return (
            (<Modal
                open={this.props.shouldPrompt}
                footer={this.renderFooter()}
                closable={false}
            >
                <p><FormattedMessage id="actionBarUnsavedChanges" /></p>
            </Modal>)
        );
    }
}

NotifyUnsavedChangesWithoutPrompt.propTypes = {
    form: PropTypes.any,
    shouldPrompt: PropTypes.bool,
    onCancel: PropTypes.func,
    onDiscardChanges: PropTypes.func,
    onSaveChanges: PropTypes.func,
    hasError: PropTypes.bool,
    messageServiceId: PropTypes.string
};

export {
    NotifyUnsavedChangesWithoutPrompt
};