import React from 'react';
import { Radio, Tooltip } from 'antd';
import { CommandBarSwitchViewElementProps } from '../propTypes';
import styles from './styles.less';
import { Icon } from '..';
import { debounce } from 'lodash';

class SwitchViewElement extends React.Component {
    constructor(props) {
        super(props);

        this.onChange = this.onChange.bind(this);
    }

    renderRadioButtons() {
        return this.props.config.options.map((option) => {
            const { label, value, disabled, icon, options: { tooltipLabel = '' }} = option;
            const iconComponent = icon ? <Icon type={icon} /> : null;

            return (
                <Tooltip
                    placement="bottom"
                    title={tooltipLabel}>
                        <Radio.Button aria-label={label} key={label} value={value} disabled={disabled} className={`${styles.cbRadioButtonChecked} ${styles.CommandBarElement}`}>
                            {iconComponent}
                            <span className={styles.viewElementLabel}>{label}</span>
                        </Radio.Button>
                    </Tooltip>
            );
        });
    }

    onChange(event) {
        const { onClickActionType } = this.props.config;
        const context = { ...this.props.actionProps, ...{ newViewSettings: event.target.value }, actionType: onClickActionType };

        this.props.onAction(context);
    }

    render() {
        const options = this.props.config.buttonView ? null : this.props.config.options;

        return (
            <Radio.Group
                {...this.props.config}
                value={this.props.actionProps.masterRecTableName}
                options={options}
                onChange={debounce(this.onChange, 300, { leading: true, trailing: true })}
            >
                {this.renderRadioButtons()}
            </Radio.Group>
        );
    }
}

SwitchViewElement.propTypes = CommandBarSwitchViewElementProps;

export { SwitchViewElement };