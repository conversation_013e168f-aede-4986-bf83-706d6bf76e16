import React from 'react';
import { shouldShowItemOnAccess } from '../../selectors/functionalAccessSelectors';
import { CommandBarMenusSectionElementProps } from '../propTypes';

const DropDown = React.lazy(() => import('./dropDown').then((module) => ({ default: module.DropDown })));

const WrappedDropDown = React.lazy(() => import('./dropDownWithState').then((module) => ({ default: module.WrappedDropDown })));

const MenusSectionElement = (props) => {
    const shouldShowItemOnAccessWrapped = (item) => {
        const { tableName: itemTableName } = item.options || {};
        const { multipleTableSelectionActive, entitiesTableName, entitiesIds = [], selectedEntitiesByTable = {} } = props.actionProps;

        let selectedIds = entitiesIds;
        let selectedEntitiesTableName = entitiesTableName;

        if (multipleTableSelectionActive) {
            selectedIds = (selectedEntitiesByTable[itemTableName] || {}).ids;
            selectedEntitiesTableName = itemTableName;
        }

        const propsData = {
            getUserHasFunctionalAccess: props.getUserHasFunctionalAccess,
            getAccessibleEntitiesIds: props.getAccessibleEntitiesIds,
            actionProps: {
                entitiesIds: selectedIds,
                entitiesTableName: selectedEntitiesTableName
            }
        };

        return shouldShowItemOnAccess(propsData, item);
    };

    const shouldShowItem = (item) => {
        const { actionProps , getIsItemActionAllowed = () => true } = props;
        const { multipleTableSelectionActive, entitiesTableName } = actionProps;
        const { isTableSelectionDependant, tableName, requireTableName } = (item.options || {});
        let showItemOnSingleTableSelection = true;

        if (isTableSelectionDependant || requireTableName) {
            showItemOnSingleTableSelection = !multipleTableSelectionActive && (tableName ? tableName === entitiesTableName : true);
        }

        return showItemOnSingleTableSelection && shouldShowItemOnAccessWrapped(item) && getIsItemActionAllowed(item);
    };

    const commonMenuProps = {
        actionProps: props.actionProps,
        onAction: props.onAction,
        getPopupContainer: props.getPopupContainer
    };

    const getControlledMenuProps = (config) => {
        return {
            ...commonMenuProps,
            visible: config.visible,
            setVisibility: (visible) => props.setSectionVisibility(config.key, visible),
            getItemDisabled: props.getItemDisabled,
            shouldShowItem: props.shouldShowItem ? props.shouldShowItem : shouldShowItem,
            getEntityInfo : props.getEntityInfo,
            staticMessages: props.staticMessages
        };
    };

    const actions = (props.config.menusSection || []).map((menuConfig) => {
        const { userDropdownWithState = false, key } = menuConfig;

        return (
            <React.Fragment key={key}>
                <React.Suspense fallback={<div />}>
                    {
                        userDropdownWithState
                            ? <WrappedDropDown key={`${key}_dropdown`} config={menuConfig} {...commonMenuProps} />
                            : <DropDown key={`${key}_dropdown`} config={menuConfig} {...getControlledMenuProps(menuConfig)} />
                    }
                </React.Suspense>
            </React.Fragment>
        );
    });

    return actions.length === 0 ? null : actions;
};

MenusSectionElement.propTypes = CommandBarMenusSectionElementProps;

export { MenusSectionElement };