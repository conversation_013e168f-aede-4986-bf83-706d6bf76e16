import React from 'react';
import { Dropdown, Tooltip } from 'antd';
import { CommandBarActionWithComponentProps } from '../propTypes';
import styles from './styles.less';
import { Icon } from '../';
import { getEntityDependantMessage } from '../../utils/entityStructureUtils';
import { commandBarStringTemplates } from '../../constants/stringTemplateConsts';
import { getLocalStorageItem } from '../../localStorage';
import { USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY } from '../../constants/localStorageConsts';

class ActionWithComponent extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            dropdownOpened: false,
            showTooltip: !getLocalStorageItem(USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY)
        };

        this.onVisibleChange = this.onVisibleChange.bind(this);
        this.handleStorage = this.handleStorage.bind(this);
        this.renderComponent = this.renderComponent.bind(this);
    }

    onVisibleChange(visible) {
        this.setState({ dropdownOpened: visible });
    }

    renderComponent() {
        const { config, actionProps, getEntityInfo, staticMessages, onAction } = this.props;
        const { componentType, label } = config;
        //SpecificComponent should be wrapped at Menu itself
        const SpecificComponent = actionProps.components[componentType];

        if (!SpecificComponent) {
            console.error(`Invalid component type: ${componentType}`);
            return <div></div>;
        }

        const icon = config.icon ? <Icon type={config.icon} /> : null;
        const parentProps = { ...actionProps, ...config, onVisibleChange: this.onVisibleChange, getEntityInfo, staticMessages, onAction };

        return (
            <div id={`cb_${config.label}_action_menu`}>
                <SpecificComponent {...parentProps}>
                    {label} {icon}
                </SpecificComponent>
            </div>
        );
    }

    handleStorage() {
        const shouldShowTooltip = !getLocalStorageItem(USER_ALREADY_LOGGED_LOCAL_STORAGE_KEY);

        this.setState({ showTooltip: shouldShowTooltip });
    }

    componentDidMount() {
        window.addEventListener('storage', this.handleStorage);
    }

    componentWillUnmount() {
        window.removeEventListener('storage', this.handleStorage);
    }

    getPopupContainer(trigger) {
        return trigger.parentNode;
    }

    render() {
        const { config, actionProps, getEntityInfo, staticMessages, getPopupContainer = this.getPopupContainer } = this.props;
        const { dropdownOpened } = this.state;
        const { icon, iconLeft, label, openIcon, closedIcon, options } = config;

        const className = `${styles.actionElement} ${styles.CommandBarElement} ${dropdownOpened ? '__drop-down-open' : ''}`;
        const anchorClassName = `${styles.actionLink} ant-dropdown-link`;
        const expandIcon = openIcon ? <Icon type={dropdownOpened ? openIcon : closedIcon} /> : null;
        let updatedLabel = label;

        if (options && options.isEntityDependant) {
            const { tableName, tableNamePropName } = options;
            const entityTableName = tableNamePropName ? actionProps[tableNamePropName] : tableName;
            updatedLabel = getEntityDependantMessage(getEntityInfo, commandBarStringTemplates, { ...options, tableName: entityTableName, fallbackValue: entityTableName }, label);
        }

        const tooltipText = staticMessages[config.tooltipTextLabel] || null;

        return (
            <div id={`cb_${config.label}_action`} className={actionProps.listPageAndBulkUpdateFeatureFlag ? styles.viewJobFilter : ''}>
                <Tooltip
                    open={this.state.showTooltip && tooltipText !== null}
                    placement="bottom"
                    title={tooltipText}>
                    <Dropdown
                        arrow
                        key={updatedLabel}
                        trigger={['click']}
                        open={dropdownOpened}
                        onOpenChange={this.onVisibleChange}
                        dropdownRender={this.renderComponent}
                        className={className}
                        getPopupContainer={getPopupContainer}>
                        <button role="link" aria-label={label} aria-expanded={dropdownOpened} className={anchorClassName}>
                            <Icon type={actionProps.listPageAndBulkUpdateFeatureFlag ? iconLeft : icon} /><span className={styles.itemDropdownLabel}>{label}</span>
                            {expandIcon}
                        </button>
                    </Dropdown>
                </Tooltip>
            </div>
        );
    }
}

ActionWithComponent.propTypes = CommandBarActionWithComponentProps;

export { ActionWithComponent };