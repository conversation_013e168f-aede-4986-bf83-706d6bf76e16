import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Toolt<PERSON> } from 'antd';
import styles from './tpSkillsSection.less';
import { SkillsList } from '../../lib/skillsComponents/skillsList';
import TpSection from '../../lib/talentProfile/tpSection';
import { SKILLS_SECTION_TITLE, SKILL_EDIT_BUTTON_TEXT } from '../../constants/skillsSectionConsts';
import { getTpSectionTitle } from '../../utils/talentProfileUtils';
import { FEATURE_FLAGS } from '../../constants/globalConsts.js';
import SkillApprovalAlert from '../../lib/skillsComponents/skillApprovalAlert/skillApprovalAlert';

class TpSkillsSection extends React.Component {
    constructor(props) {
        super(props);

        this.onEditClick = this.onEditClick.bind(this);
    }

    componentDidMount() {
        const { id, config, visible, talentProfilePageTransformedEnabled } = this.props;
        const title = config.SectionTitle || SKILLS_SECTION_TITLE;

        visible && setTimeout(() => this.props.registerLink({ [id]: { title, index: talentProfilePageTransformedEnabled ? 5 : 9 } }), 0);
    }

    onEditClick() {
        const { resourceId, onEditClick, loadRecommendations, recommendationsEnabled, skillApprovalFeatureFlagEnabled } = this.props;
        onEditClick(resourceId);
        if (recommendationsEnabled) {
            loadRecommendations(resourceId);
        }
    }

    render() {
        const { id, resourceSkills, visible, config, hasResourceSkills, skillWindowPopover, editingDisabled, skillsStaticLabels = {}, skillsAuditInfo = {}, getAriaLabelDeleteSkillButton, getIsSectionSysMaintained, skillApprovalFeatureFlagEnabled, resourceManager, talentProfilePageTransformedEnabled, resourceSkillApprovalPermission } = this.props;
        const { SectionTitle } = config;
        const editable = !editingDisabled;
        const defaultSectionTitle = skillsStaticLabels.skillsSectionTitle || SKILLS_SECTION_TITLE;

        let skillEditable = editable;

        if (skillApprovalFeatureFlagEnabled && resourceSkillApprovalPermission) {
            skillEditable = (editable && resourceManager !== null);
        }

        return visible && (
            <TpSection className={styles.skillsSection}
                id={id}
                title={getTpSectionTitle((SectionTitle || defaultSectionTitle), skillsAuditInfo)}
                skillsStaticLabels={skillsStaticLabels}
            >
                {

                    !skillEditable ? (
                        <Tooltip title={skillsStaticLabels.noManagerToApproveMessage} placement="top">
                            <Button
                                id="tp-add-edit-skills-button"
                                type="secondary"
                                disabled={!skillEditable}
                                className={`${styles.headingButton} ${styles.editButton}`}
                                onClick={this.onEditClick}>
                                {this.props.editButtonText || SKILL_EDIT_BUTTON_TEXT}
                            </Button>
                        </Tooltip>
                    ) : (
                        <Button
                            id="tp-add-edit-skills-button"
                            type="secondary"
                            disabled={!skillEditable}
                            className={`${styles.headingButton} ${styles.editButton}`}
                            onClick={this.onEditClick}>
                            {this.props.editButtonText || SKILL_EDIT_BUTTON_TEXT}
                        </Button>
                    )

                }
                {skillApprovalFeatureFlagEnabled && !!resourceSkillApprovalPermission && (
                    <SkillApprovalAlert style={{ marginBottom: '10px' }} />
                )}

                <SkillsList getAriaLabelDeleteSkillButton={getAriaLabelDeleteSkillButton} getIsSectionSysMaintained={getIsSectionSysMaintained} listItems={resourceSkills} editable={skillEditable} allowCollapseAllSections={true} skillWindowPopover={skillWindowPopover} skillsStaticLabels={skillsStaticLabels} talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled} />
                {
                    // Show divider only when there is no data and the feature flag is disabled
                    hasResourceSkills && !talentProfilePageTransformedEnabled && <Divider className="divider" />
                }
            </TpSection>
        );
    }
}

TpSkillsSection.propTypes = {
    visible: PropTypes.bool.isRequired,
    id: PropTypes.string.isRequired,
    resourceId: PropTypes.string,
    config: PropTypes.shape({
        SectionTitle: PropTypes.string
    }).isRequired,
    editButtonText: PropTypes.string,
    resourceSkills: PropTypes.object,
    skillsStaticLabels: PropTypes.object,
    onEditClick: PropTypes.func.isRequired,
    registerLink: PropTypes.func,
    hasResourceSkills: PropTypes.bool,
    editable: PropTypes.bool,
    skillWindowPopover: PropTypes.func,
    getAriaLabelDeleteSkillButton: PropTypes.func,
    loadRecommendations: PropTypes.func,
    recommendationsEnabled: PropTypes.bool,
    skillApprovalFeatureFlagEnabled: PropTypes.bool,
    getFeatureFlag: PropTypes.func,
    resourceManager: PropTypes.object,
    resourceSkillApprovalPermission: PropTypes.bool,
    editingDisabled: PropTypes.bool
};

export default TpSkillsSection;
