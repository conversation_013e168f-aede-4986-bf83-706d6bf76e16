import React from 'react';
import PropTypes from 'prop-types';
import TpSection from './tpSection';
import ConnectedEducationForm from '../../connectedComponents/connectedEducation/connectedEducationForm';
import { TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import { Button, Divider } from 'antd';
import { getTpSectionTitle } from '../../utils/talentProfileUtils';
import { FEATURE_FLAGS } from '../../constants/globalConsts.js';


class TpEducationSection extends React.Component {
    constructor(props) {
        super(props);
        const { visible } = props;
        this.state = {
            visible: visible
        };
        this.onEditClick = this.onEditClick.bind(this);
    }

    componentDidMount() {
        const { id, config, visible, talentProfilePageTransformedEnabled } = this.props;
        const title = config.SectionTitle;
        visible && setTimeout(() => this.props.registerLink({ [id]: { title, index: talentProfilePageTransformedEnabled ? 4 : 7 } }), 0);
    }

    onEditClick() {
        const { onEditClick, hasEducationData = false } = this.props;
        onEditClick(!hasEducationData);
    }

    render() {
        const { config = {}, visible = true, editable, buttonLabel, hasEducationData = false, disableAddEditButton = false, educationAuditInfo = {}, talentProfilePageTransformedEnabled } = this.props;
        const { SectionKey = '', SectionTitle = '' } = config;
        return visible &&
            (<TpSection {...this.props}
                title={getTpSectionTitle(SectionTitle, educationAuditInfo)}
                id={SectionKey}
                className={'educationSectionContainer'}
            >
                {
                    editable && (
                        <Button
                            id="tp-add-edit-education-button"
                            type="secondary"
                            className={'headingButton editButton'}
                            onClick={this.onEditClick}
                            disabled={disableAddEditButton}>
                            {buttonLabel}
                        </Button>
                    )
                }
                <ConnectedEducationForm alias={TALENT_PROFILE_ALIAS} editable={editable} talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled} />
                {
                    // Show divider only when there is no data and the feature flag is disabled
                    !hasEducationData && !talentProfilePageTransformedEnabled && <Divider className="divider" />
                }
            </TpSection>);
    }
}

TpEducationSection.propTypes = {
    registerLink: PropTypes.func,
    config: PropTypes.object,
    visible: PropTypes.bool,
    editable: PropTypes.bool,
    buttonLable: PropTypes.string,
    hasEducationData: PropTypes.bool,
    getFeatureFlag: PropTypes.func
};

export default TpEducationSection;