import React from 'react';
import PropTypes from 'prop-types';
import { TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import ConnectedWorkHistoryDataGrid from '../../connectedComponents/connectedWorkHistoryDataGrid';
import { getWorkHistoryPageDataForTalentProfile, talentProfilePagedDataSelector } from '../../selectors/workHistorySelector';
import { Typography } from 'antd';
import { densityFields } from '../../utils/commonDataGridUtils';
import { ConnectedTableOptions } from '../../connectedComponents/connectedTableOptions';
import { WORK_HISTORY_ALIAS } from '../../constants/workHistoryConstants';
import { FEATURE_FLAGS } from '../../constants/globalConsts.js';


class TpWorkHistorySection extends React.Component {
    constructor(props) {
        super(props);
        const { showRecentWork } = props;
        this.state = {
            showRecentWork: showRecentWork,
            defaultPageNumber: 1
        };
    }

    componentDidMount() {
        this.props.showRecentWork && setTimeout(() => this.handleRegisterLink(), 0);
    }

    componentWillUnmount() {
        const { defaultPageNumber } = this.state;
        const { dispatchtoSetDefaultPageOnUnmount } = this.props;
        const tpWorkHistory_alias = `${TALENT_PROFILE_ALIAS}_${WORK_HISTORY_ALIAS}`;
        dispatchtoSetDefaultPageOnUnmount(tpWorkHistory_alias, defaultPageNumber);
    }

    componentDidUpdate(newProps, stateProps) {
        const { config = {}, showRecentWork } = newProps;
        const { SectionKey } = config;
        const id = SectionKey || '';

        if (showRecentWork !== stateProps.showRecentWork) {
            this.setState({ showRecentWork });
            if (showRecentWork) {
                this.handleRegisterLink();
            } else if (!showRecentWork && id) {
                this.props.unRegisterLink(newProps.config.SectionKey);
            }
        }
    }

    handleRegisterLink() {
        const { config = {}, registerLink } = this.props;
        const { SectionTitle, SectionKey } = config;
        const title = SectionTitle || '';
        const id = SectionKey || '';
        const talentProfilePageTransformedEnabled = this.props.getFeatureFlag ? this.props.getFeatureFlag(FEATURE_FLAGS.TALENT_PROFILE_PAGE_TRANSFORMED) : false;

        registerLink({ [id]: { title, index: talentProfilePageTransformedEnabled ? 0 : 1 } });
    }

    render() {
        const { config = {}, showRecentWork, omitFields, fixedFields, tableName, density, profilePageMaxFieldsSelection, configAlias } = this.props;
        const { SectionKey = '', SectionTitle = '' } = config;

        return (
            showRecentWork && (
                <>
                    <div
                        id={SectionKey}
                        className={'workHistoryHeader'}
                        data-scrollspy
                    >
                        <Typography.Title level={2}>{SectionTitle}</Typography.Title>
                        <div className={'header-cog-icon'}>
                            <React.Suspense fallback={<div />}>
                                <ConnectedTableOptions
                                    densityFields={densityFields}
                                    triggerSubMenuAction={'click'}
                                    tableName={tableName}
                                    density={density}
                                    fixedColumns={fixedFields}
                                    omitFields={omitFields}
                                    maxFieldsSelection={profilePageMaxFieldsSelection}
                                    getDataGridPageState={
                                        getWorkHistoryPageDataForTalentProfile
                                    }
                                    configAlias={configAlias}
                                    dropdownContainer={(trigger) => trigger.parentNode}
                                />
                            </React.Suspense>
                        </div>
                    </div>
                    <ConnectedWorkHistoryDataGrid
                        {...this.props}
                        alias={TALENT_PROFILE_ALIAS}
                        getDataGridPageState={getWorkHistoryPageDataForTalentProfile}
                        wrappedGetPageTableDatasByAliasSelector={
                            talentProfilePagedDataSelector
                        }
                    />
                </>
            )
        );
    }
}

TpWorkHistorySection.propTypes = {
    registerLink: PropTypes.func,
    recentWorkConfig: PropTypes.object,
    visibleRecentWork: PropTypes.bool
};

export default TpWorkHistorySection;