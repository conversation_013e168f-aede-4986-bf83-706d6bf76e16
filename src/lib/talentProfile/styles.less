/* Overrides */
@import "./../../../styles/mixins.less";

@custom-border-radius: 8px;
@custom-margin-bottom: 0.5em;

// Styles for old Talent Profile
.talent-profile-legacy-body {
  /* Overrides */

  :local(.lastLoginWrapper) {
    margin-left: 10px;
    align-self: end;
    margin-bottom: 3px;

    font-size: small !important;
    font-weight: normal !important;
    margin-bottom: 5px !important;
  }
  :local(.readOnlyHeader) {
    display: flex;

    span {
      font-size: small !important;
      font-weight: normal !important;
    }
  }

  .talent-profile-ctn {
    width: 100%;
    height: 100%;
    display: flex;
    flex-flow: row nowrap;
    padding: 1.4em;
    background: @white-color;
  }

  .talentProfilePage {
    .in-page-nav {
      a {
        color: @white-color !important;
      }
    }
  }

  .column-layout-ctn {
    flex-flow: column wrap;
    width: inherit;
  }

  .header-ctn {
    display: flex;
    flex-flow: row nowrap;
    padding: 1.4em 0 2em 0;
  }

  .quick-ref-area,
  .profile-section {
    h2 {
      margin-bottom: 1rem;
    }
  }

  .quick-ref-area {
    padding: 2rem;
    width: 25%;
  }

  .profile-section {
    padding: 2rem 1.8rem;
    width: 75%;

    .ant-col {
      &:not(.skill-popover-col) {
        padding-right: 1.4em;
      }

      .ant-col {
        padding-right: 0;
      }
    }
  }

  .profile-resume {
    width: 100%;
    margin-left: 5rem;

    h1 {
      cursor: pointer;
      margin: 0.4em 0;
      margin-bottom: 0 !important;
    }
    .ant-legacy-form-item-label {
      text-align: left;
    }
    .role-row {
      .role-col {
        display: inline-block;
        vertical-align: top;
      }
      .icon-bullet {
        font-size: 2em;
      }
      .ant-col-16 {
        width: 100%;
      }
    }
    .ant-col-1 {
      width: 2rem;
      padding-left: 0.4rem;
    }
  }

  .educationSectionContainer {
    .headingButton {
      float: right;
    }

    .sectionTitle {
      display: inline-block;
      margin: 0;
    }

    .editButton {
      margin: 10px;
      margin-top: -3em;

      &:focus {
        background-color: @white-color;
        color: @primary-color;
        border-color: @primary-color;
      }
    }

    .button {
      height: 28px !important;
      margin-left: 4px;
      border-color: #3072ab;
    }

    .buttonPrimary {
      color: white;
      background-color: #3072ab !important;
    }
  }

  .experienceSectionContainer {
    .headingButton {
      float: right;
    }

    .sectionTitle {
      display: inline-block;
      margin: 0;
    }

    .editButton {
      margin: 10px;
      margin-top: -3em;

      &:focus {
        background-color: @white-color;
        color: @primary-color;
        border-color: @primary-color;
      }
    }

    .button {
      height: 28px !important;
      margin-left: 4px;
      border-color: #3072ab;
    }

    .buttonPrimary {
      color: white;
      background-color: #3072ab !important;
    }
  }

  .experience,
  .education {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .ant-legacy-form-item-control {
      margin-bottom: 0px;
    }
    .formInlineEdit {
      border: 1px solid rgb(255, 255, 255);
      box-shadow: @contextual-box-shadow;
      padding: 1rem;
    }
    .addDeleteController {
      display: flex;
      align-items: flex-start;
      height: 100%;
      gap: 10px;
      margin-top: 10px;
    }

    .container {
      width: 100%;
      display: flex;
      flex-direction: column;
      .role-row {
        .role-col {
          display: inline-block;
          vertical-align: middle;

          .strong {
            font-weight: bold;
          }
        }
        .icon-bullet {
          display: inline-block;
          vertical-align: middle;
          margin: 0px 8px -7px 8px;
          font-size: 15px;
        }
        .ant-col-16 {
          width: 100%;
        }
      }
      .readonlyControl {
        &.has-no-value {
          color: #767676;
          font-style: italic;
        }
      }
      .ant-col-1 {
        width: 2rem;
        padding-left: 0.4rem;
      }
    }
    .ant-legacy-form-item-label {
      text-align: left;
      label::after {
        visibility: hidden !important;
      }
    }

    .ant-legacy-form-item-control-wrapper {
      display: block;
      width: 75% !important;
      textarea.ant-input {
        height: 100px;
      }
    }
  }

  .profile-description {
    margin-bottom: 1.4em;
    .ant-row {
      .ant-legacy-form-item-control {
        margin-bottom: 0;
      }
    }
  }

  .main-section {
    margin-bottom: 1.4em;
    > div.ant-row:last-child {
      .fieldControlStyle {
        .ant-legacy-form-item-control {
          margin-bottom: 0;
        }
      }
    }
  }

  .section-ctn {
    display: flex;
    flex-flow: row nowrap;
    height: auto;
  }

  .quick-ref-area {
    width: 250px;
  }

  /* Skills */

  .skills-ctn {
    h3.ant-typography {
      font-size: @heading-3-size;
    }

    padding-bottom: 60px;
  }

  .skill-category {
    display: flex;
    flex-flow: column nowrap;
  }

  .skill-popover-row {
    margin-bottom: 0px;
  }

  .skill-popover-col > label > span {
    white-space: initial;
  }

  .attachments-message-area {
    width: 50%;
  }

  .navSideBar {
    list-style: none;
    padding: 0;
    margin: 0;
    border-left: 1px solid #fff;
    li {
      position: relative;
      display: flex;
      align-items: center;

      a {
        text-decoration: none;
      }
    }
    li.currentSection {
      &:before {
        content: "";
        width: 8px;
        height: 8px;
        background: #fff;
        position: absolute;
        left: -4px;
        border-radius: 50%;
      }
      a {
        font-weight: bold;
      }
    }
  }

  :local(.tpHeaderWrapper) {
    display: flex;
    align-items: center;
  }

  :local(.infoCircleTpHeader) {
    margin-left: 1.4rem;
  }

  :local(.moreInfoTooltip) {
    .ant-tooltip-arrow::before {
      background-color: @white-color;
    }

    .ant-tooltip-inner {
      background-color: @white-color;
      color: @black-color;
    }
  }

  //overwriting some wierd ant design styles
  .talent-profile-ctn .ant-col-16 {
    max-width: 100% !important;
  }

  //overwriting details pane containerwidth
  .talent-profile-ctn .ant-col-16 .readonlyControlContainer {
    width: 100% !important;
  }

  #CommandBar {
    .anticon-user {
      background-color: transparent;
      color: rgba(0, 0, 0, 0.88);
    }

    .ant-popover-inner[role="tooltip"] {
      padding: 0;

      & .ant-popover-title {
        border-bottom: 1px solid gainsboro;
        padding: 12px;
        margin: 0;
      }

      & .ant-popover-inner-content {
        padding: 12px;
      }
    }
  }

  .cMeRow {
    display: flex;
    flex-direction: column;
    align-items: end;
  }
}

// Styles for new (transformed) Talent Profile
.talent-profile-body {
  :local(.lastLoginWrapper) {
    gap: 8px;
    display: flex;
    color: @input-normal-text-color;
    margin-bottom: 0 !important;
  }

  .profile-resume {
    display: flex;
    flex-direction: column;
    min-width: 300px;
    gap: 8px;
    flex: 1;

    h1 {
      cursor: pointer;
      margin: 0.4em 0;
      margin-bottom: 0 !important;
    }

    .ant-legacy-form-item-label {
      text-align: left;
    }

    .ant-col-1 {
      width: 2rem;
      padding-left: 0.4rem;
    }

    .role-row {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      align-items: center;
      .role-col {
        display: inline-block;
        vertical-align: top;
      }
      .icon-bullet {
        font-size: 1em;
      }
      .ant-col-16 {
        width: 100%;
      }
      .basic-details-primary-skill,
      .basic-details-secondary-skill {
        border-radius: @card-border-radius;
        padding: 2px 7px 3px;
      }

      .basic-details-primary-skill {
        color: @primary-color;
        background-color: @primary-light;
      }

      .basic-details-secondary-skill {
        color: @input-normal-text-color;
        background-color: #edf0f5;
      }
    }
  }

  /*overridden styles for profile-resume */
  .profile-resume .field-value-prefix {
    color: @input-normal-text-color;
  }

  .profile-resume .ant-row.ant-legacy-form-item.formControlLayoutItem {
    margin-bottom: 0 !important;
  }

  .profile-resume
    .ant-row.ant-legacy-form-item:not(.um-first-name, .um-last-name) {
    margin-top: 0 !important;
  }

  .profile-resume .ant-legacy-form-item-control {
    line-height: normal !important;
  }

  .profile-resume
    .readonlyControl:not(.basic-details-primary-skill):not(
      .basic-details-secondary-skill
    ) {
    padding-top: 0 !important;
  }

  /* truncate text within container*/
  .truncateTextreadonlyControlContainer {
    display: flex;
  }

  .truncated-text {
    flex: 1;
    vertical-align: bottom;
    .truncate-text;
  }

  .see-more-text {
    margin-left: 6px;
    display: inline-block;
    font-weight: normal;
    text-decoration: underline;
  }

  .profile-resume .see-more-text a {
    color: @primary-color !important;
  }

  .talent-profile-ctn {
    width: 100%;
    height: 100%;
    display: flex;
    flex-flow: row nowrap;
    padding: 15px;
    background: @transparent-color;
  }

  .header-ctn {
    display: flex;
    /* Arrange flex items in a horizontal row and allow them to wrap to the next line if needed */
    flex-flow: row wrap;
    gap: 20px;
  }
}

// Common styles shared by both versions
.talent-profile-ctn .ant-col .ant-legacy-form-item-no-colon {
  white-space: nowrap;
}

/* Text styles: to be globalised */

.talent-profile-ctn h2.ant-typography,
.talent-profile-ctn h3.ant-typography {
  font-weight: @regular-weight;
  margin-bottom: 0.8rem;
}

/* Form elements */

.header-ctn .ant-legacy-form-item label > .anticon {
  vertical-align: middle;
  font-size: 1.2rem;
}

// Form fields on a new line from the label
.quick-ref-area .ant-legacy-form-item-label,
.quick-ref-area .ant-legacy-form-item-control-wrapper,
.profile-section .ant-legacy-form-item-label,
.profile-section .ant-legacy-form-item-control-wrapper {
  &:not(.skill-popover-col) {
    // Ensures column rows stay the set length,
    display: block; // otherwise it will take up the full row.
    width: 100%;
  }
}

.quick-ref-area
  .ant-legacy-form-item-control
  div.editable-history-field-wrapper,
.profile-section
  .ant-legacy-form-item-control
  div.editable-history-field-wrapper {
  //padding-left: 1rem !important;
  background-color: @white-color;
  z-index: 1;
}

.quick-ref-area .ant-legacy-form-item-label > label,
.profile-section .ant-legacy-form-item-label > label {
  font-weight: @medium-weight;
}

.profile-section .recentWork-ctn {
  margin-bottom: 20px;
  .workHistoryHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-cog-icon {
      padding-right: 15px;
    }
    .y4ILZ95llAxkk8vWzHZfX {
      position: relative !important;
    }
  }
  .workHistoryGrid {
    border: solid 0.3px #f5f5f5;
  }
}

/* Layout elements */

#CommandBar {
  display: flex;
}

.share-popover,
.upload-file-button,
.view-other-resources {
  margin: 14px 0 0 1em;
}

.view-other-resources {
  margin-left: auto;

  &.ant-btn-link {
    color: @primary-color;

    &:hover {
      color: @primary-color !important;
    }
  }
}

.in-page-nav {
  display: flex;
  justify-content: center;

  .ant-anchor-wrapper {
    background: none;
  }

  .ant-anchor-ink:before {
    background-color: @white-color;
  }

  li {
    padding: 0.8rem 0 0.8rem 1.4rem;
  }
}

.talent-profile-ctn {
  .ant-anchor {
    width: 100%;

    &-wrapper {
      display: flex;
      justify-content: space-between; // Even distribution
      background: @white-color;
    }

    &-link {
      flex: 1;
      text-align: center;
      padding: 12px 8px;
      white-space: nowrap;
      min-width: 0; // Allows text truncation

      a {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: @text-color;
        transition: all 0.3s;

        &:hover {
          color: @primary-color;
        }
      }

      &-active a {
        color: @primary-color;
        font-weight: 500;
        position: relative;

        &::after {
          content: "";
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 24px;
          height: 3px;
          background: @primary-color;
          border-radius: 2px;
        }
      }
    }
  }

  /* Custom scrollbar (optional) */
  .ant-anchor-wrapper::-webkit-scrollbar {
    height: 4px;
  }

  .ant-anchor-wrapper::-webkit-scrollbar-thumb {
    background: @white-color;
    border-radius: 2px;
  }
}

/* Vertical layout (default) */
.profile-anchor-nav.vertical {
  .ant-anchor {
    &-wrapper {
      padding: 8px 0;
    }

    &-link {
      padding: 8px 0 8px 16px;
    }
  }
}

.talentProfilePage {
  .in-page-nav {
    a {
      color: @white-color !important;
    }
  }
}

.column-layout-ctn {
  flex-flow: column wrap;
  width: inherit;
}

.quick-ref-area,
.profile-section {
  h2 {
    margin-bottom: 1rem;
  }
}

.educationSectionContainer {
  .headingButton {
    float: right;
  }

  .sectionTitle {
    display: inline-block;
    margin: 0;
  }

  .editButton {
    margin: 10px;
    margin-top: -3em;

    &:focus {
      background-color: @white-color;
      color: @primary-color;
      border-color: @primary-color;
    }
  }

  .button {
    height: 28px !important;
    margin-left: 4px;
    border-color: #3072ab;
  }

  .buttonPrimary {
    color: white;
    background-color: #3072ab !important;
  }
}

.experienceSectionContainer {
  .headingButton {
    float: right;
  }

  .sectionTitle {
    display: inline-block;
    margin: 0;
  }

  .editButton {
    margin: 10px;
    margin-top: -3em;

    &:focus {
      background-color: @white-color;
      color: @primary-color;
      border-color: @primary-color;
    }
  }

  .button {
    height: 28px !important;
    margin-left: 4px;
    border-color: #3072ab;
  }

  .buttonPrimary {
    color: white;
    background-color: #3072ab !important;
  }
}

.experience,
.education {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .ant-legacy-form-item-control {
    margin-bottom: 0px;
  }
  .formInlineEdit {
    border: 1px solid rgb(255, 255, 255);
    box-shadow: @contextual-box-shadow;
    padding: 1rem;
  }
  .addDeleteController {
    display: flex;
    align-items: flex-start;
    height: 100%;
    gap: 10px;
    margin-top: 10px;
  }

  .container {
    width: 100%;
    display: flex;
    flex-direction: column;
    .role-row {
      .role-col {
        display: inline-block;
        vertical-align: middle;

        .strong {
          font-weight: bold;
        }
      }
      .icon-bullet {
        display: inline-block;
        vertical-align: middle;
        margin: 0px 8px -7px 8px;
        font-size: 15px;
      }
      .ant-col-16 {
        width: 100%;
      }
    }
    .readonlyControl {
      &.has-no-value {
        color: #767676;
        font-style: italic;
      }
    }
    .ant-col-1 {
      width: 2rem;
      padding-left: 0.4rem;
    }
  }
  .ant-legacy-form-item-label {
    text-align: left;
    label::after {
      visibility: hidden !important;
    }
  }

  .ant-legacy-form-item-control-wrapper {
    display: block;
    width: 75% !important;
    textarea.ant-input {
      height: 100px;
    }
  }
}

.profile-description {
  margin-bottom: 1.4em;
  .ant-row {
    .ant-legacy-form-item-control {
      margin-bottom: 0;
    }
  }
}

.section-ctn {
  display: flex;
  flex-flow: row nowrap;
  height: auto;
}

.quick-ref-area {
  width: 250px;
}

/* Skills */

.skills-ctn {
  h3.ant-typography {
    font-size: @heading-3-size;
  }
}

.profile-row {
  margin-bottom: @custom-margin-bottom;

  .ant-card-bordered {
    border: none;
    height: 100%;
  }
}

.profile-card-row {
  background-color: @white-color;
  border-radius: @custom-border-radius;
  box-shadow: @card-box-shadow;
  padding: 1em;
  margin-bottom: @custom-margin-bottom;
  // Modifier class: removes bottom padding for anchor comp
   &.profile-card-row--pb0 {
    padding-bottom: 0 !important;
  }
}

.skill-category {
  display: flex;
  flex-flow: column nowrap;
}

.skill-popover-row {
  margin-bottom: 0px;
}

.skill-popover-col > label > span {
  white-space: initial;
}

.attachments-message-area {
  width: 50%;
}

.navSideBar {
  list-style: none;
  padding: 0;
  margin: 0;
  border-left: 1px solid #fff;
  li {
    position: relative;
    display: flex;
    align-items: center;

    a {
      text-decoration: none;
    }
  }
  li.currentSection {
    &:before {
      content: "";
      width: 8px;
      height: 8px;
      background: #fff;
      position: absolute;
      left: -4px;
      border-radius: 50%;
    }
    a {
      font-weight: bold;
    }
  }
}

:local(.tpHeaderWrapper) {
  display: flex;
  align-items: center;
}

:local(.infoCircleTpHeader) {
  margin-left: 1.4rem;
}

:local(.moreInfoTooltip) {
  .ant-tooltip-arrow::before {
    background-color: @white-color;
  }

  .ant-tooltip-inner {
    background-color: @white-color;
    color: @black-color;
  }
}

//overwriting some wierd ant design styles
.talent-profile-ctn .ant-col-16 {
  max-width: 100% !important;
}

//overwriting details pane containerwidth
.talent-profile-ctn .ant-col-16 .readonlyControlContainer {
  width: 100% !important;
}

#CommandBar {
  .anticon-user {
    background-color: transparent;
    color: rgba(0, 0, 0, 0.88);
  }

  .ant-popover-inner[role="tooltip"] {
    padding: 0;

    & .ant-popover-title {
      border-bottom: 1px solid gainsboro;
      padding: 12px;
      margin: 0;
    }

    & .ant-popover-inner-content {
      padding: 12px;
    }
  }
}

.cMeRow {
  display: flex;
  flex-direction: column;
  align-items: end;
}

.profile-resume .ant-legacy-form-item-label {
  line-height: 1.6rem;
  margin-bottom: 25px !important;

  .ant-legacy-form-item-required > span {
    padding: unset;
  }

  // mandatory field asterisk
  .ant-legacy-form-item-required::before {
    margin: 0;
  }
}

.profile-resume .ant-form-item-control {
  margin-bottom: 0;
}

.profile-section {
  width: 100%;

  .ant-col {
    &:not(.skill-popover-col) {
      padding-right: 1.4em;
    }

    .ant-col {
      padding-right: 0;
    }

    &.profile-card-column {
      padding-right: 0.5em; //Space between the side by side panels in New TP
    }
  }
}

.talent-profile-documents {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.talent-profile-section-title {
  font-weight: 400;
  margin-bottom: 0.8rem;
}

.button-group {
  display: flex;
  gap: 25px;
  justify-content: flex-end;
  margin-top: -3.2em;
  padding-bottom: 20px;
}

.cMeProfileButton {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  text-decoration: none !important;
  padding-left: 2;

  &:hover {
    svg {
      transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      color: @primary-dark-hover-bg;
    }
  }
}

.infoCircleTpHeader {
  color: @main-text-color;
}
