import React from 'react';
import PropTypes from 'prop-types';
import { Spin } from 'antd';
import { Form } from '@ant-design/compatible';
import ProfilePictureArea from './profilePictureArea';
import { ProfileCard, ProfileCardTwoColumn } from './ProfileCard';
import ProfileSectionsContainer from './profileSectionsContainer';
import ProfilePageAnchorNav from './../../components/navigation/pageNavSection/enhancedPageNavSection';
import './styles.less';

/**
 * Root component for displaying a talent's complete profile.
 * Renders avatar, resume, recommendations, and other profile-related sections.
 */
class TalentProfileRoot extends React.Component {
    constructor(props) {
        super(props);
    }

    /**
     * Renders the anchor-based navigation component for profile sections.
     * @returns {JSX.Element}
     */
    renderAnchorNavigation() {
        const { talentProfileNavSectionLinks, talentProfilePageTransformedEnabled } = this.props;

        return (
            <ProfilePageAnchorNav
                links={talentProfileNavSectionLinks}
                containerClass="tp-profile-content-container"
            />
        );
    }

    render() {

        const { form, hasConfig, avatarProps, onAvatarClick, editingDisabled, showRecommendations, showCMeSection, showRecentWork } = this.props;
        const { recommendations, resume, employment, documents, additional, skillsSection, recentWorkSection, educationSection, experienceSection, cMeSection } = this.props.children({ form, editingDisabled, registerLink: this.props.registerLink, unRegisterLink: this.props.unRegisterLink });

        if (!hasConfig) {
            return (<Spin className="spin-light" size="large" />);
        }

        return (
            <div className="talent-profile-body">
                <div className="talent-profile-ctn">
                    <div className="column-layout-ctn">

                        {/* Header containing profile picture */}
                        <div className="header-ctn profile-card-row">
                            <ProfilePictureArea
                                onAvatarClick={onAvatarClick}
                                avatarProps={avatarProps}
                            />
                            {resume}
                        </div>

                        {/* Recommendations */}
                        {showRecommendations && (
                            <ProfileCard noCard="true">
                                {recommendations}
                            </ProfileCard>
                        )}

                        {/* Anchor Navigation */}
                        {/* Modifier class to remove bottom padding, aligning the anchor line at the bottom */}
                        <ProfileCard noCard additionalClassName='profile-card-row--pb0'>
                            {this.renderAnchorNavigation()}
                        </ProfileCard>

                        {/* Main Profile Sections */}
                        <div className="section-ctn">
                            <ProfileSectionsContainer className="profile-section">

                                {/* Recent Work - Full Width */}
                                {showRecentWork && (
                                    <ProfileCard containerClassName="recentWork-ctn" noCard="true">
                                        {recentWorkSection}
                                    </ProfileCard>
                                )}

                                {/* Employee details */}
                                <ProfileCard noCard="true">
                                    {employment}
                                </ProfileCard>

                                {/* Additional details */}
                                <ProfileCard noCard="true">
                                    {additional}
                                </ProfileCard>

                                {/* Experience & Education */}
                                <ProfileCardTwoColumn
                                    leftContent={experienceSection}
                                    rightContent={educationSection}
                                />

                                {/* Skills Section */}
                                <ProfileCard noCard="true">
                                    {skillsSection}
                                </ProfileCard>

                                {/* C-me Section */}
                                {showCMeSection && (
                                    <ProfileCard noCard="true">
                                        {cMeSection}
                                    </ProfileCard>
                                )}

                                {/* Documents Section */}
                                <ProfileCard noCard="true">
                                    {documents}
                                </ProfileCard>
                            </ProfileSectionsContainer>
                        </div>
                    </div>
                </div>
            </div >
        );
    }
}

TalentProfileRoot.propTypes = {
    form: PropTypes.object.isRequired,
    hasConfig: PropTypes.bool.isRequired,
    avatarProps: PropTypes.object,
    onAvatarClick: PropTypes.func,
    editingDisabled: PropTypes.bool,
    talentProfileNavSectionLinks: PropTypes.arrayOf(PropTypes.object),
    children: PropTypes.func.isRequired,
    registerLink: PropTypes.func,
    unRegisterLink: PropTypes.func,
    showRecommendations: PropTypes.bool,
    showCMeSection: PropTypes.bool,
    showRecentWork: PropTypes.bool
};

const TalentProfileRootCreated = Form.create({
    onFieldsChange: (props, fields) => props.onFieldsChange(props, fields),
    mapPropsToFields: (props) => (props.dataLoaded ? props.mapPropsToFields(props) : null)
})(TalentProfileRoot);

export default TalentProfileRootCreated;