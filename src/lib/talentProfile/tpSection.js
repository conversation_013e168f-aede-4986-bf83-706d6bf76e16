import React from 'react';
import { Typography, Row, Col } from 'antd';
import './styles.less';
import ProfileSectionsContainer from './profileSectionsContainer';
import styles from './styles.less';
import { TALENT_PROFILE_ALIAS, TALENT_PROFILE_SECTIONS } from '../../constants/talentProfileConsts';
import { ConnectedUploadFiles } from '../../connectedComponents/connectedAttachments/connectedUploadFiles';
import { TABLE_NAMES } from '../../constants';
import PropTypes from 'prop-types';

const wrapper = ({ className, children, id }) => <ProfileSectionsContainer className={className} id={id}>{children}</ProfileSectionsContainer>;
const noWrapper = ({ children }) => (<>{children}</>);

export default class TpSection extends React.Component {
    render() {
        const { id, title, noWrap, children, className, resourceId, talentProfilePageTransformedEnabled, hasEditFna } = this.props;

        const Root = true === noWrap ? noWrapper : wrapper;

        // If the title is 'Documents' and talent profile page feature flag is enabled then render title and ConnectedUploadFiles else render only title.
        const attachment = (title === TALENT_PROFILE_SECTIONS.ATTACHMENTS) && talentProfilePageTransformedEnabled ?
            (
                <div className="talent-profile-documents">
                    <Typography.Title level={2} className={styles.tpHeaderWrapper}>{title}</Typography.Title>
                    {hasEditFna &&
                        < ConnectedUploadFiles
                            moduleName={TALENT_PROFILE_ALIAS}
                            tableName={TABLE_NAMES.RESOURCE}
                            entityId={resourceId}
                        />
                    }
                </div>
            ) : <Typography.Title level={2} className={styles.tpHeaderWrapper}>{title}</Typography.Title>;

        return (
            <Root className={className} id={id}>
                {attachment}
                {children}
            </Root>
        );
    }
}

export class ColumsSection extends React.Component {

    buildColumnsRenderProps(columnsNum, columnMaxSize, childrenLen) {

        const renderProps = [];
        let columnIndex = 0;

        for (let i = 0; i < columnsNum; i++)
            renderProps.push([]);

        for (let i = 0; i < childrenLen; i++) {
            renderProps[columnIndex] = [...renderProps[columnIndex], i];
            // if (0 < columnMaxSize && columnMaxSize == (i % (columnIndex + 1)))
            if (renderProps[columnIndex].length === columnMaxSize)
                if (columnIndex++ === columnsNum)
                    break;
        }

        return renderProps;
    }

    getColContent(columnsRenderProps, rowIndex, colIndex, children) {
        let content = null;
        let childIndex = columnsRenderProps[colIndex][rowIndex];

        if (undefined !== childIndex)
            content = children[childIndex];

        return content;
    }

    renderSectionColumns() {

        const { columnsNum, columnMaxSize } = this.props;
        const children = this.props.children || [];
        const childrenLength = children.length;
        const columnsRenderProps = this.buildColumnsRenderProps(columnsNum, columnMaxSize, childrenLength);

        const rows = [];
        const rowCount = columnsRenderProps[0] ? columnsRenderProps[0].length : 0;
        for (let i = 0; i < rowCount; i++) {

            let cols = [];
            const colSpan = 24 / columnsNum;
            for (let j = 0; j < columnsNum; j++)
                cols.push(<Col key={j} span={colSpan}>{this.getColContent(columnsRenderProps, i, j, children)}</Col>);

            rows.push(<Row key={i}>{cols}</Row>);
        }

        return rows;
    }

    render() {
        return (
            <TpSection {...this.props}>
                {this.renderSectionColumns()}
            </TpSection>
        );
    }
}

export class TpAttachmentsSection extends React.Component {
    render() {
        const { hasEditFna, resourceId, moduleName, tableName, getAttachmentSectionComponent, ...props } = this.props;
        const AttachmentsSection = getAttachmentSectionComponent();

        return (
            <TpSection {...this.props}>
                <AttachmentsSection moduleName={moduleName} tableName={tableName} entityId={resourceId} readonly={!hasEditFna} />
            </TpSection>
        );
    }
}

ColumsSection.propTypes = {
    columnsNum: PropTypes.number.isRequired,
    columnMaxSize: PropTypes.number,
    children: PropTypes.node
};

TpSection.propTypes = {
    id: PropTypes.string,
    title: PropTypes.string,
    noWrap: PropTypes.bool,
    children: PropTypes.node,
    className: PropTypes.string,
    resourceId: PropTypes.string,
    talentProfilePageTransformedEnabled: PropTypes.bool,
    hasEditFna: PropTypes.bool
};

TpAttachmentsSection.propTypes = {
    resourceId: PropTypes.string,
    hasEditFna: PropTypes.bool,
    moduleName: PropTypes.string,
    tableName: PropTypes.string,
    getAttachmentSectionComponent: PropTypes.func.isRequired
};
