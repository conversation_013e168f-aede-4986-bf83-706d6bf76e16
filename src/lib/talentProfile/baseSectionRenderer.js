import React from 'react';
import { Spin } from 'antd';
import { TALENT_PROFILE_SECTIONS, talentProfileAnchorLinks } from '../../constants/talentProfileConsts';
import { FieldControl } from '../fieldControl';

const displayTypesToOmit = [talentProfileAnchorLinks.Skills, talentProfileAnchorLinks.RecentWork, talentProfileAnchorLinks.Education, talentProfileAnchorLinks.Experience, talentProfileAnchorLinks.cMeSectionType];

const createSectionsFilter = (attachmentsEnabled = false) => section =>
    !displayTypesToOmit.includes(section.DisplayType) &&
    (section.Fields.length || (section.DisplayType === TALENT_PROFILE_SECTIONS.ATTACHMENTS && attachmentsEnabled));

/**
 * Base class used to render 'Documents'  'Additional Details' and 'Employment Details'
 * @class BaseSectionRenderer
 * @typedef {BaseSectionRenderer}
 * @extends {React.Component}
 */
class BaseSectionRenderer extends React.Component {
    SectionSpecificFieldOptionsMap = {
        [TALENT_PROFILE_SECTIONS.INFO]: { showLabel: false }
    };

    // Defines which section to render
    sectionRenderers = {};

    createSectionField = (
        tableName,
        configField,
        fieldsOptions,
        entity,
        uiEntity,
        index,
        avatarConfig,
        errorMessages
    ) => {
        const field = {
            name: configField.FieldName,
            table: tableName,
            icon: configField.Icon
        };

        const fieldInfo = fieldsOptions.getFieldInfo(tableName, field.name);
        const hasEditableAccess = fieldsOptions.hasEditableAccess(fieldInfo);

        const isContextuallyEditable =
            hasEditableAccess &&
            uiEntity[field.name]?.isContextuallyEditable &&
            !fieldsOptions.editingDisabled;

        const options = {
            ...fieldsOptions,
            showReadOnlyTooltip: !isContextuallyEditable,
            suggestions: (fieldsOptions.suggestions || {})[field.name] || [],
            isContextuallyEditable,
            isContextuallyEditing: uiEntity[field.name]?.isContextuallyEditing,
            isInAutoCompleteState: fieldsOptions.isInAutoCompleteState(field.name),
            avatarConfig,
            errorMessages
        };

        return (
            <FieldControl
                key={index}
                options={options}
                field={field}
                entity={entity}
                uiEntity={uiEntity}
            />
        );
    };

    createSection = (
        type,
        props,
        tableName,
        fields = [],
        fieldsOptions,
        entity,
        uiEntity,
        avatarConfig,
        errorMessages,
        getAttachmentSectionComponent
    ) => {
        const sectionSpecificFieldOptions = this.SectionSpecificFieldOptionsMap[type] || {};
        const mergedOptions = { ...fieldsOptions, ...sectionSpecificFieldOptions };

        const sectionFields = fields.map((field, index) =>
            this.createSectionField(tableName, field, mergedOptions, entity, uiEntity, index, avatarConfig, errorMessages));

        const renderFn = this.sectionRenderers[type];
        if (typeof renderFn === 'function') {
            return renderFn({ props, sectionFields, getAttachmentSectionComponent });
        }

        return null;
    };

    renderSections = () => {
        const {
            config,
            attachmentsEnabled = false,
            dataLoaded,
            registerLink,
            form,
            getFieldInfo,
            getLinkedData,
            suggestions,
            isInAutoCompleteState,
            getTableAlias,
            getFieldBlankValue,
            getFieldPrefix,
            getFieldSuffix,
            talentProfilePageTransformedEnabled,
            staticLabels,
            onContextualEditStart,
            onContextualEditApply,
            editingDisabled,
            onContextualEditCancel,
            onAutoCompleteSearchSuggest,
            onAutoCompleteDropdownVisibilityChange,
            onAutoCompleteInput,
            moduleName,
            resourceId,
            getHasFunctionalAccess,
            hasEditableAccess,
            showReadOnlyTooltip,
            hasEditFna,
            entity,
            uiEntity,
            avatarConfig,
            errorMessages,
            getAttachmentSectionComponent,
            tableName
        } = this.props;

        const fieldsOptions = {
            getFieldInfo,
            getLinkedData,
            getFieldDecorator: form?.getFieldDecorator,
            form,
            suggestions,
            onAutoCompleteDropdownVisibilityChange,
            onAutoCompleteInput,
            onAutoCompleteSearchSuggest,
            onContextualEditStart,
            onContextualEditApply,
            onContextualEditCancel,
            isInAutoCompleteState,
            getTableAlias,
            getFieldBlankValue,
            getFieldPrefix,
            getFieldSuffix,
            talentProfilePageTransformedEnabled,
            staticLabels,
            editingDisabled,
            showReadOnlyTooltip,
            getHasFunctionalAccess,
            moduleName,
            hasEditableAccess
        };

        const sectionsFilter = createSectionsFilter(attachmentsEnabled);

        if (!dataLoaded) return <Spin className="spin" size="large" />;

        if (registerLink) {
            const links = config
                .filter(sectionsFilter)
                .map((section, index) => ({
                    id: section.SectionKey,
                    title: section.SectionTitle,
                    index: index + 1
                }))
                .reduce((acc, { id, title, index }) => {
                    acc[id] = { title, index };

                    return acc;
                }, {});

            const { ATTACHMENTS } = TALENT_PROFILE_SECTIONS;

            const documentConfig = config.find(cfg => cfg.SectionTitle === ATTACHMENTS);

            // Register link for the 'Documents' to be at the last
            if (documentConfig) {
                setTimeout(() => {
                    registerLink({
                        [documentConfig.SectionKey]: {
                            title: documentConfig.SectionTitle,
                            index: 7
                        }
                    });
                }, 0);
            }

            // Filter out the 'Documents' section and return only 'Employee Details' and 'Additional Details'
            const linksWithoutDocuments = Object.fromEntries(
                Object.entries(links).filter(([, value]) => value.title !== ATTACHMENTS)
            );

            setTimeout(() => registerLink(linksWithoutDocuments), 0);
        }

        return config
            .filter(sectionsFilter)
            .map((section, index) => {
                const {
                    SectionTitle: title,
                    SectionKey: id,
                    DisplayType: type,
                    NumberOfColumns: columnsNum,
                    MaxFieldsPerColumn: columnMaxSize
                } = section;

                return this.createSection(
                    type,
                    {
                        moduleName,
                        tableName,
                        resourceId,
                        title,
                        index,
                        id,
                        type,
                        columnsNum,
                        columnMaxSize,
                        hasEditFna,
                        talentProfilePageTransformedEnabled
                    },
                    tableName,
                    section.Fields,
                    fieldsOptions,
                    entity,
                    uiEntity,
                    avatarConfig,
                    errorMessages,
                    getAttachmentSectionComponent
                );
            });
    };

    render() {
        return <>{this.renderSections()}</>;
    }
}

export default BaseSectionRenderer;
