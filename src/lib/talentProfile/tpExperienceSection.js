import React from 'react';
import TpSection from './tpSection';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Divider } from 'antd';
import { TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import ConnectedExperienceForm from '../../connectedComponents/connectedExperience/connectedExperienceForm';
import { getTpSectionTitle } from '../../utils/talentProfileUtils';
class TpExperienceSection extends React.Component {
    constructor(props) {
        super(props);
        const { visible } = props;
        this.state = {
            visible: visible
        };
        this.onEditClick = this.onEditClick.bind(this);
    }

    componentDidMount() {
        const { id, config, visible, talentProfilePageTransformedEnabled } = this.props;
        const title = config.SectionTitle;
        visible && setTimeout(() => this.props.registerLink({ [id]: { title, index: talentProfilePageTransformedEnabled ? 3 : 6 } }), 0);
    }

    onEditClick() {
        const { onEditClick, hasExperienceData } = this.props;
        onEditClick(!hasExperienceData);
    }

    render() {
        const { config = {}, visible = true, editable, buttonLabel, hasExperienceData = false, disableAddEditButton = false, experienceAuditInfo = {}, talentProfilePageTransformedEnabled } = this.props;
        const { SectionKey = '', SectionTitle = '' } = config;
        return (
            visible && (
                <TpSection {...this.props}
                    title={getTpSectionTitle(SectionTitle, experienceAuditInfo)}
                    id={SectionKey}
                    className={'experienceSectionContainer'}
                >
                    {editable && (
                        <Button
                            id="tp-add-edit-education-button"
                            type="secondary"
                            className={'headingButton editButton'}
                            onClick={this.onEditClick}
                            disabled={disableAddEditButton}>
                            {buttonLabel}
                        </Button>
                    )}
                    <ConnectedExperienceForm
                        alias={TALENT_PROFILE_ALIAS}
                        editable={editable}
                        talentProfilePageTransformedEnabled={talentProfilePageTransformedEnabled}
                    />
                    {/* Show divider only when there is no data and the feature flag is disabled */}
                    {!hasExperienceData && !talentProfilePageTransformedEnabled && <Divider className="divider" />}
                </TpSection>
            )
        );
    }
}

TpExperienceSection.propTypes = {
    id: PropTypes.string,
    config: PropTypes.object,
    visible: PropTypes.bool,
    registerLink: PropTypes.func,
    recentWorkConfig: PropTypes.object,
    visibleRecentWork: PropTypes.bool,
    getFeatureFlag:PropTypes.func
};

export default TpExperienceSection;