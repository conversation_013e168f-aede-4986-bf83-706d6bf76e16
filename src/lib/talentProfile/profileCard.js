import React from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Card } from 'antd';
import './styles.less';

// Helper function to wrap content with container div if needed
const wrapContent = (content, containerClassName) =>
    containerClassName ? <div className={containerClassName}>{content}</div> : content;

/**
 * ProfileCard - A reusable card component with Ant Design Row/Col/Card layout
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The content to display inside the card
 * @param {string} [props.className='cards-stn'] - CSS class name for the Card component
 * @param {string} [props.containerClassName=''] - CSS class name for the container div wrapper
 * @param {number} [props.colSpan=24] - Column span (1-24) for Ant Design Col component
 * @param {number[]} [props.gutter=[0,0]] - Gutter spacing for Row component [horizontal, vertical]
 * @param {boolean} [props.noCard=false] - If true, removes Card wrapper and applies styling to each column
 * @returns {React.ReactElement} A card component wrapped in Row/Col layout
 */
const ProfileCard = ({
    children,
    className = 'cards-stn',
    additionalClassName='',
    containerClassName = '',
    colSpan = 24,
    gutter = [0, 0],
    noCard = false
}) => {
const rowClassNames = `profile-row profile-card-row ${additionalClassName}`.trim();
    if (noCard) {
        // Apply card styling to the row itself
        return (
            <Row gutter={gutter} className={rowClassNames}>
                <Col span={colSpan}>
                    {wrapContent(children, containerClassName)}
                </Col>
            </Row>
        );
    }

    return (<Row gutter={gutter} className={rowClassNames}>
        <Col span={colSpan}>
            <Card className={className}>
                {wrapContent(children, containerClassName)}
            </Card>
        </Col>
    </Row>
    );
};

/**
 * ProfileCardTwoColumn - A two-column card layout component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.leftContent - Content for the left column
 * @param {React.ReactNode} props.rightContent - Content for the right column
 * @param {number[]} [props.gutter=[16,0]] - Gutter spacing for Row component [horizontal, vertical]
 * @param {string} [props.className='cards-stn'] - CSS class name for both Card components
 * @param {string} [props.leftContainerClassName=''] - CSS class name for left column container div
 * @param {string} [props.rightContainerClassName=''] - CSS class name for right column container div
 * @param {boolean} [props.noCard=false] - If true, removes Card wrapper and applies styling to each column
 * @returns {React.ReactElement} A two-column card layout with responsive breakpoints
 */
const ProfileCardTwoColumn = ({
    leftContent,
    rightContent,
    gutter = [16, 0],
    className = 'cards-stn',
    leftContainerClassName = '',
    rightContainerClassName = '',
    noCard = false
}) => {
    if (noCard) {
        // Apply card styling to each column
        return (
            <Row gutter={gutter} className="profile-row">
                <Col xs={24} md={12} className="profile-card-column">
                    {wrapContent(leftContent, leftContainerClassName)}
                </Col>
                <Col xs={24} md={12} className="profile-card-column">
                    {wrapContent(rightContent, rightContainerClassName)}
                </Col>
            </Row>
        );
    }

    return (<Row gutter={gutter} className="profile-row">
        <Col xs={24} md={12} className="profile-card-column">
            <Card className={className}>
                {wrapContent(leftContent, leftContainerClassName)}
            </Card>
        </Col>
        <Col xs={24} md={12} className="profile-card-column">
            <Card className={className}>
                {wrapContent(rightContent, rightContainerClassName)}
            </Card>
        </Col>
    </Row>
    );
};

// PropTypes
ProfileCard.propTypes = {
    children: PropTypes.node.isRequired,
    className: PropTypes.string,
    containerClassName: PropTypes.string,
    colSpan: PropTypes.number,
    gutter: PropTypes.arrayOf(PropTypes.number),
    noCard: PropTypes.bool
};
ProfileCardTwoColumn.propTypes = {
    leftContent: PropTypes.node.isRequired,
    rightContent: PropTypes.node.isRequired,
    gutter: PropTypes.arrayOf(PropTypes.number),
    className: PropTypes.string,
    leftContainerClassName: PropTypes.string,
    rightContainerClassName: PropTypes.string,
    noCard: PropTypes.bool
};

export { ProfileCard, ProfileCardTwoColumn };