import React from 'react';
import { getFieldAlias } from '../../utils/commonUtils';
import { getFieldReadOnlyValue, getFieldControlValue, shouldShowExplanation } from '../../utils/fieldControlUtils';
import { MessageArea } from '../messageArea';
import { FieldControlLabelProps, FieldControlMessagesProps, FieldControlValueExplanationProps } from '../propTypes/fieldControl.propTypes';
import { ENTITY_WINDOW_MODULES, TABLE_NAMES } from '../../constants';
import { BOOKING_DATE_RANGE } from '../../constants/fieldConsts';
import { BILLING_TYPE_FIXED_PRICE, ENTITY_WINDOW_OPERATIONS } from '../../constants/entityWindowConsts';
import { EDIT_REPEAT_BOOKING_TYPE, FEATURE_FLAGS } from '../../constants/globalConsts';
import { jobBillingTypeField, jobFixedPriceField } from '../../state/entityWindow/fieldsConfig';

export const BaseFieldControlLabel = ({ field, options }) => {
    const { entity, moduleName, getFieldInfo, getLinkedData, className = '' } = options;
    const { table, name, label, actualFieldName, labelSuffix } = field;

    const fieldName = actualFieldName ? actualFieldName : name;
    const fieldInfo = getFieldInfo(table, fieldName);
    const fieldAlias = getFieldAlias(fieldInfo);
    const fieldLabel = (fieldAlias ? fieldAlias : fieldInfo.name) || label;

    const displayValue = labelSuffix ? `${labelSuffix} ${fieldLabel}` : fieldLabel;
    let fieldLabelInfo = '';
    const talentProfilePageTransformedEnabled = options.talentProfilePageTransformedEnabled ?? false;
    if (name === jobFixedPriceField.name) {
        const isEnabledBudgetHoursAndRevenue = options.getFeatureFlag(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE);
        if (isEnabledBudgetHoursAndRevenue) {
            const billingType = getLinkedData(jobBillingTypeField.name, entity[jobBillingTypeField.name], table, moduleName);
            if (billingType && billingType.value === BILLING_TYPE_FIXED_PRICE) {
                fieldLabelInfo = BILLING_TYPE_FIXED_PRICE.toLowerCase();
            }
        }
    }

    return (
        <span className={className} id={name}>
            {displayValue}
            {fieldLabelInfo ? ` (${fieldLabelInfo})` : ''}
            {/* Show colon only if old TP is enabled */}
            {!talentProfilePageTransformedEnabled && ':'}
        </span>
    );
};
BaseFieldControlLabel.propTypes = FieldControlLabelProps;

const withFieldPopulatedStyling = (WrappedComponent) => {
    const ProxyComponentWithFieldPopulatedStyling = (wrappedComponentProps) => {
        const { entity, uiEntity, field, options } = wrappedComponentProps;
        const { table, name, actualFieldName } = field;

        const { getFieldInfo, getLinkedData } = options;
        const fieldName = actualFieldName ? actualFieldName : name;
        const fieldInfo = getFieldInfo(table, fieldName);
        const fieldValue = getFieldControlValue(entity, uiEntity, field, fieldInfo, getFieldInfo, getLinkedData);
        const className = fieldValue ? 'field-has-data' : 'field-has-no-data';

        return <WrappedComponent {...wrappedComponentProps} className={className} />;
    };

    return ProxyComponentWithFieldPopulatedStyling;
};

export const FieldControlLabel = withFieldPopulatedStyling(BaseFieldControlLabel);
FieldControlLabel.propTypes = FieldControlLabelProps;

export const FieldControlValueExplanation = ({ field, entity, uiEntity, options }) => {
    const { table: tableName, name: fieldName } = field;
    let {
        getFieldInfo,
        getLinkedData,
        getFieldValueExplanation,
        readonly,
        repeatBookingExplanation,
        moduleName,
        getFieldValueCaption
    } = options;

    const isEnabledBudgetHoursAndRevenue = options.getFeatureFlag ? options.getFeatureFlag(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE) : false;

    const getExplanations = () => {
        if (!shouldShowExplanation(field, options, uiEntity)) {
            return null;
        }

        const fieldInfo = getFieldInfo(tableName, fieldName);
        const fieldValue = getFieldReadOnlyValue(entity, uiEntity, fieldInfo, getLinkedData, getFieldInfo);
        const explanation = getFieldValueExplanation ? getFieldValueExplanation(fieldName, fieldValue) : '';

        const getRepeatBookingSummaryExplanation = () => {
            if (options.editRepeatBookingType === EDIT_REPEAT_BOOKING_TYPE.SELECTED_ONLY && options.operation === ENTITY_WINDOW_OPERATIONS.EDIT) {
                return (
                    <span>{explanation && (', ')}{repeatBookingExplanation}</span>
                );
            } else if (options.operation === ENTITY_WINDOW_OPERATIONS.CREATE
                || (options.operation === ENTITY_WINDOW_OPERATIONS.EDIT && options.editRepeatBookingType === EDIT_REPEAT_BOOKING_TYPE.SELECTED_AND_FUTURE)
                || (options.operation === ENTITY_WINDOW_OPERATIONS.EDIT && options.editRepeatBookingType === EDIT_REPEAT_BOOKING_TYPE.ALL)) {
                return (
                    <>{explanation && (', ')}<a onClick={() => options.onOpenRepeatBookingsModal(tableName, options.collectionAlias)}>{repeatBookingExplanation}</a></>
                );
            }
        };

        const bookingStartDate = entity?.booking_start || '';
        const bookingEndDate = entity?.booking_end || '';
        const plannerPageModal = moduleName === ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL;
        const tableViewModal = moduleName === ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL;
        const bookingsTable = tableName === TABLE_NAMES.BOOKING;
        const dateRangeField = fieldName === BOOKING_DATE_RANGE;
        const dateRangeExplanation = (plannerPageModal || tableViewModal) && !readonly && bookingStartDate && bookingEndDate && bookingsTable && dateRangeField;

        return (
            <div>
                {
                    Array.isArray(explanation)
                        ? explanation.map((ex) => (
                            <div aria-live="polite" key={ex.explanation} className={ex.primary ? 'fieldControlPrimaryExplanation' : ''} >
                                {ex.explanation}
                            </div>
                        ))
                        : (
                            <>
                                {explanation}
                                {dateRangeExplanation && getRepeatBookingSummaryExplanation()}
                            </>
                        )
                }
            </div>
        );
    };

    const getCaption = () => {
        if (!getFieldValueCaption || !field.caption || Object.keys(field.caption).length <= 0) {
            return null;
        }

        const { dependant, percentage, translationSection } = field.caption;

        if (!(dependant in entity) || entity[dependant] === null || !(percentage in entity)) {
            return null;
        }

        return (
            <div>{getFieldValueCaption(entity[percentage], translationSection, options.getFieldCaption)}</div>
        );
    };

    if (!getExplanations() && !getCaption()) {
        return null;
    }

    return (
        <div className="fieldControlValueExplanation">
            {getExplanations()}
            {isEnabledBudgetHoursAndRevenue && getCaption()}
        </div>
    );
};

FieldControlValueExplanation.propTypes = FieldControlValueExplanationProps;

export const FieldControlMessages = ({ fieldMessages }) => {

    return (
        <>
            <MessageArea messages={fieldMessages} />
        </>
    );
};

FieldControlMessages.propTypes = FieldControlMessagesProps;