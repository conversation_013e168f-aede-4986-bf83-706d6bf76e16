/* eslint-disable react/display-name */
import React from 'react';
import { CompletedMilestone, OverdueMilestone, UnfinishedMilestone } from './milestones';
/**
 * Immutable dictionary object describing the custom SVG components to swap with Ant icons.
 * These are called using the Icon wrapper component through the "type" property.
 * To add a new SVG, make a new entry in this dictionary and use this tool to custom strip your markup:
 * https://jakearchibald.github.io/svgomg/. Replace icons in ant or create new entries.
 */

const IconDefinitions = {
    'caret-up on': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="#2d2d2d" width="1em" height="1em" viewBox="0 0 18 24">
            <path fill="#888" d="M17.1916514 14.04931389c0 .19498324-.0706506.39199756-.2119518.55651467L9.4532008 23.2135262c-.18325.2092007-.459229.3310653-.7528706.3310653-.2914338 0-.5696206-.1218646-.7506628-.3310653L.4629096 14.64238792C.12952702 14.26054574.19576197 13.702.6108344 13.39530761c.4150723-.30669239 1.0222261-.24576013 1.3556087.13608205L8.702538 21.2413519l6.7736281-7.74855267c.3333826-.38184218.9405363-.44277444 1.3556087-.13608205.236238.17670356.3598766.43261906.3598766.69259671z" />
            <path d="M17.1916511 9.95068611c0-.19498324-.0706507-.39199756-.2119519-.55651467L9.45320049.7864738C9.26995045.5772731 8.99397147.4554085 8.70032983.4554085c-.2914338 0-.56962061.1218646-.75066282.3310653L.46290927 9.35761208C.12952666 9.73945426.19576162 10.298.610834 10.60469239c.41507239.30669239 1.02222614.24576013 1.35560875-.13608205L8.70253767 2.7586481l6.77362803 7.74855267c.3333826.38184218.9405364.44277444 1.3556088.13608205.236238-.17670356.3598766-.43261906.3598766-.69259671z" />
        </svg>
    ),
    'caret-down on': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="#000" width="1em" height="1em" viewBox="0 0 18 24">
            <path fill="#888" d="M17.1916514 9.95068611c0-.19498324-.0706506-.39199756-.2119518-.55651467L9.4532008.7864738c-.18325-.2092007-.459229-.3310653-.7528706-.3310653-.2914338 0-.5696206.1218646-.7506628.3310653L.4629096 9.35761208C.12952702 9.73945426.19576197 10.298.6108344 10.60469239c.4150723.30669239 1.0222261.24576013 1.3556087-.13608205L8.702538 2.7586481l6.7736281 7.74855267c.3333826.38184218.9405363.44277444 1.3556087.13608205.236238-.17670356.3598766-.43261906.3598766-.69259671z" />
            <path d="M17.1916511 14.04931389c0 .19498324-.0706507.39199756-.2119519.55651467L9.45320049 23.2135262c-.18325004.2092007-.45922902.3310653-.75287066.3310653-.2914338 0-.56962061-.1218646-.75066282-.3310653L.46290927 14.64238792C.12952666 14.26054574.19576162 13.702.610834 13.39530761c.41507239-.30669239 1.02222614-.24576013 1.35560875.13608205l6.73609492 7.70996224 6.77362803-7.74855267c.3333826-.38184218.9405364-.44277444 1.3556088-.13608205.236238.17670356.3598766.43261906.3598766.69259671z" />
        </svg>
    ),
    'left': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 15 24">
            <path d="M13.717 24c-.282 0-.566-.1-.804-.3L.478 13.037A1.404 1.404 0 010 11.971c0-.413.176-.807.478-1.064L12.86.301c.552-.472 1.359-.379 1.802.21.443.588.355 1.448-.197 1.92L3.327 11.974l11.194 9.596c.552.472.64 1.332.197 1.92-.256.335-.625.51-1 .51z" />
        </svg>
    ),
    'right': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 15 24">
            <path d="M1.283 24c.282 0 .566-.1.804-.3l12.435-10.663c.302-.26.478-.65.478-1.066 0-.413-.176-.807-.478-1.064L2.14.301C1.588-.171.78-.078.338.511c-.443.588-.355 1.448.197 1.92l11.138 9.543L.479 21.57c-.552.472-.64 1.332-.197 1.92.256.335.625.51 1 .51z" />
        </svg>
    ),
    'up': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 25 16">
            <path d="M24.5 14.217c0-.282-.1-.566-.3-.804L13.537.978A1.404 1.404 0 0012.471.5c-.413 0-.807.176-1.064.478L.801 13.36c-.472.552-.379 1.359.21 1.802.588.443 1.448.355 1.92-.197l9.543-11.138 9.596 11.194c.472.552 1.332.64 1.92.197.335-.256.51-.625.51-1z" />
        </svg>
    ),
    'down': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 25 16">
            <path d="M24.5 1.783c0 .282-.1.566-.3.804L13.537 15.022c-.26.302-.65.478-1.066.478-.413 0-.807-.176-1.064-.478L.801 2.64C.329 2.088.422 1.28 1.011.838c.588-.443 1.448-.355 1.92.197l9.543 11.138L22.07.979c.472-.552 1.332-.64 1.92-.197.335.256.51.625.51 1z" />
        </svg>
    ),
    'user': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M14.7 13.5c-1.345 0-1.992.75-4.2.75s-2.85-.75-4.2-.75A6.302 6.302 0 000 19.8v1.95A2.25 2.25 0 002.25 24h16.5A2.25 2.25 0 0021 21.75V19.8c0-3.478-2.822-6.3-6.3-6.3zm4.8 8.25c0 .413-.337.75-.75.75H2.25a.752.752 0 01-.75-.75V19.8c0-2.648 2.152-4.8 4.8-4.8.919 0 1.833.75 4.2.75 2.363 0 3.281-.75 4.2-.75 2.648 0 4.8 2.152 4.8 4.8v1.95zm-9-9.75a6 6 0 100-12 6 6 0 100 12zm0-10.5C12.98 1.5 15 3.52 15 6s-2.02 4.5-4.5 4.5S6 8.48 6 6s2.02-4.5 4.5-4.5z" />
        </svg>
    ),
    'user-filled': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" height="0.75em" viewBox="0 0 448 512">
            <path d="M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"/>
        </svg>
    ),
    'avatar': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M12 0C5.371 0 0 5.371 0 12s5.371 12 12 12 12-5.371 12-12S18.629 0 12 0zm6.194 20.4A10.397 10.397 0 0112 22.452c-2.318 0-4.456-.77-6.194-2.052v-.658a3.1 3.1 0 013.097-3.097c.537 0 1.33.552 3.097.552 1.771 0 2.555-.552 3.097-.552a3.1 3.1 0 013.097 3.097v.658zm1.48-1.33c-.329-2.246-2.24-3.973-4.577-3.973-.992 0-1.471.551-3.097.551s-2.1-.551-3.097-.551c-2.337 0-4.248 1.727-4.577 3.972A10.395 10.395 0 011.548 12C1.548 6.237 6.238 1.548 12 1.548c5.763 0 10.452 4.69 10.452 10.452 0 2.729-1.06 5.206-2.778 7.07zM12 5.42a4.258 4.258 0 100 8.516 4.258 4.258 0 000-8.517zm0 6.967a2.711 2.711 0 01-2.71-2.71A2.711 2.711 0 0112 6.967a2.711 2.711 0 012.71 2.71 2.711 2.711 0 01-2.71 2.71z" />
        </svg>
    ),
    'client': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 15.5 15.06429">
            <path d="M10,1.5v3h1v-3A1.5,1.5,0,0,0,9.5,0h-8A1.5,1.5,0,0,0,0,1.5V13.75A.25.25,0,0,0,.25,14h.5A.25.25,0,0,0,1,13.75V1.5A.5.5,0,0,1,1.5,1h8A.5.5,0,0,1,10,1.5ZM6.375,3A.375.375,0,0,0,6,3.375v1.25A.375.375,0,0,0,6.375,5h1.25A.375.375,0,0,0,8,4.625V3.375A.375.375,0,0,0,7.625,3ZM4.625,6H3.375A.375.375,0,0,0,3,6.375v1.25A.375.375,0,0,0,3.375,8h1.25A.375.375,0,0,0,5,7.625V6.375A.375.375,0,0,0,4.625,6Zm0,3H3.375A.375.375,0,0,0,3,9.375v1.25A.375.375,0,0,0,3.375,11h1.25A.375.375,0,0,0,5,10.625V9.375A.375.375,0,0,0,4.625,9Zm0-6H3.375A.375.375,0,0,0,3,3.375v1.25A.375.375,0,0,0,3.375,5h1.25A.375.375,0,0,0,5,4.625V3.375A.375.375,0,0,0,4.625,3ZM7.82906,6.82563a3.0256,3.0256,0,0,1,.16125-.49844A.37063.37063,0,0,0,7.625,6H6.375A.375.375,0,0,0,6,6.375v1.25A.375.375,0,0,0,6.375,8H7.55781Z" transform="translate(0 0)"/>
            <path d="M12.95,11.11786c-.54453,0-.80636.30357-1.7.30357s-1.15357-.30357-1.7-.30357a2.55066,2.55066,0,0,0-2.55,2.55v.48571a.911.911,0,0,0,.91071.91072h6.67858A.911.911,0,0,0,15.5,14.15357v-.48571A2.55066,2.55066,0,0,0,12.95,11.11786Zm1.63929,3.03571H7.91071v-.48571A1.64215,1.64215,0,0,1,9.55,12.02857a7.42859,7.42859,0,0,0,1.7.30357,7.35719,7.35719,0,0,0,1.7-.30357,1.64215,1.64215,0,0,1,1.63929,1.63929ZM11.25,10.81429A2.73215,2.73215,0,1,0,8.51786,8.08214,2.73285,2.73285,0,0,0,11.25,10.81429Zm0-4.55357A1.82143,1.82143,0,1,1,9.42857,8.08214,1.82433,1.82433,0,0,1,11.25,6.26072Z" transform="translate(0 0)"/>
        </svg>
    ),
    'calendar': () => (
        <svg viewBox="64 64 896 896" focusable="false" data-icon="calendar" width="1em" height="1em" fill="#607085" aria-hidden="true">
            <path d="M112 880c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V460H112v420zm768-696H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v176h800V216c0-17.7-14.3-32-32-32z"></path>
        </svg>
    ),
    'calendar-range': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M18.75 3H16.5V.375A.376.376 0 0016.125 0h-.75A.376.376 0 0015 .375V3H6V.375A.376.376 0 005.625 0h-.75A.376.376 0 004.5.375V3H2.25A2.25 2.25 0 000 5.25v16.5A2.25 2.25 0 002.25 24h16.5A2.25 2.25 0 0021 21.75V5.25A2.25 2.25 0 0018.75 3zm.75 18.75c0 .413-.337.75-.75.75H2.25a.752.752 0 01-.75-.75V9h18v12.75zm0-14.25h-18V5.25c0-.412.337-.75.75-.75h16.5c.413 0 .75.338.75.75V7.5zm-15.75 9h13.5c.413 0 .75-.337.75-.75v-4.5a.752.752 0 00-.75-.75H3.75a.752.752 0 00-.75.75v4.5c0 .413.337.75.75.75zM4.5 12h12v3h-12v-3z" />
        </svg>
    ),
    'booking': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 16">
            <path d="M17.667 4H6.333a1 1 0 00-1 1v6a1 1 0 001 1h11.334a1 1 0 001-1V5a1 1 0 00-1-1zm-.334 6.667H6.667V5.333h10.666v5.334zm5.334-4H24V2a2 2 0 00-2-2H2a2 2 0 00-2 2v4.667h1.333a1.333 1.333 0 010 2.666H0V14a2 2 0 002 2h20a2 2 0 002-2V9.333h-1.333a1.333 1.333 0 010-2.666zm0 4V14a.667.667 0 01-.667.667H2A.667.667 0 011.333 14v-3.333A2.67 2.67 0 004 8a2.67 2.67 0 00-2.667-2.667V2c0-.368.3-.667.667-.667h20c.368 0 .667.3.667.667v3.333A2.67 2.67 0 0020 8a2.67 2.67 0 002.667 2.667z" />
        </svg>
    ),
    'booking-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 576 512">
            <path d="M64 64C28.7 64 0 92.7 0 128v64c0 8.8 7.4 15.7 15.7 18.6C34.5 217.1 48 235 48 256s-13.5 38.9-32.3 45.4C7.4 304.3 0 311.2 0 320v64c0 35.3 28.7 64 64 64H512c35.3 0 64-28.7 64-64V320c0-8.8-7.4-15.7-15.7-18.6C541.5 294.9 528 277 528 256s13.5-38.9 32.3-45.4c8.3-2.9 15.7-9.8 15.7-18.6V128c0-35.3-28.7-64-64-64H64zm64 112l0 160c0 8.8 7.2 16 16 16H432c8.8 0 16-7.2 16-16V176c0-8.8-7.2-16-16-16H144c-8.8 0-16 7.2-16 16zM96 160c0-17.7 14.3-32 32-32H448c17.7 0 32 14.3 32 32V352c0 17.7-14.3 32-32 32H128c-17.7 0-32-14.3-32-32V160z"/>
        </svg>
    ),
    'calendar-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M96 32V64H48C21.5 64 0 85.5 0 112v48H448V112c0-26.5-21.5-48-48-48H352V32c0-17.7-14.3-32-32-32s-32 14.3-32 32V64H160V32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192H0V464c0 26.5 21.5 48 48 48H400c26.5 0 48-21.5 48-48V192z"/></svg>
    ),
    'paper-clip-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M364.2 83.8c-24.4-24.4-64-24.4-88.4 0l-184 184c-42.1 42.1-42.1 110.3 0 152.4s110.3 42.1 152.4 0l152-152c10.9-10.9 28.7-10.9 39.6 0s10.9 28.7 0 39.6l-152 152c-64 64-167.6 64-231.6 0s-64-167.6 0-231.6l184-184c46.3-46.3 121.3-46.3 167.6 0s46.3 121.3 0 167.6l-176 176c-28.6 28.6-75 28.6-103.6 0s-28.6-75 0-103.6l144-144c10.9-10.9 28.7-10.9 39.6 0s10.9 28.7 0 39.6l-144 144c-6.7 6.7-6.7 17.7 0 24.4s17.7 6.7 24.4 0l176-176c24.4-24.4 24.4-64 0-88.4z"/></svg>
    ),
    'clock-circle-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/></svg>
    ),
    'booking-notification': () => (
        <svg width="30" height="31" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="30" height="30.7802" rx="8" fill="url(#paint0_linear_594_39901)" />
            <path d="M26.1 17.0481C25.1148 17.0481 24.3132 16.3049 24.3132 15.3915C24.3132 14.477 25.1148 13.7338 26.1 13.7338C26.3388 13.7338 26.568 13.6459 26.736 13.4902C26.9052 13.3333 27 13.1208 27 12.8994L26.9988 9.89333C26.9988 7.35227 24.768 5.28516 22.0272 5.28516H7.9728C5.232 5.28516 3.0012 7.35227 3.0012 9.89333L3 12.9962C3 13.2176 3.0948 13.4301 3.264 13.587C3.432 13.7427 3.6612 13.8306 3.9 13.8306C4.9188 13.8306 5.6868 14.5015 5.6868 15.3915C5.6868 16.3049 4.8852 17.0481 3.9 17.0481C3.4032 17.0481 3 17.4219 3 17.8825V20.8864C3 23.4275 5.2296 25.4957 7.9716 25.4957H22.0284C24.7704 25.4957 27 23.4275 27 20.8864V17.8825C27 17.4219 26.5968 17.0481 26.1 17.0481Z" fill="white" />
            <rect x="7.30942" y="10.7051" width="15.1579" height="8.8421" rx="2" fill="#E07493" />
        </svg>
    ),
    'role': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 16">
            <path d="M22.67,6.67H24V2a2,2,0,0,0-2-2H2A2,2,0,0,0,0,2V6.67H1.33a1.33,1.33,0,1,1,0,2.66H0V14a2,2,0,0,0,2,2H22a2,2,0,0,0,2-2V9.33H22.67a1.33,1.33,0,1,1,0-2.66Zm0,4V14a.67.67,0,0,1-.67.67H2A.67.67,0,0,1,1.33,14V10.67a2.67,2.67,0,0,0,0-5.34V2A.67.67,0,0,1,2,1.33H22a.67.67,0,0,1,.67.67V5.33a2.67,2.67,0,0,0,0,5.34Z" transform="translate(0 0)" />
            <path d="M13.65,9c-.61,0-.9.34-1.9.34S10.46,9,9.85,9A2.85,2.85,0,0,0,7,11.8v.54a1,1,0,0,0,1,1h7.46a1,1,0,0,0,1-1V11.8A2.85,2.85,0,0,0,13.65,9Zm1.83,3.39H8V11.8A1.84,1.84,0,0,1,9.85,10a8.26,8.26,0,0,0,1.9.34,8.14,8.14,0,0,0,1.9-.34,1.84,1.84,0,0,1,1.83,1.84ZM11.75,8.61A3.06,3.06,0,1,0,8.7,5.55,3.05,3.05,0,0,0,11.75,8.61Zm0-5.09a2,2,0,1,1-2,2A2,2,0,0,1,11.75,3.52Z" transform="translate(0 0)" />
        </svg>
    ),
    'role-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 576 512"><path d="M112 48a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm40 304V480c0 17.7-14.3 32-32 32s-32-14.3-32-32V256.9L59.4 304.5c-9.1 15.1-28.8 20-43.9 10.9s-20-28.8-10.9-43.9l58.3-97c17.4-28.9 48.6-46.6 82.3-46.6h29.7c33.7 0 64.9 17.7 82.3 46.6l44.9 74.7c-16.1 17.6-28.6 38.5-36.6 61.5c-1.9-1.8-3.5-3.9-4.9-6.3L232 256.9V480c0 17.7-14.3 32-32 32s-32-14.3-32-32V352H152zM432 224a144 144 0 1 1 0 288 144 144 0 1 1 0-288zm0 240a24 24 0 1 0 0-48 24 24 0 1 0 0 48zM368 321.6V328c0 8.8 7.2 16 16 16s16-7.2 16-16v-6.4c0-5.3 4.3-9.6 9.6-9.6h40.5c7.7 0 13.9 6.2 13.9 13.9c0 5.2-2.9 9.9-7.4 12.3l-32 16.8c-5.3 2.8-8.6 8.2-8.6 14.2V384c0 8.8 7.2 16 16 16s16-7.2 16-16v-5.1l23.5-12.3c15.1-7.9 24.5-23.6 24.5-40.6c0-25.4-20.6-45.9-45.9-45.9H409.6c-23 0-41.6 18.6-41.6 41.6z"/></svg>
    ),
    'role-notification': () => (
        <svg width="30" height="31" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="30" height="30.7802" rx="8" fill="url(#paint0_linear_594_39964)" />
            <path fillRule="evenodd" clipRule="evenodd" d="M22.3729 15.3915C22.3729 16.1146 23.0076 16.703 23.7875 16.703C24.1808 16.703 24.5 16.9989 24.5 17.3635V19.7416C24.5 21.7533 22.7349 23.3906 20.5642 23.3906H9.43585C7.2651 23.3906 5.5 21.7533 5.5 19.7416V17.3635C5.5 16.9989 5.8192 16.703 6.2125 16.703C6.99245 16.703 7.62705 16.1146 7.62705 15.3915C7.62705 14.6869 7.01905 14.1558 6.2125 14.1558C6.02345 14.1558 5.842 14.0862 5.709 13.9629C5.57505 13.8387 5.5 13.6705 5.5 13.4952L5.50095 11.0388C5.50095 9.02709 7.267 7.39062 9.4368 7.39062H20.5632C22.733 7.39062 24.4991 9.02709 24.4991 11.0388L24.5 13.4186C24.5 13.5939 24.4249 13.7621 24.291 13.8863C24.158 14.0096 23.9766 14.0792 23.7875 14.0792C23.0076 14.0792 22.3729 14.6675 22.3729 15.3915ZM18 12.8073C18 14.4714 16.6406 15.8073 15 15.8073C13.3359 15.8073 12 14.4714 12 12.8073C12 11.1667 13.3359 9.80729 15 9.80729C16.6406 9.80729 18 11.1667 18 12.8073ZM13.8047 16.9323H16.1719C18.4219 16.9323 20.25 18.7604 20.25 21.0104C20.25 21.4557 19.875 21.8073 19.4297 21.8073H10.5469C10.1016 21.8073 9.75 21.4557 9.75 21.0104C9.75 18.7604 11.5547 16.9323 13.8047 16.9323Z" fill="white" />
        </svg>
    ),
    'job': () => (
        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_308_3045)">
                <path d="M8.625 3H15.375C15.5813 3 15.75 3.16875 15.75 3.375V5.25H8.25V3.375C8.25 3.16875 8.41875 3 8.625 3ZM6 3.375V5.25H3C1.34531 5.25 0 6.59531 0 8.25V12.75H9H15H24V8.25C24 6.59531 22.6547 5.25 21 5.25H18V3.375C18 1.92656 16.8234 0.75 15.375 0.75H8.625C7.17656 0.75 6 1.92656 6 3.375ZM24 14.25H15V15.75C15 16.5797 14.3297 17.25 13.5 17.25H10.5C9.67031 17.25 9 16.5797 9 15.75V14.25H0V20.25C0 21.9047 1.34531 23.25 3 23.25H21C22.6547 23.25 24 21.9047 24 20.25V14.25Z"/>
            </g>
            <defs>
                <clipPath>
                    <rect width="24" height="24" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    ),
    'list': () => (
        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 6.75C3.59674 6.75 4.16903 6.51295 4.59099 6.09099C5.01295 5.66903 5.25 5.09674 5.25 4.5C5.25 3.90326 5.01295 3.33097 4.59099 2.90901C4.16903 2.48705 3.59674 2.25 3 2.25C2.40326 2.25 1.83097 2.48705 1.40901 2.90901C0.987053 3.33097 0.75 3.90326 0.75 4.5C0.75 5.09674 0.987053 5.66903 1.40901 6.09099C1.83097 6.51295 2.40326 6.75 3 6.75ZM9 3C8.17031 3 7.5 3.67031 7.5 4.5C7.5 5.32969 8.17031 6 9 6H22.5C23.3297 6 24 5.32969 24 4.5C24 3.67031 23.3297 3 22.5 3H9ZM9 10.5C8.17031 10.5 7.5 11.1703 7.5 12C7.5 12.8297 8.17031 13.5 9 13.5H22.5C23.3297 13.5 24 12.8297 24 12C24 11.1703 23.3297 10.5 22.5 10.5H9ZM9 18C8.17031 18 7.5 18.6703 7.5 19.5C7.5 20.3297 8.17031 21 9 21H22.5C23.3297 21 24 20.3297 24 19.5C24 18.6703 23.3297 18 22.5 18H9ZM3 21.75C3.59674 21.75 4.16903 21.5129 4.59099 21.091C5.01295 20.669 5.25 20.0967 5.25 19.5C5.25 18.9033 5.01295 18.331 4.59099 17.909C4.16903 17.4871 3.59674 17.25 3 17.25C2.40326 17.25 1.83097 17.4871 1.40901 17.909C0.987053 18.331 0.75 18.9033 0.75 19.5C0.75 20.0967 0.987053 20.669 1.40901 21.091C1.83097 21.5129 2.40326 21.75 3 21.75ZM5.25 12C5.25 11.7045 5.1918 11.4119 5.07873 11.139C4.96566 10.866 4.79992 10.6179 4.59099 10.409C4.38206 10.2001 4.13402 10.0343 3.86104 9.92127C3.58806 9.8082 3.29547 9.75 3 9.75C2.70453 9.75 2.41194 9.8082 2.13896 9.92127C1.86598 10.0343 1.61794 10.2001 1.40901 10.409C1.20008 10.6179 1.03434 10.866 0.921271 11.139C0.808198 11.4119 0.75 11.7045 0.75 12C0.75 12.2955 0.808198 12.5881 0.921271 12.861C1.03434 13.134 1.20008 13.3821 1.40901 13.591C1.61794 13.7999 1.86598 13.9657 2.13896 14.0787C2.41194 14.1918 2.70453 14.25 3 14.25C3.29547 14.25 3.58806 14.1918 3.86104 14.0787C4.13402 13.9657 4.38206 13.7999 4.59099 13.591C4.79992 13.3821 4.96566 13.134 5.07873 12.861C5.1918 12.5881 5.25 12.2955 5.25 12Z" fill="white"/>
        </svg>
    ),
    'job-avatar': () => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="24" height="24" rx="8" fill="#FF7875" />
            <rect width="24" height="24" rx="8" fill="url(#paint0_linear_25_1466)" />
            <g clipPath="url(#clip0_25_1466)">
                <path d="M10.0312 6.75H13.9688C14.0891 6.75 14.1875 6.84844 14.1875 6.96875V8.0625H9.8125V6.96875C9.8125 6.84844 9.91094 6.75 10.0312 6.75ZM8.5 6.96875V8.0625H6.75C5.78477 8.0625 5 8.84727 5 9.8125V12.4375H10.25H13.75H19V9.8125C19 8.84727 18.2152 8.0625 17.25 8.0625H15.5V6.96875C15.5 6.12383 14.8137 5.4375 13.9688 5.4375H10.0312C9.18633 5.4375 8.5 6.12383 8.5 6.96875ZM19 13.3125H13.75V14.1875C13.75 14.6715 13.359 15.0625 12.875 15.0625H11.125C10.641 15.0625 10.25 14.6715 10.25 14.1875V13.3125H5V16.8125C5 17.7777 5.78477 18.5625 6.75 18.5625H17.25C18.2152 18.5625 19 17.7777 19 16.8125V13.3125Z" fill="white" />
            </g>
            <defs>
                <linearGradient id="paint0_linear_25_1466" x1="7.6875" y1="4.5" x2="18.5625" y2="20.25" gradientUnits="userSpaceOnUse">
                    <stop stopColor="white" stopOpacity="0" />
                    <stop offset="1" stopColor="white" stopOpacity="0.2" />
                </linearGradient>
                <clipPath id="clip0_25_1466">
                    <rect width="14" height="14" fill="white" transform="translate(5 5)" />
                </clipPath>
            </defs>
        </svg>
    ),
    'job-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M184 48H328c4.4 0 8 3.6 8 8V96H176V56c0-4.4 3.6-8 8-8zm-56 8V96H64C28.7 96 0 124.7 0 160v96H192 320 512V160c0-35.3-28.7-64-64-64H384V56c0-30.9-25.1-56-56-56H184c-30.9 0-56 25.1-56 56zM512 288H320v32c0 17.7-14.3 32-32 32H224c-17.7 0-32-14.3-32-32V288H0V416c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V288z"/></svg>
    ),
    'today': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M18.75 3H16.5V.375A.376.376 0 0016.125 0h-.75A.376.376 0 0015 .375V3H6V.375A.376.376 0 005.625 0h-.75A.376.376 0 004.5.375V3H2.25A2.25 2.25 0 000 5.25v16.5A2.25 2.25 0 002.25 24h16.5A2.25 2.25 0 0021 21.75V5.25A2.25 2.25 0 0018.75 3zm.75 18.75c0 .413-.337.75-.75.75H2.25a.752.752 0 01-.75-.75V9h18v12.75zm0-14.25h-18V5.25c0-.412.337-.75.75-.75h16.5c.413 0 .75.338.75.75V7.5zM5.25 18h4.5c.412 0 .75-.337.75-.75v-4.5a.752.752 0 00-.75-.75h-4.5a.752.752 0 00-.75.75v4.5c0 .413.338.75.75.75zM6 13.5h3v3H6v-3z" />
        </svg>
    ),
    'filter': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M22.498 0H1.503C.169 0-.503 1.617.442 2.56L9 11.122v8.754c0 .433.187.844.512 1.129l3 2.624c.958.838 2.488.173 2.488-1.129V11.121l8.56-8.56C24.5 1.619 23.833 0 22.498 0zM13.5 10.5v12l-3-2.625V10.5l-9-9h21l-9 9z" />
        </svg>
    ),
    'display-density': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 20">
            <path d="M22.2 0H1.8C.80625 0 0 .80625 0 1.8v12c0 .99375.80625 1.8 1.8 1.8h9.6V18h-6c-.33 0-.6.27-.6.6 0 .33.27.6.6.6h13.2c.33 0 .6-.27.6-.6 0-.33-.27-.6-.6-.6h-6v-2.4h9.6c.99375 0 1.8-.80625 1.8-1.8v-12c0-.99375-.80625-1.8-1.8-1.8zm.6 13.8c0 .33-.27.6-.6.6H1.8c-.33 0-.6-.27-.6-.6v-12c0-.33.27-.6.6-.6h20.4c.33 0 .6.27.6.6v12z" />
            <path d="M5 4h14v1H5zM5 7h14v1H5zM5 10h14v1H5z"/>
        </svg>
    ),
    'error': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M12 1.548c5.74 0 10.452 4.65 10.452 10.452 0 5.772-4.675 10.452-10.452 10.452-5.77 0-10.452-4.673-10.452-10.452C1.548 6.232 6.223 1.548 12 1.548zM12 0C5.373 0 0 5.375 0 12c0 6.629 5.373 12 12 12s12-5.371 12-12c0-6.625-5.373-12-12-12zm-.556 5.806h1.112a.58.58 0 01.58.605l-.339 8.13a.58.58 0 01-.58.556h-.434a.58.58 0 01-.58-.557l-.34-8.129a.58.58 0 01.581-.605zM12 16.065a1.355 1.355 0 100 2.71 1.355 1.355 0 000-2.71z" />
        </svg>
    ),
    'plus': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M23.5 10.5h-10V.5A.5.5 0 0013 0h-2a.5.5 0 00-.5.5v10H.5a.5.5 0 00-.5.5v2a.5.5 0 00.5.5h10v10a.5.5 0 00.5.5h2a.5.5 0 00.5-.5v-10h10a.5.5 0 00.5-.5v-2a.5.5 0 00-.5-.5z" />
        </svg>
    ),
    'rounded-plus': () => (
        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_26_9630)">
                <path d="M13.8462 1.84615C13.8462 0.825 13.0212 0 12 0C10.9788 0 10.1538 0.825 10.1538 1.84615V10.1538H1.84615C0.825 10.1538 0 10.9788 0 12C0 13.0212 0.825 13.8462 1.84615 13.8462H10.1538V22.1538C10.1538 23.175 10.9788 24 12 24C13.0212 24 13.8462 23.175 13.8462 22.1538V13.8462H22.1538C23.175 13.8462 24 13.0212 24 12C24 10.9788 23.175 10.1538 22.1538 10.1538H13.8462V1.84615Z" />
            </g>
        </svg>
    ),
    'comment': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M21 0H3C1.345 0 0 1.345 0 3v13.5c0 1.655 1.345 3 3 3h4.5v3.938a.56.56 0 00.895.45L14.25 19.5H21c1.655 0 3-1.345 3-3V3c0-1.655-1.345-3-3-3zm1.5 16.5c0 .825-.675 1.5-1.5 1.5h-7.252l-.398.3L9 21.562V18H3c-.825 0-1.5-.675-1.5-1.5V3c0-.825.675-1.5 1.5-1.5h18c.825 0 1.5.675 1.5 1.5v13.5z" />
        </svg>
    ),
    'attachment': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 23 24">
            <path d="M6.052 24c-1.55 0-3.101-.59-4.281-1.77a6.061 6.061 0 010-8.563L14.055 1.383a4.734 4.734 0 016.687 0 4.734 4.734 0 010 6.687L10.306 18.505a3.406 3.406 0 01-4.811 0 3.406 3.406 0 010-4.811l7.983-7.983c.22-.22.575-.22.795 0l.265.265c.22.22.22.576 0 .796l-7.982 7.983a1.904 1.904 0 000 2.69 1.904 1.904 0 002.69 0L19.68 7.008a3.232 3.232 0 000-4.566 3.232 3.232 0 00-4.565 0L2.831 14.729a4.56 4.56 0 000 6.44 4.56 4.56 0 006.441 0L19.48 10.963c.22-.22.576-.22.795 0l.266.265c.22.22.22.576 0 .796L10.333 22.229A6.036 6.036 0 016.052 24z" />
        </svg>
    ),
    'warning': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 22">
            <path d="M11.239 6.655h1.476a.25.25 0 01.25.258l-.312 8.153a.251.251 0 01-.25.24h-.852a.249.249 0 01-.25-.24l-.312-8.153a.25.25 0 01.25-.258zm.74 9.484c-.645 0-1.164.52-1.164 1.164 0 .645.52 1.165 1.164 1.165.645 0 1.165-.52 1.165-1.165 0-.644-.52-1.164-1.165-1.164zm11.709 2.163L13.71.998c-.766-1.33-2.692-1.33-3.461 0L.27 18.302c-.765 1.326.192 2.994 1.73 2.994h19.962c1.53 0 2.496-1.663 1.726-2.994zm-1.726 1.663H1.997a.667.667 0 01-.579-.998l9.983-17.303a.666.666 0 011.152 0l9.983 17.303a.665.665 0 01-.574.998z" />
        </svg>
    ),
    'cut': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M13.367 12l10.57-10.563a.214.214 0 000-.302 1.714 1.714 0 00-2.424 0l-9.51 9.502-2.605-2.604a5.143 5.143 0 10-1.364 1.363L10.639 12l-2.605 2.604a5.143 5.143 0 101.364 1.363l2.605-2.604 9.51 9.502c.67.67 1.755.67 2.424 0a.214.214 0 000-.302L13.367 12zM5.143 8.571a3.432 3.432 0 01-3.429-3.428 3.432 3.432 0 013.429-3.429A3.432 3.432 0 018.57 5.143 3.432 3.432 0 015.143 8.57zm0 13.715a3.432 3.432 0 01-3.429-3.429 3.432 3.432 0 013.429-3.428 3.432 3.432 0 013.428 3.428 3.432 3.432 0 01-3.428 3.429z" />
        </svg>
    ),
    'split': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 22 25">
            <path d="M10.325 10.167a.33.33 0 00-.328-.334h-.655a.33.33 0 00-.327.334v4.666a.33.33 0 00.327.334h.655a.33.33 0 00.328-.334v-4.666zm9.824-4.334v8h-4.585a.66.66 0 00-.655.667v4.667h-2.62V20.5h3.389c.347 0 .68-.14.925-.39.97-.985 3.514-3.577 4.478-4.559.244-.248.38-.585.378-.936V5.833c0-.736-.586-1.333-1.31-1.333h-7.86v1.333h7.86zm-.542 9.334l-3.388 3.448v-3.448h3.388zm-9.282 9v-6a.33.33 0 00-.328-.334h-.655a.33.33 0 00-.327.334v6a.33.33 0 00.327.333h.655a.33.33 0 00.328-.333zm-.983-17h.655a.33.33 0 00.328-.334v-6A.33.33 0 009.997.5h-.655a.33.33 0 00-.327.333v6a.33.33 0 00.327.334zm-7.532 12V5.833h5.24V4.5H1.81C1.086 4.5.5 5.097.5 5.833v13.334c0 .736.586 1.333 1.31 1.333h5.24v-1.333H1.81z" />
        </svg>
    ),
    'move': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M23.567 10.93L20.45 7.815c-.97-.97-2.639-.282-2.639 1.092v2.367h-5.038V6.229h2.367c1.373 0 2.067-1.663 1.092-2.638L13.115.478a1.542 1.542 0 00-2.184 0L7.814 3.595c-.97.97-.282 2.64 1.092 2.64h2.367v5.038H6.229V8.906c0-1.374-1.663-2.067-2.638-1.092L.478 10.93a1.542 1.542 0 000 2.184l3.117 3.117c.97.97 2.64.28 2.64-1.092v-2.367h5.038v5.043H8.906c-1.374 0-2.067 1.664-1.092 2.639l3.117 3.117a1.542 1.542 0 002.184 0l3.117-3.117c.97-.97.28-2.64-1.092-2.64h-2.367v-5.042h5.043v2.367c0 1.373 1.664 2.067 2.639 1.092l3.117-3.117c.6-.605.6-1.58-.005-2.184zM4.88 15.14a.192.192 0 01-.328.135L1.435 12.16a.192.192 0 010-.272L4.552 8.77a.192.192 0 01.328.136v6.234zm10.26 4.026c.173 0 .257.206.135.328L12.16 22.61a.192.192 0 01-.272 0L8.77 19.494a.192.192 0 01.136-.328h6.234zM8.906 4.88a.192.192 0 01-.136-.328l3.117-3.117a.192.192 0 01.272 0l3.116 3.117a.192.192 0 01-.135.328H8.906zm13.705 7.279l-3.117 3.116a.192.192 0 01-.328-.135V8.91c0-.173.206-.257.328-.135l3.117 3.116a.18.18 0 010 .268z" />
        </svg>
    ),
    'edit': () => (
        <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.3344 1.10315L9.8219 2.61565L13.8844 6.67815L15.3969 5.16565C16.1782 4.3844 16.1782 3.11877 15.3969 2.33752L14.1657 1.10315C13.3844 0.321899 12.1188 0.321899 11.3375 1.10315H11.3344ZM9.11565 3.3219L1.83128 10.6094C1.50628 10.9344 1.26878 11.3375 1.13753 11.7781L0.0312776 15.5375C-0.0468474 15.8032 0.0250276 16.0875 0.218778 16.2813C0.412528 16.475 0.696903 16.5469 0.959403 16.4719L4.71878 15.3657C5.1594 15.2344 5.56253 14.9969 5.88753 14.6719L13.1782 7.3844L9.11565 3.3219Z" />
        </svg>
    ),
    'select': () => (
        <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
            <path d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h360c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H184V184h656v320c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V144c0-17.7-14.3-32-32-32zM653.3 599.4l52.2-52.2a8.01 8.01 0 00-4.7-13.6l-179.4-21c-5.1-.6-9.5 3.7-8.9 8.9l21 179.4c.8 6.6 8.9 9.4 13.6 4.7l52.4-52.4 256.2 256.2c3.1 3.1 8.2 3.1 11.3 0l42.4-42.4c3.1-3.1 3.1-8.2 0-11.3L653.3 599.4z"/>
        </svg>
    ),
    'info': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M12 1.5483871c5.7397258 0 10.4516129 4.64879032 10.4516129 10.4516129 0 5.7721452-4.6746774 10.4516129-10.4516129 10.4516129-5.76987097 0-10.4516129-4.6723548-10.4516129-10.4516129C1.5483871 6.2321129 6.22267742 1.5483871 12 1.5483871zM12 0C5.37304839 0 0 5.37498387 0 12c0 6.6288871 5.37304839 12 12 12 6.6269516 0 12-5.3711129 12-12 0-6.62501613-5.3730484-12-12-12zM9.88415888 18c-.68524152 0-1.18717328-.4134163-.70821907-2.2345477l.78685335-3.2268468c.13633344-.5173952.15982164-.7233535 0-.7233535-.20628732 0-1.09373126.3559279-1.62068301.7078567L8 11.9652216c1.6666381-1.3872192 3.5824549-2.19855493 4.4055617-2.19855493.6837097 0 .7975762.80683663.4569979 2.04858533l-.9012304 3.3923133c-.1603323.5993786-.0903784.8068366.068422.8068366.2052661 0 .8792741-.2489496 1.5415381-.7658449l.387044.5168953C12.3381609 17.3796256 10.5688898 18 9.88415888 18M12.6728809 5c.8568 0 1.2854524.70838186 1.2854524 1.51831069 0 1.0117993-.7437044 1.94835598-1.7141049 1.94835598-.8128745 0-1.287472-.58175402-1.2642471-1.54461503C10.9799813 6.11273455 11.5439446 5 12.6728809 5" />
        </svg>
    ),
    'history': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M.58064445.7742634h.48387037c.32066089 0 .58064444.25998355.58064444.58064445v4.57862339C3.73528572 2.37330982 7.60832934-.0137195 12.0383561.00005934 18.637719.02078046 24.0102769 5.41985445 23.9999852 12.0192173c-.0103696 6.6186209-5.3790081 11.9808239-12 11.9808239-3.09318972 0-5.9129927-1.1703372-8.04086104-3.0923187-.24740293-.2234514-.2590642-.6077896-.02332256-.8435312l.34204797-.342048c.21788683-.2178868.5684993-.2280965.79751515-.0219677 1.83343322 1.6500947 4.2601883 2.6547064 6.92462048 2.6547064 5.6972833 0 10.354826-4.6127847 10.354826-10.3548227 0-5.69728658-4.6127847-10.35482923-10.354826-10.35482923-4.00944666 0-7.48726495 2.27791655-9.2075209 5.61289631h4.7559135c.32066089 0 .58064444.25998355.58064444.58064445v.48387037c0 .32066089-.25998355.58064445-.58064444.58064445H.58064445C.25998355 8.90328565 0 8.64330209 0 8.3226412V1.35490785C0 1.03424695.25998355.7742634.58064445.7742634zM16.1441898 16.0275511l.2277094-.3130642c.1886127-.2593545.131274-.6224992-.1280805-.8111119l-3.4696409-2.5233356V5.22587082c0-.32066089-.2599835-.58064445-.5806444-.58064445h-.3870963c-.3206609 0-.5806445.25998356-.5806445.58064445v7.94258698l4.1072853 2.9871254c.2593545.188661.6224992.1313224.8111119-.1280321z" />
        </svg>
    ),
    'time': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M12 0C5.37096774 0 0 5.37096774 0 12c0 6.6290323 5.37096774 12 12 12 6.6290323 0 12-5.3709677 12-12 0-6.62903226-5.3709677-12-12-12zm10.4516129 12c0 5.7435484-4.65 10.4516129-10.4516129 10.4516129-5.74354839 0-10.4516129-4.65-10.4516129-10.4516129 0-5.74354839 4.65-10.4516129 10.4516129-10.4516129 5.7435484 0 10.4516129 4.65 10.4516129 10.4516129zm-7.2048387 4.2725806l-3.9290323-2.8548387c-.15-.1112903-.2370967-.2854838-.2370967-.4693548V5.22580645c0-.31935484.2612903-.58064516.5806451-.58064516h.6774194c.3193548 0 .5806451.26129032.5806451.58064516v7.07903225l3.4112904 2.4822581c.2612903.1887097.3145161.5516129.1258064.8129032l-.3967742.5467742c-.1887097.2564516-.5516129.3145161-.8129032.1258064z" />
        </svg>
    ),
    'more': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 6">
            <path d="M14.5263158 2.52631579c0 1.39736842-1.1289474 2.52631579-2.5263158 2.52631579S9.47368421 3.92368421 9.47368421 2.52631579 10.6026316 0 12 0s2.5263158 1.12894737 2.5263158 2.52631579zM21.4736842 0c-1.3973684 0-2.5263158 1.12894737-2.5263158 2.52631579s1.1289474 2.52631579 2.5263158 2.52631579S24 3.92368421 24 2.52631579 22.8710526 0 21.4736842 0zM2.52631579 0C1.12894737 0 0 1.12894737 0 2.52631579s1.12894737 2.52631579 2.52631579 2.52631579 2.52631579-1.12894737 2.52631579-2.52631579S3.92368421 0 2.52631579 0z" />
        </svg>
    ),
    'search': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M2.526316 9.32276c0-3.75344 3.041506-6.79644 6.794618-6.79644 3.75434 0 7.100119 3.34484 7.100119 7.09951 0 3.75222-3.042733 6.79522-6.798299 6.79522-3.751886 0-7.096438-3.34484-7.096438-7.09829m15.193927 5.24255c.865634-1.42352 1.363638-3.09253 1.363638-4.87781C19.083881 4.49727 14.586578 0 9.396298 0 4.207192 0 0 4.20833 0 9.39739c0 5.19024 4.496129 9.68633 9.685234 9.68633 1.725395 0 3.342733-.46746 4.732211-1.27906l5.770501 5.77163c.566127.56495 1.481091.56495 2.046044 0l1.431761-1.43175c.563778-.56495.359408-1.27554-.20437-1.84048l-5.741138-5.73875z" />
        </svg>
    ),
    'redo': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M21.3994021.44834025l-2.3856412 2.38564123C17.0185839 1.29593334 14.5138987.38167562 11.7996961.38167562 5.29513335.38643738-.01422787 5.70532213.00002864 12.2146466.01434268 18.7239711 5.29513335 24 11.8092196 24c3.042764 0 5.8188694-1.1523457 7.9140434-3.042764.2428497-.2190409.2571349-.5999816.0238088-.828546l-.3380849-.3380849c-.2142792-.2142792-.5571258-.2238027-.7856903-.0238088-1.8570859 1.6713774-4.2570125 2.6142057-6.814077 2.6142057-5.61887557 0-10.19016415-4.5522416-10.19016415-10.1901642 0-5.61887554 4.55224155-10.19016413 10.19016415-10.19016413 2.2142179 0 4.3189154.70950208 6.0474339 1.98565348l-2.5523028 2.55230279c-.9571136.95711355-.2809438 2.59992039 1.0761575 2.59992039h6.0950514c.8428314 0 1.5237629-.68093153 1.5237629-1.52376287V1.52449777c0-1.3571013-1.6475686-2.03327107-2.5999204-1.07615752zm1.0761575 7.17120897h-6.0950514l6.0950514-6.09505145v6.09505145z" />
        </svg>
    ),
    'undo': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M12.1996264.38167562c-2.71420258 0-5.21412603.91425772-7.21406478 2.45230586L2.59992038.44834025C1.6475686-.5087733 0 .16739647 0 1.52449777v6.09505145c0 .84283134.68093153 1.52376287 1.52376286 1.52376287h6.09505145c1.3571013 0 2.03327107-1.64280684 1.07615753-2.59992039l-2.5523028-2.55706455c1.7285185-1.2761514 3.83321595-1.98565348 6.04743386-1.98565348 5.6379226 0 10.1901641 4.57128859 10.1901641 10.19016413 0 5.6379226-4.5712885 10.1901642-10.1901641 10.1901642-2.55706455 0-4.96175282-.9428283-6.81407705-2.6142057-.22380267-.2047556-.57141107-.1904704-.78569023.0238088l-.33808488.3380849c-.23332619.2333262-.21904091.6095051.02380879.828546C6.37123347 22.8476543 9.14733894 24 12.1901029 24c6.5140862 0 11.7948769-5.2760289 11.809191-11.7853534C24.0135504 5.70532213 18.7041891.38643738 12.1996264.38167562zm-4.58081209 7.2378736H1.52376286V1.52449777l6.09505145 6.09505145z" />
        </svg>
    ),
    'sort-asc': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 18 24">
            <g>
                <path d="M17.1916514 14.04931389c0 .19498324-.0706506.39199756-.2119518.55651467L9.4532008 23.2135262c-.18325.2092007-.459229.3310653-.7528706.3310653-.2914338 0-.5696206-.1218646-.7506628-.3310653L.4629096 14.64238792C.12952702 14.26054574.19576197 13.702.6108344 13.39530761c.4150723-.30669239 1.0222261-.24576013 1.3556087.13608205L8.702538 21.2413519l6.7736281-7.74855267c.3333826-.38184218.9405363-.44277444 1.3556087-.13608205.236238.17670356.3598766.43261906.3598766.69259671z" fillOpacity=".35" />
                <path d="M17.1916511 9.95068611c0-.19498324-.0706507-.39199756-.2119519-.55651467L9.45320049.7864738C9.26995045.5772731 8.99397147.4554085 8.70032983.4554085c-.2914338 0-.56962061.1218646-.75066282.3310653L.46290927 9.35761208C.12952666 9.73945426.19576162 10.298.610834 10.60469239c.41507239.30669239 1.02222614.24576013 1.35560875-.13608205L8.70253767 2.7586481l6.77362803 7.74855267c.3333826.38184218.9405364.44277444 1.3556088.13608205.236238-.17670356.3598766-.43261906.3598766-.69259671z" />
            </g>
        </svg>
    ),
    'sort-desc': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 18 24">
            <g>
                <path d="M17.1916514 9.95068611c0-.19498324-.0706506-.39199756-.2119518-.55651467L9.4532008.7864738c-.18325-.2092007-.459229-.3310653-.7528706-.3310653-.2914338 0-.5696206.1218646-.7506628.3310653L.4629096 9.35761208C.12952702 9.73945426.19576197 10.298.6108344 10.60469239c.4150723.30669239 1.0222261.24576013 1.3556087-.13608205L8.702538 2.7586481l6.7736281 7.74855267c.3333826.38184218.9405363.44277444 1.3556087.13608205.236238-.17670356.3598766-.43261906.3598766-.69259671z" fillOpacity=".35" />
                <path d="M17.1916511 14.04931389c0 .19498324-.0706507.39199756-.2119519.55651467L9.45320049 23.2135262c-.18325004.2092007-.45922902.3310653-.75287066.3310653-.2914338 0-.56962061-.1218646-.75066282-.3310653L.46290927 14.64238792C.12952666 14.26054574.19576162 13.702.610834 13.39530761c.41507239-.30669239 1.02222614-.24576013 1.35560875.13608205l6.73609492 7.70996224 6.77362803-7.74855267c.3333826-.38184218.9405364-.44277444 1.3556088-.13608205.236238.17670356.3598766.43261906.3598766.69259671z" />
            </g>
        </svg>
    ),
    'sort': () => (
        <svg width="16" height="18" viewBox="0 0 16 18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M15.6786 7.16695L13.5551 0.527107C13.5413 0.482677 13.5136 0.44387 13.476 0.416432C13.4385 0.388994 13.393 0.374383 13.3465 0.374763H11.5325C11.4364 0.374763 11.3543 0.435701 11.3239 0.527107L9.18402 7.16695C9.17699 7.18804 9.1723 7.21148 9.1723 7.23492C9.1723 7.35445 9.27074 7.45289 9.39027 7.45289H10.7121C10.8106 7.45289 10.895 7.38726 10.9231 7.29351L11.3332 5.84976H13.4192L13.8246 7.29117C13.8504 7.38492 13.9371 7.45054 14.0356 7.45054H15.47C15.4934 7.45054 15.5145 7.4482 15.5356 7.44117C15.5918 7.42242 15.6364 7.38492 15.6645 7.33336C15.6903 7.28179 15.695 7.2232 15.6786 7.16695ZM11.5465 4.62867L12.3153 1.88883H12.4629L13.2153 4.62867H11.5465ZM14.9098 16.181H11.8723V16.1716L14.9801 11.7443C15.0059 11.7068 15.02 11.6646 15.02 11.6177V10.7646C15.02 10.6451 14.9215 10.5466 14.802 10.5466H10.0207C9.90121 10.5466 9.80277 10.6451 9.80277 10.7646V11.7724C9.80277 11.892 9.90121 11.9904 10.0207 11.9904H12.8942V11.9998L9.77465 16.4271C9.74861 16.4642 9.73469 16.5084 9.7348 16.5537V17.4068C9.7348 17.5263 9.83324 17.6248 9.95277 17.6248H14.9075C15.027 17.6248 15.1254 17.5263 15.1254 17.4068V16.399C15.1257 16.3705 15.1204 16.3422 15.1097 16.3157C15.099 16.2893 15.0832 16.2653 15.0631 16.245C15.0431 16.2247 15.0192 16.2086 14.9929 16.1976C14.9666 16.1867 14.9383 16.181 14.9098 16.181ZM5.75043 13.4529H3.96918V1.03101C3.96918 0.927888 3.8848 0.843513 3.78168 0.843513H2.46918C2.36605 0.843513 2.28168 0.927888 2.28168 1.03101V13.4529H0.50043C0.343398 13.4529 0.254336 13.6357 0.352773 13.7576L2.97777 17.0834C2.99531 17.1058 3.01772 17.1239 3.0433 17.1364C3.06889 17.1488 3.09697 17.1553 3.12543 17.1553C3.15389 17.1553 3.18197 17.1488 3.20755 17.1364C3.23314 17.1239 3.25555 17.1058 3.27309 17.0834L5.89809 13.7576C5.99418 13.6357 5.90746 13.4529 5.75043 13.4529Z" />
        </svg>
    ),
    'resize': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 9">
            <path d="M23.585504 3.40413793l-3.000293-3.000293c-.9422796-.94227952-2.559625-.27658951-2.559625 1.05947847v2.20334017H6.02441401V1.4633234c0-1.33138002-1.61734545-2.00644594-2.55962497-1.05947847l-3.000293 3.000293c-.58599472.58599473-.58599472 1.53765016 0 2.12364489l3.000293 3.000293c.94227952.94227948 2.55962497.27658951 2.55962497-1.05947847V5.26525718H18.025586v2.20334017c0 1.33138002 1.6173454 2.00644595 2.559625 1.05947847l3.000293-3.000293c.5859947-.59068269.5859947-1.53765016 0-2.12364489zM4.71178582 7.46390939c0 .16407853-.20158219.25314973-.31878113.13126282l-3.000293-3.000293c-.07500732-.07500732-.07500732-.19220627 0-.26252563l3.000293-3.000293c.11719894-.11719895.31878113-.0328157.31878113.13126282v6.00058599zM22.6572883 4.59487921l-3.000293 3.000293c-.1171989.11719895-.3187811.03281571-.3187811-.13126282V1.4633234c0-.16407852.2015822-.25314972.3187811-.13126282l3.000293 3.000293c.0750073.07031936.0750073.19220627 0 .26252563z" />
        </svg>
    ),
    'home': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 21">
            <path d="M23.8597803 9.24147979L12.9436001.33413925c-.5523767-.445519-1.3406676-.445519-1.8930443 0L.13812601 9.24147979c-.16028369.13062489-.18460944.36633302-.05438165.52693952l.47349547.58178999c.13006228.1595227.36438561.1844195.52506428.0557881l1.91554703-1.56487912V20.233138c0 .4142643.33582755.7500919.75009183.7500919h6.00073468c.41426425 0 .75009185-.3358276.75009185-.7500919v-6.0007347l3.0003673.0140643v6.0044851c0 .4142643.3358276.7500918.7500919.7500918l6.0007346-.0154706c.4142643 0 .7500919-.3358276.7500919-.7500918V8.84111828l1.915547 1.56487912c.1606799.1305456.3967474.1062044.5274083-.0543817l.4734955-.58178997c.0627211-.07751298.0919939-.17681144.0813496-.27595221-.0106442-.09914078-.0603291-.18996197-.1380753-.25239373zM19.4947146 19.4891407h.0046881l-4.500551.0140642v-6.0087044c-.0012781-.4128204-.3349334-.7475217-.7477478-.7500918l-4.50055102-.0126578c-.19934254-.000623-.39073408.0781292-.53191096.2188663-.14117689.1407371-.22052588.331882-.22052491.5312255v6.007298h-4.5000822V7.61706216l7.50091839-6.12121818 7.5009183 6.12121818-.0051569 11.87207854z" />
        </svg>
    ),
    'plans-page': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 18">
            <path d="M2.34375 3.375c0 .46599.37776.84375.84375.84375s.84375-.37776.84375-.84375-.37776-.84375-.84375-.84375-.84375.37776-.84375.84375zM22.3125 0H1.6875C.754687 0 0 .75469 0 1.6875v14.625C0 17.24531.754687 18 1.6875 18h20.625C23.245313 18 24 17.24531 24 16.3125V1.6875C24 .75469 23.245313 0 22.3125 0zM6 16.3125H1.6875V6.75H6v9.5625zm16.3125 0H7.6875v-3.96094c0 .01172.009375.02344.023438.02344h14.578124c.011719 0 .023438-.00938.023438-.02344v3.96094zm0-5.60156c0-.01172-.009375-.02344-.023438-.02344H7.710938c-.011719 0-.023438.00938-.023438.02344V6.75h14.625v3.96094zm0-5.64844H1.6875v-3.375h20.625v3.375zM4.78125 3.375c0 .46599.37776.84375.84375.84375s.84375-.37776.84375-.84375-.37776-.84375-.84375-.84375-.84375.37776-.84375.84375zm2.4375 0c0 .46599.37776.84375.84375.84375s.84375-.37776.84375-.84375-.37776-.84375-.84375-.84375-.84375.37776-.84375.84375z" />
        </svg>
    ),
    'settings': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 25">
            <path d="M23.3953756 14.6416383l-1.6392138-.9463808c.1030479-.8160987.1030479-1.6419176 0-2.4580163l1.6392138-.9463808c.4813588-.2778771.713543-.84961637.5552516-1.38239853-.5636965-1.89753693-1.5670803-3.60556648-2.892124-5.00636283-.3837399-.40565639-.9961453-.49442829-1.4797159-.2152442l-1.6368009.94492302c-.6547086-.49826118-1.3698032-.911722-2.1282634-1.23054131V1.51048569c0-.55947405-.3846447-1.04575905-.9291391-1.1745939-1.8922589-.44783058-3.8765068-.44788085-5.76916778 0-.54444415.12883485-.92913912.61506958-.92913912 1.1745939v1.89075086c-.75846591.31880772-1.473562.73226935-2.12826342 1.23054131l-1.63680091-.94492302c-.48362083-.27918409-1.09602626-.19041219-1.47971588.2152442C1.61645312 5.30289539.61306933 7.0109752.04937284 8.90846187c-.15829145.53278216.0738928 1.10447113.5552516 1.38239853l1.63921374.9463808c-.10304785.8160987-.10304785 1.6419176 0 2.4580163l-1.63921374.9463808c-.4813588.2778771-.71354305.8496164-.5552516 1.3823986.5636965 1.8974866 1.56708028 3.6055664 2.89212395 5.0063628.38373989.4056564.99614531.4944283 1.47971588.2152944l1.63680091-.944923c.65469786.4982638 1.36979507.9117092 2.12826342 1.2304911v1.8907508c0 .5594741.3846447 1.0457591.92913912 1.1745939 1.89230918.4478306 3.87650678.4478809 5.76916778 0 .5444441-.1288348.9291391-.6150696.9291391-1.1745939v-1.8907508c.7584544-.3188099 1.4735482-.7322533 2.1282634-1.2304911l1.6368009.944923c.4835706.2791841 1.095976.1904122 1.4797159-.2152944 1.3250437-1.4007964 2.3284275-3.1088762 2.892124-5.0063628.1582914-.5327822-.0738928-1.1044712-.5552516-1.3823986zm-3.2914467 5.0498942l-2.3278243-1.3441452c-1.3566115 1.1597148-1.8377189 1.451516-3.5709323 2.0639214v2.6882402c-1.4548002.3000958-2.9555444.3000958-4.41034456 0v-2.6882402c-1.69003388-.5971242-2.17933491-.8743981-3.57093237-2.0639214l-2.32782425 1.3441452c-.99076672-1.1104026-1.74371927-2.4122731-2.20818829-3.8181967l2.3288296-1.3441453c-.32849622-1.7803137-.32864703-2.3446637 0-4.1258823l-2.3288296-1.34414524c.46446902-1.40592361 1.21747184-2.70784436 2.20818829-3.81874971L6.22389537 6.5855638C7.6000608 5.40760199 8.086949 5.12454733 9.79482774 4.52113973V1.83294985c1.45474886-.30059762 2.95559566-.30059762 4.41034456 0V4.52119c1.7079792.60345786 2.1949177.88656279 3.5709323 2.06442407l2.3278243-1.34515055c.9907164 1.11085508 1.7437192 2.41277583 2.2081883 3.81874971l-2.3288296 1.34414517c.3285465 1.7807662.328647 2.3446638 0 4.1258824l2.3288296 1.3441452c-.4644691 1.4058734-1.2174216 2.7077941-2.2081883 3.8181465zM12 7.64062228c-2.66089479 0-4.82565221 2.16475742-4.82565221 4.82565222 0 2.6608948 2.16475742 4.8256522 4.82565221 4.8256522 2.6608948 0 4.8256522-2.1647574 4.8256522-4.8256522 0-2.6608948-2.1647574-4.82565222-4.8256522-4.82565222zm0 8.04275372c-1.7739299 0-3.21710147-1.4431716-3.21710147-3.2171015S10.2260701 9.24917302 12 9.24917302c1.7739299 0 3.2171015 1.44317158 3.2171015 3.21710148 0 1.7739299-1.4431716 3.2171015-3.2171015 3.2171015z" />
        </svg>
    ),
    'admin-settings': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 20">
            <path d="M23.5612284 13.4362377l-.6187495-.3562497c.03-.3187497.03-.6412494 0-.9599991l.6224995-.3562497c.3562496-.2062498.5174995-.6262494.3937496-1.0124991-.2699998-.87749917-.7462493-1.70249841-1.3762487-2.38124779-.2774998-.30374972-.7237494-.37124966-1.0762491-.16499984l-.6187494.35624967c-.2624997-.18749983-.5399995-.34874968-.8324992-.47999956v-.71249935c0-.41249962-.2812498-.7612493-.6824994-.85124922-.8962492-.20249981-1.8487483-.20249981-2.7449975 0-.4012496.08999992-.6824993.4424996-.6824993.85124922v.71249935c-.2924998.13124988-.5699995.29249973-.8324993.47999956l-.6187494-.35624967c-.3562497-.20624982-.7987493-.13874988-1.076249.16499984-.6262494.67874938-1.102499 1.50374862-1.3762488 2.37749779-.1237498.3899997.045.8174993.3974997 1.0199991l.6187494.3562497c-.03.3187497-.03.6412494 0 .9599991l-.6224994.3562497c-.3487497.2024998-.5174995.6337494-.3937497 1.016249.2699998.8774992.7462494 1.7024985 1.3762488 2.3812478.2774997.2999998.7199993.3674997 1.076249.1649999l.6187494-.3562497c.2624998.1874998.5399995.3487497.8324993.4799996v.7124993c0 .4124996.2812497.7612493.6824993.8512492.4499996.1012499.9112492.1499999 1.3724988.1499999.4612495 0 .9262491-.04875 1.3724987-.1499999.4012496-.0899999.6824994-.4424996.6824994-.8512492v-.7124993c.2924997-.1312499.5699995-.2924998.8324992-.4799996l.6187494.3562497c.3524997.2024998.7987493.1349999 1.0762491-.1649999.6262494-.6787493 1.1024989-1.5037486 1.3762487-2.3774978.1237499-.3899996-.045-.8212492-.3974996-1.023749zm-1.9349983.2699997l1.102499.6374994c-.1949998.5362495-.4874995 1.0424991-.8549992 1.4812487l-1.1024921-.6374994c-.8025061.6862493-.918756.7537493-1.9162551 1.1062489v1.2749989c-.5662495.0974999-1.147499.0974999-1.7099984 0V16.293735c-1.0087491-.3562496-1.132499-.4387496-1.9162483-1.1062489l-1.102499.6374994c-.3674996-.4424996-.6599994-.9449992-.8549992-1.4812487l1.102499-.6374994c-.1837498-1.0049991-.1949998-1.1474989 0-2.212498l-1.102499-.6374994c.1949998-.5362495.4874996-1.03874902.8549992-1.48124861l1.102499.63749941c.8024993-.68624936.9187492-.7537493 1.9162483-1.10624898V7.63124299c.5662494-.09374991 1.1512489-.09374991 1.7099984 0v1.27499883c1.0049991.35624967 1.1324989.4349996 1.9162551 1.10624898l1.1024921-.63749941c.3674997.44249959.6599994.94499911.8549992 1.48124861l-1.102499.6374994c.1837499 1.0049991.1949999 1.147499 0 2.212498zm-3.6262466-3.5249967c-1.3349988 0-2.4187478 1.087499-2.4187478 2.4187477 0 1.3312488 1.083749 2.4187478 2.4187478 2.4187478 1.3349987 0 2.4187477-1.087499 2.4187477-2.4187478 0-1.3312487-1.083749-2.4187477-2.4187477-2.4187477zm0 3.6374966c-.6712494 0-1.2187489-.5474995-1.2187489-1.2187489s.5474995-1.2187489 1.2187489-1.2187489 1.2187489.5474995 1.2187489 1.2187489-.5474995 1.2187489-1.2187489 1.2187489zM8.39999229 9.59999118c2.65124761 0 4.79999561-2.14874802 4.79999561-4.79999559C13.1999879 2.14874803 11.0512399 0 8.39999229 0c-2.65124757 0-4.7999956 2.14874803-4.7999956 4.79999559 0 2.65124757 2.14874803 4.79999559 4.7999956 4.79999559zm0-8.39999228c1.98374821 0 3.59999671 1.61624851 3.59999671 3.59999669 0 1.98374818-1.6162485 3.5999967-3.59999671 3.5999967-1.98374818 0-3.5999967-1.61624852-3.5999967-3.5999967S6.41624411 1.1999989 8.39999229 1.1999989zM1.79999835 17.9999835c-.3299997 0-.59999945-.2699998-.59999945-.5999995v-1.5599985c0-2.1187481 1.72124842-3.8399965 3.83999647-3.8399965.73499933 0 1.46624866.5999994 3.35999692.5999994.71999933 0 1.42499869-.1237499 2.11874801-.3262497.01875-.4349996.0675-.8624992.1574999-1.2749988-.3337497.1012499-1.12874897.4012496-2.27624791.4012496-1.76624838 0-2.27999791-.5999994-3.35999692-.5999994C2.25749793 10.7999901 0 13.057488 0 15.8399855v1.5599985c0 .9937491.80624926 1.7999984 1.79999835 1.7999984H14.4374867c-.5999994-.3224997-1.1474989-.7312494-1.6312485-1.1999989H1.79999835z" />
        </svg>
    ),
    'file-pen': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1.35em" height="1.35em" viewBox="0 0 576 512">
            <path d="M0 64C0 28.7 28.7 0 64 0H224V128c0 17.7 14.3 32 32 32H384V285.7l-86.8 86.8c-10.3 10.3-17.5 23.1-21 37.2l-18.7 74.9c-2.3 9.2-1.8 18.8 1.3 27.5H64c-35.3 0-64-28.7-64-64V64zm384 64H256V0L384 128zM549.8 235.7l14.4 14.4c15.6 15.6 15.6 40.9 0 56.6l-29.4 29.4-71-71 29.4-29.4c15.6-15.6 40.9-15.6 56.6 0zM311.9 417L441.1 287.8l71 71L382.9 487.9c-4.1 4.1-9.2 7-14.9 8.4l-60.1 15c-5.5 1.4-11.2-.2-15.2-4.2s-5.6-9.7-4.2-15.2l15-60.1c1.4-5.6 4.3-10.8 8.4-14.9z"/>
        </svg>
    ),
    'checkbox-off': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M21.4285714 0H2.57142857C1.15178571 0 0 1.15178571 0 2.57142857V21.4285714C0 22.8482143 1.15178571 24 2.57142857 24H21.4285714C22.8482143 24 24 22.8482143 24 21.4285714V2.57142857C24 1.15178571 22.8482143 0 21.4285714 0zm.8571429 21.4285714c0 .4714286-.3857143.8571429-.8571429.8571429H2.57142857c-.47142857 0-.85714286-.3857143-.85714286-.8571429V2.57142857c0-.47142857.38571429-.85714286.85714286-.85714286H21.4285714c.4714286 0 .8571429.38571429.8571429.85714286V21.4285714z" />
        </svg>
    ),
    'checkbox-on': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M21.4285714 0H2.57142857C1.15125 0 0 1.15125 0 2.57142857V21.4285714C0 22.84875 1.15125 24 2.57142857 24H21.4285714C22.84875 24 24 22.84875 24 21.4285714V2.57142857C24 1.15125 22.84875 0 21.4285714 0zm0 1.71428571c.4726607 0 .8571429.38453572.8571429.85714286V21.4285714c0 .4726072-.3844822.8571429-.8571429.8571429H2.57142857c-.47260714 0-.85714286-.3845357-.85714286-.8571429V2.57142857c0-.47260714.38453572-.85714286.85714286-.85714286H21.4285714zm-1.8375535 5.26569643l-.4527322-.45642857c-.2500178-.25205357-.6570536-.25371428-.9091071-.00364286L9.47341071 15.2054464l-3.69514285-3.7249821c-.25001786-.2520536-.65705357-.2537143-.90910715-.0036429l-.45642857.4527322c-.25205357.2500178-.25371428.6570535-.00364285.9091071l4.60060714 4.6377857c.25001786.2520536.65705357.2537143.90910714.0036429L19.587375 7.88908929c.252-.25001786.2536607-.65705358.0036429-.90910715z" />
        </svg>
    ),
    'notifications': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M10.4954396 22.5c-.8278125 0-1.49999999-.6740625-1.49999999-1.5014062h-1.5C7.49543961 22.65375 8.84168961 24 10.4954396 24c1.65375 0 3-1.34625 3-3.0014062h-1.5c0 .8273437-.6721875 1.5014062-1.5 1.5014062zm9.8146875-6.8057813c-1.310625-1.2478125-2.3128125-2.5537499-2.3128125-6.9801562 0-3.73078125-2.9714062-6.7734375-6.751875-7.14140625V.75c0-.414375-.335625-.75-.75-.75s-.75.335625-.75.75v.823125c-3.78046874.36796875-6.75187499 3.41015625-6.75187499 7.1409375 0 4.4259375-1.00265625 5.7323437-2.31328125 6.9801562-.65484375.6234376-.8615625 1.5660938-.52734375 2.4014063.34125.853125 1.1625 1.404375 2.0925 1.404375H18.7454396c.93 0 1.75125-.5517187 2.0925-1.4048438.3342188-.8353125.1270313-1.7775-.5278125-2.4009375zM18.7454396 18H2.24543961c-.66703125 0-1.0003125-.7720313-.530625-1.2192187 1.6340625-1.5557813 2.77875-3.2971876 2.77875-8.06625005C4.49356461 5.55609375 7.17809586 3 10.4954396 3c3.316875 0 6.001875 2.555625 6.001875 5.7140625 0 4.7507813 1.1348438 6.5015625 2.77875 8.06625C19.7476271 17.2298438 19.4096584 18 18.7454396 18z" />
        </svg>
    ),
    'missing-user': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M14.7 13.5c-1.3453125 0-1.9921875.75-4.2.75s-2.85-.75-4.2-.75c-3.478125 0-6.3 2.821875-6.3 6.3v1.95C0 22.9921875 1.0078125 24 2.25 24h16.5c1.2421875 0 2.25-1.0078125 2.25-2.25V19.8c0-3.478125-2.821875-6.3-6.3-6.3zm4.8 8.25c0 .4125-.3375.75-.75.75H2.25c-.4125 0-.75-.3375-.75-.75V19.8c0-2.6484375 2.1515625-4.8 4.8-4.8.91875 0 1.8328125.75 4.2.75 2.3625 0 3.28125-.75 4.2-.75 2.6484375 0 4.8 2.1515625 4.8 4.8v1.95zm-9-9.75c3.3140625 0 6-2.6859375 6-6s-2.6859375-6-6-6-6 2.6859375-6 6 2.6859375 6 6 6zm0-10.5C12.9796875 1.5 15 3.5203125 15 6s-2.0203125 4.5-4.5 4.5S6 8.4796875 6 6s2.0203125-4.5 4.5-4.5z" opacity=".5" />
        </svg>
    ),
    'missing-job': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 21">
            <path d="M21.75 4.48457143H16.5V1.12114286C16.5.50171143 15.9965625 0 15.375 0h-6.75C8.0034375 0 7.5.50171143 7.5 1.12114286v3.36342857H2.25c-1.24265625 0-2.25 1.00389-2.25 2.24228571V18.6857143C0 19.92411 1.00734375 20.928 2.25 20.928h19.5c1.2426562 0 2.25-1.00389 2.25-2.2422857V6.72685714c0-1.23839571-1.0073438-2.24228571-2.25-2.24228571zM9 1.49485714h6v2.98971429H9V1.49485714zM22.5 18.6857143c0 .41202-.3365625.7474286-.75.7474286H2.25c-.4134375 0-.75-.3354086-.75-.7474286v-6.7268572H9v1.8685715c0 .6189643.50390625 1.1211428 1.125 1.1211428h3.75c.6210938 0 1.125-.5021785 1.125-1.1211428v-1.8685715h7.5v6.7268572zm-12-5.232v-1.4948572h3v1.4948572h-3zM22.5 10.464h-21V6.72685714c0-.41202.3365625-.74742857.75-.74742857h19.5c.4134375 0 .75.33540857.75.74742857V10.464z" opacity=".5" />
        </svg>
    ),
    'missing-client': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M9 5.015625v1.875c0 .31064063-.25185937.5625-.5625.5625h-1.875c-.31064063 0-.5625-.25185937-.5625-.5625v-1.875c0-.31064062.25185937-.5625.5625-.5625h1.875c.31064063 0 .5625.25185938.5625.5625zm5.4375-.5625h-1.875c-.3106406 0-.5625.25185938-.5625.5625v1.875c0 .31064063.2518594.5625.5625.5625h1.875c.3106406 0 .5625-.25185937.5625-.5625v-1.875c0-.31064062-.2518594-.5625-.5625-.5625zm-6 4.5h-1.875c-.31064063 0-.5625.25185937-.5625.5625v1.875c0 .3106406.25185937.5625.5625.5625h1.875c.31064063 0 .5625-.2518594.5625-.5625v-1.875c0-.31064063-.25185937-.5625-.5625-.5625zm6 0h-1.875c-.3106406 0-.5625.25185937-.5625.5625v1.875c0 .3106406.2518594.5625.5625.5625h1.875c.3106406 0 .5625-.2518594.5625-.5625v-1.875c0-.31064063-.2518594-.5625-.5625-.5625zm-6 4.5h-1.875c-.31064063 0-.5625.2518594-.5625.5625v1.875c0 .3106406.25185937.5625.5625.5625h1.875c.31064063 0 .5625-.2518594.5625-.5625v-1.875c0-.3106406-.25185937-.5625-.5625-.5625zm6 0h-1.875c-.3106406 0-.5625.2518594-.5625.5625v1.875c0 .3106406.2518594.5625.5625.5625h1.875c.3106406 0 .5625-.2518594.5625-.5625v-1.875c0-.3106406-.2518594-.5625-.5625-.5625zM21 23.0625V24H0v-.9375c0-.3106406.25185937-.5625.5625-.5625H1.5V1.125C1.5.50367187 2.00367187 0 2.625 0h15.75c.6213281 0 1.125.50367187 1.125 1.125V22.5h.9375c.3106406 0 .5625.2518594.5625.5625zM18 22.5v-21H3v21h6v-3.984375c0-.3106406.25185937-.5625.5625-.5625h1.875c.3106406 0 .5625.2518594.5625.5625V22.5h6z" opacity=".5" />
        </svg>
    ),
    'location': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M23.625 11.25h-2.6629688C20.600625 6.879375 17.120625 3.399375 12.75 3.03796875V.375c0-.2071875-.1678125-.375-.375-.375h-.75c-.2071875 0-.375.1678125-.375.375v2.66296875C6.879375 3.399375 3.399375 6.879375 3.03796875 11.25H.375c-.2071875 0-.375.1678125-.375.375v.75c0 .2071875.1678125.375.375.375h2.66296875C3.399375 17.120625 6.879375 20.600625 11.25 20.9620312V23.625c0 .2071875.1678125.375.375.375h.75c.2071875 0 .375-.1678125.375-.375v-2.6629688c4.370625-.3614062 7.850625-3.8414062 8.2120312-8.2120312H23.625c.2071875 0 .375-.1678125.375-.375v-.75c0-.2071875-.1678125-.375-.375-.375zM12 19.5c-4.1353125 0-7.5-3.3646875-7.5-7.5 0-4.1353125 3.3646875-7.5 7.5-7.5 4.1353125 0 7.5 3.3646875 7.5 7.5 0 4.1353125-3.3646875 7.5-7.5 7.5zm0-12c-2.4853125 0-4.5 2.0146875-4.5 4.5s2.0146875 4.5 4.5 4.5 4.5-2.0146875 4.5-4.5-2.0146875-4.5-4.5-4.5zm0 7.5c-1.6542188 0-3-1.3457812-3-3s1.3457812-3 3-3c1.6542187 0 3 1.3457812 3 3 0 1.6542187-1.3457812 3-3 3z" />
        </svg>
    ),
    'telephone': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M22.8656393 1.12997566L18.1406988.03780192c-.6890538-.15937299-1.3968574.19687252-1.6781038.84842681l-2.1796601 5.08587342c-.2578092.595305-.0843739 1.29842114.4171823 1.71091594l2.5265306 2.06716146c-1.5937299 3.24370915-4.2327591 5.88742585-7.48115569 7.48115575l-2.06716146-2.5265307c-.4124948-.5015562-1.11561094-.6749915-1.71091594-.4171822l-5.08118598 2.17966c-.65155429.2812465-1.0077998.9843626-.8484268 1.6734164l1.08748629 4.7202531C1.27997377 23.5312559 1.87059133 24 2.55495771 24 14.3766838 24 24 14.4282456 24 2.55495771c0-.68436638-.4687441-1.27498394-1.1343607-1.42498205zM2.58308235 22.5000189l-1.07811141-4.6686912 5.03431157-2.1562228 2.78902735 3.412457c4.85618884-2.2780963 7.48584314-4.9171255 9.75456464-9.7545646l-3.412457-2.78902735 2.1562228-5.03431157 4.6686912 1.07811141C22.4859566 13.5798188 13.5751314 22.4859566 2.58308235 22.5000189z" />
        </svg>
    ),
    'email': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 18">
            <path d="M21.75 0H2.25C1.0078125 0 0 1.0078125 0 2.25v13.5C0 16.9921875 1.0078125 18 2.25 18h19.5c1.2421875 0 2.25-1.0078125 2.25-2.25V2.25C24 1.0078125 22.9921875 0 21.75 0zM2.25 1.5h19.5c.4125 0 .75.3375.75.75v1.940625c-1.0265625.8671875-2.49375 2.0625-7.059375 5.6859375C14.6484375 10.5046875 13.0875 12.01875 12 12c-1.0875.01875-2.653125-1.4953125-3.440625-2.1234375C3.99375 6.253125 2.5265625 5.0578125 1.5 4.190625V2.25c0-.4125.3375-.75.75-.75zm19.5 15H2.25c-.4125 0-.75-.3375-.75-.75V6.140625c1.06875.8765625 2.75625 2.23125 6.1265625 4.9078125C8.5875 11.8171875 10.284375 13.509375 12 13.5c1.70625.0140625 3.3890625-1.6640625 4.3734375-2.4515625C19.74375 8.371875 21.43125 7.0171875 22.5 6.140625V15.75c0 .4125-.3375.75-.75.75z" />
        </svg>
    ),
    'user-manager': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 17">
            <path d="M18 8.4c1.9875 0 3.6-1.6125 3.6-3.6S19.9875 1.2 18 1.2s-3.6 1.6125-3.6 3.6 1.6125 3.6 3.6 3.6zm0-6c1.32375 0 2.4 1.07625 2.4 2.4S19.32375 7.2 18 7.2s-2.4-1.07625-2.4-2.4 1.07625-2.4 2.4-2.4zm-10.8 6c2.32125 0 4.2-1.87875 4.2-4.2S9.52125 0 7.2 0 3 1.87875 3 4.2s1.87875 4.2 4.2 4.2zm0-7.2c1.65375 0 3 1.34625 3 3s-1.34625 3-3 3-3-1.34625-3-3 1.34625-3 3-3zm3.00375 7.95C8.95125 9.15 8.64 9.6 7.2 9.6c-1.44 0-1.75125-.45-3.00375-.45-1.36125 0-2.685.6075-3.46125 1.75875C.27 11.59875 0 12.4275 0 13.32V15c0 .99375.80625 1.8 1.8 1.8h10.8c.99375 0 1.8-.80625 1.8-1.8v-1.68c0-.8925-.27-1.72125-.735-2.41125C12.88875 9.7575 11.565 9.15 10.20375 9.15zM13.2 15c0 .33-.27.6-.6.6H1.8c-.33 0-.6-.27-.6-.6v-1.68c0-.6225.18375-1.22625.52875-1.74.5175-.76875 1.44-1.23 2.46375-1.23 1.0275 0 1.395.45 3.0075.45 1.6125 0 1.98-.45 3.00375-.45 1.02375 0 1.94625.46125 2.46375 1.23.345.51375.52875 1.1175.52875 1.74V15H13.2zm10.18875-4.30875C22.74 9.73125 21.6375 9.225 20.505 9.225c-1.0425 0-1.305.375-2.505.375s-1.4625-.375-2.505-.375c-.495 0-.97875.1125-1.42875.30375.57.5775.69375.885.7575.9975.21375-.06.435-.0975.67125-.0975.8175 0 1.125.375 2.505.375s1.6875-.375 2.505-.375c.7875 0 1.4925.34875 1.89.9375.26625.39375.40875.85875.40875 1.33875V14.1c0 .165-.135.3-.3.3H15.6c0 .66375.01125.84375-.06 1.2h6.96c.82875 0 1.5-.67125 1.5-1.5v-1.39875c0-.74625-.225-1.43625-.61125-2.01z" />
        </svg>
    ),
    'user-profile': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 20">
            <path d="M8.40032814 9.60037501c2.65135356 0 4.80018746-2.14883393 4.80018746-4.8001875S11.0516817 0 8.40032814 0C5.74897457 0 3.60014063 2.14883394 3.60014063 4.80018751s2.14883394 4.8001875 4.80018751 4.8001875zm0-8.40032813c1.98382746 0 3.60014066 1.61631313 3.60014066 3.60014063 0 1.98382749-1.6163132 3.60014063-3.60014066 3.60014063-1.9838275 0-3.60014063-1.61631314-3.60014063-3.60014063 0-1.9838275 1.61631313-3.60014063 3.60014063-3.60014063zM23.6484238 8.85409586l-1.3013009-1.30130083c-.2362592-.23625923-.5437712-.35251377-.8550334-.35251377-.307512 0-.6187741.11625454-.8550334.35251377l-8.3440759 8.34782607-.2850111 2.5575999c-.0450018.4012657.2700105.742529.6637759.742529.026251 0 .0487519 0 .0750029-.0037501l2.5575999-.2850112 8.344076-8.3440759c.4687683-.4762686.4687683-1.24129848 0-1.71381694zm-8.8990977 8.91034804l-1.4775577.1687566.1650065-1.4813078 5.8839798-5.8839799 1.3125513 1.3125513-5.8839799 5.8839798zm6.731513-6.7315129l-1.3125513-1.3125513 1.3200516-1.32005156h.0037501l.0075003.00375014 1.3013009 1.31255128-1.3200516 1.31630144zm-16.44064222.9675378c.73502871 0 1.46630728.6000234 3.36013126.6000234 1.89007386 0 2.62510256-.6000234 3.36013126-.6000234.7762803 0 1.4963084.2362592 2.100082.6337747l.8550334-.8550334c-.8325325-.6075237-1.8488222-.9750427-2.9551154-.9750427-1.076292 0-1.5938123.6000281-3.36013126.6000281-1.766319 0-2.28008907-.6000281-3.36013126-.6000281C2.25758819 10.8004219 0 13.0580101 0 15.8406188v1.5600609c0 .9937888.8062815 1.8000703 1.80007032 1.8000703H10.931677c-.1050041-.2775108-.153756-.5775225-.1200047-.8775342l.0375015-.3225126H1.80007032c-.3300129 0-.60002344-.2700106-.60002344-.6000235v-1.5600609c0-2.1188328 1.72131724-3.84015 3.84015-3.84015z" />
        </svg>
    ),
    'upload-files': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 21">
            <path d="M23.3666667 13.5269531L19.425 9.88886719c-.1-.09433594-.2583333-.08613281-.3541667.01230469L18.5041667 10.5c-.0958334.0984375-.0875.2542969.0125.3486328L20.9875 13.1291016H16.175l-1.3333333 2.625H9.15833333L7.825 13.1291016H3.01666667L5.4875 10.8486328c.1-.0943359.10416667-.2501953.0125-.3486328l-.57083333-.59882812C4.83333333 9.80273438 4.675 9.79863281 4.575 9.88886719L.63333333 13.5269531C.22916667 13.8960938 0 14.4169922 0 14.9625v4.06875C0 20.1181641.89583333 21 2 21h20c1.1041667 0 2-.8818359 2-1.96875V14.9625c0-.5455078-.2291667-1.0664062-.6333333-1.4355469zm-.7 5.5042969c0 .3609375-.3.65625-.6666667.65625H2c-.36666667 0-.66666667-.2953125-.66666667-.65625v-3.9375c0-.3609375.3-.65625.66666667-.65625h5l1.33333333 2.625h7.33333337L17 14.4375h5c.3666667 0 .6666667.2953125.6666667.65625v3.9375zM17.3333333 5.25h-2.6666666V.984375c0-.54140625-.45-.984375-1-.984375h-3.3333334c-.54999997 0-.99999997.44296875-.99999997.984375V5.25H6.66666667C5.48333333 5.25 4.88333333 6.66503906 5.725 7.48945312l5.3333333 5.24999998c.5208334.5126953 1.3666667.5126953 1.8875 0l5.3333334-5.24999997C19.1125 6.66503906 18.5208333 5.25 17.3333333 5.25zM12 11.8125l-5.33333333-5.25h4.00000003v-5.25h2.6666666v5.25h4L12 11.8125z" />
        </svg>
    ),
    'drag-files': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 1024 1024">
            <path d="M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"></path>
        </svg>
    ),
    'share': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 21">
            <path d="M23.5377917 8.05341797L16.2045833.50719922c-1.0289166-1.05885938-2.87125-.35355469-2.87125 1.13408203v3.61569141C6.4395 5.33838867 0 6.56672461 0 13.5839648c0 3.8956641 2.32666667 6.1618184 3.71375 7.1601387 1.00970833.7268789 2.41754167-.20475 2.07-1.4154492-1.57804167-5.5110234 1.11520833-6.1227715 7.5495833-6.1970918v3.6028125c0 1.4863652 1.84125 2.1940898 2.87125 1.134082l7.33325-7.546875c.61625-.63451169.61625-1.63365231-.0000416-2.26816403zm-.963625 1.36089844l-7.3333334 7.54687499c-.2055416.2116406-.5741666.070957-.5741666-.2268164V11.8125c-7.14150003 0-12.30470837.3981387-10.16583337 7.8626953-1.50083333-1.0803516-3.1675-3.039668-3.1675-6.0912305C1.33333333 7.02105469 8.12025 6.5625 14.6666667 6.5625V1.640625c0-.29785547.3687916-.43845703.5741666-.22681641l7.3333334 7.546875c.1237532.12667566.1237532.32695716 0 .45363282z" />
        </svg>
    ),
    'link': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M14.1355789 18.476358l-3.7052631 3.6975875c-2.37557896 2.3719378-6.22395323 2.3731051-8.60070176 0-2.3765614-2.3709572-2.377731-6.2118443 0-8.5839688l3.70479532-3.69805452a6.20903882 6.20903882 0 01.16523976-.15889494c.35321638-.32708171.92608187-.09357199.94540351.38689496.00902924.2244513.02797661.4485759.05688889.6716264.02250292.1735565-.03490058.3477199-.15892398.4714086-.77099415.7689805-3.5154152 3.5079222-3.52280701 3.5152996-1.71873685 1.7163736-1.71840936 4.4912685 0 6.2073152 1.71971929 1.715393 4.50002339 1.7150661 6.2194152 0l3.70526317-3.6980545.0168421-.0168093c1.6982924-1.7123113 1.6907602-4.4857588-.0173099-6.1905059-.3842807-.383533-.8223158-.6807782-1.2905264-.892249-.213614-.0964669-.3474152-.3112996-.3337543-.54490272.0140951-.24207155.0585109-.48142832.1322105-.71248249.0983859-.3082179.4458947-.46510505.7447485-.33992218.7051696.29528405 1.3654737.72933853 1.9384795 1.30122958 2.3713685 2.36680151 2.3709474 6.21805451 0 8.58448251zm-4.22437422-4.3682802c.57300582.5718911 1.23330992 1.0059455 1.93847952 1.3012296.2988538.1251362.6463626-.031751.7447485-.3399222.0736996-.2310542.1181154-.4704109.1322106-.7124825.0136608-.2336031-.1201872-.4484358-.3337544-.5449027-.4682105-.2114241-.9062456-.5086693-1.2905263-.892249-1.7080702-1.7047471-1.71560237-4.47819458-.01731-6.19050586l.0168421-.01680934 3.7052632-3.69805448c1.7193918-1.71506614 4.4996959-1.71539299 6.2194152 0 1.7184094 1.7160467 1.7187368 4.49094164 0 6.20731518-.007345.00733074-2.7517661 2.7463191-3.522807 3.5152996-.1240234.1237354-.1814269.2978521-.158924.4714086.0288816.2229282.0478631.4470229.0568889.6716264.0193216.4805136.5922339.7139767.9454035.386895a6.13870693 6.13870693 0 00.1652398-.158895l3.7047953-3.6980544c2.377731-2.37212456 2.3765614-6.21301172 0-8.58396892-2.3767018-2.37310506-6.2251228-2.37193774-8.6007018 0L9.91120468 5.52359533c-2.37094737 2.36647471-2.37136842 6.21772767 0 8.58448247z" />
        </svg>
    ),
    'user-accept-invite': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 19">
            <path d="M23.8764431 5.36601562l-.5279252-.52695312c-.1160686-.11503906-.3070202-.11875-.4230889 0l-4.3132605 4.14511719-1.9956318-1.98164063c-.1160687-.11503906-.3070203-.11503906-.423089 0l-.5316692.52324219c-.1160687.11503906-.1160687.30429687 0 .41933594l2.6283931 2.61250001c.175975.1744141.4605304.1744141.6365055 0l4.9460218-4.77226564c.1198128-.11503906.1198128-.30429687.0037442-.41933594zM11.7416537 10.6875c-1.074571 0-1.5912637.59375-3.35475822.59375-1.76349454 0-2.27644306-.59375-3.35475819-.59375C2.25397816 10.6875 0 12.9214844 0 15.675v1.54375C0 18.2021484.8049922 19 1.79719189 19H14.9765991c.9921997 0 1.7971919-.7978516 1.7971919-1.78125V15.675c0-2.7535156-2.2539782-4.9875-5.0321373-4.9875zm3.8340093 6.53125c0 .3265625-.2695788.59375-.5990639.59375H1.79719189c-.32948518 0-.59906396-.2671875-.59906396-.59375V15.675c0-2.0966797 1.71856474-3.8 3.83400936-3.8.73385335 0 1.46396255.59375 3.35475819.59375 1.88705152 0 2.62090482-.59375 3.35475822-.59375 2.1154446 0 3.8340093 1.7033203 3.8340093 3.8v1.54375zM8.38689548 9.5c2.64711392 0 4.79251172-2.12636719 4.79251172-4.75S11.0340094 0 8.38689548 0c-2.64711389 0-4.7925117 2.12636719-4.7925117 4.75S5.73978159 9.5 8.38689548 9.5zm0-8.3125c1.98065522 0 3.59438382 1.59941406 3.59438382 3.5625s-1.6137286 3.5625-3.59438382 3.5625c-1.98065523 0-3.59438378-1.59941406-3.59438378-3.5625s1.61372855-3.5625 3.59438378-3.5625z" />
        </svg>
    ),
    'show-password': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 16">
            <path d="M12 9.33333333c1.4727593 0 2.6666667-1.19390733 2.6666667-2.66666666C14.6666667 5.19390733 13.4727593 4 12 4c-.0416667 0-.0783333.01-.11875.01208333.2623433.72247144.0826676 1.53149685-.4608344 2.07499891-.5435021.54350206-1.3525275.7231777-2.07499893.46083443 0 .04166666-.01208334.07833333-.01208334.11875C9.33333333 8.139426 10.5272407 9.33333333 12 9.33333333zm11.855-1.94166666C21.5954167 2.98291667 17.1220833 0 12 0 6.87791667 0 2.40333333 2.985.145 7.39208333c-.19330295.38237492-.19330295.83387508 0 1.21625C2.40458333 13.0170833 6.87791667 16 12 16c5.1220833 0 9.5966667-2.985 11.855-7.39208333.193303-.38237492.193303-.83387508 0-1.21625zM12 1.33333333c2.9455187 0 5.3333333 2.38781467 5.3333333 5.33333334C17.3333333 9.61218533 14.9455187 12 12 12c-2.94551867 0-5.33333333-2.38781467-5.33333333-5.33333333C6.6698816 3.7224807 9.05581403 1.33654827 12 1.33333333zm0 13.33333337c-4.47333333 0-8.56083333-2.5545834-10.66666667-6.6666667 1.18434593-2.32503692 3.07836338-4.21248704 5.4075-5.38875-.86958333 1.12583333-1.4075 2.52083333-1.4075 4.05541667 0 3.68189833 2.98476834 6.66666663 6.66666667 6.66666663 3.6818983 0 6.6666667-2.9847683 6.6666667-6.66666663 0-1.53458334-.5379167-2.92958334-1.4075-4.05541667 2.3291366 1.17626296 4.223154 3.06371308 5.4075 5.38875C20.56125 12.1120833 16.4733333 14.6666667 12 14.6666667z" />
        </svg>
    ),
    'hide-password': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 19">
            <path d="M23.850234 17.9742218L.86115445.1016537C.7319986-.00025397.54363415.02042743.440312.14785992l-.37441497.4624319c-.10330807.12741485-.08252911.31336894.04642745.41548639L23.1014041 18.8983463c.0620481.0490559.1413027.0717496.2203081.063083.0790053-.0086667.1512811-.0479829.2009087-.1092892l.374415-.4624319c.1032213-.1275121.0822733-.3134793-.0468019-.4154864zM11.9812793 3.58560311c2.6456335.00285216 4.7896227 2.11955486 4.7925117 4.73151751 0 .79918288-.2209049 1.54107004-.5765991 2.20200388l.9528861.7392996c.5092044-.8679377.8218409-1.86414394.8218409-2.94130348 0-1.36142023-.4833698-2.59900778-1.2647738-3.59780155C18.8006397 5.76274629 20.5032265 7.43719083 21.5681747 9.5c-.6364303 1.2198123-1.4968276 2.3122988-2.5374103 3.2218677l.9480188.7370817c1.0659594-.9714397 1.9795319-2.1166148 2.6557254-3.4196303.1737012-.33922759.1737012-.73978026 0-1.07900782-2.0304524-3.91126459-6.0501716-6.55758916-10.6532293-6.55758916-1.681748-.00087871-3.34376285.35763435-4.87188772 1.05091601l1.71369734 1.3307393c.84580344-.73597276 1.94358818-1.19877432 3.15819038-1.19877432zm2.2786895 5.4275681c.2380733-.71642297.1109841-1.50225911-.3412186-2.10987074-.4522026-.60761164-1.1738273-.96216784-1.9374709-.9519386-.0374415 0-.0707645.00887159-.1067083.01071984.1453299.38422767.1421307.80785716-.008986 1.18990273l2.3943838 1.86118677zM6.11195008 7.19597276c-.07784675.36884266-.11848209.74439337-.12135443 1.12114786-.00777258 2.01389368 1.02684746 3.89222028 2.743326 4.98035938 1.71647855 1.088139 3.87700385 1.2353329 5.72860565.3902826l-1.1075195-.8609144c-.4380655.1304864-.8929797.2217899-1.3733541.2217899-2.6456944-.0028517-4.78979093-2.119495-4.79288615-4.73151748 0-.09019455.02209048-.17447471.02695788-.26319066l-1.10377535-.8579572zm5.86932922 8.21842414c-4.01971924 0-7.69310457-2.266323-9.58502345-5.9143969.6526053-1.25680934 1.53847114-2.31844358 2.55762871-3.20560311l-.96823713-.75297666c-1.06633386.97143969-1.97953199 2.11624514-2.65572543 3.4192607-.17370125.33922756-.17370125.73978023 0 1.07900777 2.02895476 3.9112646 6.04867394 6.5575891 10.6513573 6.5575891 1.6817453.0008554 3.3437552-.3576566 4.8718876-1.0509159l-1.0951638-.8501946c-1.191014.4535603-2.4606552.7182296-3.7767238.7182296z" />
        </svg>
    ),
    'sign-out': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 18">
            <path d="M7.5 7.1765625c0-.4125.3375-.75.75-.75H15V2.025c0-.3328125.403125-.5015625.6375-.2671875L22.275 8.465625c.2953125.2953125.2953125.76875 0 1.0640625L15.6375 16.2375c-.234375.234375-.6375.0703125-.6375-.2671875V11.56875H8.25c-.4125 0-.75-.3375-.75-.75V7.1765625zm-1.5 0V10.81875c0 1.2421875 1.0078125 2.25 2.25 2.25h5.25v2.9015625c0 1.6640625 2.015625 2.5078125 3.196875 1.3265625l6.6421875-6.703125c.88125-.88125.88125-2.30625 0-3.1875L16.696875.6984375C15.5203125-.478125 13.5.35625 13.5 2.025v2.9015625H8.25c-1.2421875 0-2.25 1.0125-2.25 2.25zM0 2.25v13.5C0 16.9921875 1.0078125 18 2.25 18h6.1875C8.746875 18 9 17.746875 9 17.4375v-.375c0-.309375-.253125-.5625-.5625-.5625H2.25c-.4125 0-.75-.3375-.75-.75V2.25c0-.4125.3375-.75.75-.75h6.1875C8.746875 1.5 9 1.246875 9 .9375v-.375C9 .253125 8.746875 0 8.4375 0H2.25C1.0078125 0 0 1.0078125 0 2.25z" />
        </svg>
    ),
    'sign-in': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 18">
            <path d="M1.5 7.1765625c0-.4125.3375-.75.75-.75H9V2.025c0-.3328125.403125-.5015625.6375-.2671875L16.275 8.465625c.2953125.2953125.2953125.76875 0 1.0640625L9.6375 16.2375c-.234375.234375-.6375.0703125-.6375-.2671875V11.56875H2.25c-.4125 0-.75-.3375-.75-.75V7.1765625zm-1.5 0V10.81875c0 1.2421875 1.0078125 2.25 2.25 2.25H7.5v2.9015625c0 1.6640625 2.015625 2.5078125 3.196875 1.3265625l6.6421875-6.703125c.88125-.88125.88125-2.30625 0-3.1875L10.696875.6984375C9.5203125-.478125 7.5.35625 7.5 2.025v2.9015625H2.25c-1.2421875 0-2.25 1.0125-2.25 2.25zM24 15.75V2.25C24 1.0078125 22.9921875 0 21.75 0h-6.1875C15.253125 0 15 .253125 15 .5625v.375c0 .309375.253125.5625.5625.5625H21.75c.4125 0 .75.3375.75.75v13.5c0 .4125-.3375.75-.75.75h-6.1875c-.309375 0-.5625.253125-.5625.5625v.375c0 .309375.253125.5625.5625.5625H21.75c1.2421875 0 2.25-1.0078125 2.25-2.25z" />
        </svg>
    ),
    'help': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 16 24">
            <path d="M8.02140967 0C4.39230183 0 2.04171806 1.53295312.1962695 4.26684375-.13850192 4.76278125-.03529247 5.44275.43169617 5.8078125l1.96134322 1.53323437c.47162625.36867188 1.14266951.28246875 1.51190472-.1944375C5.04383988 5.675625 5.88865693 4.8286875 7.66763315 4.8286875c1.39873806 0 3.12883755.92807812 3.12883755 2.32645312 0 1.057125-.84645385 1.60003125-2.22755083 2.3983125-1.6105675.93093748-3.7418653 2.08949998-3.7418653 4.98773438V15c0 .6213281.48853987 1.125 1.0912012 1.125h3.2950184c.60266133 0 1.09120123-.5036719 1.09120123-1.125v-.2706094C10.3044754 12.7203281 16 12.6366562 16 7.2 16 3.10575 11.88067 0 8.02140967 0zM7.5657877 17.5058906c-1.7366467 0-3.14952491 1.4566406-3.14952491 3.2470781C4.41626279 22.5433594 5.829141 24 7.5657877 24c1.7366467 0 3.1495249-1.4566406 3.1495249-3.2470781s-1.4128782-3.2470313-3.1495249-3.2470313z" />
        </svg>
    ),
    'copy': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 19 24">
            <path d="M17.4165083 21.6580364c0 .4-.35625.7272727-.7916666.7272727H2.37484167c-.43541667 0-.79166667-.3272727-.79166667-.7272727V5.65803636c0-.4.35625-.72727272.79166667-.72727272h2.375v.90909091c0 .29963636.26758333.54545454.59375.54545454h8.31250003c.3269583 0 .59375-.24581818.59375-.54545454v-.90909091h2.375c.4354166 0 .7916666.32727272.7916666.72727272V21.6580364zM9.49984167 2.02167273c.65866663 0 1.18750003.48654545 1.18750003 1.09090909 0 .60436363-.5288334 1.09090909-1.18750003 1.09090909-.657875 0-1.1875-.48654546-1.1875-1.09090909 0-.60436364.529625-1.09090909 1.1875-1.09090909zm7.12500003 1.45454545h-4.3834584c.0197917-.11781818.0292917-.24072727.0292917-.36363636 0-1.40509091-1.2413333-2.54545455-2.77083333-2.54545455-1.52870834 0-2.77083334 1.14036364-2.77083334 2.54545455 0 .12290909.01029167.24581818.03008334.36363636h-4.38425c-1.311 0-2.375.97745455-2.375 2.18181818V21.6580364c0 1.2043636 1.064 2.1818181 2.375 2.1818181H16.6248417c1.311 0 2.375-.9774545 2.375-2.1818181V5.65803636c0-1.20436363-1.064-2.18181818-2.375-2.18181818z" />
        </svg>
    ),
    'rollForward': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 32 32" version="1.1">
            <path d="M27.6619315,4.51050917 C28.1049876,5.09852838 28.0169632,5.95866287 27.4653436,6.43095489 L16.3273224,15.973756 L27.5210924,25.5697291 C28.072712,26.0420211 28.1607364,26.9021556 27.7176802,27.4901748 C27.4624095,27.8248453 27.092707,28 26.7171362,28 C26.4354581,28 26.1508459,27.8999116 25.91318,27.6997349 L13.4782659,17.037195 C13.1760488,16.7775908 13,16.3866206 13,15.9706283 C13,15.5577637 13.1760488,15.1636657 13.4782659,14.9071893 L25.8603653,4.30094913 C26.4119849,3.82865711 27.2188753,3.92248996 27.6619315,4.51050917 Z M18.6619315,4.51050917 C19.1049876,5.09852838 19.0169632,5.95866287 18.4653436,6.43095489 L7.32732245,15.973756 L18.5210924,25.5697291 C19.072712,26.0420211 19.1607364,26.9021556 18.7176802,27.4901748 C18.4624095,27.8248453 18.092707,28 17.7171362,28 C17.4354581,28 17.1508459,27.8999116 16.91318,27.6997349 L4.47826593,17.037195 C4.17604881,16.7775908 4,16.3866206 4,15.9706283 C4,15.5577637 4.17604881,15.1636657 4.47826593,14.9071893 L16.8603653,4.30094913 C17.4119849,3.82865711 18.2188753,3.92248996 18.6619315,4.51050917 Z" id="chevron-double-left" fill="#2D2D2D" transform="translate(16.000000, 16.000000) scale(-1, 1) translate(-16.000000, -16.000000) "/>
        </svg>
    ),
    'duplicateBar': () => (
        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 21C0 22.6547 1.34531 24 3 24H13.5C15.1547 24 16.5 22.6547 16.5 21V18H10.5C8.01562 18 6 15.9844 6 13.5V7.5H3C1.34531 7.5 0 8.84531 0 10.5V21ZM10.5 16.5H21C22.6547 16.5 24 15.1547 24 13.5V3C24 1.34531 22.6547 0 21 0H10.5C8.84531 0 7.5 1.34531 7.5 3V13.5C7.5 15.1547 8.84531 16.5 10.5 16.5Z" />
        </svg>
    ),
    'paste': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 20 24">
            <path d="M15.20096 12.39V9.93c.12.0225.232.0825.32.1575l2.216 2.0175c.088.075.152.17925.184.285h-2.72zm2.488 9.3225c0 .3375-.296.615-.656.615h-9.152c-.352 0-.656-.2775-.656-.615V10.53c0-.33825.304-.62325.656-.62325h5.88V12.705c0 .51675.44.93.984.93h2.944v8.0775zm-15.448-5.445c-.288 0-.52-.225-.52-.5025V4.7775c0-.27.232-.49575.52-.49575h1.56V4.905c0 .21.176.375.392.375h5.472c.216 0 .392-.165.392-.375v-.62325h1.56c.288 0 .52.22575.52.49575v3.62925h-4.48c-1.136 0-2.056.86325-2.056 1.92825v5.9325h-3.36zM6.96896 1.77c.472 0 .848.35925.848.8025 0 .4425-.376.80175-.848.80175-.48 0-.856-.35925-.856-.80175 0-.44325.376-.8025.856-.8025zm11.744 9.285l-2.224-2.08575c-.384-.35925-.912-.5625-1.456-.5625h-1.296V4.425c0-.885-.76-1.5975-1.704-1.5975h-3.136c.008-.09.016-.18.016-.26325 0-1.0275-.888-1.86675-1.984-1.86675s-1.984.83925-1.984 1.86675c0 .08325.008.17325.016.26325h-3.136c-.944 0-1.704.7125-1.704 1.5975v11.69925c0 .8775.76 1.5975 1.704 1.5975h3.776v4.185c0 1.065.92 1.92825 2.056 1.92825h9.6c1.136 0 2.056-.86325 2.056-1.92825V12.42c0-.51075-.216-1.005-.6-1.365z" />
        </svg>
    ),
    'status-planned': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M12,0 C5.37096774,0 0,5.37096774 0,12 C0,18.6290323 5.37096774,24 12,24 C18.6290323,24 24,18.6290323 24,12 C24,5.37096774 18.6290323,0 12,0 Z" />
        </svg>
    ),
    'status-done': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M21.4285714 24H2.57142857C1.15125 24 0 22.84875 0 21.4285714V2.57142857C0 1.15125 1.15125 0 2.57142857 0H21.4285714C22.84875 0 24 1.15125 24 2.57142857V21.4285714C24 22.84875 22.84875 24 21.4285714 24zM10.46325 18.7468393l9.8571429-9.85714287c.3347142-.33471429.3347142-.87744643 0-1.21216072L19.1082321 6.465375c-.3347142-.33471429-.8774464-.33476786-1.2122142 0l-8.03887504 8.0388214-3.75316072-3.7531607c-.33471428-.3347143-.87744643-.3347143-1.21221428 0l-1.21216072 1.2121607c-.33471428.3347143-.33471428.8774465 0 1.2121607l5.57142857 5.5714286c.33476786.3348214.87744639.3348214 1.21221429.0000536z" />
        </svg>
    ),
    'status-inprogress': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M19.89375 10.0444444l-16.5-9.73567247C2.053125-.48187135 0 .28538012 0 2.24093567V21.7076023c0 1.754386 1.9078125 2.8116959 3.39375 1.9321638l16.5-9.7309942c1.471875-.865497 1.4765625-2.9988304 0-3.8643275z"/>
        </svg>
    ),
    'status-unconfirmed': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="4 4 24 24">
            <path d="M16,4 C22.627417,4 28,9.372583 28,16 C28,22.627417 22.627417,28 16,28 C9.372583,28 4,22.627417 4,16 C4,9.372583 9.372583,4 16,4 Z M16,6 C10.4771525,6 6,10.4771525 6,16 C6,21.5228475 10.4771525,26 16,26 C21.5228475,26 26,21.5228475 26,16 C26,10.4771525 21.5228475,6 16,6 Z" />
        </svg>
    ),
    'close': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M14.5459546 12.000375l7.6922403-7.69224037 1.5862996-1.58629957c.2340073-.23400732.2340073-.6142692 0-.84827651L22.1271915.17625551c-.2340073-.23400731-.6142692-.23400731-.8482765 0l-9.27854 9.27853995L2.72183506.17550549c-.23400732-.23400732-.6142692-.23400732-.84827651 0L.17550548 1.87280852c-.2340073.23400731-.2340073.61426919 0 .8482765L9.45479547 12.000375l-9.27928997 9.27854c-.23400732.2340073-.23400732.6142692 0 .8482765l1.69730304 1.697303c.23400731.2340073.61426919.2340073.8482765 0l9.27928997-9.2785399 7.6922404 7.6922403 1.5862996 1.5862996c.2340073.2340073.6142692.2340073.8482765 0l1.697303-1.697303c.2340073-.2340073.2340073-.6142692 0-.8482765l-9.2785399-9.27854z" />
        </svg>
    ),
    'close-circle': () => (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
            <path d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"></path>
        </svg>
    ),
    'cancel': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 24">
            <path d="M14.5459546 12.000375l7.6922403-7.69224037 1.5862996-1.58629957c.2340073-.23400732.2340073-.6142692 0-.84827651L22.1271915.17625551c-.2340073-.23400731-.6142692-.23400731-.8482765 0l-9.27854 9.27853995L2.72183506.17550549c-.23400732-.23400732-.6142692-.23400732-.84827651 0L.17550548 1.87280852c-.2340073.23400731-.2340073.61426919 0 .8482765L9.45479547 12.000375l-9.27928997 9.27854c-.23400732.2340073-.23400732.6142692 0 .8482765l1.69730304 1.697303c.23400731.2340073.61426919.2340073.8482765 0l9.27928997-9.2785399 7.6922404 7.6922403 1.5862996 1.5862996c.2340073.2340073.6142692.2340073.8482765 0l1.697303-1.697303c.2340073-.2340073.2340073-.6142692 0-.8482765l-9.2785399-9.27854z" />
        </svg>
    ),
    'submit': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 19">
            <path d="M22.2757525.19057087L7.0921698 15.3741536l-5.36792233-5.3679223c-.25409449-.2540945-.66608978-.2540945-.9202385 0l-.6134381.6134381c-.25409449.2540945-.25409449.6660897 0 .9202385l6.44150683 6.4415068c.2540945.2540945.6660897.2540945.9202385 0L23.8094291 1.72430169c.2540945-.25409449.2540945-.66608978 0-.92023849l-.6134923-.61349233c-.2540945-.25409449-.6660898-.25409449-.9201843 0z" />
        </svg>
    ),
    'dashboard': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 21">
            <path d="M19.875 15.75h.75c.20625 0 .375-.196875.375-.4375V2.1875c0-.240625-.16875-.4375-.375-.4375h-.75c-.20625 0-.375.196875-.375.4375v13.125c0 .240625.16875.4375.375.4375zm-4.5 0h.75c.20625 0 .375-.196875.375-.4375v-7.875c0-.240625-.16875-.4375-.375-.4375h-.75c-.20625 0-.375.196875-.375.4375v7.875c0 .240625.16875.4375.375.4375zm-9 0h.75c.20625 0 .375-.196875.375-.4375v-4.375c0-.240625-.16875-.4375-.375-.4375h-.75c-.20625 0-.375.196875-.375.4375v4.375c0 .240625.16875.4375.375.4375zm4.5 0h.75c.20625 0 .375-.196875.375-.4375V3.9375c0-.240625-.16875-.4375-.375-.4375h-.75c-.20625 0-.375.196875-.375.4375v11.375c0 .240625.16875.4375.375.4375zm12.75 3.5H1.5V.4375C1.5.19578125 1.3321875 0 1.125 0h-.75C.1678125 0 0 .19578125 0 .4375V20.125c0 .4834375.335625.875.75.875h22.875c.2071875 0 .375-.1957812.375-.4375v-.875c0-.2417188-.1678125-.4375-.375-.4375z" />
        </svg>
    ),
    'change-order': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 24 25">
            <path d="M24 12.0473438c0-.4678125-.2723438-.8878125-.6923437-1.0692188l-4.6753125-2.02828125 4.6734374-2.0278125C23.7276562 6.7396875 24 6.32015625 24 5.8528125c0-.46734375-.2723438-.886875-.6923437-1.06828125L12.7471875.20296875c-.4734375-.208125-1.0176563-.20859375-1.4939062-.0009375L.69421875 4.7840625C.27234375 4.96640625 0 5.3859375 0 5.85328125c0 .46734375.27234375.886875.69234375 1.06828125l4.67484375 2.02875L.69375 10.978125C.27234375 11.16 0 11.5795313 0 12.0473438c0 .4673437.27234375.886875.69234375 1.0682812l4.674375 2.0278125L.69375 17.1707813C.27234375 17.3535938 0 17.773125 0 18.2409375c0 .4673438.27234375.886875.69234375 1.0682813l10.56187495 4.5825c.2352404.1020568.4888881.1548628.7453126.1551562.2540625 0 .508125-.0515625.7453124-.1551562l10.5604688-4.5815625c.421875-.1823438.6942187-.601875.6942187-1.0692188 0-.4678125-.2723437-.8878125-.6923437-1.0692187l-4.674375-2.0278125 4.6729687-2.0273438C23.7276562 12.9342188 24 12.5146875 24 12.0473438zM2.12015625 5.85328125l9.75140625-4.2309375.0014063-.0009375c.0815625-.03328125.1710937-.035625.2554687.0009375l9.7514062 4.2309375-9.7514062 4.23140625c-.0848437.0360938-.1753125.0360938-.256875 0L2.12015625 5.85328125zM21.8798437 18.2409375l-9.7514062 4.2309375c-.0848437.0360937-.1753125.0360937-.256875 0l-9.75140625-4.2314062 5.191875-2.2528125 3.94265625 1.7104687c.2352403.1020569.488888.1548603.7453125.1551563.2540625 0 .508125-.0515625.7453125-.1551563l3.9426562-1.7104687 5.191875 2.2532812zm-9.7514062-1.9626562c-.0848437.0360937-.1753125.0360937-.256875 0L2.12015625 12.046875 7.3125 9.7940625 11.2546875 11.505c.2352403.1020569.488888.1548603.7453125.1551563.2540625 0 .508125-.0515625.7453125-.1551563L16.6875 9.79453125l5.1923437 2.25328125-9.7514062 4.2304688z" />
        </svg>
    ),
    'date-stepper': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 3 4">
            <rect transform="matrix(1 0 0 -1 -322 4318)" x="322" y="4314" width="3" height="4" rx="1.5" fillRule="evenodd" />
        </svg>
    ),
    'warning-triangle-yellow': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 15 15" id="Atom/icon/exclamation-triangle">
            <path d="M13.5745234,11.1071274 L7.96161179,1.37402513 C7.53110919,0.625324957 6.44783362,0.625324957 6.01499133,1.37402513 L0.402079723,11.1071274 C-0.0284228769,11.8534879 0.509705373,12.7917028 1.37538995,12.7917028 L12.6035529,12.7917028 C13.4645581,12.7917028 14.0073657,11.8558276 13.5745234,11.1071274 Z M12.6035529,12.0430026 L1.37305026,12.0430026 C1.08526863,12.0430026 0.905112652,11.7318241 1.04783362,11.4814775 L6.66308492,1.74837522 C6.80580589,1.50036828 7.16845754,1.4980286 7.31117851,1.74837522 L12.9264298,11.4814775 C13.0714905,11.7294844 12.8913345,12.0430026 12.6035529,12.0430026 Z" id="exclamation-triangle" fill="#2D2D2D"/>
            <path d="M12.6056609,12.031511 L1.37515829,12.031511 C1.08737666,12.031511 0.907220678,11.7203325 1.04994165,11.4699859 L6.66519295,1.73688361 C6.80791392,1.48887668 7.17056557,1.48653699 7.31328654,1.73688361 L12.9285378,11.4699859 C13.0735985,11.7179928 12.8934425,12.031511 12.6056609,12.031511 Z" id="Path" fill="#F0AB00"/>
            <path d="M6.57183709,4.55600087 L7.40242634,4.55600087 C7.48197574,4.55600087 7.54514731,4.62151213 7.54280763,4.70106153 L7.36733102,9.28685009 C7.36499133,9.3617201 7.30181976,9.42255199 7.22694974,9.42255199 L6.74731369,9.42255199 C6.67244367,9.42255199 6.6092721,9.36405979 6.60693241,9.28685009 L6.43145581,4.70106153 C6.42911612,4.62151213 6.49228769,4.55600087 6.57183709,4.55600087 Z" id="Path" fill="#2D2D2D"/>
            <path d="M6.98830156,9.8904896 C6.62564991,9.8904896 6.33318891,10.1829506 6.33318891,10.5456023 C6.33318891,10.9082539 6.62564991,11.2007149 6.98830156,11.2007149 C7.35095321,11.2007149 7.64341421,10.9082539 7.64341421,10.5456023 C7.64341421,10.1829506 7.35095321,9.8904896 6.98830156,9.8904896 Z" id="Path" fill="#2D2D2D"/>
        </svg>
    ),
    'role-group': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 448 512">
            <path d="M80 104a24 24 0 1 0 0-48 24 24 0 1 0 0 48zm80-24c0 32.8-19.7 61-48 73.3v87.8c18.8-10.9 40.7-17.1 64-17.1h96c35.3 0 64-28.7 64-64v-6.7C307.7 141 288 112.8 288 80c0-44.2 35.8-80 80-80s80 35.8 80 80c0 32.8-19.7 61-48 73.3V160c0 70.7-57.3 128-128 128H176c-35.3 0-64 28.7-64 64v6.7c28.3 12.3 48 40.5 48 73.3c0 44.2-35.8 80-80 80s-80-35.8-80-80c0-32.8 19.7-61 48-73.3V352 153.3C19.7 141 0 112.8 0 80C0 35.8 35.8 0 80 0s80 35.8 80 80zm232 0a24 24 0 1 0 -48 0 24 24 0 1 0 48 0zM80 456a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"/>
        </svg>
    ),
    'role-group-without-fill': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 448 512">
            <path d="M80 104a24 24 0 1 0 0-48 24 24 0 1 0 0 48zm80-24c0 32.8-19.7 61-48 73.3v87.8c18.8-10.9 40.7-17.1 64-17.1h96c35.3 0 64-28.7 64-64v-6.7C307.7 141 288 112.8 288 80c0-44.2 35.8-80 80-80s80 35.8 80 80c0 32.8-19.7 61-48 73.3V160c0 70.7-57.3 128-128 128H176c-35.3 0-64 28.7-64 64v6.7c28.3 12.3 48 40.5 48 73.3c0 44.2-35.8 80-80 80s-80-35.8-80-80c0-32.8 19.7-61 48-73.3V352 153.3C19.7 141 0 112.8 0 80C0 35.8 35.8 0 80 0s80 35.8 80 80zm232 0a24 24 0 1 0 -48 0 24 24 0 1 0 48 0zM80 456a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"/>
        </svg>
    ),
    'empty-state-resources': () => (
        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.4 8.4C3.7425 8.4 2.4 7.0575 2.4 5.4C2.4 3.7425 3.7425 2.4 5.4 2.4C7.0575 2.4 8.4 3.7425 8.4 5.4C8.4 7.0575 7.0575 8.4 5.4 8.4ZM19.2 8.4C17.5425 8.4 16.2 7.0575 16.2 5.4C16.2 3.7425 17.5425 2.4 19.2 2.4C20.8575 2.4 22.2 3.7425 22.2 5.4C22.2 7.0575 20.8575 8.4 19.2 8.4ZM0 13.6013C0 11.3925 1.7925 9.6 4.00125 9.6H5.6025C6.19875 9.6 6.765 9.73125 7.275 9.96375C7.22625 10.2338 7.20375 10.515 7.20375 10.8C7.20375 12.2325 7.83375 13.5188 8.8275 14.4C8.82 14.4 8.8125 14.4 8.80125 14.4H0.79875C0.36 14.4 0 14.04 0 13.6013ZM15.1988 14.4C15.1913 14.4 15.1838 14.4 15.1725 14.4C16.17 13.5188 16.7963 12.2325 16.7963 10.8C16.7963 10.515 16.77 10.2375 16.725 9.96375C17.235 9.7275 17.8013 9.6 18.3975 9.6H19.9988C22.2075 9.6 24 11.3925 24 13.6013C24 14.0438 23.64 14.4 23.2013 14.4H15.1988ZM15.6 10.8C15.6 12.7875 13.9875 14.4 12 14.4C10.0125 14.4 8.4 12.7875 8.4 10.8C8.4 8.8125 10.0125 7.2 12 7.2C13.9875 7.2 15.6 8.8125 15.6 10.8ZM4.8 20.5988C4.8 17.8388 7.03875 15.6 9.79875 15.6H14.2013C16.9613 15.6 19.2 17.8388 19.2 20.5988C19.2 21.15 18.7538 21.6 18.1988 21.6H5.80125C5.25 21.6 4.8 21.1538 4.8 20.5988Z" />
        </svg>
    ),
    'empty-state-hidden-suggestions': () => (
        <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.25 6.75V9H15.75V6.75C15.75 4.67812 14.0719 3 12 3C9.92812 3 8.25 4.67812 8.25 6.75ZM5.25 9V6.75C5.25 3.02344 8.27344 0 12 0C15.7266 0 18.75 3.02344 18.75 6.75V9H19.5C21.1547 9 22.5 10.3453 22.5 12V21C22.5 22.6547 21.1547 24 19.5 24H4.5C2.84531 24 1.5 22.6547 1.5 21V12C1.5 10.3453 2.84531 9 4.5 9H5.25Z" />
        </svg>
    ),
    'demand': () => (
        <svg width="1em" height="1em" viewBox="0 0 34 21" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.4,9.6 C11.05125,9.6 13.2,7.45125 13.2,4.8 C13.2,2.14875 11.05125,0 8.4,0 C5.74875,0 3.6,2.14875 3.6,4.8 C3.6,7.45125 5.74875,9.6 8.4,9.6 Z M8.4,1.2 C10.38375,1.2 12,2.81625 12,4.8 C12,6.78375 10.38375,8.4 8.4,8.4 C6.41625,8.4 4.8,6.78375 4.8,4.8 C4.8,2.81625 6.41625,1.2 8.4,1.2 Z M18.6,8.4 C15.615,8.4 13.2,10.815 13.2,13.8 C13.2,16.785 15.615,19.2 18.6,19.2 C21.585,19.2 24,16.785 24,13.8 C24,10.815 21.585,8.4 18.6,8.4 Z M18.6,18 C16.2825,18 14.4,16.1175 14.4,13.8 C14.4,11.4825 16.2825,9.6 18.6,9.6 C20.9175,9.6 22.8,11.4825 22.8,13.8 C22.8,16.1175 20.9175,18 18.6,18 Z M13.5075,18 L1.8,18 C1.47,18 1.2,17.73 1.2,17.4 L1.2,15.84 C1.2,13.72125 2.92125,12 5.04,12 C5.775,12 6.50625,12.6 8.4,12.6 C10.29,12.6 11.025,12 11.76,12 C11.925,12 12.0825,12.03 12.24375,12.04875 C12.3525,11.6475 12.5025,11.26125 12.6825,10.89375 C12.3825,10.8375 12.07875,10.8 11.76375,10.8 C10.6875,10.8 10.17,11.4 8.40375,11.4 C6.6375,11.4 6.12375,10.8 5.04375,10.8 C2.2575,10.8 0,13.0575 0,15.84 L0,17.4 C0,18.39375 0.80625,19.2 1.8,19.2 L14.80875,19.2 C14.325,18.85875 13.8825,18.4575 13.5075,18 Z M20.63625,13.2 L19.2,13.2 L19.2,11.16375 C19.2,10.965 19.035,10.8 18.83625,10.8 L18.36375,10.8 C18.165,10.8 18,10.965 18,11.16375 L18,14.03625 C18,14.235 18.165,14.4 18.36375,14.4 L20.63625,14.4 C20.835,14.4 21,14.235 21,14.03625 L21,13.56375 C21,13.365 20.835,13.2 20.63625,13.2 L20.63625,13.2 Z" id="Shape"></path>
        </svg>
    ),
    'user-outlined': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 21 24">
            <path d="M14.7 13.5c-1.345 0-1.992.75-4.2.75s-2.85-.75-4.2-.75A6.302 6.302 0 000 19.8v1.95A2.25 2.25 0 002.25 24h16.5A2.25 2.25 0 0021 21.75V19.8c0-3.478-2.822-6.3-6.3-6.3zm4.8 8.25c0 .413-.337.75-.75.75H2.25a.752.752 0 01-.75-.75V19.8c0-2.648 2.152-4.8 4.8-4.8.919 0 1.833.75 4.2.75 2.363 0 3.281-.75 4.2-.75 2.648 0 4.8 2.152 4.8 4.8v1.95zm-9-9.75a6 6 0 100-12 6 6 0 100 12zm0-10.5C12.98 1.5 15 3.52 15 6s-2.02 4.5-4.5 4.5S6 8.48 6 6s2.02-4.5 4.5-4.5z"></path>
        </svg>
    ),
    'widget-role-list': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 18 18">
            <path d="M0 4C0 2.89688 0.896875 2 2 2H16C17.1031 2 18 2.89688 18 4V14C18 15.1031 17.1031 16 16 16H2C0.896875 16 0 15.1031 0 14V4ZM4 10C4.55313 10 5 9.55313 5 9C5 8.44687 4.55313 8 4 8C3.44687 8 3 8.44687 3 9C3 9.55313 3.44687 10 4 10ZM5 6C5 5.44687 4.55313 5 4 5C3.44687 5 3 5.44687 3 6C3 6.55313 3.44687 7 4 7C4.55313 7 5 6.55313 5 6ZM4 13C4.55313 13 5 12.5531 5 12C5 11.4469 4.55313 11 4 11C3.44687 11 3 11.4469 3 12C3 12.5531 3.44687 13 4 13ZM7 5.25C6.58437 5.25 6.25 5.58437 6.25 6C6.25 6.41563 6.58437 6.75 7 6.75H14C14.4156 6.75 14.75 6.41563 14.75 6C14.75 5.58437 14.4156 5.25 14 5.25H7ZM7 8.25C6.58437 8.25 6.25 8.58437 6.25 9C6.25 9.41563 6.58437 9.75 7 9.75H14C14.4156 9.75 14.75 9.41563 14.75 9C14.75 8.58437 14.4156 8.25 14 8.25H7ZM7 11.25C6.58437 11.25 6.25 11.5844 6.25 12C6.25 12.4156 6.58437 12.75 7 12.75H14C14.4156 12.75 14.75 12.4156 14.75 12C14.75 11.5844 14.4156 11.25 14 11.25H7Z"/>
        </svg>
    ),
    'criteria-role': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 512 512">
            <path d="M0 96C0 60.7 28.7 32 64 32H512c35.3 0 64 28.7 64 64V416c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V96zM128 288a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm32-128a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM128 384a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm96-248c-13.3 0-24 10.7-24 24s10.7 24 24 24H448c13.3 0 24-10.7 24-24s-10.7-24-24-24H224zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24H448c13.3 0 24-10.7 24-24s-10.7-24-24-24H224zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24H448c13.3 0 24-10.7 24-24s-10.7-24-24-24H224z"/>
        </svg>
    ),
    'criteria-role-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 576 512"><path d="M0 96C0 60.7 28.7 32 64 32H512c35.3 0 64 28.7 64 64V416c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V96zM128 288a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm32-128a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM128 384a32 32 0 1 0 0-64 32 32 0 1 0 0 64zm96-248c-13.3 0-24 10.7-24 24s10.7 24 24 24H448c13.3 0 24-10.7 24-24s-10.7-24-24-24H224zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24H448c13.3 0 24-10.7 24-24s-10.7-24-24-24H224zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24H448c13.3 0 24-10.7 24-24s-10.7-24-24-24H224z"/></svg>
    ),
    'archive': () => (
        <svg width="1em" height="1em" viewBox="0 0 32 28" version="1.1" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M29,0 L3,0 C1.34375,0 0,1.34375 0,3 L0,8 C0,8.55 0.45,9 1,9 L2,9 L2,26 C2,27.10625 2.89375,28 4,28 L28,28 C29.10625,28 30,27.10625 30,26 L30,9 L31,9 C31.55,9 32,8.55 32,8 L32,3 C32,1.34375 30.65625,0 29,0 Z M27,25 L5,25 L5,9 L27,9 L27,25 Z M29,6 L3,6 L3,3 L29,3 L29,6 Z M12.75,15 L19.25,15 C19.6625,15 20,14.6625 20,14.25 L20,12.75 C20,12.3375 19.6625,12 19.25,12 L12.75,12 C12.3375,12 12,12.3375 12,12.75 L12,14.25 C12,14.6625 12.3375,15 12.75,15 Z" id="Shape"></path>
        </svg>
    ),
    'assigned-tick': () => (
        <svg viewBox="64 64 896 896" focusable="false" data-icon="check-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true">
            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z">
            </path>
        </svg>
    ),
    'external-link': () => (
        <svg width="16" height="14" xmlns="http://www.w3.org/2000/svg">
            <path d="m16 .4-.005 4.533a.4.4 0 0 1-.4.4h-.925a.4.4 0 0 1-.4-.408l.076-2.045-.058-.057-7.746 7.746a.333.333 0 0 1-.471 0l-.64-.64a.333.333 0 0 1 0-.471l7.746-7.746-.057-.058-2.045.076a.4.4 0 0 1-.408-.4V.405c0-.221.179-.4.4-.4L15.6 0c.22 0 .4.18.4.4Zm-4.125 6.093-.666.666a.333.333 0 0 0-.098.236v5.327a.167.167 0 0 1-.167.167H1.5a.167.167 0 0 1-.167-.167V3.278c0-.092.075-.167.167-.167h8.388c.297 0 .446-.359.236-.569l-.667-.667a.333.333 0 0 0-.235-.097H1.333C.597 1.778 0 2.375 0 3.11v9.778c0 .736.597 1.333 1.333 1.333h9.778c.736 0 1.333-.597 1.333-1.333v-6.16a.333.333 0 0 0-.569-.236Z" fill="#005B82" fillRule="nonzero" />
        </svg>
    ),
    'department': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 448 512">
            <path d="M192 107v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12zm116-12h-40c-6.627 0-12 5.373-12 12v40c0 6.627 5.373 12 12 12h40c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12zm-128 96h-40c-6.627 0-12 5.373-12 12v40c0 6.627 5.373 12 12 12h40c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12zm128 0h-40c-6.627 0-12 5.373-12 12v40c0 6.627 5.373 12 12 12h40c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12zm-128 96h-40c-6.627 0-12 5.373-12 12v40c0 6.627 5.373 12 12 12h40c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12zm128 0h-40c-6.627 0-12 5.373-12 12v40c0 6.627 5.373 12 12 12h40c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12zm140 205v20H0v-20c0-6.627 5.373-12 12-12h20V24C32 10.745 42.745 0 56 0h336c13.255 0 24 10.745 24 24v456h20c6.627 0 12 5.373 12 12zm-64-12V32H64v448h128v-85c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v85h128z" />
        </svg>
    ),
    'division': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 640 512">
            <path d="M132 368H92c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96H92c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96H92c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm96 192h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm192 96h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12V92c0-6.63-5.38-12-12-12zm128 288h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm0-96h-40c-6.62 0-12 5.37-12 12v40c0 6.63 5.38 12 12 12h40c6.62 0 12-5.37 12-12v-40c0-6.63-5.38-12-12-12zm68-80H512V24c0-13.26-10.75-24-24-24H312c-13.25 0-24 10.74-24 24v72h-64V8c0-4.42-3.58-8-8-8h-16c-4.42 0-8 3.58-8 8v88h-64V8c0-4.42-3.58-8-8-8h-16c-4.42 0-8 3.58-8 8v88H24c-13.25 0-24 10.74-24 24v384c0 4.42 3.58 8 8 8h16c4.42 0 8-3.58 8-8V128h288V32h160v192h128v280c0 4.42 3.58 8 8 8h16c4.42 0 8-3.58 8-8V216c0-13.26-10.75-24-24-24z" />
        </svg>
    ),
    'star': () => (
        <svg width="32px" height="32px" viewBox="0 0 24 21" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
            <path d="M22.770822,7.70371836 L16.2168149,6.74700074 L13.2874743,0.800079933 C12.7626155,-0.259945225 11.2418705,-0.273420121 10.7125257,0.800079933 L7.78318506,6.74700074 L1.229178,7.70371836 C0.0538529828,7.87440038 -0.417174218,9.32519752 0.435160717,10.1561494 L5.17683454,14.7825304 L4.05534121,21.317855 C3.85347241,22.4991543 5.09608702,23.3840058 6.13683284,22.831535 L12,19.7457838 L17.8631672,22.831535 C18.903913,23.3795141 20.1465276,22.4991543 19.9446588,21.317855 L18.8231655,14.7825304 L23.5648393,10.1561494 C24.4171742,9.32519752 23.946147,7.87440038 22.770822,7.70371836 L22.770822,7.70371836 Z M17.2844766,14.2794676 L18.5315772,21.5559115 L12,18.1243047 L5.47290878,21.5604032 L6.72000937,14.2839593 L1.43553277,9.12307408 L8.7342114,8.06304892 L12,1.43789168 L15.2657886,8.06304892 L22.5644672,9.12307408 L17.2844766,14.2794676 Z" id="Shape"></path>
        </svg>
    ),
    'star-fill-without-background': () => (
        <svg width="55" height="54" viewBox="0 0 65 64" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
            <path d="M34.2341 17.7431C33.913 17.0853 33.2346 16.6667 32.4895 16.6667C31.7444 16.6667 31.072 17.0853 30.7449 17.7431L26.8498 25.6547L18.1511 26.9225C17.4242 27.0301 16.8184 27.5325 16.5943 28.2202C16.3702 28.9079 16.5519 29.6674 17.0728 30.1757L23.3849 36.3411L21.8947 45.0541C21.7736 45.7717 22.0764 46.5012 22.6761 46.9258C23.2758 47.3504 24.0694 47.4042 24.7236 47.0634L32.4956 42.967L40.2675 47.0634C40.9217 47.4042 41.7153 47.3564 42.315 46.9258C42.9147 46.4953 43.2175 45.7717 43.0964 45.0541L41.6002 36.3411L47.9122 30.1757C48.4332 29.6674 48.6209 28.9079 48.3908 28.2202C48.1606 27.5325 47.5609 27.0301 46.834 26.9225L38.1291 25.6547L34.2341 17.7431Z" />
        </svg>
    ),
    'lockAlt': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 448 512">
            <path d="M224 420c-11 0-20-9-20-20v-64c0-11 9-20 20-20s20 9 20 20v64c0 11-9 20-20 20zm224-148v192c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V272c0-26.5 21.5-48 48-48h16v-64C64 71.6 136-.3 224.5 0 312.9.3 384 73.1 384 161.5V224h16c26.5 0 48 21.5 48 48zM96 224h256v-64c0-70.6-57.4-128-128-128S96 89.4 96 160v64zm320 240V272c0-8.8-7.2-16-16-16H48c-8.8 0-16 7.2-16 16v192c0 8.8 7.2 16 16 16h352c8.8 0 16-7.2 16-16z" />
        </svg>
    ),
    'lock-trail': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 192V144C80 64.5 144.5 0 224 0s144 64.5 144 144v48h16c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64H80z"/></svg>
    ),
    'user-slashed': () => (
        <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
            <path d="M4.02307325,2.24173412 L20.7320331,20.1598998 C21.014526,20.4628364 20.9979532,20.9374209 20.6950166,21.2199139 C20.3920799,21.5024068 19.9174955,21.485834 19.6350025,21.1828973 L2.92604269,3.26473166 C2.64354972,2.96179503 2.66012258,2.48721058 2.9630592,2.20471761 C3.26599582,1.92222464 3.74058028,1.93879749 4.02307325,2.24173412 Z M8.725,12.125 C8.77939476,12.125 8.83225737,12.1266235 8.8838522,12.129696 L10.2864314,13.6360893 C9.58768048,13.4711239 9.15703125,13.25 8.725,13.25 C6.80274698,13.25 5.22949564,14.7612432 5.12999582,16.6589797 L5.125,16.85 L5.125,18.3125 C5.125,18.5909375 5.33003125,18.8238125 5.59657187,18.8676031 L5.6875,18.875 L15.173,18.875 L16.222,20 L5.6875,20 C4.80489309,20 4.08005107,19.321611 4.00619681,18.4580558 L4,18.3125 L4,16.85 C4,14.2414062 6.11640625,12.125 8.725,12.125 Z M15.7718842,12.1837584 C17.890719,12.5205552 19.5413285,14.2670009 19.7316601,16.4310432 L15.7718842,12.1837584 Z M11.875,2 C14.3605469,2 16.375,4.01445313 16.375,6.5 C16.375,8.17134448 15.4641535,9.62968122 14.1117049,10.4057658 L13.3145967,9.5518917 C14.457166,9.01026675 15.25,7.84526995 15.25,6.5 C15.25,4.64023438 13.7347656,3.125 11.875,3.125 C10.6135406,3.125 9.51059188,3.82212389 8.93148958,4.85103607 L8.13509367,3.9963185 C8.94262183,2.79234542 10.3161632,2 11.875,2 Z" id="Combined-Shape" />
        </svg>
    ),
    'info-circle': () => (
        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.5 21C16.2996 21 21 16.2996 21 10.5C21 4.70039 16.2996 0 10.5 0C4.70039 0 0 4.70039 0 10.5C0 16.2996 4.70039 21 10.5 21ZM8.85938 13.7812H9.84375V11.1562H8.85938C8.31387 11.1562 7.875 10.7174 7.875 10.1719C7.875 9.62637 8.31387 9.1875 8.85938 9.1875H10.8281C11.3736 9.1875 11.8125 9.62637 11.8125 10.1719V13.7812H12.1406C12.6861 13.7812 13.125 14.2201 13.125 14.7656C13.125 15.3111 12.6861 15.75 12.1406 15.75H8.85938C8.31387 15.75 7.875 15.3111 7.875 14.7656C7.875 14.2201 8.31387 13.7812 8.85938 13.7812ZM10.5 7.875C9.77402 7.875 9.1875 7.28848 9.1875 6.5625C9.1875 5.83652 9.77402 5.25 10.5 5.25C11.226 5.25 11.8125 5.83652 11.8125 6.5625C11.8125 7.28848 11.226 7.875 10.5 7.875Z" fill="#2A66E2"/>
        </svg>
    ),
    'info-circle-tp-header': () => (
        <svg width="21" height="21" viewBox="0 0 21 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.5 21C16.2996 21 21 16.2996 21 10.5C21 4.70039 16.2996 0 10.5 0C4.70039 0 0 4.70039 0 10.5C0 16.2996 4.70039 21 10.5 21ZM8.85938 13.7812H9.84375V11.1562H8.85938C8.31387 11.1562 7.875 10.7174 7.875 10.1719C7.875 9.62637 8.31387 9.1875 8.85938 9.1875H10.8281C11.3736 9.1875 11.8125 9.62637 11.8125 10.1719V13.7812H12.1406C12.6861 13.7812 13.125 14.2201 13.125 14.7656C13.125 15.3111 12.6861 15.75 12.1406 15.75H8.85938C8.31387 15.75 7.875 15.3111 7.875 14.7656C7.875 14.2201 8.31387 13.7812 8.85938 13.7812ZM10.5 7.875C9.77402 7.875 9.1875 7.28848 9.1875 6.5625C9.1875 5.83652 9.77402 5.25 10.5 5.25C11.226 5.25 11.8125 5.83652 11.8125 6.5625C11.8125 7.28848 11.226 7.875 10.5 7.875Z"/>
        </svg>
    ),
    'refresh-icon': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" className="__web-inspector-hide-shortcut__">
            <path d="M13.6953 2.20312H12.9917C12.8206 2.20312 12.683 2.34399 12.6871 2.51507L12.7467 5.01796C11.6187 3.3214 9.69009 2.20312 7.5 2.20312C4.42299 2.20312 1.86158 4.41018 1.3122 7.32756C1.27696 7.51467 1.42159 7.6875 1.61199 7.6875H2.33755C2.48162 7.6875 2.60522 7.58639 2.63556 7.44555C3.11862 5.20247 5.11117 3.52344 7.5 3.52344C9.5197 3.52344 11.2563 4.72396 12.0367 6.45103L8.82757 6.37463C8.65652 6.37056 8.51562 6.50811 8.51562 6.67924V7.38281C8.51562 7.55108 8.65205 7.6875 8.82031 7.6875H13.6953C13.8636 7.6875 14 7.55108 14 7.38281V2.50781C14 2.33955 13.8636 2.20312 13.6953 2.20312ZM13.388 9.3125H12.6624C12.5184 9.3125 12.3948 9.41361 12.3644 9.55445C11.8814 11.7975 9.88883 13.4766 7.5 13.4766C5.48028 13.4766 3.74366 12.276 2.9633 10.549L6.17243 10.6254C6.34348 10.6294 6.48438 10.4919 6.48438 10.3208V9.61719C6.48438 9.44892 6.34795 9.3125 6.17969 9.3125H1.30469C1.13642 9.3125 1 9.44892 1 9.61719V14.4922C1 14.6605 1.13642 14.7969 1.30469 14.7969H2.00829C2.17939 14.7969 2.31696 14.656 2.3129 14.4849L2.25331 11.982C3.38134 13.6786 5.30991 14.7969 7.5 14.7969C10.577 14.7969 13.1384 12.5898 13.6878 9.67244C13.723 9.48533 13.5784 9.3125 13.388 9.3125Z" fill="currentColor"/>
        </svg>
    ),
    'not-shortlisted': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 217.929 217.929" fill="currentColor">
            <path d="M212.39,101.703c5.023-4.897,6.797-12.083,4.629-18.755s-7.827-11.443-14.769-12.452l-52.969-7.697   c-0.097-0.014-0.18-0.075-0.223-0.162L125.371,14.64C122.267,8.349,115.98,4.44,108.964,4.44S95.662,8.349,92.558,14.64   L68.87,62.637c-0.043,0.087-0.126,0.147-0.223,0.162l-52.968,7.697c-6.942,1.009-12.601,5.78-14.769,12.452   s-0.394,13.858,4.629,18.755l38.328,37.361c0.07,0.068,0.102,0.166,0.085,0.262l-9.048,52.755   c-1.186,6.914,1.604,13.771,7.279,17.894c5.676,4.125,13.059,4.657,19.268,1.393l47.376-24.907c0.086-0.046,0.19-0.045,0.276,0   l47.376,24.907c2.701,1.42,5.623,2.121,8.531,2.121c3.777,0,7.53-1.184,10.736-3.514c5.675-4.123,8.464-10.98,7.279-17.895   l-9.048-52.754c-0.017-0.096,0.016-0.194,0.085-0.262L212.39,101.703z M156.235,142.368l9.048,52.754   c0.024,0.14,0.031,0.182-0.118,0.29c-0.149,0.108-0.187,0.088-0.312,0.022l-47.377-24.908c-5.33-2.801-11.695-2.801-17.027,0   l-47.376,24.907c-0.125,0.065-0.163,0.086-0.312-0.022c-0.149-0.108-0.142-0.15-0.118-0.289l9.048-52.755   c1.018-5.936-0.949-11.989-5.262-16.194L18.103,88.813c-0.101-0.099-0.132-0.128-0.075-0.303c0.057-0.175,0.099-0.181,0.239-0.202   l52.968-7.697c5.961-0.866,11.111-4.607,13.776-10.008l23.688-47.998c0.063-0.126,0.081-0.165,0.265-0.165s0.203,0.039,0.265,0.165   l23.688,47.998c2.666,5.401,7.815,9.143,13.776,10.008l52.968,7.697c0.14,0.021,0.182,0.027,0.239,0.202   c0.057,0.175,0.026,0.205-0.075,0.303l-38.328,37.361C157.185,130.378,155.218,136.432,156.235,142.368z" />
        </svg>
    ),
    'shortlisted': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 329.942 329.942" fill="#F0AB00">
            <path id="XMLID_16_" d="M329.208,126.666c-1.765-5.431-6.459-9.389-12.109-10.209l-95.822-13.922l-42.854-86.837  c-2.527-5.12-7.742-8.362-13.451-8.362c-5.71,0-10.925,3.242-13.451,8.362l-42.851,86.836l-95.825,13.922  c-5.65,0.821-10.345,4.779-12.109,10.209c-1.764,5.431-0.293,11.392,3.796,15.377l69.339,67.582L57.496,305.07  c-0.965,5.628,1.348,11.315,5.967,14.671c2.613,1.899,5.708,2.865,8.818,2.865c2.387,0,4.784-0.569,6.979-1.723l85.711-45.059  l85.71,45.059c2.208,1.161,4.626,1.714,7.021,1.723c8.275-0.012,14.979-6.723,14.979-15c0-1.152-0.13-2.275-0.376-3.352  l-16.233-94.629l69.339-67.583C329.501,138.057,330.972,132.096,329.208,126.666z" />
        </svg>
    ),
    'reset-icon': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" clipRule="evenodd" d="M10.1248 2.73855C8.42839 2.73855 6.86594 3.30996 5.61598 4.27124L4.12495 2.78021C3.52973 2.18202 2.5 2.60462 2.5 3.45281V7.26222C2.5 7.78899 2.92558 8.21457 3.45235 8.21457H7.26176C8.10995 8.21457 8.53255 7.18782 7.93436 6.58962L6.33917 4.99145C7.41949 4.19386 8.73493 3.75042 10.1188 3.75042C13.6425 3.75042 16.4877 6.60748 16.4877 10.1193C16.4877 13.643 13.6306 16.4881 10.1188 16.4881C8.52065 16.4881 7.01772 15.8989 5.86002 14.8542C5.72014 14.7263 5.50288 14.7352 5.36896 14.8691L5.15766 15.0804C5.01183 15.2263 5.02076 15.4614 5.17254 15.5983C6.48202 16.7798 8.21709 17.5 10.1188 17.5C14.1901 17.5 17.4906 14.2025 17.4995 10.1342C17.5085 6.06583 14.1901 2.74152 10.1248 2.73855ZM7.26176 7.26222H3.45235V3.45281L7.26176 7.26222Z" />
        </svg>
    ),
    'clock-icon': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 567 450">
            <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm216 248c0 118.7-96.1 216-216 216-118.7 0-216-96.1-216-216 0-118.7 96.1-216 216-216 118.7 0 216 96.1 216 216zm-148.9 88.3l-81.2-59c-3.1-2.3-4.9-5.9-4.9-9.7V116c0-6.6 5.4-12 12-12h14c6.6 0 12 5.4 12 12v146.3l70.5 51.3c5.4 3.9 6.5 11.4 2.6 16.8l-8.2 11.3c-3.9 5.3-11.4 6.5-16.8 2.6z" />
        </svg>
    ),
    'completed-milestone': () => <CompletedMilestone/>,
    'unfinished-milestone': () => <UnfinishedMilestone/>,
    'overdue-milestone': () => <OverdueMilestone/>,
    'people-switcher': () => (
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M9.22069 12.3972C9.17404 12.398 9.12653 12.3984 9.07812 12.3984C8.03274 12.3984 7.40336 12.1992 6.85099 12.0242C6.41906 11.8875 6.03422 11.7656 5.53438 11.7656C2.59971 11.7656 0.21875 14.1466 0.21875 17.0812V18.7266C0.21875 19.7747 1.06909 20.625 2.11719 20.625H9.23527C8.97326 20.2279 8.748 19.8044 8.56433 19.3594H2.11719C1.76914 19.3594 1.48438 19.0746 1.48438 18.7266V17.0812C1.48438 14.8466 3.29976 13.0312 5.53438 13.0312C5.83194 13.0312 6.12892 13.1245 6.49488 13.2394C6.99527 13.3965 7.62463 13.5941 8.5608 13.6492C8.74171 13.2093 8.96323 12.7904 9.22069 12.3972ZM14.1406 5.4375C14.1406 8.23374 11.8744 10.5 9.07812 10.5C6.28188 10.5 4.01562 8.23374 4.01562 5.4375C4.01562 2.64126 6.28188 0.375 9.07812 0.375C11.8744 0.375 14.1406 2.64126 14.1406 5.4375ZM12.875 5.4375C12.875 3.34526 11.1704 1.64062 9.07812 1.64062C6.98589 1.64062 5.28125 3.34526 5.28125 5.4375C5.28125 7.52974 6.98589 9.23438 9.07812 9.23438C11.1704 9.23438 12.875 7.52974 12.875 5.4375ZM17.9547 8.94674L21.1648 11.8634C21.3617 12.0424 21.3617 12.3326 21.1648 12.5116L17.9547 15.4283C17.7577 15.6072 17.4383 15.6072 17.2413 15.4283C17.0443 15.2493 17.0443 14.9591 17.2413 14.7801L19.5903 12.6458H12.0312V11.7292H19.5903L17.2413 9.59489C17.0443 9.41591 17.0443 9.12572 17.2413 8.94674C17.4383 8.76775 17.7577 8.76775 17.9547 8.94674ZM9.64774 17.7697L12.8578 14.853C13.0548 14.674 13.3742 14.674 13.5712 14.853C13.7682 15.032 13.7682 15.3222 13.5712 15.5011L11.2222 17.6354H18.7812V18.5521H11.2222L13.5712 20.6864C13.7682 20.8653 13.7682 21.1555 13.5712 21.3345C13.3742 21.5135 13.0548 21.5135 12.8578 21.3345L9.64774 18.4178C9.45075 18.2388 9.45075 17.9487 9.64774 17.7697Z" fill="#005B82"/>
        </svg>
    ),
    'megaphone': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 21 18" fill="currentColor" >
            <path fillRule="evenodd" clipRule="evenodd" d="M15.2179 2.38779L14.7621 2.52551C14.5103 2.6016 14.368 2.8642 14.444 3.11233L14.5554 3.47553L1.82082 10.7421C1.71256 10.5586 1.49408 10.4603 1.27872 10.5253L0.822935 10.663C0.571113 10.7391 0.42878 11.0017 0.504873 11.2499L1.88213 15.7408C1.95822 15.9889 2.22382 16.1283 2.47564 16.0522L2.93143 15.9145C3.14679 15.8494 3.27299 15.6469 3.25988 15.4346L6.76571 15.1932C6.78213 15.3655 6.80061 15.5385 6.85346 15.7108C7.30985 17.199 8.90417 18.0354 10.4145 17.579C11.6734 17.1987 12.4635 16.0401 12.4294 14.8031L17.9135 14.4256L18.0249 14.7888C18.101 15.0369 18.3666 15.1762 18.6184 15.1001L19.0742 14.9624C19.326 14.8863 19.4684 14.6237 19.3923 14.3756L15.8114 2.69916C15.7353 2.45104 15.4697 2.3117 15.2179 2.38779ZM10.0011 16.2318C9.24703 16.4597 8.44838 16.0407 8.22054 15.2977C8.20762 15.2556 8.20555 15.2137 8.20348 15.1718C8.20222 15.1462 8.20095 15.1205 8.19718 15.0947L11.0092 14.901C10.999 15.497 10.6098 16.0479 10.0011 16.2318ZM2.85783 14.0505L2.26389 12.1138L5.03139 10.5346L6.04239 13.8312L2.85783 14.0505ZM14.9794 4.8579L6.22515 9.85337L7.41604 13.7366L17.4895 13.0429L14.9794 4.8579ZM17.8189 5.37228C17.6255 5.57146 17.6314 5.8885 17.832 6.08041C18.0326 6.27232 18.352 6.26643 18.5454 6.06725L19.5218 5.06162C19.7152 4.86245 19.7094 4.54541 19.5088 4.3535C19.3081 4.16158 18.9888 4.16747 18.7954 4.36665L17.8189 5.37228ZM19.0999 10.0011C19.1647 9.73217 19.4355 9.56598 19.7047 9.62993L21.0798 9.95664C21.349 10.0206 21.5146 10.2905 21.4498 10.5594C21.3849 10.8284 21.1141 10.9946 20.845 10.9306L19.4698 10.6039C19.2007 10.5399 19.035 10.2701 19.0999 10.0011ZM18.9522 7.46363C18.6902 7.55281 18.551 7.83717 18.6414 8.09877C18.7318 8.36036 19.0174 8.50013 19.2794 8.41095L21.6075 7.61853C21.8695 7.52935 22.0086 7.24499 21.9183 6.98339C21.8279 6.72179 21.5422 6.58202 21.2802 6.6712L18.9522 7.46363Z"/>
        </svg>
   
    ),
    'table': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 19" fill="currentColor">
            <path fillRule="evenodd" clipRule="evenodd" d="M22.3125 0H1.6875C0.754687 0 0 0.759434 0 1.69811V16.4151C0 17.3538 0.754687 18.1132 1.6875 18.1132H22.3125C23.2453 18.1132 24 17.3538 24 16.4151V1.69811C24 0.759434 23.2453 0 22.3125 0ZM21.7359 16.3019H13.1321V12.6792C13.1321 12.69 13.1376 12.7007 13.1459 12.7007H21.7221C21.729 12.7007 21.7359 12.6921 21.7359 12.6792V16.3019ZM11.3208 16.3019H2.26415V12.6792C2.26415 12.69 2.26996 12.7007 2.27867 12.7007H11.3062C11.3135 12.7007 11.3208 12.6921 11.3208 12.6792V16.3019ZM21.7359 10.8679C21.7359 10.8572 21.7303 10.8465 21.7221 10.8465H13.1459C13.139 10.8465 13.1321 10.8551 13.1321 10.8679V7.24528H21.7359V10.8679ZM11.3208 10.8679C11.3208 10.8572 11.315 10.8465 11.3062 10.8465H2.27867C2.27141 10.8465 2.26415 10.8551 2.26415 10.8679V7.24528H11.3208V10.8679ZM21.7359 5.43396C21.7359 5.42325 21.7303 5.41253 21.7221 5.41253H13.1459C13.139 5.41253 13.1321 5.4211 13.1321 5.43396V1.81132H21.7359V5.43396ZM11.3208 5.43396C11.3208 5.42325 11.315 5.41253 11.3062 5.41253H2.27867C2.27141 5.41253 2.26415 5.4211 2.26415 5.43396V1.81132H11.3208V5.43396Z" /*fill="#2D2D2D"*//>
        </svg>
    ),
    'people-finder': () => (
        <svg width="1em" height="1em" viewBox="0 0 20 18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M7.41896 16.875C7.78442 17.3066 8.20978 17.6859 8.6822 18H2.4375C1.50586 18 0.75 17.2441 0.75 16.3125V14.85C0.75 12.2414 2.86641 10.125 5.475 10.125C5.9085 10.125 6.24469 10.2281 6.61738 10.346C6.45201 10.6808 6.31695 11.0332 6.21578 11.3997C5.941 11.3144 5.7082 11.25 5.475 11.25C3.48867 11.25 1.875 12.8637 1.875 14.85V16.3125C1.875 16.6219 2.12812 16.875 2.4375 16.875H7.41896ZM12 7C12.1209 7 12.2411 7.00358 12.3602 7.01064C12.8432 6.29352 13.125 5.42971 13.125 4.5C13.125 2.01445 11.1105 0 8.625 0C6.13945 0 4.125 2.01445 4.125 4.5C4.125 6.64335 5.62295 8.43639 7.62921 8.88948C8.00204 8.49319 8.42815 8.14762 8.89611 7.8642C8.80666 7.87135 8.71624 7.875 8.625 7.875C6.76523 7.875 5.25 6.35977 5.25 4.5C5.25 2.64023 6.76523 1.125 8.625 1.125C10.4848 1.125 12 2.64023 12 4.5C12 5.56394 11.5041 6.51512 10.7315 7.13435C11.1404 7.04634 11.5648 7 12 7ZM11.8279 9.85526C10.1859 9.85526 8.85526 11.1866 8.85526 12.8287C8.85526 14.4708 10.3185 15.9342 11.96 15.9342C13.603 15.9342 14.9342 14.6029 14.9342 12.9613C14.9342 11.3186 13.4704 9.85526 11.8279 9.85526ZM16.0992 12.9883C16.0992 13.7693 15.8813 14.4995 15.5026 15.1223L18.0144 17.633C18.261 17.8802 18.3504 18.1911 18.1038 18.4382L17.4774 19.0646C17.2302 19.3118 16.8299 19.3118 16.5822 19.0646L14.0576 16.5395C13.4497 16.8946 12.7422 17.0991 11.9873 17.0991C9.71706 17.0991 7.75 15.1321 7.75 12.8614C7.75 10.5911 9.59065 8.75 11.8609 8.75C14.1316 8.75 16.0992 10.7176 16.0992 12.9883Z" />
        </svg>
    ),
    'external-link-goToProfile': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="13" viewBox="0 0 10 9" fill="none">
            <path d="M7.77778 4.13272V7.92969C7.77778 8.38274 7.40469 8.75 6.94444 8.75H0.833333C0.37309 8.75 0 8.38274 0 7.92969V1.91406C0 1.46101 0.37309 1.09375 0.833333 1.09375H6.73569C6.9213 1.09375 7.01425 1.31465 6.883 1.44384L6.46634 1.85399C6.42727 1.89245 6.37428 1.91406 6.31903 1.91406H0.9375C0.909873 1.91406 0.883378 1.92487 0.863843 1.9441C0.844308 1.96333 0.833333 1.98941 0.833333 2.0166V7.82715C0.833333 7.85434 0.844308 7.88042 0.863843 7.89965C0.883378 7.91888 0.909873 7.92969 0.9375 7.92969H6.84028C6.8679 7.92969 6.8944 7.91888 6.91394 7.89965C6.93347 7.88042 6.94444 7.85434 6.94444 7.82715V4.54287C6.94444 4.48848 6.96639 4.43632 7.00547 4.39787L7.42214 3.98771C7.55337 3.85851 7.77778 3.95001 7.77778 4.13272ZM9.79167 0H7.43082C7.24578 0 7.15236 0.220972 7.28351 0.350085L8.12009 1.17383L3.39436 5.82572C3.313 5.90581 3.313 6.03565 3.39436 6.11575L3.78719 6.50245C3.86856 6.58253 4.00047 6.58253 4.08182 6.50245L8.80754 1.85057L9.64436 2.67417C9.77479 2.80256 10 2.71236 10 2.52916V0.205078C10 0.0918237 9.90672 0 9.79167 0Z" fill="#2D2D2D"/>
        </svg>
    ),
    'expand': () => (
        <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M16.0421 2.63158L10.9743 8.14721C10.749 8.39237 10.3646 8.40972 10.1366 8.17362C9.93217 7.961 9.92842 7.6189 10.1351 7.39393L15.1976 1.88407C15.1985 1.88311 15.1985 1.88128 15.1966 1.88137L11.3247 2.16071C11.0118 2.18348 10.7444 1.94669 10.7279 1.63364C10.7191 1.47302 10.7782 1.32349 10.8807 1.21197C10.9779 1.10621 11.1137 1.03596 11.266 1.02525L16.5666 0.641913C16.7208 0.631105 16.8715 0.682713 16.9846 0.786626C17.0968 0.889673 17.1619 1.03637 17.1641 1.19092L17.2299 6.47655C17.2346 6.79114 16.9825 7.05418 16.6692 7.06416C16.355 7.0751 16.0973 6.82773 16.0936 6.51401L16.0448 2.63235C16.0457 2.63139 16.043 2.63062 16.0421 2.63158ZM2.02123 16.3781L7.07043 10.8454C7.29486 10.5995 7.67922 10.5809 7.90803 10.8162C8.11315 11.0281 8.11805 11.3702 7.9121 11.5959L2.86819 17.1228C2.86731 17.1237 2.86737 17.1256 2.8692 17.1255L6.74013 16.8331C7.05297 16.8093 7.32113 17.0451 7.33872 17.3581C7.34811 17.5187 7.28944 17.6685 7.18735 17.7803C7.09053 17.8864 6.95496 17.9571 6.80263 17.9683L1.50338 18.3695C1.34923 18.3808 1.19833 18.3297 1.08489 18.2262C0.972387 18.1235 0.906795 17.9771 0.904018 17.8225L0.820409 12.5371C0.814661 12.2226 1.06588 11.9587 1.37918 11.9477C1.69336 11.9357 1.95184 12.1822 1.95664 12.4959L2.01846 16.3773C2.01758 16.3783 2.02035 16.3791 2.02123 16.3781Z" fill="#005B82" />
        </svg>
    ),
    'save-as-template': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 512 512">
            <path d="M464 0H144c-26.51 0-48 21.49-48 48v48H48c-26.51 0-48 21.49-48 48v320c0 26.51 21.49 48 48 48h320c26.51 0 48-21.49 48-48v-48h48c26.51 0 48-21.49 48-48V48c0-26.51-21.49-48-48-48zm-80 464c0 8.82-7.18 16-16 16H48c-8.82 0-16-7.18-16-16V144c0-8.82 7.18-16 16-16h48v240c0 26.51 21.49 48 48 48h240v48zm96-96c0 8.82-7.18 16-16 16H144c-8.82 0-16-7.18-16-16V48c0-8.82 7.18-16 16-16h320c8.82 0 16 7.18 16 16v320z"/>
        </svg>
    ),
    'templates-empty-state': () => (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="1em" height="1em">
            <path d="M464 0H144c-26.51 0-48 21.49-48 48v48H48c-26.51 0-48 21.49-48 48v320c0 26.51 21.49 48 48 48h320c26.51 0 48-21.49 48-48v-48h48c26.51 0 48-21.49 48-48V48c0-26.51-21.49-48-48-48zm-80 464c0 8.82-7.18 16-16 16H48c-8.82 0-16-7.18-16-16V144c0-8.82 7.18-16 16-16h48v240c0 26.51 21.49 48 48 48h240v48zm96-96c0 8.82-7.18 16-16 16H144c-8.82 0-16-7.18-16-16V48c0-8.82 7.18-16 16-16h320c8.82 0 16 7.18 16 16v320z"/>
        </svg>
    ),
    'education': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
            <path d="M10 3.75C8.48125 3.75 7.25 4.98125 7.25 6.5C7.25 8.01875 8.48125 9.25 10 9.25C11.5188 9.25 12.75 8.01875 12.75 6.5C12.75 4.98125 11.5188 3.75 10 3.75ZM11.5 7.25C11.5 7.38812 11.3881 7.5 11.25 7.5H9.75C9.61187 7.5 9.5 7.38812 9.5 7.25V5.25C9.5 5.11188 9.61187 5 9.75 5H10.25C10.3881 5 10.5 5.11188 10.5 5.25V6.5H11.25C11.3881 6.5 11.5 6.61188 11.5 6.75V7.25ZM19 6H16V4.33344C16 3.99906 15.8328 3.68688 15.5547 3.50156L10.5547 0.168125C10.3905 0.0583827 10.1975 -0.000132815 10 2.26359e-07C9.80259 -9.39884e-05 9.60957 0.0583006 9.44531 0.167813L4.44531 3.50125C4.30839 3.59258 4.19611 3.71628 4.11843 3.86139C4.04075 4.0065 4.00007 4.16853 4 4.33313V6H1C0.447812 6 0 6.44781 0 7V15.5C0 15.7762 0.22375 16 0.5 16H1C1.27625 16 1.5 15.7762 1.5 15.5V7.5H4V16H5.5V4.60094L10 1.60094L14.5 4.60094V16H16V7.5H18.5V15.5C18.5 15.7762 18.7237 16 19 16H19.5C19.7763 16 20 15.7762 20 15.5V7C20 6.44781 19.5522 6 19 6Z" fill="white"/>
        </svg>
    ),
    'experience': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="16" viewBox="0 0 20 16" fill="none">
            <path fillRule="evenodd" clipRule="evenodd" d="M14.8295 3.05766H11.25V0.764416C11.25 0.342076 10.9067 0 10.483 0H5.88068C5.45689 0 5.11364 0.342076 5.11364 0.764416V3.05766H1.53409C0.686825 3.05766 0 3.74213 0 4.58649V12.7403C0 13.5846 0.686825 14.2691 1.53409 14.2691H9.25099C9.07296 13.946 8.92323 13.6052 8.80513 13.2499H1.53409C1.2522 13.2499 1.02273 13.0212 1.02273 12.7403V8.15377H6.13636V9.42779C6.13636 9.84981 6.47994 10.1922 6.90341 10.1922H8.59607C8.66107 9.84105 8.756 9.5004 8.87811 9.17299H7.15909V8.15377H9.20455V8.44668C9.45836 7.96815 9.77372 7.52726 10.1401 7.13455H1.02273V4.58649C1.02273 4.30557 1.2522 4.07688 1.53409 4.07688H14.8295C15.1114 4.07688 15.3409 4.30557 15.3409 4.58649V5.22662C15.6917 5.26752 16.0335 5.33804 16.3636 5.43562V4.58649C16.3636 3.74213 15.6768 3.05766 14.8295 3.05766ZM6.13636 1.01922H10.2273V3.05766H6.13636V1.01922ZM14.4546 15C16.6637 15 18.4546 13.2091 18.4546 11C18.4546 8.79086 16.6637 7 14.4546 7C12.2455 7 10.4546 8.79086 10.4546 11C10.4546 13.2091 12.2455 15 14.4546 15ZM14.4546 16C17.216 16 19.4546 13.7614 19.4546 11C19.4546 8.23858 17.216 6 14.4546 6C11.6932 6 9.45459 8.23858 9.45459 11C9.45459 13.7614 11.6932 16 14.4546 16ZM16.0781 13.2293L14.3177 11.9502C14.2505 11.9003 14.2115 11.8222 14.2115 11.7399V8.27974C14.2115 8.13666 14.3286 8.01958 14.4716 8.01958H14.7752C14.9183 8.01958 15.0353 8.13666 15.0353 8.27974V11.4515L16.5638 12.5637C16.6808 12.6483 16.7047 12.8109 16.6201 12.9279L16.4424 13.1729C16.3578 13.2878 16.1952 13.3138 16.0781 13.2293Z" fill="White"/>
        </svg>
    ),
    'empty-accounts-grid': () => (
        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512" fill="#efefef">
            <path d="M121 32C91.6 32 66 52 58.9 80.5L1.9 308.4C.6 313.5 0 318.7 0 323.9V416c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V323.9c0-5.2-.6-10.4-1.9-15.5l-57-227.9C446 52 420.4 32 391 32H121zm0 64H391l48 192H387.8c-12.1 0-23.2 6.8-28.6 17.7l-14.3 28.6c-5.4 10.8-16.5 17.7-28.6 17.7H195.8c-12.1 0-23.2-6.8-28.6-17.7l-14.3-28.6c-5.4-10.8-16.5-17.7-28.6-17.7H73L121 96z"/>
        </svg>
    ),
    'reports': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="17" viewBox="0 0 16 17">
            <path d="M2 1.5C2 0.946875 1.55313 0.5 1 0.5C0.446875 0.5 0 0.946875 0 1.5V12C0 13.3813 1.11875 14.5 2.5 14.5H15C15.5531 14.5 16 14.0531 16 13.5C16 12.9469 15.5531 12.5 15 12.5H2.5C2.225 12.5 2 12.275 2 12V1.5ZM14.7063 4.20625C15.0969 3.81563 15.0969 3.18125 14.7063 2.79063C14.3156 2.4 13.6812 2.4 13.2906 2.79063L10 6.08437L8.20625 4.29063C7.81563 3.9 7.18125 3.9 6.79063 4.29063L3.29063 7.79062C2.9 8.18125 2.9 8.81563 3.29063 9.20625C3.68125 9.59688 4.31563 9.59688 4.70625 9.20625L7.5 6.41563L9.29375 8.20938C9.68437 8.6 10.3188 8.6 10.7094 8.20938L14.7094 4.20937L14.7063 4.20625Z" />
        </svg>
    ),
    'print': () => (
        <svg width="16" height="17" viewBox="0 0 16 17" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2657_33586)">
                <path d="M4 0.5C2.89687 0.5 2 1.39688 2 2.5V5.5H4V2.5H11.0844L12 3.41563V5.5H14V3.41563C14 2.88438 13.7906 2.375 13.4156 2L12.5 1.08438C12.125 0.709375 11.6156 0.5 11.0844 0.5H4ZM12 11.5V12.5V14.5H4V12.5V12V11.5H12ZM14 12.5H15C15.5531 12.5 16 12.0531 16 11.5V8.5C16 7.39687 15.1031 6.5 14 6.5H2C0.896875 6.5 0 7.39687 0 8.5V11.5C0 12.0531 0.446875 12.5 1 12.5H2V14.5C2 15.6031 2.89687 16.5 4 16.5H12C13.1031 16.5 14 15.6031 14 14.5V12.5ZM13.5 9.75C13.0844 9.75 12.75 9.41562 12.75 9C12.75 8.58438 13.0844 8.25 13.5 8.25C13.9156 8.25 14.25 8.58438 14.25 9C14.25 9.41562 13.9156 9.75 13.5 9.75Z" />
            </g>
            <defs>
                <clipPath id="clip0_2657_33586">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>
    ),
    'role-avatar': () => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="8" fill="#FF7875" />
            <rect width="32" height="32" rx="8" fill="url(#paint0_linear_804_23626)" />
            <path d="M7 11C7 9.89688 7.89688 9 9 9H23C24.1031 9 25 9.89688 25 11V21C25 22.1031 24.1031 23 23 23H9C7.89688 23 7 22.1031 7 21V11ZM11 17C11.5531 17 12 16.5531 12 16C12 15.4469 11.5531 15 11 15C10.4469 15 10 15.4469 10 16C10 16.5531 10.4469 17 11 17ZM12 13C12 12.4469 11.5531 12 11 12C10.4469 12 10 12.4469 10 13C10 13.5531 10.4469 14 11 14C11.5531 14 12 13.5531 12 13ZM11 20C11.5531 20 12 19.5531 12 19C12 18.4469 11.5531 18 11 18C10.4469 18 10 18.4469 10 19C10 19.5531 10.4469 20 11 20ZM14 12.25C13.5844 12.25 13.25 12.5844 13.25 13C13.25 13.4156 13.5844 13.75 14 13.75H21C21.4156 13.75 21.75 13.4156 21.75 13C21.75 12.5844 21.4156 12.25 21 12.25H14ZM14 15.25C13.5844 15.25 13.25 15.5844 13.25 16C13.25 16.4156 13.5844 16.75 14 16.75H21C21.4156 16.75 21.75 16.4156 21.75 16C21.75 15.5844 21.4156 15.25 21 15.25H14ZM14 18.25C13.5844 18.25 13.25 18.5844 13.25 19C13.25 19.4156 13.5844 19.75 14 19.75H21C21.4156 19.75 21.75 19.4156 21.75 19C21.75 18.5844 21.4156 18.25 21 18.25H14Z" fill="white" />
            <defs>
                <linearGradient id="paint0_linear_804_23626" x1="10.25" y1="6" x2="24.75" y2="27" gradientUnits="userSpaceOnUse">
                    <stop stopColor="white" stopOpacity="0" />
                    <stop offset="1" stopColor="white" stopOpacity="0.2" />
                </linearGradient>
            </defs>
        </svg>
    ),
    'role-group-avatar': () => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="8" fill="#DE9035" />
            <rect width="32" height="32" rx="8" fill="url(#paint0_linear_624_36234)" />
            <path d="M10.9375 10.6562C11.4051 10.6562 11.7812 10.2801 11.7812 9.8125C11.7812 9.34492 11.4051 8.96875 10.9375 8.96875C10.4699 8.96875 10.0938 9.34492 10.0938 9.8125C10.0938 10.2801 10.4699 10.6562 10.9375 10.6562ZM13.75 9.8125C13.75 10.9656 13.0574 11.957 12.0625 12.3895V15.4762C12.7234 15.093 13.4934 14.875 14.3125 14.875H17.6875C18.9285 14.875 19.9375 13.866 19.9375 12.625V12.3895C18.9426 11.957 18.25 10.9656 18.25 9.8125C18.25 8.25859 19.5086 7 21.0625 7C22.6164 7 23.875 8.25859 23.875 9.8125C23.875 10.9656 23.1824 11.957 22.1875 12.3895V12.625C22.1875 15.1105 20.173 17.125 17.6875 17.125H14.3125C13.0715 17.125 12.0625 18.134 12.0625 19.375V19.6105C13.0574 20.043 13.75 21.0344 13.75 22.1875C13.75 23.7414 12.4914 25 10.9375 25C9.38359 25 8.125 23.7414 8.125 22.1875C8.125 21.0344 8.81758 20.043 9.8125 19.6105V19.375V12.3895C8.81758 11.957 8.125 10.9656 8.125 9.8125C8.125 8.25859 9.38359 7 10.9375 7C12.4914 7 13.75 8.25859 13.75 9.8125ZM21.9062 9.8125C21.9062 9.34492 21.5301 8.96875 21.0625 8.96875C20.5949 8.96875 20.2188 9.34492 20.2188 9.8125C20.2188 10.2801 20.5949 10.6562 21.0625 10.6562C21.5301 10.6562 21.9062 10.2801 21.9062 9.8125ZM10.9375 23.0312C11.4051 23.0312 11.7812 22.6551 11.7812 22.1875C11.7812 21.7199 11.4051 21.3438 10.9375 21.3438C10.4699 21.3438 10.0938 21.7199 10.0938 22.1875C10.0938 22.6551 10.4699 23.0312 10.9375 23.0312Z" fill="white" />
            <defs>
                <linearGradient id="paint0_linear_624_36234" x1="10.25" y1="6" x2="24.75" y2="27" gradientUnits="userSpaceOnUse">
                    <stop stopColor="white" stopOpacity="0" />
                    <stop offset="1" stopColor="white" stopOpacity="0.2" />
                </linearGradient>
            </defs>
        </svg>
    ),
    'planned-booking-avatar': () => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="8" fill="#903FF7" />
            <rect width="32" height="32" rx="8" fill="url(#paint0_linear_624_36184)" />
            <path d="M9 10C7.89688 10 7 10.8969 7 12V14.5C7.82812 14.5 8.5 15.1719 8.5 16C8.5 16.8281 7.82812 17.5 7 17.5V20C7 21.1031 7.89688 22 9 22H23C24.1031 22 25 21.1031 25 20V17.5C24.1719 17.5 23.5 16.8281 23.5 16C23.5 15.1719 24.1719 14.5 25 14.5V12C25 10.8969 24.1031 10 23 10H9ZM11 13V19H21V13H11ZM10 13C10 12.4469 10.4469 12 11 12H21C21.5531 12 22 12.4469 22 13V19C22 19.5531 21.5531 20 21 20H11C10.4469 20 10 19.5531 10 19V13Z" fill="white" />
            <defs>
                <linearGradient id="paint0_linear_624_36184" x1="10.25" y1="6" x2="24.75" y2="27" gradientUnits="userSpaceOnUse">
                    <stop stopColor="white" stopOpacity="0" />
                    <stop offset="1" stopColor="white" stopOpacity="0.2" />
                </linearGradient>
            </defs>
        </svg>
    ),
    'unplanned-booking-avatar': () => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="8" fill="#607085" />
            <rect width="32" height="32" rx="8" fill="url(#paint0_linear_624_36209)" />
            <path d="M9 10C7.89688 10 7 10.8969 7 12V14.5C7.82812 14.5 8.5 15.1719 8.5 16C8.5 16.8281 7.82812 17.5 7 17.5V20C7 21.1031 7.89688 22 9 22H23C24.1031 22 25 21.1031 25 20V17.5C24.1719 17.5 23.5 16.8281 23.5 16C23.5 15.1719 24.1719 14.5 25 14.5V12C25 10.8969 24.1031 10 23 10H9ZM11 13V19H21V13H11ZM10 13C10 12.4469 10.4469 12 11 12H21C21.5531 12 22 12.4469 22 13V19C22 19.5531 21.5531 20 21 20H11C10.4469 20 10 19.5531 10 19V13Z" fill="white" />
            <defs>
                <linearGradient id="paint0_linear_624_36209" x1="10.25" y1="6" x2="24.75" y2="27" gradientUnits="userSpaceOnUse">
                    <stop stopColor="white" stopOpacity="0" />
                    <stop offset="1" stopColor="white" stopOpacity="0.2" />
                </linearGradient>
            </defs>
        </svg>
    ),
    'slash': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-slash-lg" viewBox="0 0 16 16">
            <path fillRule="evenodd" d="M13.854 2.146a.5.5 0 0 1 0 .708l-11 11a.5.5 0 0 1-.708-.708l11-11a.5.5 0 0 1 .708 0Z"/>
        </svg>
    ),
    'project-health': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 512 512">
            <path d="M96 352V96c0-35.3 28.7-64 64-64H416c35.3 0 64 28.7 64 64V293.5c0 17-6.7 33.3-18.7 45.3l-58.5 58.5c-12 12-28.3 18.7-45.3 18.7H160c-35.3 0-64-28.7-64-64zM272 128c-8.8 0-16 7.2-16 16v48H208c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h48v48c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16V256h48c8.8 0 16-7.2 16-16V208c0-8.8-7.2-16-16-16H320V144c0-8.8-7.2-16-16-16H272zm24 336c13.3 0 24 10.7 24 24s-10.7 24-24 24H136C60.9 512 0 451.1 0 376V152c0-13.3 10.7-24 24-24s24 10.7 24 24l0 224c0 48.6 39.4 88 88 88H296z"/>
        </svg>
    ),
    'delete': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 448 512">
            <path d="M135.2 17.7C140.6 6.8 151.7 0 163.8 0H284.2c12.1 0 23.2 6.8 28.6 17.7L320 32h96c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 96 0 81.7 0 64S14.3 32 32 32h96l7.2-14.3zM32 128H416V448c0 35.3-28.7 64-64 64H96c-35.3 0-64-28.7-64-64V128zm96 64c-8.8 0-16 7.2-16 16V432c0 8.8 7.2 16 16 16s16-7.2 16-16V208c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16V432c0 8.8 7.2 16 16 16s16-7.2 16-16V208c0-8.8-7.2-16-16-16zm96 0c-8.8 0-16 7.2-16 16V432c0 8.8 7.2 16 16 16s16-7.2 16-16V208c0-8.8-7.2-16-16-16z"/>
        </svg>
    ),
    'arrows-up-down': () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="1em" height="1em" viewBox="0 0 320 512">
            <path d="M182.6 9.4c-12.5-12.5-32.8-12.5-45.3 0l-96 96c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L128 109.3V402.7L86.6 361.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l96 96c12.5 12.5 32.8 12.5 45.3 0l96-96c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 402.7V109.3l41.4 41.4c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3l-96-96z" />
        </svg>
    ),
    'star-filled': () => (
        <svg width="65" height="64" viewBox="0 0 65 64" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.5" width="64" height="64" rx="8" fill="#607085" />
            <rect x="0.5" width="64" height="64" rx="8" fill="url(#paint0_linear_25_1457)" />
            <g clipPath="url(#clip0_25_1457)">
                <path d="M34.2341 17.7431C33.913 17.0853 33.2346 16.6667 32.4895 16.6667C31.7444 16.6667 31.072 17.0853 30.7449 17.7431L26.8498 25.6547L18.1511 26.9225C17.4242 27.0301 16.8184 27.5325 16.5943 28.2202C16.3702 28.9079 16.5519 29.6674 17.0728 30.1757L23.3849 36.3411L21.8947 45.0541C21.7736 45.7717 22.0764 46.5012 22.6761 46.9258C23.2758 47.3504 24.0694 47.4042 24.7236 47.0634L32.4956 42.967L40.2675 47.0634C40.9217 47.4042 41.7153 47.3564 42.315 46.9258C42.9147 46.4953 43.2175 45.7717 43.0964 45.0541L41.6002 36.3411L47.9122 30.1757C48.4332 29.6674 48.6209 28.9079 48.3908 28.2202C48.1606 27.5325 47.5609 27.0301 46.834 26.9225L38.1291 25.6547L34.2341 17.7431Z" fill="white" />
            </g>
            <defs>
                <linearGradient id="paint0_linear_25_1457" x1="21" y1="12" x2="50" y2="54" gradientUnits="userSpaceOnUse">
                    <stop stopColor="white" stopOpacity="0" />
                    <stop offset="1" stopColor="white" stopOpacity="0.2" />
                </linearGradient>
                <clipPath id="clip0_25_1457">
                    <rect width="32" height="32" fill="white" transform="translate(16.5 16)" />
                </clipPath>
            </defs>
        </svg>
    ),
    'rotate': () => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1659_48126)">
                <path d="M6.345 6.34499C9.455 3.23499 14.48 3.21999 17.61 6.29499L15.55 8.34999C15.205 8.69499 15.105 9.20999 15.29 9.65999C15.475 10.11 15.915 10.4 16.4 10.4H22.375H22.8C23.465 10.4 24 9.86499 24 9.19999V2.79999C24 2.31499 23.71 1.87499 23.26 1.68999C22.81 1.50499 22.295 1.60499 21.95 1.94999L19.87 4.02999C15.49 -0.295014 8.435 -0.280014 4.08 4.07999C2.86 5.29999 1.98 6.73499 1.44 8.26999C1.145 9.10499 1.585 10.015 2.415 10.31C3.245 10.605 4.16 10.165 4.455 9.33499C4.84 8.24499 5.465 7.21999 6.345 6.34499ZM0 14.8V15.18V15.215V21.2C0 21.685 0.29 22.125 0.74 22.31C1.19 22.495 1.705 22.395 2.05 22.05L4.13 19.97C8.51 24.295 15.565 24.28 19.92 19.92C21.14 18.7 22.025 17.265 22.565 15.735C22.86 14.9 22.42 13.99 21.59 13.695C20.76 13.4 19.845 13.84 19.55 14.67C19.165 15.76 18.54 16.785 17.66 17.66C14.55 20.77 9.525 20.785 6.395 17.71L8.45 15.65C8.795 15.305 8.895 14.79 8.71 14.34C8.525 13.89 8.085 13.6 7.6 13.6H1.62H1.585H1.2C0.535 13.6 0 14.135 0 14.8Z" />
            </g>
            <defs>
                <clipPath id="clip0_1659_48126">
                    <rect width="24" height="24" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    ),
    'circle-exclamation': () => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1115_46664)">
                <path d="M12 24C18.6281 24 24 18.6281 24 12C24 5.37188 18.6281 0 12 0C5.37188 0 0 5.37188 0 12C0 18.6281 5.37188 24 12 24ZM12 6C12.6234 6 13.125 6.50156 13.125 7.125V12.375C13.125 12.9984 12.6234 13.5 12 13.5C11.3766 13.5 10.875 12.9984 10.875 12.375V7.125C10.875 6.50156 11.3766 6 12 6ZM13.5 16.5C13.5 17.3297 12.8297 18 12 18C11.1703 18 10.5 17.3297 10.5 16.5C10.5 15.6703 11.1703 15 12 15C12.8297 15 13.5 15.6703 13.5 16.5Z" />
            </g>
            <defs>
                <clipPath id="clip0_1115_46664">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    ),
    'circle-minus': () => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1655_48120)">
                <path d="M12 24C18.6281 24 24 18.6281 24 12C24 5.37188 18.6281 0 12 0C5.37188 0 0 5.37188 0 12C0 18.6281 5.37188 24 12 24ZM8.625 10.875H15.375C15.9984 10.875 16.5 11.3766 16.5 12C16.5 12.6234 15.9984 13.125 15.375 13.125H8.625C8.00156 13.125 7.5 12.6234 7.5 12C7.5 11.3766 8.00156 10.875 8.625 10.875Z" />
            </g>
            <defs>
                <clipPath id="clip0_1655_48120">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    ),
    'gear': () => (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
        >
            <g clipPath="url(#clip0_425_1115)">
                <path
                    d="M15.4969 5.20625C15.5969 5.47813 15.5125 5.78125 15.2969 5.975L13.9437 7.20625C13.9781 7.46563 13.9969 7.73125 13.9969 8C13.9969 8.26875 13.9781 8.53437 13.9437 8.79375L15.2969 10.025C15.5125 10.2187 15.5969 10.5219 15.4969 10.7937C15.3594 11.1656 15.1937 11.5219 15.0031 11.8656L14.8562 12.1187C14.65 12.4625 14.4187 12.7875 14.1656 13.0938C13.9812 13.3187 13.675 13.3937 13.4 13.3062L11.6594 12.7531C11.2406 13.075 10.7781 13.3438 10.2844 13.5469L9.89375 15.3313C9.83125 15.6156 9.6125 15.8406 9.325 15.8875C8.89375 15.9594 8.45 15.9969 7.99687 15.9969C7.54375 15.9969 7.1 15.9594 6.66875 15.8875C6.38125 15.8406 6.1625 15.6156 6.1 15.3313L5.70937 13.5469C5.21562 13.3438 4.75312 13.075 4.33437 12.7531L2.59687 13.3094C2.32187 13.3969 2.01562 13.3188 1.83125 13.0969C1.57812 12.7906 1.34687 12.4656 1.14062 12.1219L0.993746 11.8687C0.803121 11.525 0.637496 11.1687 0.499996 10.7969C0.399996 10.525 0.484371 10.2219 0.699996 10.0281L2.05312 8.79688C2.01875 8.53438 2 8.26875 2 8C2 7.73125 2.01875 7.46563 2.05312 7.20625L0.699996 5.975C0.484371 5.78125 0.399996 5.47813 0.499996 5.20625C0.637496 4.83438 0.803121 4.47813 0.993746 4.13438L1.14062 3.88125C1.34687 3.5375 1.57812 3.2125 1.83125 2.90625C2.01562 2.68125 2.32187 2.60625 2.59687 2.69375L4.3375 3.24688C4.75625 2.925 5.21875 2.65625 5.7125 2.45312L6.10312 0.66875C6.16562 0.384375 6.38437 0.159375 6.67187 0.1125C7.10312 0.0375 7.54687 0 8 0C8.45312 0 8.89687 0.0375 9.32812 0.109375C9.61562 0.15625 9.83437 0.38125 9.89687 0.665625L10.2875 2.45C10.7812 2.65313 11.2437 2.92188 11.6625 3.24375L13.4031 2.69062C13.6781 2.60312 13.9844 2.68125 14.1687 2.90313C14.4219 3.20938 14.6531 3.53437 14.8594 3.87812L15.0062 4.13125C15.1969 4.475 15.3625 4.83125 15.5 5.20312L15.4969 5.20625ZM8 10.5C9.38125 10.5 10.5 9.38125 10.5 8C10.5 6.61875 9.38125 5.5 8 5.5C6.61875 5.5 5.5 6.61875 5.5 8C5.5 9.38125 6.61875 10.5 8 10.5Z"
                    fill=""
                />
            </g>
            <defs>
                <clipPath id="clip0_425_1115">
                    <rect width="16" height="16" fill="white" />
                </clipPath>
            </defs>
        </svg>
    ),
    'recurring': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
            <g clipPath="url(#clip0_552_2659)">
                <path d="M0 5.74997C0 6.16481 0.335156 6.49997 0.75 6.49997C1.16484 6.49997 1.5 6.16481 1.5 5.74997C1.5 4.50778 2.50781 3.49997 3.75 3.49997H7.5V4.24997C7.5 4.55231 7.68281 4.82653 7.96406 4.94372C8.24531 5.06091 8.56641 4.99528 8.78203 4.782L10.282 3.282C10.575 2.98903 10.575 2.51325 10.282 2.22028L8.78203 0.72028C8.56641 0.504655 8.24531 0.441374 7.96406 0.558561C7.68281 0.675749 7.5 0.947624 7.5 1.24997V1.99997H3.75C1.67812 1.99997 0 3.67809 0 5.74997ZM12 7.24997C12 6.83512 11.6648 6.49997 11.25 6.49997C10.8352 6.49997 10.5 6.83512 10.5 7.24997C10.5 8.49216 9.49219 9.49997 8.25 9.49997H4.5V8.74997C4.5 8.44762 4.31719 8.1734 4.03594 8.05622C3.75469 7.93903 3.43359 8.00465 3.21797 8.21794L1.71797 9.71794C1.425 10.0109 1.425 10.4867 1.71797 10.7797L3.21797 12.2797C3.43359 12.4953 3.75469 12.5586 4.03594 12.4414C4.31719 12.3242 4.5 12.0523 4.5 11.7476V11H8.25C10.3219 11 12 9.32184 12 7.24997Z" fill=""/>
            </g>
            <defs>
                <clipPath id="clip0_552_2659">
                    <rect width="12" height="12" fill="" transform="translate(0 0.5)"/>
                </clipPath>
            </defs>
        </svg>
    ),
    'ellipse': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="6" height="7" viewBox="0 0 6 7" fill="none">
            <circle cx="3" cy="3.5" r="3" fill=""/>
        </svg>
    ),
    'solid-gear': () => (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15.4969 5.20625C15.5969 5.47813 15.5125 5.78125 15.2969 5.975L13.9437 7.20625C13.9781 7.46563 13.9969 7.73125 13.9969 8C13.9969 8.26875 13.9781 8.53438 13.9437 8.79375L15.2969 10.025C15.5125 10.2188 15.5969 10.5219 15.4969 10.7937C15.3594 11.1656 15.1937 11.5219 15.0031 11.8656L14.8562 12.1187C14.65 12.4625 14.4187 12.7875 14.1656 13.0938C13.9812 13.3188 13.675 13.3937 13.4 13.3062L11.6594 12.7531C11.2406 13.075 10.7781 13.3438 10.2844 13.5469L9.89375 15.3313C9.83125 15.6156 9.6125 15.8406 9.325 15.8875C8.89375 15.9594 8.45 15.9969 7.99687 15.9969C7.54375 15.9969 7.1 15.9594 6.66875 15.8875C6.38125 15.8406 6.1625 15.6156 6.1 15.3313L5.70937 13.5469C5.21562 13.3438 4.75312 13.075 4.33437 12.7531L2.59687 13.3094C2.32187 13.3969 2.01562 13.3188 1.83125 13.0969C1.57812 12.7906 1.34687 12.4656 1.14062 12.1219L0.993746 11.8687C0.803121 11.525 0.637496 11.1687 0.499996 10.7969C0.399996 10.525 0.484371 10.2219 0.699996 10.0281L2.05312 8.79688C2.01875 8.53438 2 8.26875 2 8C2 7.73125 2.01875 7.46563 2.05312 7.20625L0.699996 5.975C0.484371 5.78125 0.399996 5.47813 0.499996 5.20625C0.637496 4.83438 0.803121 4.47813 0.993746 4.13438L1.14062 3.88125C1.34687 3.5375 1.57812 3.2125 1.83125 2.90625C2.01562 2.68125 2.32187 2.60625 2.59687 2.69375L4.3375 3.24688C4.75625 2.925 5.21875 2.65625 5.7125 2.45312L6.10312 0.66875C6.16562 0.384375 6.38437 0.159375 6.67187 0.1125C7.10312 0.0375 7.54687 0 8 0C8.45312 0 8.89687 0.0375 9.32812 0.109375C9.61562 0.15625 9.83437 0.38125 9.89687 0.665625L10.2875 2.45C10.7812 2.65313 11.2437 2.92188 11.6625 3.24375L13.4031 2.69062C13.6781 2.60312 13.9844 2.68125 14.1687 2.90313C14.4219 3.20938 14.6531 3.53437 14.8594 3.87812L15.0062 4.13125C15.1969 4.475 15.3625 4.83125 15.5 5.20312L15.4969 5.20625ZM8 10.5C9.38125 10.5 10.5 9.38125 10.5 8C10.5 6.61875 9.38125 5.5 8 5.5C6.61875 5.5 5.5 6.61875 5.5 8C5.5 9.38125 6.61875 10.5 8 10.5Z" fill="#607085" />
        </svg>
    ),
    'sort-up-down': () => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_27_37799)">
                <path d="M19.5018 1.49946V16.4936H22.5007C23.1051 16.4936 23.6533 16.8591 23.8876 17.4214C24.1219 17.9836 23.9907 18.6256 23.5643 19.0567L19.0661 23.5549C18.7849 23.8361 18.4054 23.9954 18.0071 23.9954C17.6088 23.9954 17.2293 23.8361 16.9481 23.5549L12.4499 19.0567C12.0188 18.6256 11.8923 17.9836 12.1266 17.4214C12.3609 16.8591 12.9044 16.4936 13.5135 16.4936H16.503V1.49946C16.503 0.670098 17.1731 4.69983e-05 18.0024 4.69983e-05C18.8318 4.69983e-05 19.5018 0.670098 19.5018 1.49946ZM7.50652 22.4913C7.50652 23.3206 6.83647 23.9907 6.0071 23.9907C5.17774 23.9907 4.50769 23.3206 4.50769 22.4913V7.49712H1.50886C0.904409 7.49712 0.356186 7.13164 0.121902 6.56936C-0.112382 6.00708 0.018817 5.36514 0.445213 4.93406L4.94346 0.435814C5.2246 0.154674 5.60414 -0.00463867 6.00242 -0.00463867C6.4007 -0.00463867 6.78024 0.154674 7.06138 0.435814L11.5596 4.93406C11.9907 5.36514 12.1172 6.00708 11.8829 6.56936C11.6487 7.13164 11.1051 7.49712 10.496 7.49712H7.49715V22.4913H7.50652Z"></path>
            </g>
        </svg>
    ),
    'skill-notification': () => (
        <svg width="30" height="31" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="30" height="30.7802" rx="8" fill="url(#paint0_linear_594_39901)" />
            <path d="M12 0C16.4625 0 20.1654 3.24863 20.8779 7.50488C20.9811 8.1142 21.192 8.70981 21.5811 9.19727L23.5498 11.6621C23.8404 12.0277 24 12.4785 24 12.9473C23.9998 14.0813 23.0813 14.9998 21.9473 15H21V18C21 19.6547 19.6547 21 18 21H15V22.5C15 23.3297 14.3297 24 13.5 24H4.5C3.67031 24 3 23.3297 3 22.5V19.0918C2.99989 18.3091 2.67629 17.5686 2.19824 16.9453C0.778055 15.1126 8.66573e-05 12.8486 0 10.5098C0 9.94441 0.0457269 9.3891 0.131836 8.84766C0.784905 12.2972 3.8136 14.9062 7.45312 14.9062C11.5694 14.9062 14.9062 11.5694 14.9062 7.45312C14.9062 3.81188 12.2948 0.781988 8.84277 0.130859C9.38257 0.0451178 9.93601 0 10.5 0H12Z" fill="white" />
            <path d="M13.1868 5.38798C13.2632 5.59591 13.1987 5.82774 13.0339 5.97592L11.9992 6.91757C12.0255 7.11594 12.0398 7.31909 12.0398 7.52463C12.0398 7.73017 12.0255 7.93332 11.9992 8.13169L13.0339 9.07335C13.1987 9.22153 13.2632 9.45336 13.1868 9.66129C13.0816 9.9457 12.955 10.2182 12.8092 10.4811L12.6969 10.6746C12.5392 10.9375 12.3624 11.1861 12.1688 11.4203C12.0279 11.5924 11.7937 11.6498 11.5834 11.5828L10.2525 11.1598C9.93226 11.406 9.57861 11.6115 9.20106 11.7669L8.90237 13.1316C8.85458 13.349 8.68731 13.5211 8.46748 13.557C8.13772 13.6119 7.79841 13.6406 7.45193 13.6406C7.10545 13.6406 6.76614 13.6119 6.43638 13.557C6.21655 13.5211 6.04928 13.349 6.00149 13.1316L5.7028 11.7669C5.32526 11.6115 4.97161 11.406 4.65141 11.1598L3.32284 11.5852C3.11256 11.6522 2.87838 11.5924 2.7374 11.4227C2.54385 11.1885 2.36703 10.9399 2.20932 10.677L2.09701 10.4834C1.95125 10.2205 1.8246 9.94809 1.71947 9.66368C1.643 9.45575 1.70752 9.22392 1.8724 9.07574L2.90706 8.13408C2.88077 7.93332 2.86644 7.73017 2.86644 7.52463C2.86644 7.31909 2.88077 7.11594 2.90706 6.91757L1.8724 5.97592C1.70752 5.82774 1.643 5.59591 1.71947 5.38798C1.8246 5.10357 1.95125 4.83111 2.09701 4.56821L2.20932 4.37462C2.36703 4.11172 2.54385 3.86316 2.7374 3.62894C2.87838 3.45686 3.11256 3.3995 3.32284 3.46642L4.6538 3.88945C4.974 3.64328 5.32764 3.43774 5.70519 3.28239L6.00388 1.91771C6.05167 1.70022 6.21894 1.52814 6.43877 1.49229C6.76853 1.43493 7.10784 1.40625 7.45432 1.40625C7.8008 1.40625 8.14011 1.43493 8.46987 1.4899C8.6897 1.52575 8.85697 1.69783 8.90476 1.91532L9.20345 3.28C9.581 3.43535 9.93464 3.64089 10.2548 3.88706L11.5858 3.46403C11.7961 3.39711 12.0303 3.45686 12.1712 3.62655C12.3648 3.86077 12.5416 4.10933 12.6993 4.37223L12.8116 4.56582C12.9574 4.82872 13.084 5.10118 13.1892 5.38559L13.1868 5.38798ZM7.45432 9.43663C8.51049 9.43663 9.36594 8.58101 9.36594 7.52463C9.36594 6.46826 8.51049 5.61264 7.45432 5.61264C6.39815 5.61264 5.5427 6.46826 5.5427 7.52463C5.5427 8.58101 6.39815 9.43663 7.45432 9.43663Z" fill="white" />
        </svg>
    ),
    'right-up square': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor" width="16">
            <path d="M320 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l82.7 0L201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L448 109.3l0 82.7c0 17.7 14.3 32 32 32s32-14.3 32-32l0-160c0-17.7-14.3-32-32-32L320 0zM80 32C35.8 32 0 67.8 0 112L0 432c0 44.2 35.8 80 80 80l320 0c44.2 0 80-35.8 80-80l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16L80 448c-8.8 0-16-7.2-16-16l0-320c0-8.8 7.2-16 16-16l112 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 32z"/>
        </svg>
    ),
    'user-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <g clipPath="url(#clip0_93_5872)">
                <path d="M9 10.125C11.7949 10.125 14.0625 7.85742 14.0625 5.0625C14.0625 2.26758 11.7949 0 9 0C6.20508 0 3.9375 2.26758 3.9375 5.0625C3.9375 7.85742 6.20508 10.125 9 10.125ZM5.6707 11.25C2.53828 11.25 0 13.7883 0 16.9207C0 17.5184 0.485156 18 1.0793 18H16.9207C17.5184 18 18 17.5148 18 16.9207C18 13.7883 15.4617 11.25 12.3293 11.25H5.6707Z" fill="white"/>
            </g>
            <defs>
                <clipPath id="clip0_93_5872">
                    <rect width="18" height="18" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    ),
    'planner-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M1.125 1.125C1.74727 1.125 2.25 1.62773 2.25 2.25V14.0625C2.25 14.3719 2.50312 14.625 2.8125 14.625H16.875C17.4973 14.625 18 15.1277 18 15.75C18 16.3723 17.4973 16.875 16.875 16.875H2.8125C1.25859 16.875 0 15.6164 0 14.0625V2.25C0 1.62773 0.502734 1.125 1.125 1.125ZM4.5 4.5C4.5 3.87773 5.00273 3.375 5.625 3.375H9C9.62227 3.375 10.125 3.87773 10.125 4.5C10.125 5.12227 9.62227 5.625 9 5.625H5.625C5.00273 5.625 4.5 5.12227 4.5 4.5ZM7.875 6.75H12.375C12.9973 6.75 13.5 7.25273 13.5 7.875C13.5 8.49727 12.9973 9 12.375 9H7.875C7.25273 9 6.75 8.49727 6.75 7.875C6.75 7.25273 7.25273 6.75 7.875 6.75ZM13.5 10.125H15.75C16.3723 10.125 16.875 10.6277 16.875 11.25C16.875 11.8723 16.3723 12.375 15.75 12.375H13.5C12.8777 12.375 12.375 11.8723 12.375 11.25C12.375 10.6277 12.8777 10.125 13.5 10.125Z" fill="white"/>
        </svg>
    ),
    'reports-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M2.25 2.25C2.25 1.62773 1.74727 1.125 1.125 1.125C0.502734 1.125 0 1.62773 0 2.25V14.0625C0 15.6164 1.25859 16.875 2.8125 16.875H16.875C17.4973 16.875 18 16.3723 18 15.75C18 15.1277 17.4973 14.625 16.875 14.625H2.8125C2.50312 14.625 2.25 14.3719 2.25 14.0625V2.25ZM16.5445 5.29453C16.984 4.85508 16.984 4.14141 16.5445 3.70195C16.1051 3.2625 15.3914 3.2625 14.952 3.70195L11.25 7.40742L9.23203 5.38945C8.79258 4.95 8.07891 4.95 7.63945 5.38945L3.70195 9.32695C3.2625 9.76641 3.2625 10.4801 3.70195 10.9195C4.14141 11.359 4.85508 11.359 5.29453 10.9195L8.4375 7.78008L10.4555 9.79805C10.8949 10.2375 11.6086 10.2375 12.048 9.79805L16.548 5.29805L16.5445 5.29453Z" fill="white"/>
        </svg>
    ),
    'roles-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M8.91699 0C7.44746 0 6.1959 0.938672 5.73535 2.25H4.41699C3.17598 2.25 2.16699 3.25898 2.16699 4.5V15.75C2.16699 16.991 3.17598 18 4.41699 18H13.417C14.658 18 15.667 16.991 15.667 15.75V4.5C15.667 3.25898 14.658 2.25 13.417 2.25H12.0986C11.6381 0.938672 10.3865 0 8.91699 0ZM8.91699 2.25C9.21536 2.25 9.50151 2.36853 9.71249 2.5795C9.92347 2.79048 10.042 3.07663 10.042 3.375C10.042 3.67337 9.92347 3.95952 9.71249 4.1705C9.50151 4.38147 9.21536 4.5 8.91699 4.5C8.61862 4.5 8.33248 4.38147 8.1215 4.1705C7.91052 3.95952 7.79199 3.67337 7.79199 3.375C7.79199 3.07663 7.91052 2.79048 8.1215 2.5795C8.33248 2.36853 8.61862 2.25 8.91699 2.25ZM6.66699 9C6.66699 8.40326 6.90405 7.83097 7.326 7.40901C7.74796 6.98705 8.32026 6.75 8.91699 6.75C9.51373 6.75 10.086 6.98705 10.508 7.40901C10.9299 7.83097 11.167 8.40326 11.167 9C11.167 9.59674 10.9299 10.169 10.508 10.591C10.086 11.0129 9.51373 11.25 8.91699 11.25C8.32026 11.25 7.74796 11.0129 7.326 10.591C6.90405 10.169 6.66699 9.59674 6.66699 9ZM4.97949 15.1875C4.97949 13.6336 6.23809 12.375 7.79199 12.375H10.042C11.5959 12.375 12.8545 13.6336 12.8545 15.1875C12.8545 15.4969 12.6014 15.75 12.292 15.75H5.54199C5.23262 15.75 4.97949 15.4969 4.97949 15.1875Z" fill="white"/>
        </svg>
    ),
    'settings-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <g clipPath="url(#clip0_93_9053)">
                <path d="M17.4341 5.85703C17.5466 6.16289 17.4517 6.50391 17.2091 6.72187L15.6869 8.10703C15.7255 8.39883 15.7466 8.69766 15.7466 9C15.7466 9.30234 15.7255 9.60117 15.6869 9.89297L17.2091 11.2781C17.4517 11.4961 17.5466 11.8371 17.4341 12.143C17.2795 12.5613 17.0931 12.9621 16.8787 13.3488L16.7134 13.6336C16.4814 14.0203 16.2212 14.3859 15.9365 14.7305C15.7291 14.9836 15.3845 15.068 15.0752 14.9695L13.117 14.3473C12.6459 14.7094 12.1255 15.0117 11.5701 15.2402L11.1306 17.2477C11.0603 17.5676 10.8142 17.8207 10.4908 17.8734C10.0056 17.9543 9.5064 17.9965 8.99664 17.9965C8.48687 17.9965 7.98766 17.9543 7.5025 17.8734C7.17906 17.8207 6.93297 17.5676 6.86266 17.2477L6.4232 15.2402C5.86773 15.0117 5.34742 14.7094 4.87633 14.3473L2.92164 14.973C2.61226 15.0715 2.26773 14.9836 2.06031 14.734C1.77555 14.3895 1.51539 14.0238 1.28336 13.6371L1.11812 13.3523C0.903671 12.9656 0.717343 12.5648 0.562656 12.1465C0.450156 11.8406 0.545077 11.4996 0.787656 11.2816L2.30992 9.89648C2.27125 9.60117 2.25016 9.30234 2.25016 9C2.25016 8.69766 2.27125 8.39883 2.30992 8.10703L0.787656 6.72187C0.545077 6.50391 0.450156 6.16289 0.562656 5.85703C0.717343 5.43867 0.903671 5.03789 1.11812 4.65117L1.28336 4.36641C1.51539 3.97969 1.77555 3.61406 2.06031 3.26953C2.26773 3.01641 2.61226 2.93203 2.92164 3.03047L4.87984 3.65273C5.35094 3.29062 5.87125 2.98828 6.42672 2.75977L6.86617 0.752344C6.93648 0.432422 7.18258 0.179297 7.50601 0.126562C7.99117 0.0421875 8.49039 0 9.00016 0C9.50992 0 10.0091 0.0421875 10.4943 0.123047C10.8177 0.175781 11.0638 0.428906 11.1341 0.748828L11.5736 2.75625C12.1291 2.98477 12.6494 3.28711 13.1205 3.64922L15.0787 3.02695C15.388 2.92852 15.7326 3.01641 15.94 3.26602C16.2248 3.61055 16.4849 3.97617 16.717 4.36289L16.8822 4.64766C17.0966 5.03437 17.283 5.43516 17.4377 5.85352L17.4341 5.85703ZM9.00016 11.8125C10.5541 11.8125 11.8127 10.5539 11.8127 9C11.8127 7.44609 10.5541 6.1875 9.00016 6.1875C7.44625 6.1875 6.18766 7.44609 6.18766 9C6.18766 10.5539 7.44625 11.8125 9.00016 11.8125Z" fill="white"/>
            </g>
            <defs>
                <clipPath id="clip0_93_9053">
                    <rect width="18" height="18" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    ),
    'summary-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M17.9937 8.98438C17.9937 9.54688 17.525 9.9875 16.9937 9.9875H15.9937L16.0156 14.9937C16.0156 15.0781 16.0094 15.1625 16 15.2469V15.75C16 16.4406 15.4406 17 14.75 17H14.25C14.2156 17 14.1813 17 14.1469 16.9969C14.1031 17 14.0594 17 14.0156 17H13H12.25C11.5594 17 11 16.4406 11 15.75V15V13C11 12.4469 10.5531 12 10 12H8C7.44688 12 7 12.4469 7 13V15V15.75C7 16.4406 6.44063 17 5.75 17H5H4.00313C3.95625 17 3.90937 16.9969 3.8625 16.9937C3.825 16.9969 3.7875 17 3.75 17H3.25C2.55938 17 2 16.4406 2 15.75V12.25C2 12.2219 2 12.1906 2.00312 12.1625V9.9875H1C0.4375 9.9875 0 9.55 0 8.98438C0 8.70312 0.09375 8.45312 0.3125 8.23438L8.325 1.25C8.54375 1.03125 8.79375 1 9.0125 1C9.23125 1 9.48125 1.0625 9.66875 1.21875L17.65 8.23438C17.9 8.45312 18.025 8.70312 17.9937 8.98438Z" fill="white"/>
        </svg>
    ),
    'tableView-security': ()=>(
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M2.25 9V5.625H7.875V9H2.25ZM2.25 11.25H7.875V14.625H2.25V11.25ZM10.125 14.625V11.25H15.75V14.625H10.125ZM15.75 9H10.125V5.625H15.75V9ZM2.25 1.125C1.00898 1.125 0 2.13398 0 3.375V14.625C0 15.866 1.00898 16.875 2.25 16.875H15.75C16.991 16.875 18 15.866 18 14.625V3.375C18 2.13398 16.991 1.125 15.75 1.125H2.25Z" fill="white"/>
        </svg>
    ),
    'lists-security': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <path d="M2.25 5.0625C2.69755 5.0625 3.12678 4.88471 3.44324 4.56824C3.75971 4.25178 3.9375 3.82255 3.9375 3.375C3.9375 2.92745 3.75971 2.49822 3.44324 2.18176C3.12678 1.86529 2.69755 1.6875 2.25 1.6875C1.80245 1.6875 1.37322 1.86529 1.05676 2.18176C0.74029 2.49822 0.5625 2.92745 0.5625 3.375C0.5625 3.82255 0.74029 4.25178 1.05676 4.56824C1.37322 4.88471 1.80245 5.0625 2.25 5.0625ZM6.75 2.25C6.12773 2.25 5.625 2.75273 5.625 3.375C5.625 3.99727 6.12773 4.5 6.75 4.5H16.875C17.4973 4.5 18 3.99727 18 3.375C18 2.75273 17.4973 2.25 16.875 2.25H6.75ZM6.75 7.875C6.12773 7.875 5.625 8.37773 5.625 9C5.625 9.62227 6.12773 10.125 6.75 10.125H16.875C17.4973 10.125 18 9.62227 18 9C18 8.37773 17.4973 7.875 16.875 7.875H6.75ZM6.75 13.5C6.12773 13.5 5.625 14.0027 5.625 14.625C5.625 15.2473 6.12773 15.75 6.75 15.75H16.875C17.4973 15.75 18 15.2473 18 14.625C18 14.0027 17.4973 13.5 16.875 13.5H6.75ZM2.25 16.3125C2.69755 16.3125 3.12678 16.1347 3.44324 15.8182C3.75971 15.5018 3.9375 15.0726 3.9375 14.625C3.9375 14.1774 3.75971 13.7482 3.44324 13.4318C3.12678 13.1153 2.69755 12.9375 2.25 12.9375C1.80245 12.9375 1.37322 13.1153 1.05676 13.4318C0.74029 13.7482 0.5625 14.1774 0.5625 14.625C0.5625 15.0726 0.74029 15.5018 1.05676 15.8182C1.37322 16.1347 1.80245 16.3125 2.25 16.3125ZM3.9375 9C3.9375 8.77839 3.89385 8.55896 3.80905 8.35422C3.72424 8.14948 3.59994 7.96346 3.44324 7.80676C3.28654 7.65006 3.10052 7.52576 2.89578 7.44095C2.69104 7.35615 2.47161 7.3125 2.25 7.3125C2.02839 7.3125 1.80896 7.35615 1.60422 7.44095C1.39948 7.52576 1.21346 7.65006 1.05676 7.80676C0.900058 7.96346 0.775758 8.14948 0.690953 8.35422C0.606148 8.55896 0.5625 8.77839 0.5625 9C0.5625 9.22161 0.606148 9.44104 0.690953 9.64578C0.775758 9.85052 0.900058 10.0365 1.05676 10.1932C1.21346 10.3499 1.39948 10.4742 1.60422 10.559C1.80896 10.6439 2.02839 10.6875 2.25 10.6875C2.47161 10.6875 2.69104 10.6439 2.89578 10.559C3.10052 10.4742 3.28654 10.3499 3.44324 10.1932C3.59994 10.0365 3.72424 9.85052 3.80905 9.64578C3.89385 9.44104 3.9375 9.22161 3.9375 9Z" fill="white"/>
        </svg>
    ),
    'operationLog-security': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <g clipPath="url(#clip0_93_26190)">
                <path d="M-2.99805 0.997905C-2.99805 -0.472929 -1.80221 -1.66876 -0.33138 -1.66876H6.33529V3.66457C6.33529 4.40207 6.93112 4.9979 7.66862 4.9979H13.002V10.2354L9.38529 13.8521C8.95612 14.2812 8.65612 14.8146 8.51029 15.4021L7.73112 18.5229C7.63529 18.9062 7.65612 19.3062 7.78529 19.6687H-0.33138C-1.80221 19.6687 -2.99805 18.4729 -2.99805 17.0021V0.997905ZM13.002 3.66457H7.66862V-1.66876L13.002 3.66457ZM19.9103 8.15207L20.5103 8.75207C21.1603 9.40207 21.1603 10.4562 20.5103 11.1104L19.2853 12.3354L16.327 9.37707L17.552 8.15207C18.202 7.50207 19.2561 7.50207 19.9103 8.15207ZM9.99779 15.7062L15.3811 10.3229L18.3395 13.2812L12.9561 18.6604C12.7853 18.8312 12.5728 18.9521 12.3353 19.0104L9.83112 19.6354C9.60195 19.6937 9.36445 19.6271 9.19779 19.4604C9.03112 19.2937 8.96445 19.0562 9.02279 18.8271L9.64779 16.3229C9.70612 16.0896 9.82695 15.8729 9.99779 15.7021V15.7062Z" fill="white"/>
            </g>
            <defs>
                <clipPath id="clip0_93_26190">
                    <rect width="18" height="18" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    ),
    'eyeFilterIcon': () => (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <g clipPath="url(#clip0_114_8275)">
                <path d="M7.99979 1.77783C5.75535 1.77783 3.95813 2.80005 2.64979 4.01672C1.34979 5.22228 0.480349 6.66672 0.0692383 7.65839C-0.0224284 7.87783 -0.0224284 8.12228 0.0692383 8.34172C0.480349 9.33339 1.34979 10.7778 2.64979 11.9834C3.95813 13.2001 5.75535 14.2223 7.99979 14.2223C10.2442 14.2223 12.0415 13.2001 13.3498 11.9834C14.6498 10.7751 15.5192 9.33339 15.9331 8.34172C16.0248 8.12228 16.0248 7.87783 15.9331 7.65839C15.5192 6.66672 14.6498 5.22228 13.3498 4.01672C12.0415 2.80005 10.2442 1.77783 7.99979 1.77783ZM11.9998 8.00005C11.9998 10.2084 10.2081 12.0001 7.99979 12.0001C5.79146 12.0001 3.99979 10.2084 3.99979 8.00005C3.99979 5.79172 5.79146 4.00005 7.99979 4.00005C10.2081 4.00005 11.9998 5.79172 11.9998 8.00005ZM7.99979 6.22228C7.99979 7.20283 7.20257 8.00005 6.22202 8.00005C5.90257 8.00005 5.60257 7.91672 5.34424 7.76672C5.33868 7.8445 5.33313 7.9195 5.33313 8.00005C5.33313 9.47228 6.52757 10.6667 7.99979 10.6667C9.47202 10.6667 10.6665 9.47228 10.6665 8.00005C10.6665 6.52783 9.47202 5.33339 7.99979 5.33339C7.92202 5.33339 7.84424 5.33616 7.76646 5.3445C7.91368 5.60283 7.99979 5.90283 7.99979 6.22228Z" fill="#607085"/>
            </g>
            <defs>
                <clipPath id="clip0_114_8275">
                    <rect width="16" height="16" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    )
};

export default IconDefinitions;