@icon-size: 32px;

.peopleFinder-modal{
    .ant-layout-content {
        align-self: center;
        width: -moz-available;
        width: -webkit-fill-available;
    }

    .ant-modal-content{
        width: 50vw;
        margin: auto;
    }

    .ant-modal-footer{
        text-align: left;
    }

    .ant-btn-link span {
        text-decoration: none;
    }

    .ant-modal-body {
        height: auto !important;
    }

    .ant-modal-header {
        padding: unset;
        padding-left: 2em;
    }

    .ant-layout-header {
        background: white;
        padding-left: 0;
        border-bottom: 0;
        margin-bottom: 10px;
        padding-right: 10px;
    }

    .ant-layout{
        background: white;
        width: 100%;
        height: 100%;
    }

    .peopleFinderDialogFooter {
        text-align: left;
        align-items: center;
        padding: 0;

        .ant-btn-link span {
            text-decoration: none;
        }

        .ant-btn-tertiary {
            color: @primary-color
        }
    }

    .peopleFinder-resource-icon {
        .anticon {
            color: @secondary-color;
        }
    }

    .peopleFinderContent {
        .ant-alert-info {
            background: @primary-light;
            border-color: @secondary-color;
        }
    }

    .emptyViewContainer{
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        align-content: center;
        justify-content: center;

        .ant-empty-normal {
            display: flex;
            align-items: center;
            flex-direction: column;

            h3, p {
                margin-top: 0;
            }
        }
    }

    .peopleFinder-content-form {
        height: 365px;

        .ant-table-body {
            overflow: unset !important;
            overflow-x: scroll !important;    
        }

        .ant-table-tbody > tr > td {
            max-width: 220px !important;
        } 

        ._33rjjUGtTY6ZklFeUCvgIK {
            margin: 30px;
        }
        ._2L-h8PMITPe9xD5MBG3Eva {
            margin-bottom: 50px;
            color: #000000;
        }
    
        .peopleFinder-data-summary-wrapper { 
            display: flex;
            justify-content: space-between;
            align-items: center;
    
            .peopleFinder-data-summary {
                padding: 0.625rem 0;
                display: flex;
                padding-top: 20px;
                justify-content: center;
                align-items: center;
    
                .info-icon {
                    color: grey;
                }

                > div {
                    margin-left: 5px;
                }

                .ant-calendar-picker-input {
                    margin: 0
                }
            }

            .ant-btn-tertiary {
                color: @primary-color;
                background-color: @white-color;
                padding-top: 12px;
            }
        }

        .spin {
            position: relative;
        }
    }
    
    .peoplFinderTitleContainer {
        display: flex;
      }
    
    .peopleFinder-window-header-icon {
        font-size: 12px;
        background-color: @primary-color;
        color: @white-color;
        border-radius: 50%;
        width: @icon-size;
        min-width: @icon-size;
        height: @icon-size;
        min-height: @icon-size;
        line-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        text-align: center;
        margin-right: 15px;

        .title-icon {
            font-size: 20px;
            margin: 0;

            svg > path {
                fill: @white-color;
            }
        }
    }
    .errorMessage {
        color: @error-color;
        font-size: small;
        padding-bottom: 0.5em;
    }

    .filter-pane {
        padding-left: 0;
        span {
            &.filter-summary {
                span {
                    &.ant-badge {
                        &:hover, &.active  {
                            .anticon-close {
                                margin-left: inherit;
                                font-size: 10px;
                            }
                        }
                    }
                }
            }
        }
    }

    span.ant-dropdown-trigger, span.filter-menu-group {
        //padding-left: 0 !important;
        display: flex !important;
        align-items: center;
        margin-right: 12px;

    }
}

.peopleFinderWindowHeaderTitle {
    display: inline-block;
    padding-top: 5px;
}