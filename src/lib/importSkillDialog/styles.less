.confirmation-text {
  margin-top: 1em;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.count-text {
  font-size: larger;
}

.importModal-body {
  height: 70vh;

  .ant-table-tbody>tr.ant-table-row-selected .ant-table-selection-column {
    box-shadow: none;
  }
}

.import-library-search {
  width: 500px;
  margin-top: 10px;

  .ant-select-selection-search-input:focus-visible {
    outline: none !important;
  }
}

  .import-skill-search-divider {
    border-bottom: 1px solid lightgray;
  }

.import-skill-search-option{
  .skillName{
    font-size: medium;
  }

  .skill-detail{
    font-size: 12px;
    color: #888;
  }
}

// Table height without search component
.table-banner .ant-table-body {
  height: 54vh !important;
}
// Table height with search component
.table-banner-ws .ant-table-body {
  height: 52vh !important;
}
.expandable-table {

.ant-table-wrapper {
    margin-top: 10px;
}

  .ant-alert-warning,
  .ant-alert-error {
    margin-right: 16px;
    margin-top: 15px;
  }

  .ant-alert-icon {
    margin: 0px !important;
    padding-right: 5px;
    align-self: center;
  }

  .active-row {
    font-weight: 600;
  }

  .header-text {
    color: @light-text-color
  }

  .ant-table-tbody>tr>td {
    border: none;
  }

  .ant-table-wrapper tr.ant-table-expanded-row:hover>td {
    background: @white-color;
  }

  .ant-table-tbody>tr>td {
    padding: 0.5rem 1.286rem 0.5rem 0;
  }

  .subcategory-table-container .ant-table-wrapper .ant-table-selection-column {
    padding-inline-end: 1rem;
    padding-inline-start: 0px;
  }

  .skill-table-container {
    margin-left: 3.5rem;

    .ant-table-wrapper .ant-table-selection-column {
      padding-inline-end: 1rem;
      padding-inline-start: 1rem;
    }
  }

  .ant-table-table {
    .ant-table-table-container {

      .ant-table-table-body,
      .ant-table-table-content {
        scrollbar-width: thin;
        scrollbar-color: #eaeaea transparent;
        scrollbar-gutter: stable;
      }
    }
  }

  .ant-select-open > * > .anticon-search{
    transform: rotate(180deg);
  }
}

.review-table {

  .count-text {
    margin: 20px 10px;
  }

  .ant-table-body {
    height: 52vh;
  }
}