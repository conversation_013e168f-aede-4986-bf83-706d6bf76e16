import React, { useState } from 'react';
import './styles.less';
import Stepper from '../steps';
import PropTypes from 'prop-types';
import { getStyledStringLiteralInfo, populateStringTemplates, replaceStringLiteralInfoTags } from '../../utils/translationUtils';
import { useEffect } from 'react';
import ExpandableImportRowTable from './expandableImportRowTable';
import ReviewImportTable from './reviewImportTable';
import { FormattedMessage } from 'react-intl';
import { ProvideWarningElement } from '../../components/adminSetting/userManagement/navigationComponent/navigateComponent';
import { ContactUs } from '../../components/adminSetting/userManagement/navigationComponent/navigateComponent';

//#region - Utilities used for displaying text
const getDisplayText = (label, values) => {
    const { label: labelWithValues } = populateStringTemplates({ label }, values ?? {});
    const styledInfoText = getStyledStringLiteralInfo(labelWithValues || []);

    return replaceStringLiteralInfoTags(styledInfoText || []);
};

const getSelectedCount = (selectedSections) => {
    const categories = [...new Set(selectedSections.map(x => x.categoryId))];

    return { skillCount: selectedSections.length, categoryCount: categories.length };
};

// Used to get the initial state of review page and selected skill, category count
const getSelectedSkillSections = (skillSections, selectedSkillRowKeys) => {
    if (!selectedSkillRowKeys.length) return [];

    return skillSections.flatMap(category =>
        category.subCategories.flatMap(subCategory =>
            subCategory.skills
                .filter(skill => selectedSkillRowKeys.includes(skill.id))
                .map(skill => ({
                    id: skill.id,
                    name: skill.name,
                    categoryId: category.id,
                    categoryName: category.name,
                    subCategoryId: subCategory.id,
                    subCategoryName: subCategory.name
                }))));
};
//#endregion

/**
 * ImportLibrarySkills is stepper component with 3 stages selection, review and confirm page
 *
 * @component
 * @param {Object} [librarySections] - librarySections contains properties needed for import modal
 * @param {Function} [clearImportLibraryData] - Callback function to close the modal
 * @param {Object} [messages] - Object to hold all the texts to be displayed
 * @param {Function} [fetchLibrarySections] - Callback function to fetch the skill sections
 * @param {Function} [fetchLibrarySkills] - Callback function to fetch the skills for a given category and subcategory
 * @param {Function} [updateSelectedSkills] - Callback function to update selectedSkillRowKeys
 * @param {Function} [importLibrarySkills] - Callback function to Import skills
 * @param {Number} [initialSkillCount] already existing skill count
 * @param {Object} [licenseProps] gives license count allowed
 * @param {Function} [searchLibrarySkills] search skill from the api
 * @param {Function} [selectLibrarySkill] select searched skill to be included
 * @param {boolean} [isImportLibrarySearchEnabled] - feature flag for skill search
 */
const ImportLibrarySkills = ({
    librarySections,
    clearImportLibraryData,
    messages,
    fetchLibrarySections,
    fetchLibrarySkills,
    updateSelectedSkills,
    importLibrarySkills,
    initialSkillCount,
    licenseProps,
    searchLibrarySkills,
    selectLibrarySkill,
    isImportLibrarySearchEnabled
}) => {
    const {
        importText,
        importClickInfo,
        importProgressInfo,
        importLibraryStep1,
        importLibraryStep2,
        importLibraryStep3,
        existingSkillsHeader,
        selectedImportDataLabel } = messages;
    const { initialSkillSections, selectedSkillRowKeys } = librarySections;
    const { subscribedCount:subscribedSkillCount, thresholdValue:skillThresholdValue } = licenseProps;

    /* This is a local state used to hold the selected skill along with category and subcategory details, used in review and confirmation page*/
    const [selectedSkillSections, setSelectedSkillSections] = useState([]);
    const [confirmedSkillSections, setConfirmedSkillSections] = useState([]);
    /* This is a local state used to hold banner state*/
    const [displayLicenceWarningMessage, setDisplayLicenceWarningMessage] = useState(true);
    const [displayLicenceErrorMessage, setDisplayLicenceErrorMessage] = useState(true);

    /* Fetch the initial skill sections, runs every time the modal is closed and opened */
    useEffect(() => {
        fetchLibrarySections();
    }, []);

    /* To display count of skills and categories, runs every time when selected skill is added or deleted*/
    useEffect(() => {
        const confirmedSections = getSelectedSkillSections(initialSkillSections, selectedSkillRowKeys);
        setConfirmedSkillSections(confirmedSections);
    }, [selectedSkillRowKeys]);

    /*#region
    The below state and functions are used by stepper component to navigate between pages
    NOTE:stepper component works without these but if we need to do some extra functionality than just navigating between pages we need to pass
    the state and overwrite the functions to handle them manually.
    */
    const [currentState, setCurrentState] = useState(0);

    // Navigates to next page
    const next = () => {
        if (!currentState) {
            // Transform the selected skills into a displayable object for review page
            const selectedSkills = getSelectedSkillSections(initialSkillSections, selectedSkillRowKeys);
            setSelectedSkillSections(selectedSkills);
            setConfirmedSkillSections(selectedSkills);
        }
        setCurrentState(prev => prev + 1);
    };

    // Navigates to previous page
    const prev = () => setCurrentState(prev => prev - 1);
    //#endregion

    /*#region
    The below region deals with showing banner when License threshold is exceeded */
    const totalActiveCount = initialSkillCount + selectedSkillRowKeys.length;
    const licenceThresholdExceeded = totalActiveCount > subscribedSkillCount;
    const showLicenceError = (subscribedSkillCount <= initialSkillCount && subscribedSkillCount > 0) ? true : false;
    const showLicenceWarning = ((subscribedSkillCount - initialSkillCount) <= skillThresholdValue &&
        subscribedSkillCount > 1 && !showLicenceError) ? true : false;
    const showErrorBanner = showLicenceError && displayLicenceErrorMessage;
    const showWarningBanner = showLicenceWarning && displayLicenceWarningMessage;
    // Add classes to define the height of table based on whether search component is enabled, with or without banner
    const expandableClassName = showErrorBanner || showWarningBanner ? (isImportLibrarySearchEnabled ? 'table-banner-ws' : 'table-banner') : '';

    const verifyToShowAboutLicenseLimit = () => {
        if (showErrorBanner) {
            return (<ProvideWarningElement type="error" closable={true}
                onClose={() => setDisplayLicenceErrorMessage(false)}
                message={<FormattedMessage id="skillsLicenseError" values={{ contactUs: <ContactUs /> }} />}
            />);
        } else if (showWarningBanner) {
            return (<ProvideWarningElement type="warning" closable={true}
                onClose={() => setDisplayLicenceWarningMessage(false)}
                message={<FormattedMessage id="skillsLicenseWarning" values={{ contactUs: <ContactUs /> }} />} />);

        } else return null;
    };
    //#endregion

    const expandableTableProps = {
        ...librarySections,
        fetchLibrarySkills,
        updateSelectedSkills,
        searchLibrarySkills,
        selectLibrarySkill,
        isImportLibrarySearchEnabled
    };

    /* -- Steps and its content to be displayed*/
    const steps = [
        {
            title: importLibraryStep1.title,
            content:
                <div className= {`importModal-body expandable-table ${expandableClassName}`}>
                    <p className="header-text">
                        {existingSkillsHeader}
                        <span style={{ marginLeft:'10px' , fontWeight:'bolder' }}>({<FormattedMessage id="selectionListSkillsCustomMsg" values={{ activeCount: totalActiveCount, licensedCount: subscribedSkillCount }} />})</span>
                    </p>
                    {subscribedSkillCount > 0 && verifyToShowAboutLicenseLimit(totalActiveCount, subscribedSkillCount, skillThresholdValue)}
                    <ExpandableImportRowTable
                        {...expandableTableProps}
                    />
                </div>,
            description: importLibraryStep1.description
        },
        {
            title: importLibraryStep2.title,
            content:
                <div className="importModal-body review-table">
                    <div className="count-text">{getDisplayText(selectedImportDataLabel, getSelectedCount(confirmedSkillSections))}</div>
                    <ReviewImportTable
                        skillSections={selectedSkillSections}
                        selectedSkillRowKeys={selectedSkillRowKeys}
                        updateSelectedSkills={updateSelectedSkills} />
                </div>,
            description: importLibraryStep2.description

        },
        {
            title: importLibraryStep3.title,
            content:
                <div className="importModal-body">
                    <div className="confirmation-text">
                        <div className="count-text">{getDisplayText(selectedImportDataLabel, getSelectedCount(confirmedSkillSections))}</div>
                        <div>{getDisplayText(importClickInfo)}</div>
                        <div>{getDisplayText(importProgressInfo)}</div>
                    </div>
                </div>,
            description: importLibraryStep3.description
        }
    ];

    /* -- Dispatch action to import skill and close the dialog*/
    const importSkillsAndClose = () => {
        importLibrarySkills();
        clearImportLibraryData();
    };

    return (
        <Stepper
            steps={steps}
            state={currentState}
            onNext={next}
            onPrev={prev}
            buttonLabels={{ completeLabel: importText }}
            onCancel={clearImportLibraryData}
            onComplete={importSkillsAndClose}
            disableProgress={!selectedSkillRowKeys.length || licenceThresholdExceeded}
        />
    );
};

/**
 * PropTypes for type checking and documentation
 */
ImportLibrarySkills.propTypes = {
    librarySections: PropTypes.object,
    handleCancel: PropTypes.func,
    messages: PropTypes.object,
    fetchLibrarySections: PropTypes.func,
    clearImportLibraryData:PropTypes.func,
    fetchLibrarySkills: PropTypes.func,
    updateSelectedSkills: PropTypes.func,
    importLibrarySkills: PropTypes.func,
    initialSkillCount: PropTypes.number,
    licenseProps:PropTypes.object,
    searchLibrarySkills:PropTypes.func,
    selectLibrarySkill: PropTypes.func,
    isImportLibrarySearchEnabled: PropTypes.bool
};

export default ImportLibrarySkills;