import React from 'react';
import { Table, Select, Spin, Empty } from 'antd';
import { DownOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { useEffect } from 'react';
import { FormattedMessage } from 'react-intl';

const { Option } = Select;

//#region Override Icons on subcategory and skill table level
const expandSubcategoryIcon = ({ expanded, onExpand, record, categoryId, fetchLibrarySkills }) => {
    const icon = <span onClick={(e) => {
        onExpand(record, e);
        e.stopPropagation();
        if (fetchLibrarySkills && !record.fetched) {
            fetchLibrarySkills(categoryId, record.id);
        }
    }}
    >
        {expanded ? <UpOutlined/> : <DownOutlined/>}
    </span>;

    return icon;
};

const expandCategoryIcon = ({ expanded, onExpand, record }) => {
    const icon = <span onClick={(e) => {
        onExpand(record, e);
        e.stopPropagation();
    }}
    >
        {expanded ? <UpOutlined/> : <DownOutlined/>}
    </span>;

    return icon;
};
//#endregion

/** Get the count of skills at category and subcategory level
* @returns {Object} contains category and subcategory id with skill count
{
  "categoryId": {
    "total": 3,
    "subCategories": {
      "subcategoryId": 2,
      "subcategoryId": 1
    }
  }
};
*/
const getTotalSelectedSkillCount = (data, selectedIds) => {
    const selectedSet = new Set(selectedIds);
    const counts = {};

    data.forEach(category => {
        counts[category.id] = { total: 0, subcategories: {} };

        category.subCategories.forEach(subCategory => {
            counts[category.id].subcategories[subCategory.id] = 0;

            subCategory.skills.forEach(skill => {
                if (selectedSet.has(skill.id)) {
                    counts[category.id].total++;
                    counts[category.id].subcategories[subCategory.id]++;
                }
            });
        });
    });

    return counts;
};

/**
 * ExpandableImportRowTable is an expandable table with 3-level tables one inside another.
 *
 * @component
 * @param {import('../types/standardSkillTaxonomy.jsdoc').StandardSkillTaxonomy} [initialSkillSections] - Initial skill Sections that is a tree structure, containing categories, subcategories and skills
 * @param {Function} [fetchLibrarySkills] - Callback function to fetch skills on demand
 * @param {boolean} [loading] - Loading state for category level table (Top level table)
 * @param {boolean} [childLoading] - Loading state for skill level table (bottom level table)
 * @param {Array} [selectedSkillRowKeys] - Contains skill ids of all the selected skills
 * @param {Function} [updateSelectedSkills] - Callback function to update selectedSkillRowKeys
 * @param {Function} [searchLibrarySkills] - Search skills in library
 * @param {Function} [selectLibrarySkill] - Select library skill from search result
 * @param {boolean} [isImportLibrarySearchEnabled] - feature flag for skill search
 */
const ExpandableImportRowTable = ({
    initialSkillSections,
    fetchLibrarySkills,
    loading,
    childLoading,
    selectedSkillRowKeys,
    updateSelectedSkills,
    searchLibrarySkills,
    selectLibrarySkill,
    skillSearchResult,
    isImportLibrarySearchEnabled
}) => {
    /* This is a local state to Store subcategory selects */
    const [selectedSubCategoryRowKeys, setSelectedSubCategoryRowKeys] = useState([]);
    const [selectedCategoryRowKeys, setSelectedCategoryRowKeys] = useState([]);
    const [totalSelectedSkillCount, setTotalSelectedSkillCount] = useState([]);
    const { skills:searchOptions = [], loading: loadingSearch = false } = skillSearchResult || {};
    /* Use effect will update the selectedRowKeys for subcategory table and category table*/
    useEffect(() =>{
        if (initialSkillSections) {
            const categories = initialSkillSections.filter(x =>
                x.subCategories.some(x => x.skills.length
                    && x.skills.some(x => selectedSkillRowKeys.includes(x.id))));
            setSelectedCategoryRowKeys(categories.map(x => x.id)); // sets category ids

            const subCategories = initialSkillSections.flatMap(x => x.subCategories)
                .filter(x => x.skills.length
                    && x.skills.every(x => selectedSkillRowKeys.includes(x.id)));
            setSelectedSubCategoryRowKeys(subCategories.map(x => x.id)); // sets subcategory ids

            const count = getTotalSelectedSkillCount(initialSkillSections,selectedSkillRowKeys);
            setTotalSelectedSkillCount(count); // sets total counts
        }
    },[initialSkillSections, selectedSkillRowKeys, childLoading]); // childLoading is required to recalculate checkbox state on fetching new skills for subcategory table

    /*SubCategory Level Table and its functions*/
    const getSubCategoryTable = (record) => {
        const categoryId = record.id;
        const subCategories = record.subCategories || [];
        const rowSelection = {
            selectedRowKeys:selectedSubCategoryRowKeys,
            onSelect: (record, selected) => {
                const currentSkillIds = record.skills.map(x => x.id);
                const fetched = record.fetched;
                if (selected) {
                    if (fetchLibrarySkills && !fetched) {
                        /* If the checkbox is clicked without expanding then fetch all skills and mark as selected*/
                        fetchLibrarySkills(categoryId, record.id, true);

                        return;
                    }
                    const selectedRows = [...new Set([...selectedSkillRowKeys, ...currentSkillIds])];
                    updateSelectedSkills(selectedRows); // Add selected skills of subcategory
                } else {
                    const selectedRows = selectedSkillRowKeys.filter(x => !currentSkillIds.includes(x));
                    updateSelectedSkills(selectedRows); // Remove selected skills of subcategory
                }
            },
            getCheckboxProps: (record) => ({
                /* determines indeterminate state for subCategory table checkbox*/
                indeterminate: record.skills.some(s => selectedSkillRowKeys.includes(s.id))
                && !record.skills.every(s => selectedSkillRowKeys.includes(s.id))
            })
        };

        /* Render row style at subcategory level*/
        const subCategoryRowClassName = (record) => {
            if (selectedSubCategoryRowKeys.includes(record.id) || record.skills.some(x => selectedSkillRowKeys.includes(x.id))) {
                return 'active-row';
            }

            return '';
        };

        return (
            <div className="subcategory-table-container">
                <Table
                    columns={columns}
                    dataSource={subCategories}
                    rowKey="id"
                    showHeader={false} // No headers for SubCategory Level Table
                    pagination={false}
                    rowSelection={Object.assign(Object.assign({}, rowSelection))}
                    expandable={{
                        defaultExpandAllRows: false,
                        expandIcon: item => expandSubcategoryIcon({ ...item, categoryId, fetchLibrarySkills }),
                        expandedRowRender: getSkillTable
                    }}
                    rowClassName={subCategoryRowClassName}
                />
            </div>
        );
    };

    /*Skill Level Table and its functions*/
    const getSkillTable = (record) => {
        const skills = record.skills || [];

        const skillColumns = [
            { title: <FormattedMessage id="skillNameHeader"/>, dataIndex: 'name', key: 'name', width: '35%' , ellipsis: true },
            { title: <FormattedMessage id="descriptionText"/>, dataIndex: 'description', key: 'description', width: '60%' }
        ];

        const rowSelection = {
            selectedRowKeys:selectedSkillRowKeys,
            onSelect: (record, selected) => {
                if (selected) {
                    const selectedRows = [...new Set([...selectedSkillRowKeys, record.id])];
                    updateSelectedSkills(selectedRows); // Add selected skills
                } else {
                    const selectedRows = selectedSkillRowKeys.filter(x => x !== record.id);
                    updateSelectedSkills(selectedRows); // Remove selected skills
                }
            },
            hideSelectAll: true
        };

        /* Render row style at skill level*/
        const skillRowClassName = (record) => {
            if (selectedSkillRowKeys.includes(record.id)) {
                return 'active-row';
            }

            return '';
        };

        return (
            <div className="skill-table-container">
                <Table
                    columns={skillColumns}
                    dataSource={skills}
                    loading={childLoading}
                    rowKey="id"
                    pagination={true}
                    rowSelection={Object.assign(Object.assign({}, rowSelection))}
                    rowClassName={skillRowClassName}
                />
            </div>
        );
    };

    /*Category Level Table and its functions*/

    /* The renderRowText is responsible for displaying the count of selected skill for each count for both category and subcategory level*/
    const renderRowText = (text, record) => {
        let length = 0;
        // eslint-disable-next-line no-prototype-builtins
        if (record.hasOwnProperty('subCategories')) {
            // This sets count for selected skills in category row level
            length = totalSelectedSkillCount[record.id]?.total ?? 0;
        } else {
            // This sets count for selected skills in subcategory row level
            const category = Object.values(totalSelectedSkillCount).find(p =>
                p.subcategories && record.id in p.subcategories);

            length = category ? category.subcategories[record.id] : 0;
        }

        return (
            <span>
                {text}
                {length > 0 && (
                    <span style={{ marginLeft: 4, color: '#888' }}>
              ({length})
                    </span>
                )}
            </span>
        );
    };

    const columns = [
        {
            title: <FormattedMessage id="skillCategoryLabel"/>,
            dataIndex: 'name',
            key: 'name',
            render: renderRowText
        }
    ];

    /* Render row style at category level*/
    const categoryRowClassName = (record) => {
        if (selectedCategoryRowKeys.includes(record.id)) {
            return 'active-row';
        }

        return '';
    };

    /**
     * Search for a skill from database
     * @param {String} searchTerm skill to search
     */
    const handleSearch = (searchTerm) => {
        if (searchTerm.length < 3) return;
        searchLibrarySkills(searchTerm);
    };

    /**
     * Auto selects the searched skill
     *
     * @param {Number} skillId SkillId to be selected
     */
    const handleSelect = (skillId) => {
        const selectedObj = searchOptions.find((item) => item.id === parseInt(skillId));
        if (!selectedObj) return;

        selectLibrarySkill(selectedObj);
    };

    /**
     * Get Skill Name, category name and subcategory name
     * @param {Number} id skill id
     * @param {String} name skill name
     * @param {Number} categoryId category id
     * @param {Number} subcategoryId subcategory id
     */
    const getSkillOptionDetails = ({ id, name, categoryId, subcategoryId }) =>{
        const categoryDetails = initialSkillSections.find(x => x.id === categoryId);
        const subCategoryDetails = categoryDetails.subCategories.find(x => x.id === subcategoryId);

        return { skillId: id, skillName: name, categoryName:categoryDetails.name, subcategoryName:subCategoryDetails.name };
    };

    return (<>
        {isImportLibrarySearchEnabled && <Select
            className="import-library-search"
            showSearch
            allowClear
            placeholder={<FormattedMessage id="searchSkillPlaceholder"/>}
            onSearch={handleSearch}
            onSelect={handleSelect}
            filterOption={false}
            suffixIcon={<SearchOutlined/>}
            optionLabelProp="data-label"
            notFoundContent={loadingSearch ? <Spin /> : (searchOptions.length === 0 ? <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} /> : null)}
            disabled={loading} // disable search when table is loading
        >
            {searchOptions.map((item, index) => {
                const skillDetails = getSkillOptionDetails(item);

                return (
                    <Option key={skillDetails.skillId} value={skillDetails.skillId} data-label={skillDetails.skillName}>
                        {index !== 0 && <div className="import-skill-search-divider"/>}
                        <div className="import-skill-search-option">
                            <div className="skillName">{skillDetails.skillName}</div>
                            <div className="skill-detail">Category: {skillDetails.categoryName}</div>
                            <div className="skill-detail">Subcategory: {skillDetails.subcategoryName}</div>
                        </div>
                    </Option>
                );
            })}
        </Select>}

        <Table
            columns={columns}
            pagination={false}
            scroll={{ y: isImportLibrarySearchEnabled ? '56vh' : '61vh' }}
            rowKey="id"
            loading={loading}
            expandable={{
                defaultExpandAllRows: false,
                expandIcon:expandCategoryIcon,
                expandedRowRender: getSubCategoryTable
            }}
            dataSource={initialSkillSections}
            rowClassName={categoryRowClassName}
        />
    </>);
};

/**
 * PropTypes for type checking and documentation
 */
ExpandableImportRowTable.propTypes = {
    initialSkillSections: PropTypes.object,
    fetchLibrarySkills: PropTypes.func,
    loading: PropTypes.bool,
    childLoading: PropTypes.bool,
    selectedSkillRowKeys:PropTypes.array,
    updateSelectedSkills: PropTypes.func,
    searchLibrarySkills :PropTypes.func,
    selectLibrarySkill:PropTypes.func,
    skillSearchResult: PropTypes.object,
    isImportLibrarySearchEnabled: PropTypes.bool
};

export default ExpandableImportRowTable;

