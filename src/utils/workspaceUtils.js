import { getBarGroupsGuidByTable, getCellGroupsGuidByTable, getDefaultWorkspaceStructure, getWorkspaceSettings, getWorkspaceStructure, workspaceSettingsChanged } from '../selectors/workspaceSelectors';
import { getCurrentPlannerFilters } from '../selectors/plannerPageSelectors';
import { DATE_UNITS, PLANNER_ROWS_DENSITY_KEYS, SORT_ASCENDING, SORT_DESCENDING, TABLE_NAMES, WORKSPACE_ACCESS_TYPES } from '../constants';
import { cloneDeep, findIndex } from 'lodash';
import { omit } from './commonUtils';
import { differenceInCalendarDays, endOfDay, getCurrentDate, getLocalTodayDate, startOfDay } from './dateUtils';
import { getDateToggleOptionByValue, getViewModeFromDates } from './dateToggleOptionsUtils';
import * as CGUtils from '../../src/utils/calendarGridUtils';
import { addGrouppedTableDataModel } from '../actions/workspaceActions';
import { AVATAR_FIELDS, PLANNER_DATE_TOGLE_OPTIONS, PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP, PLANNER_PAGE_TABLE_TO_GROUP_ALIAS, PLANNER_VIEW_MODE_OPTIONS, RECORDSLIST_FIELDS_PER_COLUMN_TYPE, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS } from '../constants/plannerConsts';
import { ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { COMMAND_BAR_PROP_KEY_TOGGLE_VALUE } from '../constants/commandBarConsts';
import { isAdvancedFilterApplied } from './filtersUtils';

export const getFilterSettingsToSave = (workspaceSettings, plannerPage) => {
    const filterState = getCurrentPlannerFilters(workspaceSettings, plannerPage);

    const views = filterState?.views || {};

    const appliedFiltersViewsState = Object.keys(views).reduce((appliedViews, viewName) => {
        const currentView = views[viewName];

        const { filterSectionsData = {}, filterGroups = [] } = currentView;

        const appliedFilterSectionData = filterGroups.reduce((acc, group) => {
            const currentSection = filterSectionsData[group] || {};
            const { filterFields = {}, filterLinesOrder = [] } = currentSection;
            const appliedFilterFields = cloneDeep(filterFields);
            const appliedFilterLinesOrder = [];

            filterLinesOrder.forEach((filterKey) => {
                const isApplied = isAdvancedFilterApplied(filterFields[filterKey]) ;

                if (isApplied) {
                    appliedFilterLinesOrder.push(filterKey);
                } else {
                    delete appliedFilterFields[filterKey];
                }
            });

            acc = {
                ...acc,
                [group]: {
                    ...currentSection,
                    filterLinesOrder: appliedFilterLinesOrder,
                    filterFields: appliedFilterFields
                }
            };

            return acc;
        }, {});

        appliedViews = {
            ...appliedViews,
            [viewName]: {
                ...currentView,
                filterSectionsData: appliedFilterSectionData
            }
        };

        return appliedViews;
    }, {});

    return {
        ...filterState,
        views: appliedFiltersViewsState
    };
};

export const getSaveWorkspaceSettings = (plannerPage, workspaceGuid, copySelection = true) => {
    const workspaceSettings = getWorkspaceSettings(plannerPage.workspaces, workspaceGuid);

    return {
        ...workspaceSettings,
        filterState: getFilterSettingsToSave(workspaceSettings, plannerPage, copySelection)
    };
};

export const shouldSaveIfAnyChanges = (workspaces, workspaceGuid) => {
    const workspaceStructure = getWorkspaceStructure(workspaces, workspaceGuid);
    const defaultWorkspaceStructure = getDefaultWorkspaceStructure(workspaces);

    return workspaceGuid !== defaultWorkspaceStructure.workspace_guid &&
        workspaceStructure.workspace_accesstype === WORKSPACE_ACCESS_TYPES.PUBLIC &&
        workspaceSettingsChanged(workspaces, workspaceGuid);
};

export const parseWorkspaceSettings = (data, viewModeKey = 'viewMode', viewModeOptions = PLANNER_VIEW_MODE_OPTIONS, dateToggleOptions = PLANNER_DATE_TOGLE_OPTIONS) => {
    let startDate = startOfDay(getLocalTodayDate());
    let endDate = endOfDay(getCurrentDate());
    let result = { ...data };

    if (data.hasOwnProperty('endDate') && data.hasOwnProperty(viewModeKey)) {
        let offset, unit;
        const { startDate: wsStartDate, endDate: wsEndDate } = data;
        const wsViewMode = data[viewModeKey];
        const viewMode = getViewModeFromDates(wsStartDate, wsEndDate, viewModeOptions);

        if (wsViewMode.dateOption === 'custom') {
            offset = differenceInCalendarDays(wsEndDate, wsStartDate);
            startDate = CGUtils.getMinorStartDate(startDate, viewMode);
            endDate = CGUtils.addUnits(startDate, offset, DATE_UNITS.DATE_UNIT_DAY);
        } else {
            const dateToggleOptionByValue = getDateToggleOptionByValue(wsViewMode.dateOption, dateToggleOptions);
            offset = dateToggleOptionByValue.offset;
            unit = dateToggleOptionByValue.unit;
            startDate = CGUtils.getMinorStartDate(startDate, viewMode);
            endDate = CGUtils.addUnits(startDate, offset, unit);
        }

        result = {
            ...result,
            startDate,
            endDate
        };
    }

    return result;
};

const getAmendedTableViewsConfig = (viewsConfig, tableName) => {
    const bookingDisplayFieldsKey = 'bookingDisplayFields';
    const bookingBarsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[TABLE_NAMES.BOOKING];
    const roleBarsKey = PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[TABLE_NAMES.ROLEREQUEST];

    const barDisplayFields = {
        [bookingBarsKey]: viewsConfig[tableName][bookingDisplayFieldsKey],
        [roleBarsKey]: [ROLEREQUEST_FIELDS.STATUS_GUID]
    };

    return { ...omit({ ...viewsConfig[tableName], barDisplayFields }, [bookingDisplayFieldsKey]) };
};

const DRAFT_REQUESTED_TOGGLES = [COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_DRAFT_ROLES, COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_REQUESTED_ROLES];
const ROLE_ROLECRITERIA_TOGGLES = [ 
    COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_NAME, 
    COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_CRITERIA
];

export const rolesSubTogglesKeys = [...DRAFT_REQUESTED_TOGGLES, ...ROLE_ROLECRITERIA_TOGGLES];

export function areAllSubTogglesHidden(newViewConfigState) {
    return DRAFT_REQUESTED_TOGGLES.every((subToggleKey) => newViewConfigState[subToggleKey] === true)
        || ROLE_ROLECRITERIA_TOGGLES.every((subToggleKey) => newViewConfigState[subToggleKey] === true);
}

export function shouldHideUnassignedRolesToggle (newViewConfigState) {
    return [
        COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_NAME,
        COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_CRITERIA
    ].every((subToggleKey) => newViewConfigState[subToggleKey] === true);
}

export const buildWorkspaceSettings = (workspace) => {
    const { workspace_settings, workspace_guid } = workspace;
    const parsedSettings = parseWorkspaceSettings(JSON.parse(workspace_settings));
    const { masterRecTableName, subRecTableName, viewsConfig } = parsedSettings;

    let wsSettings = {
        ...parsedSettings,
        workspace_guid
    };

    let amends = {};

    if (!wsSettings.barGroupsGuids) {
        amends = {
            ...amends,
            barGroupsGuids: {
                bookingGroupsGuid: `bookingGroups_${workspace_guid}`,
                roleGroupsGuid: `rolesGroups_${workspace_guid}`
            }
        };
    }

    if (!wsSettings.barTableNamesGroupKeys) {
        amends = {
            ...amends,
            barTableNamesGroupKeys: {
                [TABLE_NAMES.BOOKING]: 'bookingGroupsGuid',
                [TABLE_NAMES.ROLEREQUEST]: 'roleGroupsGuid'
            },
            //This will be needed when shared workspaces is implemented
            cellTableNamesGroupKeys: {
                [TABLE_NAMES.BOOKINGBYWEEKVIEW]: 'bookingByWeekViewGroupGuid'
            }
        };
    }

    if (!wsSettings.hideRoleRecords) {
        amends = {
            ...amends,
            hideRoleRecords: true
        };
    }

    if (!viewsConfig[masterRecTableName].barDisplayFields) {
        amends = {
            ...amends,
            viewsConfig: {
                ...viewsConfig,
                [masterRecTableName]: getAmendedTableViewsConfig(viewsConfig, masterRecTableName)
            }
        };
    }

    if (!viewsConfig[subRecTableName].barDisplayFields) {
        amends = {
            ...amends,
            viewsConfig: {
                ...amends.viewsConfig,
                [subRecTableName]: getAmendedTableViewsConfig(viewsConfig, subRecTableName)
            }
        };
    }

    if (!wsSettings[COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_UNASSIGNED_ROLES] 
        && !wsSettings[COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_NAME] 
        && !wsSettings[COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_CRITERIA]) {
        const assignToggleButtonValue = (acc, key, tableName) => {
            if (typeof viewsConfig[tableName][key] === 'undefined') {
                acc[key] = viewsConfig[tableName].hideRolesRecords;
            }

            return acc;
        };

        amends = {
            ...amends,
            viewsConfig: {
                ...viewsConfig,
                ...amends.viewsConfig,
                [masterRecTableName]: {
                    ...viewsConfig[masterRecTableName],
                    ...(amends.viewsConfig || {})[masterRecTableName],
                    ...rolesSubTogglesKeys.reduce((acc, key) => {
                        return assignToggleButtonValue(acc, key, masterRecTableName);
                    }, {})

                },
                [subRecTableName]: {
                    ...viewsConfig[subRecTableName],
                    ...(amends.viewsConfig || {})[subRecTableName],
                    ...rolesSubTogglesKeys.reduce((acc, key) => {
                        return assignToggleButtonValue(acc, key, subRecTableName);
                    }, {})
                }
            }
        };

        amends = {
            ...amends,
            [COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_UNASSIGNED_ROLES]: true,
            [COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_NAME]: true,
            [COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_CRITERIA]: true
        };
    }

    return omit({ ...wsSettings, ...amends }, ['bookingGroupsGuid', 'bookingsTableName']);
};

export const getWorkspaceModificationMapFunction = (workspace) => {
    let amendedWorkspace = { ...workspace };
    const { workspace_settings } = workspace;
    if (workspace_settings) {
        amendedWorkspace = {
            ...amendedWorkspace,
            workspace_settings: buildWorkspaceSettings(workspace)
        };
    }

    return amendedWorkspace;
};

export const mostRecentlyUsedWorkspaceModificationMapFunction = (workspace) => {
    return buildWorkspaceSettings(workspace);
};

const createAddGrouppedTableDataModelActions = (getGroupsGuidByTable = getBarGroupsGuidByTable, tableToGroupAliasMap = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS) => (workspaceSettings) => {
    let result = [];

    Object.keys(tableToGroupAliasMap).forEach((tableName) => {
        const groupGuid = getGroupsGuidByTable(workspaceSettings, tableName);
        result.push(addGrouppedTableDataModel(tableToGroupAliasMap[tableName], groupGuid, [tableName]));
    });

    return result;
};

export const getAddGrouppedTableDataModelActions = createAddGrouppedTableDataModelActions();

export const getAddGrouppedTableDataModelActionsTV = createAddGrouppedTableDataModelActions(getCellGroupsGuidByTable, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS);

export const getUpdatedCustomColourFields = (workspace, customColourTheme) => {
    const { workspace_colourtheme_guid } = customColourTheme;
    const { workspace_custom_colour_field = [] } = workspace;
    customColourTheme.workspace_custom_colour_field = [...workspace_custom_colour_field];

    if (!workspace_colourtheme_guid) {
        const { workspace_colour_entity_name = '', workspace_colour_field_name = '' } = customColourTheme;
        const workspace_colour_table_name = workspace_colour_field_name.split('_')[0];
        const workspaceCustomColourFieldObj = { workspace_colour_entity_name, workspace_colour_field_name, workspace_colour_table_name };
        const entityIndex = findIndex(customColourTheme.workspace_custom_colour_field || [], { workspace_colour_entity_name });

        if (entityIndex > -1) {
            customColourTheme.workspace_custom_colour_field.splice(entityIndex, 1, workspaceCustomColourFieldObj);
        } else {
            customColourTheme.workspace_custom_colour_field.push(workspaceCustomColourFieldObj);
        }
    } else {
        customColourTheme.workspace_custom_colour_field = [];
    }

    return customColourTheme;
};

export const applyViewsConfigSort = (viewsConfig, sort, enableDefaultOrder) => {
    const { table, field } = sort;

    const order = viewsConfig[table].selection.order;
    const currentFieldName = order.orderFields[0].field;
    const currentFieldOrder = order.orderFields[0].order;
    const defaultSortOrder = viewsConfig[table].defaultSortOrder;
    let newSort = {};

    if (enableDefaultOrder && defaultSortOrder && currentFieldName == field && currentFieldOrder == SORT_DESCENDING) {
        newSort = defaultSortOrder;
    } else {
        newSort = {
            field,
            order:
                order.orderFields[0].field === field
                    ? order.orderFields[0].order === SORT_ASCENDING
                        ? SORT_DESCENDING
                        : SORT_ASCENDING
                    : SORT_ASCENDING
        };
    }

    return {
        ...viewsConfig,
        [table]: {
            ...viewsConfig[table],
            selection: {
                ...viewsConfig[table].selection,
                order: {
                    ...viewsConfig[table].selection.order,
                    orderFields: [newSort]
                }
            }
        }
    };
};

export const getDensityFromFieldsCount = (densityConfig, count) => {
    const { DEFAULT, MEDIUM, EXPANDED } = PLANNER_ROWS_DENSITY_KEYS;
    const defaultDensityFieldsCount = densityConfig[DEFAULT].maxFieldsCount;
    const mediumDensityFieldsCount = densityConfig[MEDIUM].maxFieldsCount;

    let density = DEFAULT;
    if (count > defaultDensityFieldsCount && count <= mediumDensityFieldsCount) {
        density = MEDIUM;
    } else if (count > mediumDensityFieldsCount) {
        density = EXPANDED;
    }

    return density;
};

const isAvatarField = (field) => 0 <= AVATAR_FIELDS.indexOf(field);

const getRecordListFieldColumnType = (field) => {
    const fieldsPerTypeMap = RECORDSLIST_FIELDS_PER_COLUMN_TYPE || {};

    return Object.keys(fieldsPerTypeMap).find(type => (fieldsPerTypeMap[type] || []).includes(field));
};

export const getRecordListDetailFields = (tableName, fields) => {
    return fields.map(field => {
        const columnType = getRecordListFieldColumnType(field);

        return {
            table: tableName,
            field,
            title: field,
            showAvatar: isAvatarField(field),
            ...(columnType && { columnType: columnType })
        };
    });
};

export const getMaxDisplayedFieldsCount = (viewConfig, barTableName) => {
    const { barDisplayFields = {} } = viewConfig;

    const displayedFields = Object.keys(barDisplayFields).reduce((accumulator, key) => {
        if (PLANNER_PAGE_TABLE_TO_BAR_DISPLAY_FIELDS_MAP[barTableName] !== key) {
            accumulator.push(barDisplayFields[key].length);
        }

        return accumulator;
    }, []);

    return Math.max(...displayedFields);
};