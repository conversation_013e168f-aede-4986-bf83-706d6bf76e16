export async function loadIntlPolyfills(locale) {
    const polyfills = [];

    if (typeof Intl.Locale === 'undefined') {
        polyfills.push(import('@formatjs/intl-locale/polyfill'));
    }

    if (!Intl.PluralRules) {
        polyfills.push(import('@formatjs/intl-pluralrules/polyfill'));

        switch (locale) {
            case 'de':
                polyfills.push(import('@formatjs/intl-pluralrules/locale-data/de'));
                break;
            case 'es':
                polyfills.push(import('@formatjs/intl-pluralrules/locale-data/es'));
                break;
            case 'fr':
                polyfills.push(import('@formatjs/intl-pluralrules/locale-data/fr'));
                break;
            case 'nl':
                polyfills.push(import('@formatjs/intl-pluralrules/locale-data/nl'));
                break;
            case 'en':
            default:
                polyfills.push(import('@formatjs/intl-pluralrules/locale-data/en'));
                break;
        }
    }

    if (!Intl.RelativeTimeFormat) {
        polyfills.push(import('@formatjs/intl-relativetimeformat/polyfill'));

        switch (locale) {
            case 'de':
                polyfills.push(import('@formatjs/intl-relativetimeformat/locale-data/de'));
                break;
            case 'es':
                polyfills.push(import('@formatjs/intl-relativetimeformat/locale-data/es'));
                break;
            case 'fr':
                polyfills.push(import('@formatjs/intl-relativetimeformat/locale-data/fr'));
                break;
            case 'nl':
                polyfills.push(import('@formatjs/intl-relativetimeformat/locale-data/nl'));
                break;
            case 'en':
            default:
                polyfills.push(import('@formatjs/intl-relativetimeformat/locale-data/en'));
                break;
        }
    }

    if (!Intl.NumberFormat || !Intl.NumberFormat.supportedLocalesOf(locale).length) {
        polyfills.push(import('@formatjs/intl-numberformat/polyfill'));

        switch (locale) {
            case 'de':
                polyfills.push(import('@formatjs/intl-numberformat/locale-data/de'));
                break;
            case 'es':
                polyfills.push(import('@formatjs/intl-numberformat/locale-data/es'));
                break;
            case 'fr':
                polyfills.push(import('@formatjs/intl-numberformat/locale-data/fr'));
                break;
            case 'nl':
                polyfills.push(import('@formatjs/intl-numberformat/locale-data/nl'));
                break;
            case 'en':
            default:
                polyfills.push(import('@formatjs/intl-numberformat/locale-data/en'));
                break;
        }
    }

    if (!Intl.DateTimeFormat || !Intl.DateTimeFormat.supportedLocalesOf(locale).length) {
        polyfills.push(import('@formatjs/intl-datetimeformat/polyfill'));

        switch (locale) {
            case 'de':
                polyfills.push(import('@formatjs/intl-datetimeformat/locale-data/de'));
                break;
            case 'es':
                polyfills.push(import('@formatjs/intl-datetimeformat/locale-data/es'));
                break;
            case 'fr':
                polyfills.push(import('@formatjs/intl-datetimeformat/locale-data/fr'));
                break;
            case 'nl':
                polyfills.push(import('@formatjs/intl-datetimeformat/locale-data/nl'));
                break;
            case 'en':
            default:
                polyfills.push(import('@formatjs/intl-datetimeformat/locale-data/en'));
                break;
        }
    }

    await Promise.all(polyfills);
}