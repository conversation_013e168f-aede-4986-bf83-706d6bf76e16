import { ENTITY_WINDOW_MODULES, JOBS_PAGE_ALIAS } from '../constants';
import { entityWindowOpen, entityWindowOpenForMultiple } from '../actions/entityWindowActions';
import { ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { TABLE_NAMES } from '../constants/globalConsts';
import { promptAction } from '../actions/promptActions';
import { getSelectedEntitiesSelector } from '../selectors/commonSelectors';
import { getAccessibleEntitiesIdsSelector } from '../selectors/userEntityAccessSelectors';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { EDIT_FNAS_PER_TABLENAME } from '../constants/tablesConsts';
import { deleteTableData } from '../actions/tableDataActions';
import { JOB_PAGE_PAGED_DATA } from '../constants/jobsPageConsts';
import { getData } from './commonUtils';
import { getJobsPageCollections } from '../selectors/dataGridPageSelectors';
import { jobDuplicateOpenDialog } from '../actions/jobDuplicateActions';

const createJob = (state, dispatch) => {
    const entity = {};

    const dispatchAction = entityWindowOpen(
        ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
        TABLE_NAMES.JOB,
        TABLE_NAMES.JOB,
        ENTITY_WINDOW_OPERATIONS.CREATE,
        entity,
        undefined,
        false
    );

    dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
};

const editJob = (state, dispatch) => {
    const selectedJobPane = state.entityWindow.window.jobsPageDetailsPane;
    const { ids } = getSelectedEntitiesSelector(state, JOBS_PAGE_ALIAS);
    const { entityId } = selectedJobPane;

    const editedIds = entityId ? [entityId] : ids;
    const accessibleIds = getAccessibleEntitiesIdsSelector(state)(TABLE_NAMES.JOB, editedIds, ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.JOB]);

    if (accessibleIds.length === 0) {
        return;
    }

    let dispatchAction;

    if (accessibleIds.length === 1) {
        dispatchAction = entityWindowOpen(
            ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
            TABLE_NAMES.JOB,
            TABLE_NAMES.JOB,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            {},
            accessibleIds[0],
            true
        );
    } else {
        dispatchAction = entityWindowOpenForMultiple(
            ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL,
            TABLE_NAMES.JOB,
            TABLE_NAMES.JOB,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            [],
            accessibleIds
        );
    }

    dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
};

const deleteJob = (state, dispatch) => {
    const selectedJobPane = state.entityWindow.window.jobsPageDetailsPane;
    const { entityId } = selectedJobPane;
    const { ids = [] } = getSelectedEntitiesSelector(state, JOBS_PAGE_ALIAS);
    const deleteIds = entityId ? [entityId] : ids;

    if (deleteIds.length !== 1) {
        return;
    }

    const jobEntity = getData(getJobsPageCollections(state), TABLE_NAMES.JOB, deleteIds[0]);
    const dispatchAction = deleteTableData(JOB_PAGE_PAGED_DATA, TABLE_NAMES.JOB, [TABLE_NAMES.JOB], TABLE_NAMES.JOB, deleteIds[0], jobEntity);

    dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
};

const duplicateJob = (state, dispatch) => {
    const selectedEntities = getSelectedEntitiesSelector(state, JOBS_PAGE_ALIAS);

    const {
        ids = []
    } = selectedEntities;

    if (ids.length !== 1) {
        return;
    }

    const jobEntity = getData(getJobsPageCollections(state), TABLE_NAMES.JOB, ids[0]);

    dispatch(jobDuplicateOpenDialog(TABLE_NAMES.JOB, ids[0], jobEntity));
};

export {
    createJob,
    editJob,
    deleteJob,
    duplicateJob
};