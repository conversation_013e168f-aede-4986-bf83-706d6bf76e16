import { FIELD_DATA_CATEGORIES, FIELD_DATA_TYPES, UNASSIGNED_BOOKINGS_RESOURCE, TABLE_NAMES } from '../constants';
import { PREFIX_FIELDS_TYPES, ALLOW_CUSTOM_FIELDS, SU<PERSON>IX_FIELDS_TYPES, HIDDEN_FIELD_PLACEHOLDER_ALIAS } from '../constants/globalConsts';
import { COLOUR_FIELD, UNASSIGNED_RESOURCE_OPTION } from '../constants/plannerConsts';
import {
    FIELD_DATA_UIFIELD_CATEGORIES,
    CUSTOM_FIELD_TYPES,
    APP_FIELDS_FIELDINFOS,
    BOOKING_PROFITPERHOUR_FIELDNAME,
    FIELD_DATA_ACCESS_LEVEL,
    ROLEREQUEST_PROFITPERHOUR_FIELDNAME,
    FIELD_TYPES,
    R<PERSON><PERSON>EQUESTRESOURCE_FIELDS,
    R<PERSON><PERSON>EQUESTRESOURCE_PROFITPERHOUR_FIELDNAME,
    PROFIT_PER_HOUR_FIELDS,
    R<PERSON><PERSON><PERSON>QUEST_FIELDS,
    BOOKING_TOTAL_COST,
    BOOKING_TOTAL_REVENUE, BOOKING_TOTAL_PROFIT, BOOKING_COST_PER_HOUR, BOOKING_REVENUE_PER_HOUR, ROLEREQUESTGROUP_FIELDS, JOB_BUDGET_CONSUMED, JOB_OPPORTUNITY_PERCENT, JOB_BOOKINGS_CALC_FIELDS, JOB_BUDGET, JOB_PROFITMARGIN, JOB_FIXEDPRICE, RESOURCE_LAST_LOGIN, ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME, ESTIMATE_PROFIT_PER_HOUR_FIELDS, JOB_TOTAL_RAGHEALTH_GUID, ROLEREQUEST_ESTIMATE_PROFIT_RATES_FIELDS_ROW, ROLEREQUEST_ESTIMATE_COST_RATES_FIELDS_ROW, ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD, ROLEREQUEST_ESTIMATE_REVENUE_RATES_FIELDS_ROW,
    JOB_PROFIT_MARGIN_TARGET } from '../constants/fieldConsts';
import { isEmptyObject } from './commonUtils';
import { isEmpty } from 'lodash';
import {
    ROLEREQUEST_CHARGE_RATES_VIEW_MODE_FIELDS_ROW,
    BOOKING_DIARY_HOURS,
    BOOKING_CHARGE_RATES_VIEW_MODE_FIELDS_ROW,
    BOOKING_DIARY_DAYS,
    BOOKING_DATE_RANGE
} from '../constants/fieldConsts';
import { COMPLEX_GROUPED_FIELDS } from '../constants/plannerConsts';
import { ENTITY_WINDOW_CUSTOM_CONTROL_TYPES } from '../constants/entityWindowConsts';
import { timeAllocationSectionFieldByTableName, dateRangeSectionFieldByTableName, bookingNonWorkSectionField, jobProfitMarginField, jobTotalRevenueField, jobTotalCostField } from '../state/entityWindow/fieldsConfig';
import { isFieldAccessHidden } from './fieldControlUtils';
import { CUSTOM_FIELD_DATA_TYPES } from '../constants/auditTrailConsts';
import { ROLE_ENTITY_TYPES } from '../constants/rolesConsts';
import { getIsCriteriaRole } from './roleRequestsUtils';
import { SYSTEM_READONLY_DATE_FIELDS } from '../constants/tablesConsts';

const BAR_ENTITY_FIELDS = [
    ...COMPLEX_GROUPED_FIELDS[TABLE_NAMES.BOOKING],
    ...COMPLEX_GROUPED_FIELDS[TABLE_NAMES.ROLEREQUEST],
    ROLEREQUEST_CHARGE_RATES_VIEW_MODE_FIELDS_ROW,
    BOOKING_CHARGE_RATES_VIEW_MODE_FIELDS_ROW,

    //Estimates
    ROLEREQUEST_ESTIMATE_PROFIT_RATES_FIELDS_ROW,
    ROLEREQUEST_ESTIMATE_COST_RATES_FIELDS_ROW,
    ROLEREQUEST_ESTIMATE_REVENUE_RATES_FIELDS_ROW,
    ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD
];

const { SYSTEM_REQUIRED, SYSTEM_READONLY, SYSTEM_HIDDEN } = FIELD_DATA_UIFIELD_CATEGORIES;

export const getIsHistoryField = (fieldInfo) => fieldInfo.category === FIELD_DATA_CATEGORIES.HISTORY || getIsCustomHistoryField(fieldInfo);

export const getIsMilestonesHistoryField = (fieldInfo) => fieldInfo.category === FIELD_DATA_CATEGORIES.FREE_TEXT_HISTORY;

export const getIsMultiValueField = (fieldInfo) => fieldInfo.category === FIELD_DATA_CATEGORIES.MULTI_VALUE || getIsCustomMultiValueField(fieldInfo);

export const getIsLastLoginField = (fieldInfo) => fieldInfo.name === RESOURCE_LAST_LOGIN;

export const getIsCalculated = (fieldInfo) => {
    return fieldInfo.category === FIELD_DATA_CATEGORIES.CALCULATED || getIsMultiValueField(fieldInfo) || getIsHistoryField(fieldInfo);
};

export const getIsMultiValueLookupField = (fieldInfo) => fieldInfo.fieldtype === FIELD_TYPES.MULTI_VALUE_LOOKUP;

export const getIsDateSensitive = (fieldInfo) => {
    return (
        fieldInfo.parameters
        && fieldInfo.parameters.startDate
        && fieldInfo.parameters.startDate === FIELD_DATA_TYPES.DATE_TIME
        && fieldInfo.parameters.endDate
        && fieldInfo.parameters.endDate === FIELD_DATA_TYPES.DATE_TIME
    );
};

export const getIsColourTypeField = (fieldName) => {
    return fieldName === COLOUR_FIELD[TABLE_NAMES.BOOKING] || fieldName === COLOUR_FIELD[TABLE_NAMES.ROLEREQUEST];
};

export const createFieldWithParams = (fieldName, params) => {
    return {
        fieldName,
        parameters: {
            ...params
        }
    };
};

export const isPrimaryTableKey = (fieldName, primaryTableName) => fieldName == `${primaryTableName}_guid`;

export const getIsFieldSetToHidden = (fieldName, data) => {
    return data[fieldName] === HIDDEN_FIELD_PLACEHOLDER_ALIAS;
};

const prefixFieldsByTypeMap = {
    [PREFIX_FIELDS_TYPES.CURRENCY_TYPE]: [
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_REVENUE,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_PROFIT,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_FIXED_PRICE,
        JOB_BUDGET,
        BOOKING_TOTAL_COST,
        BOOKING_TOTAL_REVENUE,
        BOOKING_TOTAL_PROFIT,
        BOOKING_REVENUE_PER_HOUR,
        BOOKING_COST_PER_HOUR,
        ROLEREQUEST_FIELDS.TOTALCOST,
        ROLEREQUEST_FIELDS.TOTALREVENUE,
        ROLEREQUEST_FIELDS.TOTALPROFIT,
        ROLEREQUEST_FIELDS.COST_PER_HOUR,
        ROLEREQUEST_FIELDS.REVENUE_PER_HOUR,
        ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALCOST,
        ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALPROFIT,
        ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALREVENUE,
        ROLEREQUESTGROUP_FIELDS.TOTALCOST,
        ROLEREQUESTGROUP_FIELDS.TOTALREVENUE,
        ROLEREQUESTGROUP_FIELDS.TOTALPROFIT,
        BOOKING_PROFITPERHOUR_FIELDNAME,
        ROLEREQUEST_PROFITPERHOUR_FIELDNAME,

        //Assignees budget
        ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME,
        ROLEREQUESTRESOURCE_FIELDS.TOTAL_COST,
        ROLEREQUESTRESOURCE_FIELDS.TOTAL_REVENUE,
        ROLEREQUESTRESOURCE_FIELDS.TOTAL_PROFIT,
        ROLEREQUESTRESOURCE_FIELDS.REVENUE_PER_HOUR,
        ROLEREQUESTRESOURCE_FIELDS.COST_PER_HOUR,

        //Role estimates
        ROLEREQUEST_FIELDS.ESTIMATE_COSTPERHOUR,
        ROLEREQUEST_FIELDS.ESTIMATE_REVENUEPERHOUR,
        ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME,
        ROLEREQUEST_FIELDS.ESTIMATE_TOTALCOST,
        ROLEREQUEST_FIELDS.ESTIMATE_TOTALPROFIT,
        ROLEREQUEST_FIELDS.ESTIMATE_TOTALREVENUE
    ]
};

const suffixFieldsByTypeMap = {
    [SUFFIX_FIELDS_TYPES.PERCENT_TYPE]: [JOB_BUDGET_CONSUMED, JOB_OPPORTUNITY_PERCENT, JOB_PROFITMARGIN, JOB_PROFIT_MARGIN_TARGET],
    [SUFFIX_FIELDS_TYPES.TOTAL_TYPE]: [
        BOOKING_TOTAL_COST,
        BOOKING_TOTAL_REVENUE,
        BOOKING_TOTAL_PROFIT,
        ROLEREQUEST_FIELDS.TOTALCOST,
        ROLEREQUEST_FIELDS.TOTALREVENUE,
        ROLEREQUEST_FIELDS.TOTALPROFIT,
        ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALCOST,
        ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALPROFIT,
        ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALREVENUE,
        ROLEREQUESTRESOURCE_FIELDS.TOTAL_COST,
        ROLEREQUESTRESOURCE_FIELDS.TOTAL_REVENUE,
        ROLEREQUESTRESOURCE_FIELDS.TOTAL_PROFIT,

        //Role estimates
        ROLEREQUEST_FIELDS.ESTIMATE_TOTALCOST,
        ROLEREQUEST_FIELDS.ESTIMATE_TOTALPROFIT,
        ROLEREQUEST_FIELDS.ESTIMATE_TOTALREVENUE
    ],
    [SUFFIX_FIELDS_TYPES.HOURLY_RATE_TYPE]: [
        BOOKING_COST_PER_HOUR,
        BOOKING_REVENUE_PER_HOUR,
        BOOKING_PROFITPERHOUR_FIELDNAME,
        ROLEREQUEST_FIELDS.COST_PER_HOUR,
        ROLEREQUEST_FIELDS.REVENUE_PER_HOUR,
        ROLEREQUEST_PROFITPERHOUR_FIELDNAME,
        ROLEREQUESTRESOURCE_FIELDS.REVENUE_PER_HOUR,
        ROLEREQUESTRESOURCE_FIELDS.COST_PER_HOUR,
        ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME,

        //Role estimates
        ROLEREQUEST_FIELDS.ESTIMATE_COSTPERHOUR,
        ROLEREQUEST_FIELDS.ESTIMATE_REVENUEPERHOUR,
        ROLEREQUEST_FIELDS.ESTIMATE_PROFITPERHOUR,
        ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME
    ]
};

export const getFieldPrefix = (fieldInfo, prefixSymbolsByTypeMap) => {
    const { name: fieldName } = fieldInfo;
    let prefix;

    Object.keys(prefixFieldsByTypeMap).forEach(prefixTypeKey => {

        if (prefixFieldsByTypeMap[prefixTypeKey].includes(fieldName)) {
            prefix = prefixSymbolsByTypeMap[prefixTypeKey];
        }
    });

    return prefix;
};

export const getFieldSuffix = (fieldInfo, suffixSymbolsByTypeMap) => {
    const { name: fieldName } = fieldInfo;
    let suffix;

    Object.keys(suffixFieldsByTypeMap).forEach(suffixTypeKey => {
        if (suffixFieldsByTypeMap[suffixTypeKey].includes(fieldName)) {
            suffix = suffixSymbolsByTypeMap[suffixTypeKey];
        }
    });

    return suffix;
};

export const getFieldSuffixExtraInfo = (fieldInfo, entity, currencySymbol) => {
    if (!fieldInfo && !entity) {
        return null;
    }

    if (fieldInfo.name === jobProfitMarginField.name) {
        const revenueAsBooked = entity[jobTotalRevenueField.name] || 0;
        const costAsBooked = entity[jobTotalCostField.name] || 0;

        return ({
            prefix: currencySymbol,
            suffix: (revenueAsBooked - costAsBooked).toFixed(2)
        });
    }

    return null;
};

export const getIsCustomField = (fieldInfo) => ALLOW_CUSTOM_FIELDS && fieldInfo.iscustom;

export const getIsMandatoryField = (fieldInfo) => fieldInfo.mandatory;

export const getIsSystemField = (fieldInfo = {}) => {
    return fieldInfo.uiFieldCategory === SYSTEM_REQUIRED || fieldInfo.uiFieldCategory === SYSTEM_READONLY || fieldInfo.uiFieldCategory === SYSTEM_HIDDEN;
};

export const getIsBooleanField = (fieldInfo) => fieldInfo.dataType === FIELD_DATA_TYPES.BOOL;

export const getIsRadioLookupControl = (field) => field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.RADIO_LOOKUP_CONTROL;

//function for booking_nonwork is needed because of special treatment of the field
export const getIsBookingNonWorkingField = (fieldInfo) => fieldInfo.dataType === CUSTOM_FIELD_DATA_TYPES.BOOKING_NON_WORK; //refactor?

export const getIsSystemMaintainedField = (fieldInfo) => fieldInfo.category === FIELD_DATA_CATEGORIES.SYSTEM_MAINTAINED;

export const getIsSystemReadOnlyField = (fieldInfo) => {
    return fieldInfo.uiFieldCategory === SYSTEM_READONLY;
};

export const isSubmittableField = (fieldName, tableName, fieldInfo) => {
    if (!fieldInfo || !Object.keys(fieldInfo).length) {
        return false;
    }

    if (fieldName === bookingNonWorkSectionField.name) {
        return true;
    }

    return fieldInfo.category !== FIELD_DATA_CATEGORIES.SYSTEM_MAINTAINED &&
        fieldInfo.category !== FIELD_DATA_CATEGORIES.CALCULATED &&
        fieldInfo.uiFieldCategory !== FIELD_DATA_UIFIELD_CATEGORIES.SYSTEM_READONLY &&
        fieldInfo.accessLevel !== FIELD_DATA_ACCESS_LEVEL.READONLY &&
        fieldInfo.accessLevel !== FIELD_DATA_ACCESS_LEVEL.HIDDEN &&
        !fieldInfo.appInternalField &&
        fieldName !== `${tableName}_guid`;
};

export const getMultiValueLinkedFieldAdditionalSuggestions = (tableName, value, entityIds, isMandatoryField = false) => {
    let additionalSuggestions = [];

    if (tableName === TABLE_NAMES.RESOURCE && !isMandatoryField) {
        additionalSuggestions = isEmpty(value) && !(entityIds || []).includes(UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID)
            ? [UNASSIGNED_RESOURCE_OPTION]
            : [];
    }

    return additionalSuggestions;
};

export const getIsCustomCalcField = (fieldInfo) => fieldInfo.iscustom && (getIsCustomHistoryField(fieldInfo) || getIsCustomMultiValueField(fieldInfo));

export const getIsCustomHistoryField = (fieldInfo) => fieldInfo.customFieldType === CUSTOM_FIELD_TYPES.HISTORY;

export const getIsCustomMultiValueField = (fieldInfo) => fieldInfo.customFieldType === CUSTOM_FIELD_TYPES.MULTI_VALUE;

export const getIsCustomLookupField = (fieldInfo) => fieldInfo.customFieldType === CUSTOM_FIELD_TYPES.LOOKUP;

export const getIsCustomPlanningDataField = (fieldInfo) => fieldInfo.customFieldType === CUSTOM_FIELD_TYPES.PLANNING_DATA_LOOKUP;

export const getIsPlanningDataLookup = (fieldInfo) => fieldInfo.fieldtype === FIELD_TYPES.PLANNING_DATA_LOOKUP || fieldInfo.customFieldType === CUSTOM_FIELD_TYPES.PLANNING_DATA_LOOKUP;

export const getAppFieldValue = (field, data, tableName) => {
    let value;

    if (PROFIT_PER_HOUR_FIELDS.includes(field)) {
        value = data[`${tableName}_revenueperhour`] - data[`${tableName}_costperhour`];
    } else if (ESTIMATE_PROFIT_PER_HOUR_FIELDS.includes(field)) {
        value = data[`${tableName}_estimaterevenueperhour`] - data[`${tableName}_estimatecostperhour`];
    }

    return value;
};

export const getAppFieldsData = (tableName, data) => {
    let appFieldsData = {};
    const tableAppFields = APP_FIELDS_FIELDINFOS[tableName] || {};

    if (!isEmptyObject(tableAppFields)) {
        Object.keys(tableAppFields).forEach(fieldName => {
            appFieldsData = {
                ...appFieldsData,
                [fieldName]: getAppFieldValue(fieldName, data, tableName)
            };
        });
    }

    return appFieldsData;
};

export const getAppFieldInfosByTableName = (tableName) => APP_FIELDS_FIELDINFOS[tableName] || {};

export const getSurrogateId = (tableName, data = {}) => data[`${tableName}_surrogate_id`];

export const getIsTimeAllocationField = (fieldName, tableName) => {
    const { fields: timeAllocationFields } = getTimeAllocationField(tableName);
    const isTimeAllocationField = timeAllocationFields.some(taField => taField.name === fieldName);

    return isTimeAllocationField;
};

export const getIsDateRangeField = (fieldName, tableName) => {
    const { fields: dateRangeFields, startDateField, endDateField } = dateRangeSectionFieldByTableName[tableName];

    return (
        dateRangeFields.some(drField => drField.name === fieldName)
        || fieldName === startDateField.name
        || fieldName === endDateField.name
    );
};

export const getFieldExistInSections = (fieldName, sections) => {
    return sections.some(section => {
        return section.fields.some(field => {
            let fieldExists = field.name === fieldName;
            const nestedFields = field.fields || [];

            if (!fieldExists && nestedFields.length > 0) {
                fieldExists = nestedFields.some(nestedField =>
                    nestedField.name === fieldName);
            }

            return fieldExists;
        });
    });
};

export const isDateTime = (fieldInfo) => {
    return fieldInfo.dataType === FIELD_DATA_TYPES.DATE_TIME;
};

export const isStartOrEndDate = (fieldInfo) => {
    const { name } = fieldInfo;

    return isDateTime(fieldInfo) && (name.indexOf('_end') !== -1 || name.indexOf('_start') !== -1);
};

export const isNumericDataType = (dataType) => {
    return (dataType === FIELD_DATA_TYPES.FLOAT || dataType === FIELD_DATA_TYPES.INT);
};

export const getFilteredFields = (fields, getFieldInfo, hiddenFlag) => {
    return fields.filter(field => {
        const { name } = field;
        const fieldInfo = getFieldInfo(name);
        const fieldAccessHidden = isFieldAccessHidden(fieldInfo);

        return hiddenFlag ? fieldAccessHidden : !fieldAccessHidden;
    });
};

export const filterSectionFieldForCustomComponents = (isFieldNotHidden, field, getFieldInfo, tableName) => {
    const { type } = field;
    const { MULTIFIELD_RADIO_CONTROL, DATE_RANGE_CONTROL, MULTIFIELD_SINGLE_ROW_CONTROL } = ENTITY_WINDOW_CUSTOM_CONTROL_TYPES;
    if (type === MULTIFIELD_RADIO_CONTROL) {
        //hide time allocation if the radio options are hidden
        if (field.name === `${tableName}_time_allocation`) {
            const hiddenFields = getFilteredFields(field.fields || [], getFieldInfo, true);
            const totalDiaryHoursFieldInfo = getFieldInfo(BOOKING_DIARY_HOURS);
            if (hiddenFields.length || (isFieldAccessHidden(totalDiaryHoursFieldInfo) && field.editable))
                isFieldNotHidden = false;
        } else if (field.name === `${tableName}_chargemode_section_field` || field.name === `${tableName}_estimate_chargemode_section_field`) {
            //to Hide Use different charge rate radio option
            //from custom cost n revenue component
            field.fields = getFilteredFields(field.fields || [], getFieldInfo, false);
        }
    } else if (type === MULTIFIELD_SINGLE_ROW_CONTROL && BAR_ENTITY_FIELDS.indexOf(field.name) !== -1) {
        //to Hide Rates fiels from DP
        // to Hide cost/revenue/profit single row fields from create/edit booking
        const hiddenFields = getFilteredFields(field.fields || [], getFieldInfo, true);
        if (hiddenFields.length)
            isFieldNotHidden = false;
    } else if (field.type === DATE_RANGE_CONTROL && field.name === BOOKING_DATE_RANGE) {
        //Set the show explanation flags for date range control
        const bookingDiaryDaysFieldInfo = getFieldInfo(BOOKING_DIARY_DAYS);
        if (isFieldAccessHidden(bookingDiaryDaysFieldInfo)) {
            field = { ...field, showValueExplanation: false };
        }
        field.fields = getFilteredFields(field.fields || [], getFieldInfo, false);
    }

    return { isFieldNotHidden, field };
};

export const isSkillField = (fieldInfo = {}) => {
    return fieldInfo.dataType === FIELD_DATA_TYPES.SKILL_LEVEL;
};

export const getComplexGroupedFieldsForTable = (tableName) => {
    return COMPLEX_GROUPED_FIELDS[tableName] || [];
};

export const getTimeAllocationField = (tableName, entity = {}) => {
    let result = timeAllocationSectionFieldByTableName[tableName];

    if (tableName === TABLE_NAMES.ROLEREQUEST) {
        const type = !isEmptyObject(entity) && getIsCriteriaRole(entity)
            ? ROLE_ENTITY_TYPES.ROLE_BY_CRITERIA
            : ROLE_ENTITY_TYPES.ROLE_BY_NAME;

        result = result[type];
    }

    return result;
};

export const getFieldByTableName = (tableName, entity = {}, fieldsByTable) => {
    let result = fieldsByTable[tableName];

    if (tableName === TABLE_NAMES.ROLEREQUEST) {
        const type = getIsCriteriaRole(entity) ? ROLE_ENTITY_TYPES.ROLE_BY_CRITERIA : ROLE_ENTITY_TYPES.ROLE_BY_NAME;

        result = result[type];
    }

    return result;
};

export const getIsSortableField = (fieldInfo) => {
    return !getIsDateSensitive(fieldInfo) && !getIsHistoryField(fieldInfo) && !getIsMultiValueField(fieldInfo);
};

export const mapDetailField = getFieldInfo => detailField => {
    const { table, field } = detailField;

    const fieldInfo = getFieldInfo(table, field);

    return {
        ...detailField,
        sortable: getIsSortableField(fieldInfo)
    };
};

export const getFieldMaxLength = (fieldInfo, defaultLength) => {
    const { length, fieldStorageSize } = fieldInfo;

    return length || parseInt(fieldStorageSize) || defaultLength;
};

export const getSkillFieldsSelection = (filter) => {
    let relevantSkillFilterSelection = {
        field: filter.noLevelFieldName,
        operator: filter.noLevelOperator,
        value: filter.value,
        parameters: {
            'skillGuid': filter.parameters.skillGuid
        }
    };

    if (filter.levels) {
        relevantSkillFilterSelection = {
            ...relevantSkillFilterSelection,
            field: filter.withLevelFieldName,
            operator: filter.withLevelOperator,
            value: filter.levels
        };
    }

    return relevantSkillFilterSelection;
};

const getUiEntityNumberFieldValue = (uiEntity, fieldName) => {
    const { [fieldName]: field = {} } = uiEntity || {};

    return field.value || 0;
};

export const calculateBudgetConsumed = (uiEntity) => {
    const jobBudget = getUiEntityNumberFieldValue(uiEntity, JOB_BUDGET);
    const plannedTotalCost = getUiEntityNumberFieldValue(uiEntity, JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST);

    return jobBudget === 0
        ? 0
        : plannedTotalCost / jobBudget * 100;
};

export const calculateProfitMargin = (uiEntity) => {
    const jobFixedPrice = getUiEntityNumberFieldValue(uiEntity, JOB_FIXEDPRICE);
    const plannedTotalCost = getUiEntityNumberFieldValue(uiEntity, JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST);

    return jobFixedPrice === 0
        ? 0
        : (jobFixedPrice - plannedTotalCost) / jobFixedPrice * 100;
};

export const calculateTotalProfit = (uiEntity) => uiEntity[JOB_FIXEDPRICE].value - uiEntity[JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST].value;

export function isRaghealthField(fieldInfo) {
    return fieldInfo.linkTable === TABLE_NAMES.RAGHEALTH || fieldInfo.name === JOB_TOTAL_RAGHEALTH_GUID;
}

export const getIsSystemDateField = (fieldInfo) => {
    const { dataType, name: fieldName } = fieldInfo || {};

    return dataType === FIELD_DATA_TYPES.DATE_TIME && SYSTEM_READONLY_DATE_FIELDS.includes(fieldName);
};

export const getFieldCaption = (percentage, translation) => {
    if (percentage !== null && percentage !== undefined && percentage >= 0) {
        return `${percentage.toFixed(2)}% ${translation}`;
    }

    return '';
};