import { FILE_EXTENSIONS, MIME_TYPES, TABLE_NAMES } from '../../constants/globalConsts';

const KBSize = 1024;
export const MBSize = 1024 * KBSize;

const mimeTypesMap = {
    [FILE_EXTENSIONS.DOC]: MIME_TYPES[FILE_EXTENSIONS.DOC],
    [FILE_EXTENSIONS.DOCX]: MIME_TYPES[FILE_EXTENSIONS.DOCX],
    [FILE_EXTENSIONS.PDF]: MIME_TYPES[FILE_EXTENSIONS.PDF]
};

export const getFileExtension = (fileName = '') => fileName.split('.').pop().toLowerCase();

const getFileContentType = (fileName = '') => mimeTypesMap[getFileExtension(fileName)];

export const convertBytesToMB = (size = 0) => size / MBSize;

export const validateFileSize = (size = 0, maxFileSizeMB) => convertBytesToMB(size) <= maxFileSizeMB;

export const validateFileType = (name = '', allowedFileTypes = []) =>
    allowedFileTypes.some(allowedType => allowedType.replace(/\./g, '') === getFileExtension(name));

export const validateFile = (size, name, maxFileSizeMB, allowedFileTypes) =>
    validateFileSize(size, maxFileSizeMB) && validateFileType(name, allowedFileTypes);

export const convertBase64ToBlob = (
    base64String,
    fileName,
    contentType = getFileContentType(fileName),
    sliceSize = MBSize / 2
) => {
    // eslint-disable-next-line no-undef
    const byteCharacters = atob(base64String);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);

        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        // eslint-disable-next-line no-undef
        byteArrays.push(new Uint8Array(byteNumbers));
    }

    // eslint-disable-next-line no-undef
    return new Blob(byteArrays, { type: contentType });
};

// eslint-disable-next-line no-undef
export const convertFileToObjectUrl = data => URL.createObjectURL(data);

/**
 * The method is used to check if the talent profile is transformed and table is resource.
 * @export
 * @param {string} talentProfilePageTransformedEnabled Feature flag value which defined if the feature flag is enabled or not.
 * @param {string} tableName Defined the table name
 * @returns {boolean} Return true or false
 */
export function isTalentProfilePageTransformedAndIsResourceTable(talentProfilePageTransformedEnabled, tableName) {
    return talentProfilePageTransformedEnabled && tableName == TABLE_NAMES.RESOURCE;
}