import { omit, uniqBy } from 'lodash';
import { TABLE_NAMES, ENTITY_WINDOW_MODULES } from '../constants';
import { getFieldInfoSelector, getTableStructure, getUIFieldInfos } from '../selectors/tableStructureSelectors';
import { isEmptyObject, stringReplacePlaceholders } from './commonUtils';
import { getEntityAlias } from './entityStructureUtils';
import { getFieldTableName, isTableFieldLinked } from './tableStructureUtils';
import { getFieldRules, mandatoryLinkedFieldValidator } from './validationUtils';
import { isSubmittableField } from './fieldUtils';
import { differenceInUtcCalendarDays, differenceInMonths, parseToUtcDate, isBefore, parseLocalDate, startOfUtcDay, parseUtcToLocalDate, formatToUtcISOString } from './dateUtils';
import { entityWindowOpen, entityWindowOpenForMultiple } from '../actions/entityWindowActions';
import { ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { EDIT_FNAS_PER_TABLENAME } from '../constants/tablesConsts';
import { BOOKING_START, BOOKING_STATUS, ROLEREQUESTGROUP_FIELDS } from '../constants/fieldConsts';
import { JOB_DESTINATION_JOB_GUID, JOB_RANGE_FIELD_MAX_MONTHS_RANGE } from '../constants/jobDuplicateConsts';
import { ROLL_FORWARD_DIALOG } from '../actions/actionTypes';
import { populateStringTemplates } from './translationUtils';

export const sortSelectedEntitiesByStartDate = (entities = [], fieldName) => {
    return entities.sort((entityOne = {}, entityTwo = {}) => {
        const startDateEntityOne = entityOne[fieldName];
        const startDateEntityTwo = entityTwo[fieldName];

        return differenceInUtcCalendarDays(parseToUtcDate(startDateEntityOne), parseToUtcDate(startDateEntityTwo));
    });
};

export const jobRangeValidator = (rule, value, callback, filedName, populatedMessages) => {
    const { jobRangeErrorMessage, fieldMandatoryText } = populatedMessages;
    const [startDate, endDate] = value || [];
    let error = [];

    if (Math.abs(differenceInMonths(startDate, endDate)) > JOB_RANGE_FIELD_MAX_MONTHS_RANGE) {
        error = [{
            message: jobRangeErrorMessage,
            field: filedName,
            fieldName: filedName,
            code: 1
        }];
    }

    if (!startDate || !endDate) {
        error = [{
            message: fieldMandatoryText,
            field: filedName,
            fieldName: filedName,
            code: 1
        }];
    }

    return error;
};

const getCustomValidatorRule = (fieldName, staticLabels, placeholderValues) => {
    let result = {};

    switch (fieldName) {
        case `${TABLE_NAMES.JOB}_range`: {
            const jobRangeErrorMessage = (staticLabels.forwardOptions || {}).maximumJobRangeMessage
                ? populateStringTemplates(staticLabels.forwardOptions || {}, placeholderValues).maximumJobRangeMessage
                : 'Job date range must be within 24 months from now';

            const fieldMandatoryText = (staticLabels.forwardOptions || {}).dateRangeValueMandatory || 'This field is mandatory';

            result = {
                validator: (rule, value, callback) => jobRangeValidator(rule, value, callback, fieldName, { jobRangeErrorMessage, fieldMandatoryText })
            };
            break;
        }

        case ROLEREQUESTGROUP_FIELDS.JOB_GUID:
        case JOB_DESTINATION_JOB_GUID: {
            const message = (staticLabels.forwardOptions || {}).destinationJobError
                ? populateStringTemplates((staticLabels.forwardOptions || {}), placeholderValues).destinationJobError
                : 'Job destination is mandatory';

            result = {
                message,
                validator: (rule, value, callback) => mandatoryLinkedFieldValidator(rule, value, callback, fieldName)
            };

            break;
        }
    }

    return result;
};

export const getRollForwardDialogFormRules = (formItemProps, getFieldInfo, placeholderValues, staticLabels) => {

    const {
        fieldName,
        tableName,
        rules = [],
        messages = {},
        hasFieldLevelRules = false, withCustomValidator
    } = formItemProps;

    const fieldInfo = getFieldInfo(tableName, fieldName);

    const updatedRules = rules.map(rule => {
        return {
            ...rule,
            message: stringReplacePlaceholders(rule.message, placeholderValues)
        };
    });

    if (hasFieldLevelRules) {
        const updatedMessages = Object.keys(messages).reduce((acc, messageKey) => {
            acc[messageKey] = stringReplacePlaceholders(messages[messageKey], placeholderValues);

            return acc;
        },{});
        updatedRules.push(...getFieldRules(fieldInfo, {}, getFieldInfo, {}, {}, updatedMessages, () => {}));
    }

    if (withCustomValidator) {
        const customValidatorRules = getCustomValidatorRule(fieldName, staticLabels, placeholderValues);

        if (!isEmptyObject(customValidatorRules)) {
            updatedRules.push(customValidatorRules);
        }
    }

    return {
        fieldName,
        updatedRules
    };
};

export const getAliasedPlaceholderValues = (getEntityInfo, selectedItems) => {
    const bookingEntityInfo = getEntityInfo(TABLE_NAMES.BOOKING);
    const jobEntityInfo = getEntityInfo(TABLE_NAMES.JOB);
    const singularForm = selectedItems.length === 1;
    const bookingEntityAlias = getEntityAlias(bookingEntityInfo, {
        singularForm,
        capitalized: false,
        fallbackValue: TABLE_NAMES.BOOKING
    });
    const jobEntitySingularAlias = getEntityAlias(jobEntityInfo, {
        singularForm: true,
        capitalized: false,
        fallbackValue: TABLE_NAMES.JOB
    });
    const placeholderValues = {
        bookingEntityAlias,
        noOfBooking: selectedItems.length,
        jobSingularAlias: jobEntitySingularAlias
    };

    return placeholderValues;
};

export const getLinkedSelectionFieldsWithAlias = (tableName, fields, getFieldInfo) => {
    return fields.filter((field) => {
        const fieldInfo = getFieldInfo(tableName, field.fieldName);

        return fieldInfo !== null && isTableFieldLinked(tableName, fieldInfo);
    })
        .map((field) => {
            const fieldInfo = getFieldInfo(tableName, field.fieldName);
            const linkedFieldTableName = getFieldTableName(fieldInfo, tableName);

            return {
                fieldName: `${field.fieldName}.${linkedFieldTableName}_description`
            };
        });
};

export const getTableFields = (state, tableName) => {
    const uiFields = getUIFieldInfos(getTableStructure(state), tableName);
    const fields = Object.keys(uiFields).map(field => ({
        'fieldName': field
    }));

    return fields;
};

export const getEntitySelectionQuery = (state, tableName, entityIds) => {
    const fieldInfoWraped = getFieldInfoSelector(state);
    const fields = getTableFields(state, tableName);
    const selectionFields = [
        { fieldName: `${tableName}_guid` },
        ...fields,
        ...getLinkedSelectionFieldsWithAlias(tableName, fields, fieldInfoWraped)
    ];

    return {
        fields: uniqBy(selectionFields, 'fieldName'),
        filter: {
            filterGroupOperator: 'And',
            filterLines: [{
                field: `${tableName}_guid`,
                operator: 'Contains',
                value: entityIds
            }]
        }
    };
};

const updateEntityWithFormData = (tableName, destinationStartDateOffset, entity, formData, getFieldInfo, getSuggestion) => {
    const fields = Object.keys(entity);

    const startDate = parseUtcToLocalDate(entity[`${tableName}_start`]);
    const endDate = parseLocalDate(entity[`${tableName}_end`]);

    const updatedFields = fields.reduce((acc, field) => {
        if (field in formData) {
            const value = formData[field].value;
            if (typeof value === 'object') {
                acc[field] = value.id;
            } else {
                const fieldInfo = getFieldInfo(tableName, field);
                const isLinkedField = isTableFieldLinked(tableName, fieldInfo);
                if (isLinkedField) {
                    const linkedTableData = getSuggestion(field);
                    const linkedId = (linkedTableData.find(data => data.value === formData[field].value) || {}).id;
                    acc[field] = linkedId;

                    if (linkedId == null && field === BOOKING_STATUS) {
                        acc[field] = entity[field];
                    }
                } else {
                    acc[field] = formData[field];
                }
            }
        } else {
            acc[field] = entity[field];
        }

        return acc;
    }, {});

    updatedFields[`${tableName}_start`] = formatToUtcISOString(parseToUtcDate(startDate).add(destinationStartDateOffset, 'days'));
    updatedFields[`${tableName}_end`] = endDate.add(destinationStartDateOffset, 'days').toISOString();

    return updatedFields;
};

export const getCreateEntityPayload = (tableName, entities = [], formData, getSuggestion, getFieldInfo) => {
    const updatedEntities = [];
    const tableDataWithGuid = [];
    if (entities.length > 0) {
        const entityStartDate = startOfUtcDay(parseToUtcDate(entities[0][`${tableName}_start`]));
        const destinationStartDate = startOfUtcDay(parseToUtcDate(formData[`${tableName}_start`]['value']));
        const destinationStartDateOffset = differenceInUtcCalendarDays(destinationStartDate, entityStartDate);

        entities.forEach(entity => {
            const omitFields = Object
                .keys(entity)
                .filter(fieldKey => !isSubmittableField(fieldKey, tableName, getFieldInfo(tableName, fieldKey)));
            const newEntity = omit(entity, omitFields);
            const tableData = updateEntityWithFormData(tableName, destinationStartDateOffset, newEntity, formData, getFieldInfo, getSuggestion);
            updatedEntities.push(tableData);
            tableDataWithGuid.push({ ...tableData, [`${tableName}_guid`]: entity[`${tableName}_guid`] });
        });
    }

    return { updatedEntities, tableDataWithGuid };
};

export const getEditDispatchAction = (state, collectionAlias, tableName, response, isBatch, getAccessibleEntitiesIdsSelector, externalMessages) => {
    let dispatchAction;
    let ids = [];

    isBatch ? response.forEach(res => {
        ids.push(res.id);
    }) : ids.push(response);

    const accessibleIds = getAccessibleEntitiesIdsSelector(state)(tableName, ids, ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName]) || [];

    if (accessibleIds.length == 1) {
        dispatchAction = entityWindowOpen(
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
            tableName,
            collectionAlias,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            {},
            accessibleIds[0],
            true,
            '',
            externalMessages
        );
    } else if (accessibleIds.length > 1) {
        dispatchAction = entityWindowOpenForMultiple(
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
            tableName,
            collectionAlias,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            [],
            accessibleIds,
            undefined,
            true,
            externalMessages
        );
    }

    return dispatchAction;
};

export const getRollForwardSuccessMessage = (message) => {
    //will update the logic in future to alter the success message
    return message;
};

export const rollForwardBookingCustomMessageMap = {
    [ROLL_FORWARD_DIALOG.CREATE_BOOKING_SUCCESS]: (staticMessages, succeededOperationsCount, multipleOperations) => {
        const { duplicateBooking, duplicateBookings } = staticMessages;
        const translatedMessage = multipleOperations ? duplicateBookings : duplicateBooking;

        return `${multipleOperations ? `${succeededOperationsCount} ` : ''}${translatedMessage}`;
    }
};

export const getMinimumBookingStartDate = (entities) => {
    let result = entities[0][BOOKING_START];

    entities.forEach((entity) => {
        if (isBefore(entity[BOOKING_START], result, 'd')) {
            result = entity[BOOKING_START];
        }
    });

    return result;
};