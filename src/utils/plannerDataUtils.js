import { getData, isEmptyObject, isMultiDimensionalArray, omit } from './commonUtils';
import {
    UNASSIGNED_BOOKINGS_RESOURCE,
    BOOKING_TYPES,
    PLANNER_VIEW_MODES,
    PLANNER_COLLAPSE_MODES,
    RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES,
    PLANNER_SUCCESS_MESSAGE_PROPS,
    PLANNER_BOOKING_GROUPS_ALIAS,
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    PLANNER_SUB_REC_ALIAS,
    PLANNER_MASTER_REC_ALIAS,
    PLANNER_ROLEREQUESTS_ALIAS,
    PLANNER_PAGE_TABLE_TO_GROUP_ALIAS,
    BAR_TABLE_NAMES,
    PLANNER_PAGE_TABLE_TO_GROUP_MAP,
    SVG,
    PLANNER_PAGE_ALIAS,
    TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS,
    COLOUR_FIELD
} from '../constants/plannerConsts';
import { getCurrentPlannerBarGroupsObject, getCutBar, getPlannerPageDataCollections } from '../selectors/plannerPageSelectors';
import { CRUD_OPERATIONS, TABLE_NAMES } from '../constants/globalConsts';
import { find, forEach, groupBy, reduce } from 'lodash';
import * as plannerPageSelectors from '../selectors/plannerPageSelectors';
import * as actions from '../actions/actionTypes';
import { DEFAULT_BAR_COLOUR } from '../constants/colorsConsts';
import { getStandardFillColor, getHoveredFillColor, getSelectedFillColor } from './bookingBarStylesUtils';
import { startOfDay, endOfDay, addDays, workingDaysBetweenDates, parseLocalDate, isWithinRange, parseToUtcDate, isBefore, isSameDay } from './dateUtils';
import { hexToHSLA, hslaToString, getContrastYIQFromHSLA } from './colorUtils';
import { getCustomOption, getDateToggleOptionByValue } from './dateToggleOptionsUtils';
import { getNextAvailableWorkday } from './calendarGridUtils';
import * as actionTypes from '../actions/actionTypes';
import { getEntityAliasSelector } from '../selectors/entityStructureSelectors';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';
import { getBarGroupsGuidByTable, getCellGroupsGuidByTable } from '../selectors/workspaceSelectors';
import { batchDeleteGroupedSuccess, loadGrouppedTableDataSuccess, loadMoreGrouppedTableDataSuccess, patchMultipleGrouppedTableDataSuccess, persistSelectedEdits } from '../actions/tableDataActions';
import { getFlatTableData, getLinkedTableData } from './linkedDataUtils';
import { handleLoadedUserEntityAccess, loadUserEntityAccess } from '../actions/userEntityAccessActions';
import { MARKETPLACE_ROLES_STATES_KEYS, ROLE_ITEM_STATUS_KEYS } from '../constants/rolesConsts';
import { RESOURCE_DESCRIPTION, ROLEREQUESTRESOURCE_FIELDS, ROLEREQUEST_FIELDS, RESOURCE_GUID, ROLEREQUESTRESOURCE_ASSIGNEE_GUID } from '../constants/fieldConsts';
import { ROLE_GROUP_DETAILS_PAGE } from '../constants';
import { getCurrentPageAliasSelector, getPageStateParamsSelector } from '../selectors/navigationSelectors';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_PAGED_DATA } from '../constants/roleInboxPageConsts';
import { getRoleInboxPageCollections } from '../selectors/dataGridPageSelectors';
import { getPageRoleRequestStatusDescriptionSelector } from '../selectors/roleRequestStatusSelectors';
import { JOB_PAGE_PAGED_DATA } from '../constants/jobsPageConsts';
import { ROLE_INBOX_PAGE_TABLE_DATA_ALIAS } from '../constants/roleInboxPageConsts';
import { NOTIFICATIONS_BOOKING_ALIAS } from '../constants/notificationsPageConsts';
import { getRoleGroupDetailsPageCollections } from '../selectors/roleGroupDetailsPageSelectors';
import { getPageTableDatasSelector } from '../selectors/tableDataSelectors';
import { getAssigneeDataByAssigneeIdSelector } from '../selectors/roleRequestsSelector';
import { DEFAULT_PLANNER_PAGE_LINKED_FIELDS } from '../constants/tablesConsts';
import { getFieldTableName } from './tableStructureUtils';
import { transform } from 'lodash';
import { MAXIMUM_INFINITE_SCROLL_ROWS_AMOUNT, PAGINATION_MODES } from '../constants/paginationConsts';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { PlannerMultiSelectionItemsService } from '../lib/services/planner/plannerMultiSelectionServices';
import { dayjsInstance } from '../setup/dayjsInstanceSetup';

const { REJECTED, LIVE } = ROLE_ITEM_STATUS_KEYS;
const { ROLEREQUEST, ROLEREQUESTRESOURCE } = TABLE_NAMES;

const rowColorOdd = '#E6E6E6';
const rowColorEven = '#FFFFFF';

const getSubRowSettings = (parentRecord, pathToParent) => {
    return (record, index) => {
        const isRowOdd = (
            (pathToParent.length === 1 && index % 2 === 0) ||
            (pathToParent.length > 1 && pathToParent[1] % 2 === 0)
        );

        return {
            style: {

                height: parentRecord.subRows[index].height,
                background: isRowOdd
                    ? rowColorOdd
                    : rowColorEven
            }
        };
    };
};

const getBarTableName = (barEntity) => {
    let result;

    for (let i = 0; i < BAR_TABLE_NAMES.length; i++) {
        if (barEntity[`${BAR_TABLE_NAMES[i]}_guid`]) {
            result = BAR_TABLE_NAMES[i];
            break;
        }
    }

    return result;
};

//passed bars should be sorted
function alocateConcurentBarsRows(bars = []) {
    //Directly returning if there are no bars passed
    if (null === bars || 0 === bars.length) {
        return [];
    }

    let barsByStart = {};

    bars.forEach(bar => {
        const barTableName = getBarTableName(bar);
        const barStartDate = bar[`${barTableName}_start`];

        if (!barsByStart[barStartDate]) {
            barsByStart[barStartDate] = [];
        }

        barsByStart[barStartDate].push(bar);
    });

    let rows = [];
    let rowIndex = 0;
    let barsAmmount = bars.length;
    let keys = Object.keys(barsByStart);

    while (barsAmmount > 0) {
        rows[rowIndex] = [];

        keys.forEach((key) => {
            if (canPlaceBar(barsByStart, key, rows, rowIndex)) {
                rows[rowIndex].push(barsByStart[key].pop());
                barsAmmount--;
            }
        });

        rowIndex++;
    }

    return rows;
}

const getBarDates = (bar) => {
    const barTableName = getBarTableName(bar);

    return {
        start: new Date(bar[`${barTableName}_start`]),
        end: new Date(bar[`${barTableName}_end`])
    };
};

const canPlaceBar = (barsByStart, key, rows, rowIndex) => {
    let canPlaceBar = barsByStart[key].length !== 0 && rows[rowIndex].length === 0;
    canPlaceBar = canPlaceBar || barsByStart[key].length !== 0
        && getBarDates(barsByStart[key][barsByStart[key].length - 1]).start > getBarDates(rows[rowIndex][rows[rowIndex].length - 1]).end;

    return canPlaceBar;
};

const buildConcurentRowsMap = (concurentRows = [], filter = () => true, selector = (item) => item) => {
    let barsRowsMap = {};

    if (isMultiDimensionalArray(concurentRows)) {
        for (let i = 0; i < concurentRows.length; i ++) {
            barsRowsMap[i] = concurentRows[i].filter(filter).map(selector);
        }
    }

    return barsRowsMap;
};

//Currently no alternative to bookingTypes
const isBookingUnconfirmed = (bar, bookingTypes) => { // Defaulting to unconfirmed
    const bookingTypeGuid = bar['booking_bookingtype_guid'];
    const bookingType = bookingTypes && bookingTypes.byId ? (bookingTypes.data || [])[(bookingTypes.byId || {})[bookingTypeGuid]] : null;

    return bookingType ? bookingType['bookingtype_description'] !== BOOKING_TYPES.PLANNED : true;
};

const isBarCut = (barsMap, clipboard) => {
    const cutBar = getCutBar(clipboard) || {};

    return Object.entries(barsMap).some(([tableName, bars]) => {
        const cutBarGuid = cutBar[`${tableName}_guid`];

        return cutBarGuid && bars.some(bar => bar[`${tableName}_guid`] === cutBarGuid);
    });
};

const buildSelectEditsPayload = (tableName, pagedMasterRecPlannerDataGuid, masterRecTableName, subRecPlannerDataGuid, subRecTableName, getBarGroupGuid) => {
    switch (tableName) {
        case masterRecTableName:
            return { editDataKey: PLANNER_MASTER_REC_ALIAS, dataGuid: pagedMasterRecPlannerDataGuid };
        case subRecTableName:
            return { editDataKey: PLANNER_SUB_REC_ALIAS, dataGuid: subRecPlannerDataGuid };
        default:
            return { editDataKey: [PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName]], dataGuid: getBarGroupGuid(tableName) };
    }
};

const buildBarConflictsMap = (bar, conflicts, tableName) => {
    const resourceConflicts = find(conflicts, { [RESOURCE_GUID]: bar[`${tableName}_${RESOURCE_GUID}`] });
    const conflictDates = resourceConflicts ? resourceConflicts['conflict dates'] : [];

    const barStart = parseToUtcDate(bar[`${tableName}_start`]);
    const barEnd = startOfDay(parseToUtcDate(bar[`${tableName}_end`]));

    return conflictDates.filter((date) => {
        return isWithinRange(parseToUtcDate(date), barStart, barEnd, 'day', '[]');
    });
};

const buildBarConflicts = (groupRows, conflicts, tableName, bookingTypes) => {
    let bookingsConflictsMap = {};

    forEach(groupRows, (groupRow) => {
        groupRow.forEach((booking) => {
            let filteredDates = [];

            if (!isBookingUnconfirmed(booking, bookingTypes)) {
                filteredDates = buildBarConflictsMap(booking, conflicts, tableName);
            }

            bookingsConflictsMap[booking[`${tableName}_guid`]] = filteredDates;
        });
    });

    return bookingsConflictsMap;
};

function getDatesBetween(start, end) {
    const startDate = parseToUtcDate(start);
    const endDate = dayjsInstance(end);
    const dates = [];

    let current = startDate;
    while (isBefore(current, endDate) || isSameDay(current, endDate)) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day');
    }

    return dates;
}

const buildBarPotentialConflicts = (groupRows, conflicts, tableName, bookingTypes, plannerUnassignedRolesToggleFeatureEnabled) => {
    let potentialConflictsMap = {};
    
    forEach(groupRows, (groupRow) => {
        const roleRequestResourceDateRangeMap = {}
        groupRow.forEach((bar) => {
            let resourceConflicts = find(conflicts, { [RESOURCE_GUID]: bar[`${tableName}_${RESOURCE_GUID}`] }) || {};

            if (isEmptyObject(resourceConflicts)) {
                potentialConflictsMap[bar[`${TABLE_NAMES.ROLEREQUEST}_guid`]] = buildBarConflictsMap(bar, conflicts, TABLE_NAMES.ROLEREQUEST);
            } else if (isBookingUnconfirmed(bar, bookingTypes || {})) {
                potentialConflictsMap[bar[`${tableName}_guid`]] = buildBarConflictsMap(bar, conflicts, tableName);
            }

            if (plannerUnassignedRolesToggleFeatureEnabled) {
                // Map a new object to hold a list of all the dates between the start and end date of each roles
                if (bar[ROLEREQUESTRESOURCE_ASSIGNEE_GUID]) {
                    const startToEndDateRange = getDatesBetween(bar[ROLEREQUESTRESOURCE_FIELDS.START], bar[ROLEREQUESTRESOURCE_FIELDS.END]);
                    roleRequestResourceDateRangeMap[bar[ROLEREQUESTRESOURCE_FIELDS.GUID]] = startToEndDateRange;
                } else if (bar[ROLEREQUEST_FIELDS.GUID]) {
                    const startToEndDateRange = getDatesBetween(bar[ROLEREQUEST_FIELDS.START], bar[ROLEREQUEST_FIELDS.END]);
                    roleRequestResourceDateRangeMap[bar[ROLEREQUEST_FIELDS.GUID]] = startToEndDateRange;
                }
            }
        });

        if (plannerUnassignedRolesToggleFeatureEnabled) {
            if (Object.keys(roleRequestResourceDateRangeMap).length > 1) {
                const uniqueSetOfDates = {};
    
                // For each role bar in the group, check their dates from the mapped object created above to find conflicting dates and save against the role bar.
                Object.keys(roleRequestResourceDateRangeMap).forEach(key => 
                    roleRequestResourceDateRangeMap[key].forEach(value => {
                        for (const currentKey of Object.keys(roleRequestResourceDateRangeMap)) {
                            if (currentKey !== key) {
                                if (roleRequestResourceDateRangeMap[currentKey].includes(value)) {
                                    if (uniqueSetOfDates[key]) {
                                        uniqueSetOfDates[key].push(value);
                                    } else {
                                        uniqueSetOfDates[key] = [value];
                                    }
                                }
                            }
                        }
                    }));
    
                Object.keys(uniqueSetOfDates).forEach(key => 
                    potentialConflictsMap[key] = uniqueSetOfDates[key]);
            }
        }
    });

    return potentialConflictsMap;
};

const getColoursMap = (colour) => {
    const colourHSLA = hexToHSLA(colour);
    let barColourMap = {};

    const defaultUnconfirmedStandardColor = { h: 0, s: 0, l: 100, a: 0 };
    const defaultUnconfirmedHoveredColor = { h: 0, s: 0, l: 95, a: 75 };
    const defaultUnconfirmedSelectedColor = { h: 0, s: 0, l: 100, a: 100 };

    barColourMap[`unconfirmed_${colour}`] = {
        id:`unconfirmed_${colour.slice(1)}`,
        stroke: hslaToString(colourHSLA),
        fill: {
            standard: hslaToString(defaultUnconfirmedStandardColor),
            hovered: hslaToString(defaultUnconfirmedHoveredColor),
            selected: hslaToString(defaultUnconfirmedSelectedColor)
        },
        text: {
            standard: getContrastYIQFromHSLA(defaultUnconfirmedStandardColor),
            hovered: getContrastYIQFromHSLA(defaultUnconfirmedHoveredColor),
            selected: getContrastYIQFromHSLA(defaultUnconfirmedSelectedColor)
        }
    };

    const standardDefaultColor = getStandardFillColor(colourHSLA);
    const hoveredDefaultColor = getHoveredFillColor(colourHSLA);
    const selectedDefaultColor = getSelectedFillColor(colourHSLA);

    barColourMap[colour] = {
        id: colour.slice(1),
        stroke: hslaToString(colourHSLA),
        fill: {
            standard: hslaToString(standardDefaultColor),
            hovered: hslaToString(hoveredDefaultColor),
            selected: hslaToString(selectedDefaultColor)
        },
        text: {
            standard: getContrastYIQFromHSLA(standardDefaultColor),
            hovered: getContrastYIQFromHSLA(hoveredDefaultColor),
            selected: getContrastYIQFromHSLA(selectedDefaultColor)
        }
    };

    return barColourMap;
};

const buildCustomColourSchemeId = (entityName, fieldName) => {
    return `custom_${entityName}_${fieldName}`;
};

const getBarColours = (schemes = []) => {
    let barColourMap = getDefaultBarColours();

    schemes.forEach(colourScheme => {
        if (colourScheme && Array.isArray(colourScheme.colourThemeTypeFields)) {
            const { colourThemeTypeFields = [] } = colourScheme;

            colourThemeTypeFields.forEach((typeField) => {
                const { colourThemeRules = [] } = typeField;
                colourThemeRules.forEach(rule => {
                    barColourMap = {
                        ...barColourMap,
                        ...getColoursMap(rule.colour)
                    };
                });
            });
        }
    });

    addBarStyles(barColourMap);

    return barColourMap;
};

const workingDaysAchievedDateOptionValue = (startDate, endDate, dateOption) => {
    const wokringDays = workingDaysBetweenDates(startDate, endDate) + 1;
    const dateOptionOffset = getDateToggleOptionByValue(dateOption).offset;

    return wokringDays >= dateOptionOffset;
};

const getVisibleRanges = (startDate, endDate, viewMode, hideWeekends) => {
    const { mode, dateOption } = viewMode;
    const rangeStart = parseLocalDate(startDate);
    let rangeEnd = parseLocalDate(endDate);

    if (hideWeekends && mode === PLANNER_VIEW_MODES.VIEW_MODE_DAY && dateOption !== getCustomOption().value && !workingDaysAchievedDateOptionValue(startDate, endDate, dateOption)) {
        const dateOptionOffset = getDateToggleOptionByValue(dateOption).offset;
        const endDateOffset = getNextAvailableWorkday(rangeStart, dateOptionOffset) - dateOptionOffset;

        rangeEnd = addDays(endDate, endDateOffset);
    }

    return { rangeStart, rangeEnd };
};

const isBarInsideDateRange = (bar, tableName, workspace) => {
    const { masterRecTableName, startDate, endDate, viewMode, viewsConfig } = workspace;
    const hideWeekends = viewsConfig[masterRecTableName].hideWeekends;
    const barStartDate = startOfDay(bar[`${tableName}_start`]);
    const barEndDate = endOfDay(bar[`${tableName}_end`]);
    const { rangeStart, rangeEnd } = getVisibleRanges(startDate, endDate, viewMode, hideWeekends);

    return !(barEndDate < rangeStart) && !(barStartDate > rangeEnd);
};

const getVisibleBarsGroups = (barsGroupsData, workspace, masterRecTableName, filter = () => true, plannerUnassignedRolesToggleFeatureEnabled = false) => {
    const barsGroupsGroupped = groupBy(barsGroupsData.filter(filter), (bar) => {
        const tableName = getBarTableName(bar);

        if (plannerUnassignedRolesToggleFeatureEnabled) {
            if (masterRecTableName === TABLE_NAMES.RESOURCE && bar[`${tableName}_assignee_guid`]) {
                // To render bars belonging to rolerequestresource_assignee_guid.
                return bar[`${tableName}_assignee_guid`];
            }
        }

        return bar[`${tableName}_${masterRecTableName}_guid`];
    });

    return reduce(barsGroupsGroupped, (result, value, key) => {
        const barsInVisibleDateRange = reduce(value, (bars, bar) => {
            const tableName = getBarTableName(bar);

            if (isBarInsideDateRange(bar, tableName, workspace)) {
                bars.push(bar);
            }

            return bars;
        }, []);

        (result[key] || (result[key] = [])).push(...barsInVisibleDateRange);

        return result;
    }, {});
};

const barGroupsData = (barGroups, groupIds) => {
    let result = [];

    Object.keys(groupIds).forEach((key) => {
        result.push(...(((barGroups || {})[(groupIds || {})[key]] || {}).data || []));
    });

    return result;
};

const getBookingTypeData = (bookingtype = { data: [] }, comparator) => {
    let bookingTypeData = {};

    bookingTypeData = bookingtype.data.find(entry => entry.bookingtype_description === comparator);

    return bookingTypeData ? bookingTypeData.bookingtype_guid : undefined;
};

const hasUnassignedRow = (tableName, rows) => {
    return rows.length > 0 && isUnassignedRow(tableName, rows[0].id);
};

const isUnassignedRow = (tableName, rowId) => {
    return tableName === TABLE_NAMES.RESOURCE && rowId === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID;
};

const isUnassignedResourceGuid = (resourceGuid) => {
    return resourceGuid === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID;
};

function isFlattenView(expanded, collapseMode) {
    return !expanded && collapseMode === PLANNER_COLLAPSE_MODES.SHOW_BARS;
}

const addCSSRule = (sheet, selector, rules, index) => {
    if (find(sheet.rules, { selectorText: selector })) {
        return;
    }
    if ('insertRule' in sheet) {
        sheet.insertRule(selector + ' {' + rules + '}', index);
    } else if ('addRule' in sheet) {
        sheet.addRule(selector, rules, index);
    }
};

const createStyleSheet = () => {
    // Create the <style> tag
    let style = document.createElement('style');

    // Add a media (and/or media query) here if you'd like!
    // style.setAttribute("media", "screen")
    // style.setAttribute("media", "only screen and (max-width : 1024px)")

    // WebKit hack :(
    style.appendChild(document.createTextNode(''));

    // Add the <style> element to the page
    document.head.appendChild(style);

    return style.sheet;
};

const barsStyleSheet = createStyleSheet();

const addBarStyles = (barColourMap, styleSheet = barsStyleSheet) => {

    Object.keys(barColourMap).forEach((key) => {

        const barColour = barColourMap[key];
        if (barColour) {
            addCSSRule(styleSheet, `.cs-rule-${barColour.id} .handle`, `fill: ${barColour.stroke};`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id} .bed`, `stroke: ${barColour.stroke};`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id} .time`, `stroke: ${barColour.stroke}; fill: ${barColour.fill.standard};`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id}:hover .time`, `fill: ${barColour.fill.hovered};`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id}.selected .time`, `fill: ${barColour.fill.selected} !important;`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id} .barText`, `fill: ${barColour.text.standard};`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id}:hover .barText`, `fill: ${barColour.text.hovered};`);
            addCSSRule(styleSheet, `.cs-rule-${barColour.id}.selected .barText`, `fill: ${barColour.text.selected} !important;`);
        }
    });
};

const defaultColoursMap = getColoursMap(DEFAULT_BAR_COLOUR);
addBarStyles(defaultColoursMap);

const getDefaultBarColours = () => {
    return defaultColoursMap;
};

const buildUpdateRoleRequestStatusTableData = (action, state) => {
    const { payload, type } = action;
    let changedData = {};

    if (type === actions.BATCH_UPDATE_PLANNER_ROLEREQUEST_RESOURCE) {
        const { resourceGuid } = payload;
        changedData = { [ROLEREQUEST_FIELDS.RESOURCE_GUID]: resourceGuid };
    } else if (type === `${actions.UPDATE_ROLEREQUEST_STATUS}_${PLANNER_PAGE_ALIAS}`) {
        const { newValue: status } = payload;
        const newStatusGuid = plannerPageSelectors.getPlannerRoleRequestStatusGuidSelector(state)(status);
        changedData = { [ROLEREQUEST_FIELDS.STATUS_GUID]: newStatusGuid };
    }

    return changedData;
};

const getEntityGuids = (entityData = [], tableName) => {
    return entityData.map((entity) => entity[`${tableName}_guid`]);
};

const getEntityLinkedGuids = (entityData = [], tableName, linkedTableName) => {
    return entityData.reduce((accumulator, entity) => {
        const linkedGuidField = `${tableName}_${linkedTableName}_guid`;

        if (entity[linkedGuidField]) {
            accumulator.push(entity[linkedGuidField]);
        }

        return accumulator;
    }, []);
};

const getTableDataEntryGuid = (payload, index) => {
    let { patchData, tableDataEntryGuids } = payload;

    return patchData ? patchData[index].tableDataEntryGuid : tableDataEntryGuids[index];
};

const getTableData = (payload, index) => {
    let { patchData, tableData } = payload;

    return patchData ? patchData[index].tableData : tableData[index];
};

const areRecordsExpanded = (plannerDataForGuid, tableName) => {
    const { expandCollapseAllState } = plannerDataForGuid;

    return expandCollapseAllState[tableName] === RECORDS_LIST_EXPAND_COLLAPSE_ALL_STATES.COLLAPSED;
};

const getShowToasterMessagesActionsOfInterest = () => {
    const createActionsArray = [
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_GROUPPED_DATA_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_GROUPPED_DATA_SUCCESS}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${TABLE_NAMES.JOB}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${PLANNER_MASTER_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_GROUPPED_DATA_SUCCESS}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${TABLE_NAMES.ROLEREQUEST}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${ROLE_INBOX_PAGE_TABLE_DATA_ALIAS}`,
        //Triggered from error prompt
        `${actionTypes.DIGEST_INSERT_GROUPPED_TABLE_DATA_SUCCESSFUL}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.DIGEST_INSERT_GROUPPED_TABLE_DATA_SUCCESSFUL}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.DIGEST_INSERT_TABLE_DATA_SUCCESSFUL}_${JOB_PAGE_PAGED_DATA}`,
        `${actionTypes.DIGEST_INSERT_TABLE_DATA_SUCCESSFUL}_${PLANNER_SUB_REC_ALIAS}`,
        `${actionTypes.DIGEST_INSERT_TABLE_DATA_SUCCESSFUL}_${TABLE_NAMES.CLIENT}`
    ];

    const editActionsArray = [
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_GROUPPED_DATA_SUCCESS}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${TABLE_NAMES.JOB}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${PLANNER_MASTER_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS}_${TABLE_NAMES.ROLEREQUEST}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.RESOURCE}`,
        //Triggered from error prompt
        `${actionTypes.DIGEST_PATCH_GROUPPED_TABLE_DATA_SUCCESSFUL}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.DIGEST_PATCH_GROUPPED_TABLE_DATA_SUCCESSFUL}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.DIGEST_PATCH_TABLE_DATA_SUCCESSFUL}_${PLANNER_SUB_REC_ALIAS}`,
        `${actionTypes.DIGEST_PATCH_TABLE_DATA_SUCCESSFUL}_${TABLE_NAMES.CLIENT}`,
        `${actionTypes.PATCH_PAGED_DATA_SUCCESSFUL}_${ROLE_INBOX_PAGE_PAGED_DATA}`,
        `${actionTypes.PATCH_PAGED_DATA_SUCCESSFUL}_${JOB_PAGE_PAGED_DATA}`
    ];

    const deteleActionsArray = [
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_GROUPPED_TABLE_DATA_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_GROUPPED_TABLE_DATA_SUCCESS}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.JOB}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${PLANNER_MASTER_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.ROLEREQUEST}`,
        `${actionTypes.DELETE_TABLE_DATA_SUCCESSFUL}_${ROLE_INBOX_PAGE_PAGED_DATA}`,
        `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_GROUPPED_TABLE_DATA_SUCCESS}_${NOTIFICATIONS_BOOKING_ALIAS}`,
        //Triggered from error prompt
        `${actionTypes.DIGEST_DELETE_GROUPPED_TABLE_DATA_SUCCESSFUL}_${PLANNER_BOOKING_GROUPS_ALIAS}`,
        `${actionTypes.DIGEST_DELETE_GROUPPED_TABLE_DATA_SUCCESSFUL}_${PLANNER_ROLEREQUESTS_ALIAS}`,
        `${actionTypes.DIGEST_DELETE_TABLE_DATA_SUCCESSFUL}_${JOB_PAGE_PAGED_DATA}`,
        `${actionTypes.DIGEST_DELETE_TABLE_DATA_SUCCESSFUL}_${TABLE_NAMES.CLIENT}`
    ];

    return {
        createActionsArray,
        editActionsArray,
        deteleActionsArray,
        defaultShowToasterAction: actionTypes.TOASTER_ACTIONS.SHOW_TOASTER_MESSAGE
    };
};

const toasterMessageCustomTypesSuffixes = {
    [ROLE_ITEM_STATUS_KEYS.ARCHIVED]: 'archivedSuffix',
    [ROLE_ITEM_STATUS_KEYS.REJECTED]: 'rejectedSuffix',
    [ROLE_ITEM_STATUS_KEYS.DRAFT]: 'restartedSuffix',
    [ROLE_ITEM_STATUS_KEYS.REQUESTED]: 'requestedSuffix',
    [ROLE_ITEM_STATUS_KEYS.LIVE]: 'liveSuffix',
    [MARKETPLACE_ROLES_STATES_KEYS.PUBLISHED]: 'publishedRoleSuffix',
    [MARKETPLACE_ROLES_STATES_KEYS.SCHEDULED_FOR_PUBLISHING]: 'scheduleRoleForPublishingSuffix',
    [MARKETPLACE_ROLES_STATES_KEYS.APPLICATION_SUBMITTED]: 'submittedSuffix',
    [MARKETPLACE_ROLES_STATES_KEYS.ROLE_PUBLICATION_EDITED]: 'publicationEditedSuffix',
    [MARKETPLACE_ROLES_STATES_KEYS.ROLE_PUBLICATION_REMOVED]: 'publicationRemovedSuffix',
    [ROLE_ITEM_STATUS_KEYS.ROLE_APPLICATION_WITHDRAWN]: 'withdrawnSuffix'
};

const toasterMessageCustomTypes = Object.keys(toasterMessageCustomTypesSuffixes) || [];

const getOperationSuffix = (suffixes, actionType, customType, suffixEntityAlias) => {
    const { editedSuffix = 'edited', deletedSuffix = 'deleted', createdSuffix = 'created' } = suffixes;
    const { createActionsArray, editActionsArray, deteleActionsArray } = getShowToasterMessagesActionsOfInterest();
    let result = 'modified';

    if (toasterMessageCustomTypes.indexOf(customType) !== -1) {
        const customTypeSuffixKey = toasterMessageCustomTypesSuffixes[customType];
        result = suffixes[customTypeSuffixKey];
    } else if (editActionsArray.indexOf(actionType) !== -1 || actionType === CRUD_OPERATIONS.UPDATE) {
        result = editedSuffix;
    } else if (createActionsArray.indexOf(actionType) !== -1 || actionType === CRUD_OPERATIONS.CREATE) {
        result = createdSuffix;
    } else if (deteleActionsArray.indexOf(actionType) !== -1 || actionType === CRUD_OPERATIONS.DELETE) {
        result = deletedSuffix;
    }

    if (suffixEntityAlias) {
        result = `${result} ${suffixEntityAlias}`;
    }

    return result;
};

const getAssigneeDisplayMessage = (status, resourceDescription, operationSuffix, state, succeededOperationsCount) => {
    let displayMessage = null;
    const isBatchOperation = succeededOperationsCount > 1;

    if (status === REJECTED) {
        displayMessage = `${resourceDescription} ${operationSuffix}`;
    } else if (status === LIVE) {
        const { requestedLabel, batchRequestedLabel } = getTranslationsSelector(state, { sectionName: 'common', idsArray: ['requestedLabel', 'batchRequestedLabel'] });
        displayMessage = `${isBatchOperation ? succeededOperationsCount + ' ' : ''}${isBatchOperation ? batchRequestedLabel : requestedLabel} ${operationSuffix}`;
    }

    return displayMessage;
};

const getToasterMessageConfig = (state, tableName, actionType, customType, succeededOperationsCount = 1, suffixEntityTableName = null) => {
    const { entityType, status, resourceDescription, customMessage } = customType || {};
    const { TOP_MARGIN, DURATION } = PLANNER_SUCCESS_MESSAGE_PROPS;
    const idsArray = [
        'editedSuffix',
        'deletedSuffix',
        'createdSuffix',
        'archivedSuffix',
        'restartedSuffix',
        'rejectedSuffix',
        'requestedSuffix',
        'liveSuffix',
        'scheduleRoleForPublishingSuffix',
        'publishedRoleSuffix',
        'submittedSuffix',
        'publicationEditedSuffix',
        'publicationRemovedSuffix',
        'withdrawnSuffix'
    ];
    const suffixEntityAlias = suffixEntityTableName
        ? getEntityAliasSelector(state, suffixEntityTableName, { singularForm: succeededOperationsCount === 1, capitalized: false, fallbackValue: suffixEntityTableName })
        : null;
    const suffixes = getTranslationsSelector(state, { sectionName: PLANNER_PAGE_ALIAS, idsArray }) || {};
    const operationSuffix = getOperationSuffix(suffixes, actionType, status, suffixEntityAlias);
    let displayMessage = '';
    const isBatchOperation = succeededOperationsCount > 1;

    if (customMessage) {
        displayMessage = customMessage;
    } else if (entityType !== ROLEREQUESTRESOURCE) {
        const entityAlias = getEntityAliasSelector(state, tableName, { singularForm: succeededOperationsCount === 1, capitalized: succeededOperationsCount === 1, fallbackValue: tableName });
        displayMessage = `${isBatchOperation ? succeededOperationsCount + ' ' : ''}${entityAlias} ${operationSuffix}`;
    } else if (entityType === ROLEREQUESTRESOURCE) {
        displayMessage = getAssigneeDisplayMessage(status, resourceDescription, operationSuffix, state, succeededOperationsCount);
    }

    return {
        displayMessage,
        top: TOP_MARGIN,
        duration: DURATION,
        showSuccess: true
    };
};

const getAssigneeToasterMessageCustomType = (state, status, assigneeId) => {
    let resourceDescription = null;

    if (status === REJECTED) {
        const assignee = getAssigneeDataByAssigneeIdSelector(state)(assigneeId);
        const resourceGuid = assignee[ROLEREQUESTRESOURCE_FIELDS.RESOURCE_GUID];
        const currentPageTableDatas = getPageTableDatasSelector(state);
        resourceDescription = getData(currentPageTableDatas, TABLE_NAMES.RESOURCE, resourceGuid)[RESOURCE_DESCRIPTION];
    }

    return {
        entityType: ROLEREQUESTRESOURCE,
        status,
        resourceDescription
    };
};

const getToasterMessageCustomType = (tableName, targetedRoleStatusDescriptionTableData, state, entityGuidsMap = {}) => {
    let result = null;

    if (tableName == ROLEREQUEST) {
        const { assigneesGuids = [] } = entityGuidsMap;
        const assigneesGuidsCount = assigneesGuids.length;
        const getPageRoleRequestStatusDescription = getPageRoleRequestStatusDescriptionSelector(state);

        const roleRequestStatusGuid = (targetedRoleStatusDescriptionTableData || {})[ROLEREQUEST_FIELDS.STATUS_GUID];
        const roleRequestStatusDescription = (targetedRoleStatusDescriptionTableData || {})[ROLEREQUEST_FIELDS.STATUS_DESCRIPTION];

        const status = roleRequestStatusDescription
            ? roleRequestStatusDescription
            : roleRequestStatusGuid && getPageRoleRequestStatusDescription(roleRequestStatusGuid);

        if (!assigneesGuidsCount) {
            result = {
                entityType: ROLEREQUEST,
                status,
                roleDescription: null
            };
        } else {
            result = getAssigneeToasterMessageCustomType(state, status, assigneesGuids[0]);
        }
    }

    return result;
};

const getRecordsMap = (plannerBarRecords = []) => {
    const recordsMap = {
        [TABLE_NAMES.BOOKING]: [],
        [TABLE_NAMES.ROLEREQUEST]: [],
        [TABLE_NAMES.ROLEREQUESTRESOURCE]: []
    };

    plannerBarRecords.forEach(({ plannerBarType: tableName, records = [], plannerBarChildGroupedList = {} }) => {
        recordsMap[tableName] = Array.isArray(records) ? records : [];

        const { plannerBarType: plannerChildBarType, records: childRecords } = plannerBarChildGroupedList || {};

        if (plannerChildBarType && Array.isArray(childRecords)) {
            recordsMap[plannerChildBarType] = Array.isArray(childRecords) ? childRecords : [];
        }
    });

    return recordsMap;
};

const getBarsData = (recordsMap = {}, getFieldInfo) => {
    const barsLinkedTableData = {};
    const barsGroupData = {};

    Object.keys(recordsMap).forEach((key) => {
        barsLinkedTableData[key] = getLinkedTableData(recordsMap[key], key, getFieldInfo);
        barsGroupData[key] = getFlatTableData(recordsMap[key], key, getFieldInfo);
    });

    return {
        barsLinkedTableData,
        barsGroupData
    };
};

const getCurrentRecBars = (bars = {}, recIds = [], recTableName) => {
    return Object.entries(bars).reduce((accumulator, [barTableName, bars]) => {
        accumulator[barTableName] = bars
            .filter(bar =>
                recIds.includes(bar[`${barTableName}_${recTableName}_guid`])
                || recIds.includes(bar[`${barTableName}_${TABLE_NAMES.ROLEREQUEST}_guid`]));

        return accumulator;
    }, {});
};

const barMissingFilter = (barToCheck = {}, barCollection = [], barTableName) => {
    return !barCollection.some(bar => bar[`${barTableName}_guid`] === barToCheck[`${barTableName}_guid`]);
};

const getMissingBarIdsMap = (originBars = {}, targetBars = {}) => {
    return Object.keys(originBars).reduce((accumulator, barTableName) => {
        accumulator[barTableName] = originBars[barTableName]
            .filter(originBar => barMissingFilter(originBar, targetBars[barTableName], barTableName))
            .map(bar => bar[`${barTableName}_guid`]);

        return accumulator;
    }, {});
};

const createGetPlannerLoadGroupedTableDataActions = (getGroupGuidByTable = getBarGroupsGuidByTable, aliasByTableNameMap = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS) =>
    (workspaceSettings, recordsMap, getFieldInfo) => {
        let result = [];

        Object.keys(recordsMap).forEach((tableName) => {
            const groupsGuid = getGroupGuidByTable(workspaceSettings, tableName);
            const records = recordsMap[tableName];
            const alias = aliasByTableNameMap[tableName];
            const groupData = getFlatTableData(records, tableName, getFieldInfo);
            result.push(
                loadGrouppedTableDataSuccess(
                    alias,
                    {
                        tableDataGuid: groupsGuid,
                        tableNames: [tableName]
                    },
                    groupData
                )
            );
        });

        return result;
    };

const getPlannerLoadGroupedTableDataActions = createGetPlannerLoadGroupedTableDataActions();
const getTableViewLoadGroupedTableDataActions = createGetPlannerLoadGroupedTableDataActions(getCellGroupsGuidByTable, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS);

const createGetPlannerLoadMoreGroupedTableDataActions = (getGroupGuidByTable = getBarGroupsGuidByTable, aliasByTableNameMap = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS) =>
    (workspaceSettings, recordsMap, getFieldInfo) => {
        let result = [];

        Object.keys(recordsMap).forEach((tableName) => {
            const groupsGuid = getGroupGuidByTable(workspaceSettings, tableName);
            const records = recordsMap[tableName];
            const alias = aliasByTableNameMap[tableName];
            const groupData = getFlatTableData(records, tableName, getFieldInfo);

            result.push(
                loadMoreGrouppedTableDataSuccess(
                    alias,
                    {
                        tableDataGuid: groupsGuid,
                        tableNames: [tableName],
                        tableName
                    },
                    groupData
                )
            );
        });

        return result;
    };

const getPlannerLoadMoreGroupedTableDataActions = createGetPlannerLoadMoreGroupedTableDataActions();
const getTableViewLoadMoreGroupedTableDataActions = createGetPlannerLoadMoreGroupedTableDataActions(getCellGroupsGuidByTable, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS);

const getPatchMultipleGrouppedTableDataSuccessActions = (workspaceSettings, recordsMap, getFieldInfo, additionalProps = {}) => {
    let result = [];

    Object.keys(recordsMap).forEach((tableName) => {
        const groupsGuid = getBarGroupsGuidByTable(workspaceSettings, tableName);
        const records = recordsMap[tableName];
        const alias = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName];
        const groupData = getFlatTableData(records, tableName, getFieldInfo);

        result.push(
            patchMultipleGrouppedTableDataSuccess(
                alias,
                {
                    tableDataGuid: groupsGuid,
                    tableName: tableName,
                    tableData: groupData,
                    response: true,
                    ...additionalProps
                },
                groupData
            )
        );
    });

    return result;
};

const getBatchDeleteMultipleGrouppedTableDataSuccessActions = (workspaceSettings, recordsMap = {}) => {
    return Object.keys(recordsMap).map((tableName) => {
        const tableDataGuid = getBarGroupsGuidByTable(workspaceSettings, tableName);
        const recordIds = recordsMap[tableName];
        const groupAlias = PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[tableName];

        return batchDeleteGroupedSuccess(
            groupAlias,
            tableDataGuid,
            [tableName, TABLE_NAMES.JOB, TABLE_NAMES.RESOURCE],
            recordIds
        );
    });
};

const getPlannerLoadGroupsUserEntityAccessActions = (barsGroupData) => {
    return Object.entries(barsGroupData)
        .map(([tableName, groupData]) => loadUserEntityAccess(getEntityGuids(groupData, tableName), tableName));
};

const getPlannerLoadedGroupsUserEntityAccessHandlerActions = (barsGroupData) => {
    return Object.entries(barsGroupData)
        .map(([tableName, groupData]) => handleLoadedUserEntityAccess(groupData, tableName));
};

const addGuidByIdField = (guids = [], bars, idField) => {
    return bars.reduce((accumulator, bar) => {
        if (accumulator.indexOf(bar[idField]) === -1) {
            accumulator.push(bar[idField]);
        }

        return accumulator;
    }, [...guids]);
};

const getBarsSelectionFieldsMap = (barTableName) => {
    const {
        JOB,
        RESOURCE,
        ROLEREQUEST,
        ROLEREQUESTRESOURCE
    } = TABLE_NAMES;

    let selectionFieldsMap = {
        [barTableName]:  `${barTableName}_guid`,
        [RESOURCE]: `${barTableName}_${TABLE_NAMES.RESOURCE}_guid`,
        [JOB]: `${barTableName}_${TABLE_NAMES.JOB}_guid`
    };

    if (barTableName === ROLEREQUESTRESOURCE) {
        selectionFieldsMap = {
            ...selectionFieldsMap,
            [RESOURCE]: `${barTableName}_assignee_guid`,//Needed?
            [ROLEREQUEST]: `${barTableName}_${TABLE_NAMES.ROLEREQUEST}_guid`
        };
    }

    return selectionFieldsMap;
};

const mapBarsGuidsByTableName = (barsMap) => {
    return Object.entries(barsMap).reduce((accumulator, [barTableName, bars]) => {
        const barsSelectionFields = getBarsSelectionFieldsMap(barTableName);

        Object.entries(barsSelectionFields).forEach(([tableName, field]) => {
            accumulator[tableName] = addGuidByIdField(accumulator[tableName], bars, field);
        });

        return accumulator;
    }, {});
};

const getBarsCountFromMap = (barsMap) => Object.values(barsMap).reduce((accumulator, group) => accumulator + group.length, 0);

const isSVGActor = (actor) => actor.includes(SVG);

const getEntityLinkedDescription = (state, entity = {}, tableName, linkedTableName) => {
    const pageAlias = getCurrentPageAliasSelector(state);
    let pageTableData = null;

    switch (pageAlias) {
        case PLANNER_PAGE_ALIAS:
            pageTableData = getPlannerPageDataCollections(state);
            break;
        case ROLE_INBOX_PAGE_ALIAS:
            pageTableData = getRoleInboxPageCollections(state);
            break;
        case ROLE_GROUP_DETAILS_PAGE:
            pageTableData = getRoleGroupDetailsPageCollections(state);
            break;
        default:
            pageTableData = [];
            break;
    }

    const linkedFieldId = entity[`${tableName}_${linkedTableName}_guid`];
    const linkedData = getData(pageTableData, linkedTableName, linkedFieldId);
    const linkedDescription = linkedData[`${linkedTableName}_description`] || entity.barStatus;

    return linkedDescription;
};

const getJobDescriptionForEntity = (state, entityId, tableName) => { // Remove this once the job field is included in rolerequest entity
    const pageAlias = getCurrentPageAliasSelector(state);
    let result = '';

    if (pageAlias === ROLE_GROUP_DETAILS_PAGE) {
        result = getPageStateParamsSelector(state)(pageAlias).jobName;
    } else if (pageAlias === PLANNER_PAGE_ALIAS) {
        const entityDataCollection = getPlannerPageDataCollections(state);
        const entityGroupsData = getCurrentPlannerBarGroupsObject(state.plannerPage)[tableName];
        const entityIndex = (entityGroupsData.byId[tableName] || {})[entityId];
        const jobId = (entityGroupsData.data[entityIndex] || {})[`${tableName}_${TABLE_NAMES.JOB}_guid`];

        if (jobId) {
            const jobData = getData(entityDataCollection, TABLE_NAMES.JOB, jobId);
            result = jobData[`${TABLE_NAMES.JOB}_description`];
        }
    }

    return result;
};

const getBarPersistEditsActions = (workspaceSettings, plannerPage) => {
    const barPersistEditsActions = BAR_TABLE_NAMES.map(barTableName => {
        const barGroupsDataGuid = getBarGroupsGuidByTable(workspaceSettings, barTableName);
        const barDataCollection = plannerPage[PLANNER_PAGE_TABLE_TO_GROUP_MAP[barTableName]];
        const editableGuids = barDataCollection[barGroupsDataGuid].currentEdits;

        return persistSelectedEdits(PLANNER_PAGE_TABLE_TO_GROUP_ALIAS[barTableName], {
            dataGuid: barGroupsDataGuid,
            editableGuids
        });
    });

    return barPersistEditsActions;
};

const lowerCaseTableData = (tableData) => {
    return transform(tableData, (result, value, key) => {
        result[key.toLowerCase()] = value;
    }, {});
};

const filterTableDatas = (tableDatas, getFieldInfo) => {
    const linkedFieldsTableNames = DEFAULT_PLANNER_PAGE_LINKED_FIELDS.map(field => {
        const fieldInfo = getFieldInfo(field.tableName, field.fieldName);
        const linkedTableName = getFieldTableName(fieldInfo, field.tableName);

        return linkedTableName;
    });

    const datas = omit(tableDatas, linkedFieldsTableNames);

    return datas;
};

const DEFAULT_COLOURS = ['#bebebe', '#ca005d', DEFAULT_BAR_COLOUR];

const getMappedBarColours = (barColours) => {
    const expandedBarColours = [
        ...barColours,
        ...DEFAULT_COLOURS
    ];

    const mappedBarColors = expandedBarColours.reduce((accumulator, colour) => {
        if (colour) {
            accumulator = {
                ...accumulator,
                ...getColoursMap(colour)
            };
        }

        return accumulator;
    }, {});

    addBarStyles(mappedBarColors);

    return mappedBarColors;
};

const getUniqueBarColours = (recordsMap) => {
    return Object.entries(recordsMap).reduce((accumulator, [table, records]) => {
        records.forEach((record) => {
            const currentColor = record[COLOUR_FIELD[table]];

            if (!accumulator.includes(currentColor)) {
                accumulator.push(currentColor);
            }

        });

        return accumulator;
    }, []);
};

const getPlannerPaginationMode = (rowCount, maximumInfiniteScrollRows) => {

    const maximumScrollRows = maximumInfiniteScrollRows || MAXIMUM_INFINITE_SCROLL_ROWS_AMOUNT;

    return rowCount > maximumScrollRows
        ? PAGINATION_MODES.PAGINATED_VIEW
        : PAGINATION_MODES.INFINITE_SCROLL;
};

const isInfiniteScrollAllowed = (totalCount, maximumInfiniteScrollRows, getPaginationMode = getPlannerPaginationMode) => {
    const paginationMode = getPaginationMode(totalCount, maximumInfiniteScrollRows);

    return paginationMode === PAGINATION_MODES.INFINITE_SCROLL;
};

const canScroll = (totalCount, loadedCount, maximumInfiniteScrollRows, getPaginationMode = getPlannerPaginationMode) => {
    return isInfiniteScrollAllowed(totalCount, maximumInfiniteScrollRows, getPaginationMode) && 0 < totalCount - loadedCount;
};

const getAdditionalUserAccessLoadActions = (barsGroupData) => {
    const additionalRequestFields = [
        ROLEREQUEST_FIELDS.CAN_MOVE_PENDING,
        ROLEREQUEST_FIELDS.CAN_REMOVE_PENDING,
        ROLEREQUEST_FIELDS.GUID,
        `${TABLE_NAMES.ROLEREQUEST}${ENTITY_ACCESS_TYPES.EDIT}`,
        `${TABLE_NAMES.ROLEREQUEST}${ENTITY_ACCESS_TYPES.DELETE}`,
        ROLEREQUEST_FIELDS.CAN_MANAGE_BUDGET
    ];

    const filterAdditionalRequestFieldsFunc = (fieldObject) => {
        return additionalRequestFields.includes(fieldObject.fieldName);
    };

    const overwriteExisting = false;

    return barsGroupData[TABLE_NAMES.ROLEREQUEST].length
        ? [
            loadUserEntityAccess(
                getEntityGuids(barsGroupData[TABLE_NAMES.ROLEREQUEST], TABLE_NAMES.ROLEREQUEST),
                TABLE_NAMES.ROLEREQUEST,
                filterAdditionalRequestFieldsFunc,
                overwriteExisting
            )]
        : [];
};

const getMultiSelectionItemsCountByTableName = () => {
    const { ROLEREQUESTRESOURCE, ROLEREQUEST } = TABLE_NAMES;

    const selectionItems = PlannerMultiSelectionItemsService.getSelectionItems();

    return selectionItems.reduce((accumulator, currentItem) => {
        const barTableName = currentItem.barTableName;

        if (barTableName === ROLEREQUESTRESOURCE) {
            const rolerequestGuid = currentItem[ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID];

            if (!selectionItems.some(item => item[ROLEREQUEST_FIELDS.GUID] === rolerequestGuid)) {
                accumulator[ROLEREQUEST] = (accumulator[ROLEREQUEST] || 0) + 1;
            }
        } else {
            accumulator[barTableName] = (accumulator[barTableName] || 0) + 1;
        }

        return accumulator;
    }, {});
};

const getRepeatBookingCount = (repeatIntervalNumber, repeatInterval, repeatUntil, bookingStartDate) => {
    const bookingStart = dayjsInstance(bookingStartDate);
    const repeatEnd = dayjsInstance(repeatUntil);

    let totalBookings = 0;
    let nextStartDate = bookingStart;

    while (isBefore(nextStartDate, repeatEnd) || isSameDay(nextStartDate, repeatEnd)) {
        totalBookings++;
        nextStartDate = nextStartDate.add(repeatIntervalNumber, repeatInterval);
    }

    return totalBookings;
};

export {
    getSubRowSettings,
    getBarTableName,
    alocateConcurentBarsRows,
    buildConcurentRowsMap,
    buildBarConflicts,
    buildBarPotentialConflicts,
    buildSelectEditsPayload,
    getBarColours,
    getDefaultBarColours,
    buildUpdateRoleRequestStatusTableData,
    getVisibleRanges,
    getVisibleBarsGroups,
    barGroupsData,
    getBookingTypeData,
    hasUnassignedRow,
    isUnassignedRow,
    isUnassignedResourceGuid,
    isBookingUnconfirmed,
    isBarCut,
    isFlattenView,
    buildCustomColourSchemeId,
    getEntityGuids,
    getTableDataEntryGuid,
    getTableData,
    areRecordsExpanded,
    getShowToasterMessagesActionsOfInterest,
    getToasterMessageConfig,
    getToasterMessageCustomType,
    getPlannerLoadGroupedTableDataActions,
    getTableViewLoadGroupedTableDataActions,
    getRecordsMap,
    getCurrentRecBars,
    getMissingBarIdsMap,
    getBarsData,
    getPlannerLoadMoreGroupedTableDataActions,
    getTableViewLoadMoreGroupedTableDataActions,
    getPatchMultipleGrouppedTableDataSuccessActions,
    getBatchDeleteMultipleGrouppedTableDataSuccessActions,
    getPlannerLoadedGroupsUserEntityAccessHandlerActions,
    getPlannerLoadGroupsUserEntityAccessActions,
    getBarsCountFromMap,
    mapBarsGuidsByTableName,
    isSVGActor,
    getEntityLinkedDescription,
    getJobDescriptionForEntity,
    barMissingFilter,
    getBarPersistEditsActions,
    getEntityLinkedGuids,
    lowerCaseTableData,
    filterTableDatas,
    getMappedBarColours,
    getUniqueBarColours,
    getPlannerPaginationMode,
    isInfiniteScrollAllowed,
    canScroll,
    getAdditionalUserAccessLoadActions,
    addBarStyles,
    getColoursMap,
    getMultiSelectionItemsCountByTableName,
    getRepeatBookingCount
};