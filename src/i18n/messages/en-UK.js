// Any string wrapped in ${ } or { } is a placeholder string and should not be translated literally.
export default {
    //common validation errors
    emailValidationError: 'Please provide a valid email address.',
    //commonly used text
    fieldValue: 'Field value',
    confirmation: 'Confirmation',
    invalid: 'Invalid',
    current: '{index} (Current)',
    dismissText: 'Dismiss',
    mandatoryValidation: '* Mandatory',
    //common component
    selectionListLicenseWarning: '{contactUs} to increase your limit.',
    selectionListLicenseError: '{contactUs} to increase your limit.',
    selectionListDiaryCustomMsg: '{activeCount} of {licensedCount} diaries used',
    selectionListSkillsCustomMsg: '{activeCount} of {licensedCount} skills used',
    commonComponentConfirmationModalConsequence: 'I understand the consequences of this change.',
    commonComponentConfirmationModalEmptyMsg: ' ',
    noOptionsAvailable: 'No Options Available',
    actionBarSaveButtonLabel: 'Save Changes',
    actionBarConfirmChangesLabel: 'Confirm changes',
    actionBarCancelButtonLabel: 'Cancel',
    actionBarDiscardChangesLabel: 'Discard Changes',
    actionBarLeavePageMessage: 'Are you sure you want to leave the page?',
    actionBarUnsavedChanges: 'You have unsaved changes on this page. Do you still want to leave?',
    actionBarFormErrorMessage: 'This form has errors.',
    deleteModalCancelButtonLabel: 'No, keep security profile',
    deleteModalSubmitButtonLabel: 'Yes, delete security profile',
    securityProfileDeleteModalTitle: 'Delete {profileName} security profile?',
    securityProfileDeleteModalText: 'Do you wish to permanently delete this security profile?',
    cannotBeUndoneText: 'This cannot be undone.',
    addAnItemText: 'Add an item',
    selectionListSortText: 'Sort A-Z',
    deleteSectionConfirmation: 'Are you sure you want to delete {sectionName} ?',
    deleteSectionConfirmationTitle: 'Delete Section Confirmation',
    CreateContextMenuLabel: 'Create',
    RenameContextMenuLabel: 'Rename',
    DuplicateContextMenuLabel: 'Duplicate',
    DeleteContextMenuLabel: 'Delete',
    DUPLICATE_USING_APIContextMenuLabel: 'DUPLICATE_USING_API',
    toasterDefaultSuccessMessage: 'Changes saved',
    toasterDefaultErrorMessage: 'Saving failed, please try again',
    toasterDefaultWarningMessage: 'Some changes didn\'t save, please refresh and try again',
    toasterDefaultUnsavedChangesMessage: 'Unable to save changes, please resolve errors and try again.',
    actionCannotBeUndoneMessage: 'This action cannot be undone.',
    noDataAvailableText: 'No data',
    licensedCountLabel: '{activeCount} of {licensedCount} used ',
    defaultLimitationInfo: 'The limitation is based on your current license plan.',
    contactUsText: 'Contact us',
    importText: 'Import',
    noResultsFound: 'No results found',
    reorderText: 'Reorder',
    editText: 'Edit',
    resetText: 'Reset',
    searchText: 'Search',
    nameText: 'name',

    //skill filter cascader component
    skillFilterResetButtonDisabledMessage: 'Cannot reset: No skills selected.',
    skillFilterApplyButtonDisabledMessage: 'Cannot apply: No skills selected.',
    skillFilterApplyButtonDisabledForMaxCountMessage: 'Cannot apply: Maximum {maxSkillSelection} selections allowed.',

    //Import data
    entityImportSelectValuePlaceholder: 'Select Value',
    ImportSuccessfulToasterMessage: 'Import successful',
    ImportUnsuccessfulToasterMessage: 'Import unsuccessful',
    operationLogChangesSaved: 'Operation log changes saved',
    //userManagement Messages
    userManagementLicenseWarning: 'You are close to the limit permitted by your license. Set existing users to \'Inactive\' or {contactUs} to increase the limit of your plan.',
    userManagementLicenseError: 'You have reached the limit permitted by your license. Set existing users to \'Inactive\' or {contactUs} to increase the limit of your plan.',
    userManagementLicenseErrorHeading: 'Cannot have more than {number} active users.',
    userManagementLicenseActiveUsers: 'You have {number} users set to ‘Active’.',
    userManagementLicenseUserLimit: 'Your current licence supports up to {number} active users.',
    userManagementLicenseContactUS: 'Set some users to ‘Inactive’ or {contactUs} to increase your limit.',
    userManagementInactiveUserHeading: 'Set {number} users to \'Inactive\'?',
    userManagementSetInactiveUserPopup: 'Do you wish to set {number} users to \'Inactive\'?',
    userManagementUnAssignBooks: 'They will not be able to log in and will be unassigned from current bookings. It will not be possible to assign them to future bookings.',
    userManagementReportingArchive: 'If they were assigned to past bookings, this data will be maintained for reporting/archival purposes.',
    userManagementInactiveConfirmation: 'Set {number} users to \'Inactive\' and remove them from current bookings',
    userManagementInactivePopupPrimaryButton: 'Make users Inactive',
    userManagementInactivePopupSecondaryButton: 'Cancel',
    //for confirmation modal popup need to change later
    userManagementDeletePopupMessage: 'Delete selected users?',
    userManagementDeleteWarningMessage: 'Do you wish to permanently delete {number} users?',
    userManagementDeleteBookingsWarning: 'All their details will be deleted from the system. Past bookings they were assigned to will become unassigned and may affect any reporting that involves those bookings.',
    userManagementDeleteHistoricData: 'If you would like to preserve historic data, simply set this User Status to \'Inactive\'.',
    userManagementDeleteConfirmation: 'Delete the selected users and remove them from any bookings or reports in the system',
    userManagementDeletePrimaryButton: 'Delete users',
    userManagementDeleteSecondaryButton: 'Keep selected users',
    userManagementDescription: 'Add, edit or delete users here. Navigate to their profiles to make further changes.',
    userManagementSubTitle: 'Active users  ',
    userManagementNotifyUserCountDescription: '{activeUser} of your {totalUser} users are active. Your licence supports up to {licencedActiveUser} active users.',
    userManagementAddUserButtonFroCommandBar: 'Add a user',
    userManagementToNavigateImportFeatureMessage: 'For bulk updates, you may prefer using the {Import} feature.',
    userManagementFormLabelsSecurityProfile: 'Security profile',
    userManagementFormLabelsUserStatusDescription: 'Setting a user to active allows them to log into their account.',
    userManagementFormLabelsName: 'Name',
    userManagementFormLabelsEmail: 'Email',
    userManagementFormLabelsUserStatus: 'Active user',
    userManagementFormLabelsUserLastLogin: 'Last login',
    userManagementValidationFirstName: 'Please enter first name',
    userManagementValidationLastName: 'Please enter last name',
    userManagementValidationEmailReqd: 'Please input your E-mail',
    userManagementValidationEmailMsg: 'The input is not valid E-mail',
    userManagementValidationSecurityProfile: 'Selection of Security profile is mandatory',
    userManagementTooltipGoToProfile: 'Go to profile',
    userManagementTooltipResetPass: 'Reset password',
    userManagementTooltipResendEmail: 'Send invite email',
    userManagementTooltipMarkDelete: 'Mark for deletion',
    userManagementFirstNamePlaceholder: 'First Name',
    userManagementLastNamePlaceholder: 'Last Name',
    userManagementEmailPlaceholder: 'Enter Email',
    userManagementSecurityProfilePlaceholder: 'Select a Security profile',
    userManagementTitle: 'User Management',
    userManagementStatusActive: 'Active',
    userManagementStatusInactive: 'Inactive',
    userManagementDuplicateEmail: 'Please ensure the users have unique email IDs.',
    userManagementUserAccess: 'Insufficient permissions to edit these resources',
    //comopanyInfo Messages
    companyInfoNameLabel: 'Company name',
    companyInfoLogoLabel: 'Logo',
    companyUploadLogoLabel: 'Upload logo',
    companyInfoLogoThumbnailLabel: 'Logo thumbnail',
    companyUploadLogoThumbnailLabel: 'Upload logo thumbnail',
    companyInfoSupportPhoneNumberLabel: 'Support phone number',
    companyInfoApplicationLanguage: 'Application Language',
    companyInfoSupportEmailLabel: 'Support email',
    companyInfoInstanceOwnerLabel: 'Instance owner',
    companyInfoLogoControlMessage: 'The logo should be of size 80px X 24px and be in a .png format',
    companyInfoUploadControlText: 'Click or drag file to this area to upload',
    companyInfoLogoThumbnailControlMessage: 'The logo thumbnail should be of size 24px X 24px and be in a .png format',
    companyInfoPhoneNumberErrorMessage: 'Please provide a valid phone number',
    companyInfoEmailErrorMessage: 'Please provide a valid email address',
    companyInfoLogoErrorMessage: 'Please upload valid logo',
    companyInfoLogoThumbnailErrorMessage: 'Please upload valid logo thumbnail',
    companyInfoLookUpNoMatches: 'No matches',
    companyInfoSelectValuePlaceholder: 'Select value',
    companyInfoHelpSupportMessage: 'For support, please contact',
    companyInfoVersion: 'Version {version}',
    companyInfoTitle: 'Company information',
    companyInfoFileUploadFailed: '{fileName} file upload failed.',
    //Currency Messages
    currencyHeaderText: 'Currency',
    currencyDefaultMessage: 'System Currency',
    currencyDescription: 'Select the currency to be used throughout your application.',
    baseCurrencyLabel: 'Base currency',
    //diary calendar messages
    diaryCalendarHeading: 'Diary',
    diaryCalendarSubHeading: 'Customise your year by defining a standard work pattern and non-working days such as bank holidays and office closures.',
    diaryCalendarCustomDayTitle: 'Add a Custom Day',
    diaryCalendarCustomPeriodTitle: 'Add a Custom Period',
    diaryCalendarSelectDateRangeRequiredMsg: 'Please select a date range',
    diaryCalendarCustomPeriodRequiredMsg: 'Name for Custom Period is Required',
    diaryCalendarWorkPatternRequiredMsg: 'Please select a Work Pattern',
    diaryCalendarSelectDateRequiredMsg: 'Please select a date',
    diaryCalendarCustomDayRequiredMsg: 'Name for Custom Day is Required',
    diaryCalendarDayTypeRequiredMsg: 'Please select a Day Type',
    diaryCalendarReplaceCustomPeriodMsg: 'Do you want to replace "{overlappingPeriods}" with a new custom period?',
    diaryCalendarReplaceCustomDayMsg: 'Do you want to replace "{overlappingDayName}" with a new custom day?',
    //for confirmation modal popup need to change later
    diaryCalendarSaveHistoricalDataAlert: 'Are you sure you want to save changes to historical data?',
    diaryCalendarSavePastDayChangesMsg: 'Your changes may affect calculations for past bookings. You can\'t undo this.',
    diaryCalendarStandardWorkPatternLabel: 'Standard work pattern',
    diaryCalendarCustomDaysGridHeading: 'Custom days',
    diaryCalendarCustomPeriodsGridHeading: 'Custom periods',
    diaryCalendarCustomDaysAddBtn: 'Add custom day',
    diaryCalendarCustomPeriodsAddBtn: 'Add custom period',
    diaryCalendarCustomDayNamePlaceholder: 'Name of custom day',
    diaryCalendarCustomPeriodNamePlaceholder: 'Name of custom period',
    diaryCalendarCustomDayTypePlaceholder: 'Day type of custom day',
    diaryCalendarCustomWorkPatternPlaceholder: 'Work pattern of custom period',
    diaryCalendarCustomDayIsInUseMessage: 'This overlaps with an existing custom day',
    diaryCalendarCustomPeriodIsInUseMessage: 'This overlaps with an existing custom period',
    diaryCalendarCustomGridDateRequiredMessage: 'Please provide date',
    diaryCalendarCustomGridRangeRequiredMessage: 'Please provide date range',
    diaryCalendarCustomGridNameRequiredMessage: 'Please provide name',
    diaryCalendarCustomGridDayTypesRequiredMessage: 'Please provide day type',
    diaryCalendarCustomGridWorkPatternsRequiredMessage: 'Please provide work pattern',
    diaryCalendarTotalHourSumErrorMessage: 'Total hours must be less than or equal to 24 hours',
    diaryCalendarHoursAginstDurationError: 'Start and end time must be inclusive of the number of hours assigned',
    diaryCalendarCustomDayPlaceholder: 'Please Enter Custom Day Name',
    diaryCalendarDayTypePlaceholder: 'Select Day Type',
    diaryCalendarCustomPeriodPlaceholder: 'Please Enter Custom Period Name',
    diaryCalendarWorkPatternPlaceholder: 'Select Work Pattern',
    diaryCalendarRangeDateLabel: 'Start and end Date',
    diaryCalendarPatternLabel: 'Pattern',
    diaryNameRequiredMessage: 'Please provide a diary name',
    uniqueDiaryNameMessage: 'Please enter a unique diary name',
    diaryWarningTitle: 'Can\'t delete diary calendar',
    diaryWarningMessage: 'Can\'t delete diary "{diaryNames}". This diary is allocated to resource.',
    //button labels
    confirmButtonLabel: 'Confirm',
    cancelButtonLabel: 'Cancel',
    saveButtonLabel: 'Save',
    okButtonLabel: 'OK',
    //table headings
    diaryCalendarCustomDaysDateTableHeading: 'Date',
    //same value need to check
    diaryCalendarCustomDaysNameTableHeading: 'Name',
    diaryCalendarCustomPeriodsRangeTableHeading: 'Start and end date',
    diaryCalendarCustomPeriodsWorkPatternTableHeading: 'Work pattern',
    //work pattern
    workPatternHeading: 'Work patterns',
    workPatternCommandBarFieldName: 'Edit work pattern name',
    workPatternSubHeading: 'A work pattern can be created by selecting a starting day and individual day types. Add, edit or delete work patterns here.',
    //for confirmation modal popup need to change later
    workPatternSaveInUseAlert: 'Are you sure you want to save changes for in use work pattern?',
    workPatternSaveInUseChangesMessage: 'Your changes may affect diary calendar. You can\'t undo this.',
    workPatternAddButton: 'Add a day',
    workPatternReqValidation: 'Please select a day type',
    workPatternUniqueMsg: 'Please enter a unique Work Pattern name',
    workPatternReqdMsg: 'Please provide a Work Pattern name',
    workPatternStartDayLabel: 'Starting day',
    workPatternDayTableHead: 'Day',
    workPatternWarningMessage: 'To delete it, remove from diaries.',
    workPatternWarningTitle: 'You can\'t delete {selection} work pattern as it is in use.',
    //Day Types
    dayTypePageHeading: 'Day types',
    dayTypeHeading: 'Day type',
    dayTypeCommandBarFieldName: 'Edit day type name',
    dayTypeSubHeading: 'Set up the type (working day or non-working day) to represent the regular hours of your organisation.',
    //for confirmation modal popup need to change later
    dayTypeSaveInUseAlert: 'Are you sure you want to save changes for in use day type?',
    dayTypeSaveInUseChangesMessage: 'Your changes may affect diary calendar. You can\'t undo this.',
    dayTypeWorkingHoursRequiredMessage: 'Please enter work time',
    dayTypeContingencyTimeRequiredMessage: 'Please enter contingency time',
    dayTypeWorkingHoursCannotZeroMessage: 'Work time cannot be 00:00',
    dayTypeWorkDayLabel: 'Work day',
    dayTypeNonWorkDayLabel: 'Non-work day',
    dayTypeWorkTimeLabel: 'Work time',
    dayTypeContingencyTimeHours: 'Contingency time',
    dayTypeTitleRequiredMessage: 'Please provide Day type name',
    dayTypeTitleUniqueMessage: 'Please enter a unique Day type',
    dayTypeWarningTitle: 'You can\'t delete {selection} day type as it is in use.',
    dayTypesWarningMessage: 'To delete it, remove from work pattern.',
    //entity import
    entityImportPageHeader: 'Import data',
    entityImportPageSummaryText: 'Import data into your application using two simple steps:<ol><li>Download the relevant template and fill data into the template ensuring that mandatory fields are filled in</li><li>Upload the template which you have filled in</li></ol>Data that you have filled in the template will be imported into the application.',
    entityImportDownloadTemplatesHeader: 'Download a template',
    entityImportDownloadTemplatesNumber: '1',
    entityImportUploadDataHeader: 'Upload the template that you have filled in',
    entityImportUploadDataNumber: '2',
    entityImportJobLabel: 'Jobs',
    entityImportClientLabel: 'Clients',
    entityImportResourceLabel: 'Resources',
    entityImportSkillLabel: 'Skills',
    entityImportUploadControlText: 'Click or drag a file to this area to upload',
    entityImportSelectUploadFileLabel: 'Select a file to upload',
    entityImportImportSuccessful: 'Import successful',
    entityImportImportUnsuccessful: 'Import unsuccessful',
    entityImportTemplateDownloadFailed: 'unable to download template',
    entityImportUploadControlError: 'Please select file to upload',
    entityImportUploadDropDownError: 'Please select type',
    entityImportUploadDropDownPlaceholder: 'Choose a type of data',
    entityImportFileUploadFailed: 'file upload failed.',
    entityImportTypeOfData: 'Type of data',
    entityImportUploadAndVerify: 'Upload and verify',
    entityImportCancelBtn: 'Cancel',
    entityImportConfirmImport: 'Confirm import',
    entityImportFormValidateMsg: 'Click to pre-validate before import',
    entityImportmailSubject: 'Retain Cloud licence limit',
    entityImportUploadProcessed: '{EntriesProcessedCnt} {currentEntityType} entries processed.',
    entityImportTemplateFileName: 'EntityImportTemplate',
    processFormErrorCorrectionText: 'To complete the import, please correct the following errors.',
    processFormAlternateOption: 'Alternatively you may also choose to delete them or cancel the update, update the Microsoft Excel and re-upload it.',
    processFormRowNoFromExcelMsg: 'The corresponding row number from the Microsoft Excel file are shown below.',
    processFormRequiredClientNameField: 'The Client name field is required.',
    processFormRequiredClientCodeField: 'The Client code field is required.',
    processFormRequiredJobTitleField: 'The job title field is required.',
    processFormRequiredSkillNameField: 'The Skill Name field is required.',
    processFormRequiredSkillInfoField: 'The Skill Info field is required.',
    processFormRequiredSkillSectionField: 'The Skill Section field is required.',
    processFormRequiredFirstNameField: 'The First Name field is required.',
    processFormRequiredLastNameField: 'The Last Name field is required.',
    processFormRequiredEmailField: 'The Email field is required.',
    processFormProcessed: ' processed.',
    processFormProcessedWithError: ' processed with errors.',
    processFormProcessedWithNoError: '  processed with no errors.',
    processFormWithError: ' with errors',
    processFormWithNoError: ' with no errors',
    processFormLicenseUserContError: 'These {currentEntityType} entries cannot be imported.',
    processFormLicenseUserDivLine1: 'Your license supports upto <b>{allowedActiveUsersCount}</b> Active users.',
    processFormLicenseUserDivLine2_1: 'This import will result in {totalRecordsProcessed} Active users.',
    processFormContactUs: '{contactUs}',
    processFormLicenseUserDivLine2_2: 'to increase your limit.',
    processFormLicenseUserContErrorAlert: 'Something went wrong. Import cannot be processed',
    processFormContactUsText: 'Contact us',
    //color scheme
    colourSchemeHeader: 'Colour theme',
    colourSchemeFieldName: 'Edit colour theme name',
    colourSchemeHeaderAddButtonText: 'Add a colour rule',
    colourSchemeSummaryText: 'Display each type of booking in a different colour by selecting your preferred colour for each relevant field.',
    colourSchemeRolesSummaryText: 'Display roles in a different colour by selecting your preferred colour for each relevant field.',
    colourSchemeConfirmModalText: 'Changing the field will remove all existing colour theme rules. Continue?',
    colourSchemeTableRequired: 'Please select table',
    colourSchemeFieldRequired: 'Please select field',
    colourSchemeGridEmptyText: 'No colour rules have been created',
    colourSchemeGridAddButtonText: 'Add a rule',
    colourSchemePreviewText: 'Preview text',
    colourSchemeFieldValueRequired: 'Please select field value from list',
    colourSchemeFieldValueUnique: 'Field value should be unique',
    colourSchemeLookUpNoMatches: 'No matches',
    colourSchemeSelectValuePlaceholder: 'Select value',
    colourSchemeResourceLookupPlaceholder: 'Unassigned',
    colourSchemeColourSchemeAddButton: 'Add a colour rule',
    colourSchemeTable: 'Table',
    colourSchemeField: 'Field',
    colourSchemePreviewTextTitle: 'Preview',
    colourSchemeColourCodeTextTitle: 'Colour',
    colourSchemeCreateColorTheme: 'Create colour theme',
    colourSchemeFieldDropdownPlaceholder: 'Choose field',
    colourSchemeTableDropdownPlaceholder: 'None',
    colorSchemeUniqueTitle: 'Please enter a unique Colour theme name',
    colorSchemeRequiredTitle: 'Please provide a Colour theme name',
    colourThemeTabTitle_Bookings: 'Bookings',
    colourThemeTabTitle_Roles: 'Roles',
    colourSchemeDescriptionPlaceholder: 'Colour description',
    //conflicts
    conflictPageHeader: 'Conflicts',
    conflictsMsgsSubHeaderLabel: 'When to show conflicts.',
    conflictsMsgsSubLabel_line1: 'Choose whether to display conflicts. Select the threshold equal to and above which one or more bookings assigned to a resource will show up as a conflict.',
    conflictsMsgsResourceLoadingControlLabel: 'When Resource loading is at least',
    conflictsMsgsShowConflictsLabel: 'Show conflicts',
    conflictsMsgsConfermationModelHeader: 'Please enter a valid loading threshold.',
    conflictsMsgsConfermationModelSubHeader: 'The loading threshold should be between {minValue} and {maxValue}%.',
    conflictsMsgsShowResourceLoadingNote: 'Accepted loading values are {minValue} to {maxValue}%.',
    conflictsMsgsOkBtn: 'OK',
    conflictsMsgsYesText: 'Yes',
    conflictsMsgsNoText: 'No',

    //Service accounts
    serviceAccounts: {
        maximumFieldLengthValidationMessage: 'Maximum ${maximumFieldSize} characters',
        pageHeader: 'Service account management',
        addEntity: 'Add a service account',
        saveButtonLabel: 'Save changes',
        cancelButtonLabel: 'Cancel',
        markedForDeletionMessage: 'Marked for deletion. Will be deleted when you confirm the changes. ',
        cancelDeletion: 'Cancel Deletion',
        serviceAccountManagementTitle: 'Service account management',
        serviceAccountsSubTitle: 'Active service accounts',
        serviceAccountDescription: 'Want to build something that integrates with and extends Retain? Add, edit or delete service accounts here.',
        serviceAccountNavigateToRetainApiDocumentationMessage: 'You can also read more about <linkText>Retain Cloud APIs</linkText> in our help documentation.',
        serviceAccountManagementUsedAccountsWarning: 'You are nearing the limit of 5 service accounts that can be added. Manage existing accounts or remove unused ones.',
        serviceAccountManagementUsedAccountsError: 'You have reached the limit of 5 service accounts that can be added. Manage existing accounts or remove unused ones.',
        emptyStateMessage: 'No results',
        setPassword: 'Set password',
        password: 'Password',
        name: 'Name',
        tenant: 'Tenant',
        securityProfile: 'Security profile',
        email: 'Email',
        savePasswordTooltip: 'Please save changes before setting a password',
        nameValidationMessage: 'Please enter name',
        emailValidationMessage: 'Please input your E-mail',
        typeHerePlaceholder: 'Type here',
        nameColumnTitle: 'Name',
        emailColumnTitle: 'Email',
        securityProfileColumnTitle: 'Security profile',
        actionsColumnTitle: 'Actions',
        emailExplanation: 'Enter a unique email not in use for an existing account',
        formHasErrorsMessage: 'This form has errors'
    },

    //workflows
    workflowsSettings: {
        roleByNameWorkflowPageHeader: 'Roles by name',
        roleByRequirementsWorkflowPageHeader: 'Roles by requirements',
        rolesByNamePageDescriptionLabel: 'Roles can be used to request a resource on a job. Roles by name are used when you know the resource you want.',
        rolesByRequirementsPageDescriptionLabel: 'Roles can be used to request a resource on a job. Roles by requirements lets you submit requirements to find a matching resource.',
        draftStateDescription: 'Roles can be saved as draft if they are not ready to be submitted as requests.',
        requestedStateDescription: 'Roles that can be made live as bookings, or rejected.',
        liveStateDescription: 'Roles that have been booked.',
        rejectedStateDescription: 'Roles that have been rejected.',
        archivedStateDescription: 'Roles that no longer require action.',
        statesLegendTitle: 'Workflow states',
        actorsSectionTitle: 'Actors in workflow',
        actorsSectionDescription: 'Define what each actor can do in the workflow.',
        requesterActorTitle: 'Requester',
        roleByNameRequesterActorDescription: 'Creates roles to submit requests on a job.',
        roleByRequirementsRequesterActorDescription: 'Creates roles to submit requests with a set of requirements.',
        whoCanCreateRolesLabel: 'Anyone can create roles for',
        roleByRequirementsWhoCanCreateRolesLabel: 'Anyone can create roles on',
        whoCanCreateRolesForThemselvesLabel: 'Anyone can create roles for themselves on',
        creatorActionsInfoBannerLabel: 'The below actions are available only to the creator of the role.',
        deleteRolesWithAssigneesInfoBannerLabel: 'To delete roles with multiple assignees, the user must meet this condition for all assignees.',
        draftRolesActionsTitle: 'Draft',
        requesterCanEditDeleteLabel: 'Edit and delete draft roles',
        requesterCanSubmitLabel: 'Submit request',
        requesterCanArchiveLabel: 'Archive',
        requestedRolesActionsTitle: 'Requested',
        requesterCanRestartRequestedLabel: 'Restart',
        requesterCanDeleteRequestedLabel: 'Delete requested roles',
        restartingActionsTitle: 'Restarting',
        requesterCanRestartRejectedLabel: 'Restart rejected roles',
        requesterCanRestartArchivedLabel: 'Restart archived roles',
        completedRolesActionsTitle: 'Completed roles',
        requesterCanDeleteLiveLabel: 'Delete live roles',
        requesterCanDeleteRejectedLabel: 'Delete rejected roles',
        requesterCanDeleteArchivedLabel: 'Delete archived roles',
        assignerActorTitle: 'Assigner',
        assignerActorDescription: 'Assigns resources to requested roles based on the requirements.',
        assignerWhoCanRespondLabel: 'can assign roles',
        assignerCanAssignResourcesLabel: 'Assign resources to roles',
        approverActorTitle: 'Approver',
        approverActorDescription: 'Responds to requests.',
        appproverWhoCanRespondLabel: 'can respond to requests for',
        approverCanMakeLiveLabel: 'Make live',
        criteriaRoleCanMakeLiveLabel: 'Make a role or assignee live',
        approverCanRejectLabel: 'Reject',
        criteriaRoleCanRejectLabel: 'Reject a role or assignee',
        approverCanRestartLabel: 'Restart',
        approverCanDeleteRequestedLabel: 'Delete requested roles',
        approverCanDeleteLiveLabel: 'Delete live roles',
        approverCanDeleteRejectedLabel: 'Delete rejected roles',
        approverCanDeleteArchivedLabel: 'Delete archived roles',
        approverSelectedResourcesInvalidValue: 'Max ${maxLimitCount} resources. If more approvers are needed, consider using \'Selected security profiles\' instead.',
        assignerSelectedResourcesInvalidValue: 'Max ${maxLimitCount} resources. If more assigners are needed, consider using \'Selected security profiles\' instead.',
        requesterSelectedResourcesInvalidValue: 'Max ${maxLimitCount} resources. If more requesters are needed, consider using \'Selected security profiles\' instead.',
        selectedSecurityProfilesInvalidValue: 'Max ${maxLimitCount} security profiles',
        addAnotherPrefix: 'Add a',
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'was found with this name.',
        multiValueFieldErrorMessagePrefix: 'Select at least one',
        saveButtonLabel: 'Save changes',
        cancelButtonLabel: 'Cancel',
        formHasErrorsMessage: 'Form has errors',
        noResultsFoundMessage: 'No results found',
        roleCreatorLabel: 'The role creator and',
        whoCanActAsRequesterLabel: 'can act as a requesters of a role.'
    },
    //report settings
    reportSettingsSubLabel: 'Set targets for your organisation',
    reportSettingsErrorTitle: 'Incorrect target value',
    reportSettingsBillabillityLabel: 'Global chargeable utilisation target',
    reportSettingsBillabilityRule: 'Set a global target to compare chargeable utilisation actuals against',
    reportSettingsJobOpportunityLabel: 'Job opportunity threshold',
    reportSettingsJobOpportunityRule: 'Minimum opportunity percent to count a job as billable',
    reportSettingsFieldTitle: 'Chargeable Utilisation',

    //Colour schemes
    deleteColourSchemeTitle: 'Delete "{itemTobeDeleted}" colour theme?',
    deleteColourSchemeInformationMessage: 'Any plans that use this theme will revert to the default theme. ',
    deleteColourSchemeWarningMessage: 'This action cannot be undone.',
    colourSchemeCheckMessage: 'I understand the impact of deleting this colour theme.',
    deleteColourSchemePrimaryButton: 'Delete colour theme',
    deleteColourSchemeSecondaryButton: 'Keep colour theme',

    colorPickerText: 'Colour Picker',

    //security Profile
    securityProfilePageHeader: 'Security profiles',
    securityProfileFieldName: 'Edit security profile name',
    functionalAccessHeading: 'General',
    functionalAccessSubHeading: 'Controls which pages and functionality this Security Profile has access to. For example, you may use a Functional Access rule to block access to the Administration Settings.',
    yesLabel: 'Yes',
    noLabel: 'No',
    entityAccessSubHeading: 'Control the level of access this security profile has to {entityName}s.',
    skillEntitySubHeading: 'Control the level of access this security profile has to {entityName}.',
    entityAccessSubHeadingRemaining: 'Turn access on or off or set levels of access for more granular control.',
    readEntityResource: '{entityName}s are always visible to users but you can restrict visibility of certain {entityName}s by using the controls below. {lineBreak} Read security will supersede all other security rules.',
    readEntityJob: '{entityName}s are always visible to users but you can restrict visibility of certain {entityName}s by using the controls below. {lineBreak} Users will always be able to see the Jobs that they are booked on. Read security will supersede all other security rules.',
    readEntityBooking: '{entityName}s are visible depending on the read conditions set for the Job and Resource. Read security will supersede all other security rules.',
    readEntityRole: '{entityName}s are visible depending on the read conditions set for the Job and Resource. Read security will supersede all other security rules.',
    readEntityScenario: '{entityName}s are always visible to all users. Read security will supersede all other security rules.',
    readEntityRoleRequest: '{entityName}s are always visible to all users. Read security will supersede all other security rules.',
    readEntityClient: '{entityName}s are always visible to all users. Read security will supersede all other security rules.',
    readEntitySkill: 'Skills & Certifications are always visible to users, but you can restrict which skills & certifications they can add to their own profile by using the controls below.',
    createEntity: 'Turning this off will hide options from the interface and block this Security Profile from creating {entityName}s.',
    editEntity: 'Turning this off will hide options from the interface and block this Security Profile from editing {entityName}s.',
    deleteEntity: 'Turning this off will hide options from the interface and block this Security profile from deleting {entityName}s.',
    customConditionsAreaHeader: '{entityName}s which...',
    liveRoleSubHeading: 'Live roles',
    liveRoleBookingMessage: 'Creating live bookings from roles is controlled by booking permissions, please see the ',
    workflowCardSubHeading: 'Role workflow',
    workflowCardMessage: 'Manage who can request, approve and take other actions on roles via role workflow settings.',
    workflowCardBtnText: 'Go to role workflows',
    workflowTurnedOff: 'Turn on \'Workflows\' in the General tab of your security profile to access this page.',
    subRuleCreateRequest: 'Turning this off will hide options from the interface and block this Security Profile from creating requests.',
    subRuleRejectRequest: 'Turning this off will hide options from the interface and block this Security Profile from rejecting requests.',
    subRuleAssignCriteriaRoles: 'Turning this off will hide options from the interface and block this Security Profile from assigning suggested resources to criteria roles.',
    skillReadRuleCondition: 'Which skills & certifications can they add to their own profile?',
    readRuleCondition: 'Which {entityName}s can they see?',
    createRuleCondition: 'Which {entityName}s can they create?',
    editRuleCondition: 'Which {entityName}s can they edit?',
    deleteRuleCondition: 'Which {entityName}s can they delete?',
    securityProfileNameRequiredMsg: 'Please provide a security profile name',
    uniqueSecurityProfileNameMsg: 'Please enter a unique security profile name',
    delete_failureWarningTitle: 'Can\'t delete security profile',
    delete_failureWarningMessage: 'Can\'t delete security profile {profileName}. This security profile is in use.',
    delete_failureButtonLabel: 'OK',
    fieldSecurityHeading: '{entityName} fields',
    fieldSecuritySubHeading: 'Set which {entityName} fields this Security profile can interact with. By default fields are editable but they can also be set to read only or hidden.',
    fieldSecurityInfoForCondition: 'For',
    fieldSecurityInfoThisFieldIsCondition: 'This field is',
    fieldSecurityInfoOtherwiseCondition: 'Otherwise the field is',
    mandatoryNotification: ' This is a mandatory field. Restricting a mandatory field can have unexpected results.',
    editAccessNotification: ' Edit access has been turned off for {entityName}s. Selecting \'Editable\' will have no effect on the field access.',
    readOnlyNotification: ' This is a system read only field and cannot be made editable',
    externalIdNotificationMessage: ' This field is editable only through the API Portal',
    note: 'Note',
    important: 'Important',
    emptyFieldSecurityViewMessage: 'No fields have been added yet',
    accessLevel: 'Access Level',
    tabMessage: '{tabName} tab',
    skillCategoriesLabel: 'Skill Categories',
    departmentLabel: 'Departments',
    divisionLabel: 'Divisions',
    skillEntityTypeLabel: 'Skill types',
    serviceLineLabel: 'Service Lines',
    skillsCertificationLabel: 'Skills & Certifications',
    viewbudgetEntity: 'Allow users to view estimated budget values on roles and the actual values once resources are assigned',
    managerApprovalAlert: 'Users must have a manager set in \'Reports to\' field or they will not be able to update their skills',
    managerApprovalSubHeading: 'For users with this security profile any changes they make to their own skills will need to be approved by their manager. Skill preferences can be changed without approval',
    customConditions: {
        operators: {
            Int: {
                LessThan: 'Less than',
                LessThanOrEqual: 'Less than or equals',
                Equals: 'Equals',
                GreaterThanOrEqual: 'Greater than or equals',
                GreaterThan: 'Greater than',
                NOT_EQUALS_OPERATOR: 'Does not equal'
            },
            DateTime: {
                LessThanOrEqual: 'Before',
                GreaterThanOrEqual: 'After'
            },
            ID: {
                IN_OPERATOR: 'Is one of',
                NOT_IN_OPERATOR: 'Is not one of'
            },
            Bool: {
                Equals: 'Equals'
            }
        },
        valueTypes: {
            relativeToToday: 'Relative to today',
            blank: 'Blank',
            selectedValues: 'Selected values',
            loggedInUserValue: 'Logged-in user value',
            existingValue: 'Existing value',
            customValue: 'Custom value'
        },
        relativeDateValues: {
            PLUS_180: 'Today +180 days',
            PLUS_90: 'Today +90 days',
            PLUS_28: 'Today +28 days',
            PLUS_7: 'Today +7 days',
            PLUS_1: 'Tomorrow',
            TODAY: 'Today',
            MINUS_1: 'Yesterday',
            MINUS_7: 'Today -7 days',
            MINUS_28: 'Today -28 days',
            MINUS_90: 'Today -90 days',
            MINUS_180: 'Today -180 days'
        },
        noConditionOperatorError: 'Please select operator',
        noConditionValueTypeError: 'Please select value type',
        addConditionsListButtonLabel: '+ Add AND condition',
        andOperatorLabel: 'AND',
        maxConditionsCountLabel: ' 3 maximum',
        addConditionRowButtonLabel: '+ Add OR condition',
        orOperatorLabel: 'OR',
        fieldHeaderLabel: 'Field',
        operatorHeaderLabel: 'Operator',
        valueTypeHeaderLabel: 'Value type',
        valueHeaderLabel: 'Value',
        noResultsFoundMessage: 'No results found',
        pleaseEnterFieldLabel: 'Please enter field',
        conditionFieldNamePlaceholder: 'Select field...',
        yesLabel: 'Yes',
        noLabel: 'No',
        wholeNumberInputError: 'Please enter valid value',
        commonPredefinedConditionsLabel: 'Use predefined condition',
        inheritReadPredefinedConditionsLabel: 'Use only read conditions set for Job and Resource',
        commonCustomConditionLabel: 'Create custom condition',
        inheritReadCustomConditionLabel: 'Add custom condition on top of read conditions set for Job and Resource',
        addAnotherPrefix: 'Add',
        deleteRowLabel: 'Delete row',
        pleaseEnterValueLabel: 'Please enter value',
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'was found with this name.',
        jsonConditionLabel: 'Specify custom JSON rule',
        jsonConditionWarningBannerText: 'Custom JSON rules should be used with caution as they can lead to a performance hit.\nMisconfiguration of JSON rules may lead to instability issues.',
        invalidJsonErrorMessage: 'Please ensure the JSON is valid.',
        apiPortalInfoText: 'Automatically validate JSON rules by clicking away from the text area',
        apiPortalLabel: 'API Portal.',
        inheritReadJsonConditionLabel: 'Specify custom JSON rule on top of read conditions set for Job and Resource.',
        //Skills
        addSkillHeader: 'Add skills & certifications to profile page',
        allSkillsLabel: 'All skills & certifications',
        onlyTheseSkillsLabel: 'Only these',
        onlyRelatedSkillsLabel: 'Only skills & certifications related to these',
        emptySkillErrorMessage: 'Please select at least one value',
        emptyJsonConditionErrorMessage: 'Please select at least one of the option'
    },
    //entitiesConfiguration
    planningDataAliasUseCaseInfo: 'An alias is a more familiar term that can be used throughout your application. Add aliases to make unfamiliar terms more recognisable.',
    aliasHeading: 'Alias',
    singularAliasLabel: 'Singular alias',
    pluralAliasLabel: 'Plural alias',
    BookingDescription: 'A booking represents an allocation of a resource to a job for a specific date range and is of a measurable number of hours. Bookings can be created for jobs such as Project Management or Testing and also for jobs such as holidays or sick leave. Every booking must be made against a job.',
    JobDescription: 'A job is a project, task or piece of work that requires a resource. A job may be created for activities such as Project Management or Testing, and also for activities such as vacation or sick leave.',
    ResourceDescription: 'Resource refers to materials, staff, or other assets that can be utilised by an organisation to function effectively. Any item with a finite capacity that can perform a job might be considered a resource. Staff, training rooms and machinery are examples of resources.',
    ClientDescription: 'Clients refers to your organisation’s clients or customers. A client may have several jobs.',
    rolerequestgroupDescription: 'A scenario is a collection of roles for a given job.',
    rolerequestDescription: 'A role is a precursor to one or more bookings. They may be created with a resource in mind or with certain criteria in mind.',
    DepartmentDescription: 'A department is a segment of an organisation specialising in a particular business process, often consisting of resources with similar skill sets and responsibilities',
    DivisionDescription: 'A division is a top level business unit or segment of an organisation and will be made up of multiple departments',
    //Skill Types Message
    //Levels
    skillPageHeader: 'Skills & Certifications',
    skillTypeLevelsTabTitle: 'Levels',
    skillTypeSkillsTabTitle: 'Skills',
    fieldsTabTitle: 'Fields',
    addSkill: 'Add a skill',
    levelNameDescriptionText: 'Levels for this skill category are known as',
    levelNameInfoText: '{levelName} 1 is the lowest level.',
    addLevels: 'Add {levelName}',
    skillTypeLevelNameRequiredMessage: 'Please enter a level name',
    whiteSpaceValidation: 'Only whitespace not allowed in option',
    skillLevelRequiredValidation: 'Level name is required',
    skillLevelUniqueValidation: 'Level name already exist',
    skillLevelNamePlaceholder: 'Level name',
    skillLevelDescriptionPlaceholder: 'Level description',
    //Skill fields
    skillFieldHeading: 'Additional fields for this skill category',
    addFieldButtonLabel: 'Add a field',
    skillCommandBarFieldName: 'Edit skill name',
    //reusable grid
    cancelDeletion: 'Cancel Deletion',
    markedForDeletion: 'Marked for deletion. Will be deleted when you confirm the changes. ',
    addButtonReusableGrid: 'Add a row',
    mandatory: 'Mandatory',
    markDeleteWarningTitle: 'Are you sure you want to mark "{recordName}" for deletion?',
    markDeleteWarningTitleDefault: 'Are you sure you want to mark this record for deletion?',
    //Field Properties
    fieldFormattingDisabledTooltipText: 'Cannot be changed on built-in fields',
    fieldPropetiesAliasDefinition: 'An alias is a more familiar term that can be used throughout your application.',
    fieldPropetiesChooseAliasMessage: 'Choose an alias for the selected table and enter labels for field names that can be used throughout your application.',
    noDescriptionMsg: 'Click here to add a description',
    configurePageField: '{pageTitle} fields',
    descriptionText: 'Description',
    builtInTabsTitle: 'Built-in fields',
    customFieldTabsTitle: 'Custom fields',
    builtInTabsDescription: 'These are the default {entity} fields built into the system. These cant be changed, but they can be hidden if you don\'t need them.',
    customFieldTabsDesc: 'These are fields you have added to the application. They will be displayed alongside the system {entity} fields.',
    builtInFieldTab: 'Built-in',
    customText: 'Custom',
    customFieldAddButton: 'Add custom field',
    emptyCustomViewMessage: 'No custom fields have been added yet',
    emptyBuiltInViewMessage: 'No built-in fields have been added yet',
    fieldLabelReqdValidation: 'The field label cannot be empty',
    uniqueFieldNameValidation: 'The ${label} must be unique',
    bracketsFieldNameValidation: 'The ${label} cannot have [ or ]',
    lookupFieldInputRequired: 'The lookup field cannot be empty',
    noMatchesText: 'No matches',
    lookUpField: 'Values from',
    lookUpLinkText: 'Lookup fields configuration',
    maxCharMessage: 'Maximum of {maxChar} characters',
    maxCharTagMessage: 'Limit only applied to user-defined tags. Max {maxChar}',
    incorrectDecimalFormatMsg: 'Incorrect Decimal Format (Enter numeric value with maximum 10 decimal places)',
    incorrectTargetBillabilityMessage: 'Minimum allowed value is 0',
    inputBeyondLimitMsg: 'Input value is beyond the limit',
    noOfChars: 'Number of Characters',
    typeText: 'Type',
    labelText: 'Label',
    hiddenText: 'Hidden',
    decimalPlacesText: 'Decimal Places',
    exampleText: 'Example',
    lookupValuesLabel: 'Lookup values',
    newFieldValuesLabel: 'Name of new list',
    nextLabel: 'Next',
    prevLabel: 'Previous',
    fieldValuesTitle: 'Field values',
    fieldValuesSubTitle: 'Specify the list of values for this field',
    lookupValuesNotifyMessage: 'This cannot be changed after creating the field.{lineBreak} Values in the list can later be edited on the \'Values\' page.',
    newFieldNameRequiredMessage: 'Field value name is required',
    fieldAliasRequiredValidation: '{fieldAlias} alias is required',
    fieldDescriptionRequiredValidation: '{fieldDescription} description is required',
    newFieldValuesRequiredMessage: 'Field value list cannot be empty',
    multiSelectText: 'Multi-select',
    valuesListLabel: 'Values List',
    useExistingValuesText: 'Use an existing list of values',
    createNewValuesListText: 'Create a new list of values',
    createNewValuesSaveInfo: 'New list will be saved to the \'Values\' page',
    planningDataSaveInfo: 'This cannot be changed after creating the field',
    formattingLabel: 'Formatting',
    minimumLabel: 'Min',
    maximumLabel: 'Max',
    valuesText: 'Values',
    valueFieldsCommandBarFieldName: 'Edit field value name',
    calculatedMessage: ' This field is automatically calculated from data in your application.',
    rangeLabel: 'Range',
    systemReadonlyMessage: 'This field is readonly and holds information updated by the application',
    systemRequiredMessage: 'This field is required by the system in order to create {entity}',
    fieldNameLabel: 'Field name',
    deletefieldsHeading: 'Delete fields',
    keepFieldsHeading: 'Keep fields',
    newText: 'New',
    fieldText: 'field',
    warningMessageWithFieldNames: 'You\'re about to delete the {fieldsArray} ',
    warningMessageMoreFields: 'and {number} more fields.Deleting these fields will delete any data already held in them. ',
    warningMessageLessFields: 'fields.Deleting these fields will delete any data already held in them.',
    deletefieldsHeadingConfirmation: 'Delete fields and their data',
    deleteSkillsButtonLabel: 'Delete skills',
    keepSelectedSkillsLabel: 'Keep selected skills',
    fieldPropertiesLicenseWarning: 'You are close to the limit permitted by your license. {contactUs} to increase the limit of your plan.',
    fieldPropertieslicenseError: 'You have reached the limit permitted by your license. {contactUs} to increase the limit of your plan.',
    showLookupLink: 'Show lookup values',
    lookupValueUniqueValidation: 'This value has already been added',
    pressEnterMessage: 'Press Enter to add value',
    defaultValueLabel: 'Default value',
    noDefaultValueStaticHeaderText: 'No default',
    multipleDefaultValueStaticHeaderText: 'Multiple values',
    noDefaultPlaceHolder: 'No default',
    invalidDefaultValueValidation: 'Invalid default value',
    defaultValueSelectOwnValueText: 'Select value or leave blank',
    defaultValueInheritValueText: 'Inherit value from logged in user',
    defaultValueInheritSummaryText: 'Inherit from user\'s',
    defaultValueFieldText: 'User\'s {fieldName} will be inherited',
    defaultValueFieldDisabledText: 'New list of lookup values will be available to set as a default once you save this field',
    inheritUserField: 'User\'s "{fieldName}"',
    yesMsg: 'Yes',
    noMsg: 'No',
    //Field look up values config
    fieldLookupValueAddButton: 'Add a value',
    noResultsText: 'No results',
    fieldLookupValuesDescription: 'Add, edit or delete values that are displayed in lists. For example, a list for Location may display values such as London, Paris and New York.',
    fieldLookupValuesEntityData: 'Fields currently using these values',
    saveFieldLookupValueAlert: 'Delete selected values?',
    saveFieldLookupValueWarning: 'Deleting values will remove them from lookup dropdowns and all other areas where these values are displayed.',
    fieldLookupCheckMessage: 'You have selected {deleteCount} values to be deleted.',
    yesDeleteValuesButtonLabel: 'Yes, delete the values',
    noKeepValuesButtonLabel: 'No, keep the values',
    optionIsRequiredMessage: 'Option is required',
    optionAlreadyExist: 'Option already exist',
    uniqueValueNameMessage: 'Please enter a unique Value name',
    requiredValueNameMessage: 'Please provide a Value name',
    fieldLookupValueInUseTitle: 'Value lists could not be deleted',
    fieldLookupValueInUseMessage: 'Value lists that are currently being used by a field cannot be deleted.',
    fieldLookupValueInUseSubMessage: 'You must delete any associated fields before deleting the Value lists.',
    fieldLookupValuesSortText: 'Order of values',
    fieldLookupValuesDefaultSortModeText: 'Alphabetic (A-Z)',
    fieldLookupValuesCustomSortModeText: 'Incremental (custom order)',
    fieldLookupValuesSortModeDescriptionText: 'Menus and lists follow this order when displaying this values',
    fieldLookupValuesCustomSortGridText: 'Place the least significant value first in the list',
    noneText: 'none',
    typeNewValue: 'Type a new value',
    //System Settings
    fieldPropertiesPageHeader: 'Fields',
    fieldLookupValuesPageHeader: 'Values',
    gridColoumnTitle_Option: 'Option',
    gridColoumnTitle_Target_Billability: 'Chargeable Utilization',
    //PageNames
    pageNameHeader: 'Page Names',
    pageNamesHeaderSummary: 'Select the pages and their display names that will be displayed on the left menu.',
    menuListTitle: 'Menu List',
    settingValMsg: 'Please input Setting Value',
    maxSizeFieldMsg: 'Max Size of field can be 40 characters',
    displayText: 'Display',
    enableText: 'Enable',
    summaryPageNameText: 'Logo - no visible label',
    Timesheets: 'Timesheets',
    Report: 'Report',
    Role_inbox: 'Role inbox',
    Roles_board: 'Roles board',
    Table_View: 'Table view',
    //dynamic generated child sections from setting api (System settings)
    Talent_Profile: 'Talent Profile',
    Jobs: 'Jobs',
    Scheduler: 'Scheduler',
    //Currencies
    currenciesPageHeader: 'Currencies',
    //charge codes
    chargeTypePageHeader: 'Charge types',
    whiteSpaceValidationChargeType: 'Only whitespace not allowed in charge type',
    chargeCodeContent: 'Charge types allow you to create different rates for different types of jobs. Create multiple charge types to cover the different types of work carried out in your company.',
    chargeRateContent: 'Add, edit or delete cost and revenue rates for different charge codes for specific time ranges {ChargeRateLinkText}',
    chargeRateRedirection: 'here.',
    chargeCodeUsageWarning: 'This charge type is currently being <strong>used by {chargeTypeJobCount} jobs.</strong>',
    chargeCodeWarning: 'If you delete this charge type, it will be removed from all records and cannot be used for budget information and reporting.',
    chargeTypeDeletionWarning: 'Delete Charge type {chargeTypeName}',
    deleteChargeTypePrimaryButton: 'Delete Charge Type',
    deleteChargeTypeSecondaryButton: 'Keep Charge Type',
    chargeCodeCheckMessage: 'I understand the impact of deleting this charge type',
    chargeCodeUniqueValidation: 'Please enter a unique charge type',
    chargeCodeReqdValidation: 'Charge type is required',
    chargeCodeAddButton: 'Add a charge type',
    noChargeCodeAvailable: 'No charge type available',
    waterMarkChargeCode: 'Untitled charge type',
    waterMarkDescription: 'Description of the charge type',
    chargeTypeHeader: 'Charge type',

    //charge rates
    chargeRatePageHeader: 'Charge rates',
    hourlyChargeRatesHeading: 'Hourly charge rates per job type',
    waterMarkRevenue: 'Revenue of the charge rate',
    waterMarkCost: 'Cost of the charge rate',
    chargeRateDeleteMessage: 'You will remove this charge rate for any future use.',
    chargeRateDeleteSubMessage: 'This charge rate will be retained on existing bookings.',
    cantUndoMessage: 'You can\'t undo this.',
    checkMessage: 'I understand the consequences of this deletion',
    deleteChargeRateTitle: 'Delete "{deletedChargeRate}" charge rate?',
    deleteChargeCodeSpecificChargeRateTitle: 'Delete "{deletedChargeCodeName}" charge rate from "{deletedChargeRate}"?',
    deleteChargeRateHeading: 'This charge rate is currently being used by {chargeRateResourceCount} resources.',
    deleteChargeRateWarningMessage: 'If you delete this, it will be removed from all records and cannot be used for budget information and reporting.',
    deleteChargeRateCheckboxMessage: 'I understand the impact of deleting this charge rate',
    deleteChargeRateButtonLabel: 'Delete Charge rate',
    keepChargeRateButtonLabel: 'Keep Charge rate',
    editChargeRateTitle: 'Amend current "{editedChargeRateName}" charge rate?',
    editChargeRateHeading: 'This is the current charge rate for "{editedChargeRateName}" charge type.',
    editChargeRateSubHeading: 'If you change this charge rate, it will amend them for all calculations and reports for this period.',
    editChargeRateWarningMessage: 'Are you sure you want to change the current charge rate for "{editedChargeRateName}"?',
    editChargeRatePrimaryButton: 'Amend active rate',
    addCostRevenueButtonLabel: 'Add Cost and Revenue',
    customGridRangeOverlappingMessage: 'There is already a cost and revenue defined for dates in this range',
    customGridRangeRequiredMessage: 'Please provide date range',
    customGridRevenueRequiredMessage: 'Please provide Revenue',
    customGridCostRequiredMessage: 'Please provide cost',
    chargeRateNameField: 'Charge rate name',
    chargeRateTitleRequiredMessage: 'Please provide Charge rate name',
    chargeRateTitleUniqueMessage: 'Please enter a unique Charge rate name',
    customGridNegativeMessage: 'This value cannot be negative',
    dateRangeLabel: 'Date range',
    revenueLabel: 'Revenue',
    costLabel: 'Cost',
    //skills
    addSkillsHeading: 'Add skills for this skill category',
    activeSkillTitle: '\'{activeSkillConfiguration}\' is one of the skill sections that appear on a user\'s talent profile.',
    skillHeaderText: 'Skills under {activeSkillConfiguration}',
    skillHeaderDescription: 'Users will be able to choose the following skills under the \'{activeSkillConfiguration}\' section on their profiles.',
    skillImportMessage: 'For bulk updates, you may prefer using the {Import} feature',
    addSkillButtonFromCommandBar: 'Add a skill',
    skillNameLabel: 'Skill name',
    skillType: 'Type',
    addSkillCategoryText: 'Add category',
    skillCategory: 'Category',
    skillSubCategory: 'Subcategory',
    tagsLabel: 'Tags',
    skillTagExplaination: 'Tags are labels that can be attached to skills. Tags make finding a skill easier, especially when the skill may be known by a different name. Press enter after you have added each tag before saving.',
    skillNameRequired: 'Please enter skill name',
    uniqueSkillNameRequired: 'Please enter a unique Skill name',
    uniqueSkillItem: 'Skill name must be unique',
    deleteSkillFieldsPopupHeading: 'Delete selected fields?',
    deleteSkillFieldsPopupWarningMessage: 'Do you wish to permanently delete the selected skill fields? {number} skill fields will be deleted from this skill category(s) and removed from resource profiles, and any other areas where the skill field is displayed.',
    deleteSkillFieldsButtonLabel: 'Delete skill fields',
    keepSkillFieldsButtonLabel: 'Keep skill fields',
    deleteSkillLevelsPopupHeading: 'Delete selected levels?',
    deleteSkillLevelsPopupWarningMessage: 'Do you wish to permanently delete the selected skill levels? Deleted skill levels will be removed from resource profiles, and any other areas where these skill levels are displayed.',
    deleteSkillLevelsButtonLabel: 'Delete skill levels',
    keepSkillLevelsButtonLabel: 'Keep skill levels',
    deleteSkillsPopupHeading: 'Delete selected skills?',
    deleteSkillsPopupWarningMessage: 'Do you wish to permanently delete the selected skills? {number} skills will be deleted from the system and removed from users\' profiles, and any other areas where skills are displayed.',
    keepSkillsButtonLabel: 'Keep skills',
    deleteSkillsAndLevelsPopupHeading: 'Delete selected skills and skill levels?',
    deleteSkillsAndLevelsPopupWarningMessage: 'Do you wish to permanently delete the selected skills and skill levels?',
    deleteSkillsAndFieldsPopupHeading: 'Delete selected skills and skill fields?',
    deleteSkillsAndFieldsPopupWarningMessage: 'Do you wish to permanently delete the selected skills and skill fields?',
    deleteLevelsAndFieldsPopupHeading: 'Delete selected skill levels and skill fields?',
    deleteLevelsAndFieldsPopupWarningMessage: 'Do you wish to permanently delete the selected skill levels and skill fields?',
    deleteSkillsLevelsAndFieldsPopupHeading: 'Delete selected skills, skill levels and skill fields?',
    deleteSkillsLevelsAndFieldsPopupWarningMessage: 'Do you wish to permanently delete the selected skills, skill levels and skill fields?',
    deletePopupWarningMessage: 'Items will be deleted from this skill category and removed from resource profiles and any other areas where they are displayed.',
    deleteSkillsPopupConfirmation: 'I understand the consequences of this action.',
    deleteItemsButtonLabel: 'Delete these items',
    keepItemsButtonLabel: 'Keep these items',
    deleteAndModifySkillFieldsPopupHeading: 'Save Changes and Delete selected fields?',
    deleteAndModifySkillFieldsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skill fields? {number} skill fields will be deleted from this skill category(s) and removed from resource profiles, and any other areas where the skill field is displayed.',
    deleteAndModifySkillLevelsPopupHeading: 'Save Changes and Delete selected levels?',
    deleteAndModifySkillLevelsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skill levels? Deleted skill levels will be removed from resource profiles, and any other areas where these skill levels are displayed.',
    deleteAndModifySkillsPopupHeading: 'Save Changes and Delete selected skills?',
    deleteAndModifySkillsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skills? {number} skills will be deleted from the system and removed from users\' profiles, and any other areas where skills are displayed.',
    deleteAndModifySkillsAndLevelsPopupHeading: 'Save Changes and Delete selected skills and skill levels?',
    deleteAndModifySkillsAndLevelsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skills and skill levels?',
    deleteAndModifySkillsAndFieldsPopupHeading: 'Save Changes and Delete selected skills and skill fields?',
    deleteAndModifySkillsAndFieldsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skills and skill fields?',
    deleteAndModifyLevelsAndFieldsPopupHeading: 'Save Changes and Delete selected skill levels and skill fields?',
    deleteAndModifyLevelsAndFieldsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skill levels and skill fields?',
    deleteAndModifySkillsLevelsAndFieldsPopupHeading: 'Save Changes and Delete selected skills, skill levels and skill fields?',
    deleteAndModifySkillsLevelsAndFieldsPopupWarningMessage: 'Do you wish to save changes and permanently delete the selected skills, skill levels and skill fields?',
    skillCategoryDeletePopupHeading: 'Delete skill category?',
    skillCategoryDeletePopupWarningMessage: 'Do you wish to delete this skill category? This skill category contains {number} skills, which will all be unlinked and the levels and fields associated with this category will be deleted from the system and all the profiles which reference them.',
    deleteSkillCategoryButtonLabel: 'Delete skill category',
    keepSkillCategory: 'Keep skill category',
    fieldAlreadyExistHeader: 'Please enter a unique field name',
    fieldAlreadyExistDescription: 'This field name already exists. Please use the existing field, or rename this field to be unique.',
    skillNamePlaceholder: 'Enter skill name',
    skillDescriptionPlaceholder: 'Enter skill description',
    skillNameHeader: 'Skill Name',
    fieldTypeHeader: 'Field type',
    categoryText: 'Category',
    defaultValueText: 'Default Value',
    newTag: 'New Tag',
    headerLevels: 'Levels',
    licenseCountSkill: '{activeCount} of {licensedCount} skills used for this skill category',
    skillsLicenseWarning: 'You are close to your license\'s permitted limit for skills. {contactUs} to increase the limit of your plan.',
    skillsLicenseError: 'You have reached your license\'s permitted limit for skills. {contactUs} to increase the limit of your plan.',
    licenseCountSkillLevel: '{activeCount} of {licensedCount} levels used for this skill category',
    skillLevelsLicenseWarning: 'You are close to your license\'s permitted limit for levels in this skill category. {contactUs} to increase the limit of your plan.',
    skillLevelsLicenseError: 'You have reached your license\'s permitted limit for levels in this skill category. {contactUs} to increase the limit of your plan.',
    licenseCountSkillField: '{activeCount} of {licensedCount} fields used for this skill category',
    skillFieldsLicenseWarning: 'You are close to your license\'s permitted limit for fields in this skill category. {contactUs} to increase the limit of your plan.',
    skillFieldsLicenseError: 'You have reached your license\'s permitted limit for fields in this skill category. {contactUs} to increase the limit of your plan.',
    adminCommandBarActionLabel: 'Actions',
    adminCommandBarEditLabel: 'Edit',
    adminCommandBarUserStatusLabel: 'Active user',
    adminCommandBarSetActiveLabel: 'Set to active',
    adminCommandBarSetInactiveLabel: 'Set to inactive',
    adminCommandBarResendEmailInviteLabel: 'Send invite email',
    adminCommandBarResetPassphraseLabel: 'Reset password',
    disabledSkillType: 'Please save or discard changes before continuing',
    adminCommandBarSendCMeSurveyLabel: 'Send C-me survey',
    // Skill Category
    skillCategoryRequired: 'Please enter category name',
    uniqueCategoryNameRequired: 'Please enter a unique category name',
    primarySkillLabel: 'Primary skill',
    secondarySkillLabel: 'Secondary skill',
    preferredSkillLabel: 'Preferred skill',
    skillCategoryLabel: 'Skill Category',
    subscribed: 'Subscribed',
    notSubscribed: 'Not Subscribed',
    skillExpiryEnabledInfo: 'Set this to Yes to enable Skill expiry fields for this skill category',
    skillCategoryExpiryMandatoryInfo: 'Set this to Yes to enforce Skill expiry as a mandatory field',
    skillCategorySubscribeInfo: 'Subscribe to be notified of updates to this skill category',
    skillExpiryDateLabel: 'Skill expiry is mandatory',
    skillExpiryEnabledLabel: 'Enable skill expiry dates',
    preNotificationLabel: 'Pre-expiry notification',
    watcherLabel: 'Skill category watcher',
    //skill Library
    addSkillLibrary: 'Add from Retain\'s library',
    skillLibraryHeader: 'Retain skills library',
    importLibraryStep1: {
        title: 'Choose skills',
        description: 'Choose skills you would like to import'
    },
    importLibraryStep2: {
        title: 'Review',
        description: 'Review the skills you\'ve chosen'
    },
    importLibraryStep3: {
        title: 'Import',
        description: 'Ready to import'
    },
    existingSkillsHeader: 'Skills that have been previously added or have the same name as existing skills in your organisation are hidden from this list',
    selectedImportDataLabel: '<bold>${skillCount} skills chosen across ${categoryCount} categories</bold>',
    importClickInfo: 'Clicking <bold>Import</bold> will queue this import task',
    importProgressInfo: 'Please check the <bold>Operation log</bold> to see the progress of this task',
    importInProgressText: 'Importing skills from retain library is in progress',
    searchSkillPlaceholder:'Search skill',
    //Notifications
    notificationsPage: {
        commandBarConfig: {
            pageTitle: 'Notifications'
        },
        notificationSettingSummary: {
            notificationSettingSubHeading: 'Choose whether to show notifications by type.'
        },
        notificationSettingConfig: {
            onLabel: 'On',
            offLabel: 'Off'
        },
        notificationEvents: {
            bookingAssignedLabel: 'You have been assigned to ${jobName} from ${startDate} - ${endDate} ${timeAllocation} by ${editorName}',
            bookingUpdateLabel: 'Your booking on ${jobName} from ${startDate} - ${endDate} ${timeAllocation} has been edited by ${editorName}',
            bookingDeleteLabel: 'You are no longer assigned to ${jobName} from ${startDate} - ${endDate} ${timeAllocation}',
            roleRequestCreateLabel: '${resourceName} has been requested on ${jobName} ${startDate} - ${endDate} ${timeAllocation} by ${editorName}',
            roleRequestRejectLabel: 'Your request for ${resourceName} on ${jobName} ${startDate} - ${endDate} ${timeAllocation} has been rejected by ${editorName}',
            roleRequestLiveLabel: 'Your request for ${resourceName} on ${jobName} ${startDate} - ${endDate} ${timeAllocation} has been made live by ${editorName}',
            repeatBookingAssignedLabel: 'You have been assigned to ${jobName} from ${startDate} - ${endDate} ${timeAllocation} by ${editorName}. This booking repeats every ${interval} until ${untilDate}.',
            repeatBookingUpdateLabel: 'Your booking on ${jobName} from ${startDate} - ${endDate} ${timeAllocation} has been edited by ${editorName}. This booking repeats every ${interval} until ${untilDate}.',
            repeatBookingDeleteLabel: 'You are no longer assigned to ${jobName} from ${startDate} - ${endDate} ${timeAllocation}. This booking repeated every ${interval} until ${untilDate}.',
            loadingSectionSuffix: '% of working hours',
            timeSectionSuffix: 'hours booked in total',
            hoursPerDaySuffix: 'hours per day',
            resourceSkillExpiryLabel: 'You have skills set to expire in ${expiryDay} days ⏳ Renew it before the expiry date to keep your credentials up to date',
            resourceManagerSkillExpiryLabel: '${resourceName} has skills set to expire in ${expiryDay} days ⏳ Ensure they renew it before the expiry date to keep your credentials up to date',
            resourceSkillRecommendationLabel: 'Update your skills and get noticed! Here are some skills you could add to your profile',
            skillExpiryLabel: '${skillName} ${entityType} expires on ${expiryDate}',
            extraSkillExpiryLabel: '${count} more expiring soon'
        },
        notificationActions: {
            viewBookingLabel: 'View ${bookingSingularLowerAlias}',
            editBookingLabel: 'Edit ${bookingSingularLowerAlias}',
            viewPlansLabel: 'View plans',
            viewRoleGroupLabel: 'View in ${entitySingularLower}',
            makeLiveLabel: 'Make live',
            markAsReadLabel: 'Mark as read',
            markAsUnreadLabel: 'Mark as unread',
            deleteLabel: 'Delete'
        },
        notificationHistoryPageHeader: {
            informationMessage: 'Showing all notifications. Read notifications are automatically deleted after 30 days',
            markAllReadLabel: 'Mark all as read',
            loadMoreButtonLabel: 'Show more'
        },
        notificationTabs: {
            notificationHistoryLabel: 'History',
            notificationSettingsLabel: 'Settings'
        },
        notificationsSettings: {
            pageTitle: 'Notifications',
            notificationSettingsHeader: 'Default Notifications',
            notificationsSettingsAdminPageDescription: 'Set the default notification settings for new users.\n\nUsers can override these settings in Notifications > Settings to suit their needs',
            notificationsSettingsDescription: 'Choose what you want to be notified about and how',
            bookingNotifications: '${bookingSingularAlias} notifications',
            allNotifications: 'All notifications',
            bookingPost: 'When you are assigned to a ${bookingSingularAlias}',
            bookingPatch: 'When a ${bookingSingularAlias} assigned to you is updated',
            bookingDelete: 'When you are unassigned from a ${bookingSingularAlias} or it is deleted',
            emailFrequency: 'Email frequency',
            roleNotificaitons: '${rolerequestSingularAlias} notifications',
            roleRequestCreate: 'When a ${resourceSingularAlias} you manage is requested',
            roleRequestReject: 'When a ${rolerequestSingularAlias} you created is rejected',
            roleRequestLive: 'When a ${rolerequestSingularAlias} you created goes live',
            webApp: 'Web app',
            email: 'Email',
            globalNotificationSettingsHeaderTitle: 'Global notification settings',
            globalNotificationSettingsHeaderDescription: 'Manage notification settings for all users',
            globalNotificationSettingsToggleTitle: 'Notify users about \n\nunconfirmed bookings',
            globalNotificationSettingsToggleDescription: 'Include unconfirmed bookings in booking notifications',
            resourceSkillNotifications: 'Skill notifications',
            resourceSkillExpiry: 'When a skill you have is expiring',
            resourceManagerSkillExpiry: 'When a resource you manage has a skill that\'s expiring',
            resourceSkillRecommendation: 'Recommend skills to add to your profile based on your job title or primary skill'
        }
    },

    //Timesheets Page
    timesheetsPage: {
        noTimesheetsAvailable: 'You don\'t have any hours logged',
        jobLabel: 'Job',
        monday: 'Monday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
        sunday: 'Sunday',
        total: 'Total',
        hours_logged: 'hours logged',
        commandBarConfig: {
            pageTitle: 'Timesheets'
        },
        hoursRequiredLabel: 'This field is mandatory',
        maximumHourErrorLabel: 'Maximum 24',
        minimumHourErrorLabel: 'Minimum 0',
        showTimesheetsLabel: 'Show timesheet from',
        submitButtonLabel: 'Save',
        cancelButtonLabel: 'Cancel',
        timesheetDeletionWarning: 'Remove job?',
        timesheetDataDeletionMessage: 'Do you wish to remove <bold>${jobDescription}</bold> and the associated hours from this timesheet?',
        timesheetCheckMessage: 'I understand the impact of deleting this timesheet data',
        deleteTimesheetPrimaryButton: 'Yes, remove the job',
        deleteTimesheetSecondaryButton: 'No, keep the job',
        addJobButtonLabel: 'Add Job',
        applyJobButtonLabel: 'Add',
        hoursAriaLabel: 'Enter hours'
    },

    //Reporting settings Page
    reportingTitle: 'Reporting',
    reportSettingPage: {
        datasetRefreshModalTitle: 'Dataset refresh status',
        lastRunLabel: 'Last Run',
        successStatus: 'Dataset refresh completed successfully',
        failureStatus: 'Dataset refresh failed',
        inProgressStatus: 'Dataset refresh is in progress...',
        cancelledStatus: 'Dataset refresh was cancelled',
        unknownStatus: 'Unknown status',
        datasetRefreshButton: 'Refresh dataset',
        cancelButton: 'Cancel'
    },

    //Report Page
    reportPage: {
        commandBarConfig: {
            pageTitle: 'Reports'
        },
        pageTitle: 'Reports',
        editLabel: 'Edit',
        viewLabel: 'View',
        printLabel: 'Print',
        reportsIHaveCreatedLabel: 'Reports I\'ve created',
        sharedReportsItemLabel: 'Reports shared with me',
        manageMyReportsItemLabel: 'Manage my reports...',
        reportNoEditPermissions: 'Insufficient permission to edit this report',
        emptyReportTitle: 'No reports found',
        emptyReportPageText: 'There are no reports that you can view',
        sharedReportsSectionTitle: 'Shared reports I can edit',
        reportDetailsTitle: 'Report details',
        systemInfoTitle: 'System info',
        updatedByLabel: 'Updated by',
        createdByLabel: 'Created by',
        createdOnLabel: 'Created on',
        updatedOnLabel: 'Updated on',
        nameLabel: 'Name',
        reportAccessLabel: 'Report access',
        emptyReportDetailsText: 'Start creating reports to see them listed in this modal.',
        modalHeaderTitle: 'Manage my reports',
        newReportButtonLabel: 'New report',
        newReportButtonTooltip: 'Max ${licenseCount} reports allowed per tenant. You may not see all reports without read access.',
        deleteButtonLabel: 'Delete selected report',
        saveChangesButtonLabel: 'Save changes',
        discardButtonLabel: 'Discard changes',
        cancelButtonLabel: 'Cancel',
        pendingDeleteButtonLabel: 'Deleting...',
        pendingSaveButtonLabel: 'Saving...',
        unsavedChangesLabel: 'Unsaved changes',
        youHaveUnsavedChangesLine: 'You have unsaved changes on this report. Do you still want to leave?',
        allProfilesLabel: 'All profiles',
        readOnlyLabel: 'Read only',
        editAccessLabel: 'Edit access',
        helpTextLabel: 'Creators can always view and edit their reports',
        maxLimitReachedLabel: 'Max 20 ${resourcePluralLowerAlias} or security profiles',
        reportAccessEntityPicker: {
            searchLabel: 'Search',
            applyButtonText: 'Apply',
            notLoadedLabel: 'Not loaded',
            notLoadedValueLabel: 'Not loaded value ',
            showMoreButtonText: 'Show more'
        },
        deleteReportPrompt: {
            title: 'Delete \'${reportName}\' report?',
            confirmDeleteLabel: 'Yes, delete report',
            declineDeleteLabel: 'No, keep report',
            createdByLine: 'This report was created by ${createdBy}.',
            permanentlyDeleteLine: 'Do you wish to permanently delete this report?',
            youLabel: 'you'
        },
        toasterMessages: {
            create: 'Report created',
            delete: 'Report deleted',
            edit: 'Report edited',
            save: 'Report saved'
        },
        selectLabel: 'Select',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        securityProfilesLabel: 'Security profiles',
        emptyNameError: 'Please enter a name',
        duplicateNameError: 'This name is already in use. Please enter a unique name.'
    },

    //Callback component
    callbackComponentText: 'Redirecting...',

    // Planner/Jobs page
    plannerPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            pageTitle: 'Planner',
            addLabel: 'Add',
            editLabel: 'Edit',
            editRoleByNameButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by name',
            editRoleByCriteriaButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by requirements',
            viewLabel: 'View',
            barsLabel: 'Bar options',
            showLabel: 'Show',
            increaseDateRangeLabel: 'Increase date range',
            decreaseDateRangeLabel: 'Decrease date range',
            dateRangeLabel: 'Date range',
            goToTodayLabel: 'Go to today',
            goToDateLabel: 'Go to date',
            jobsLabel: 'Jobs',
            resourcesLabel: 'Resources',
            filtersLabel: 'Filters',
            bookingLabel: 'Booking',
            roleLabel: 'Role',
            jobLabel: 'Job',
            clientLabel: 'Client',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} from a template',
            cutLabel: 'Cut',
            copyLabel: 'Copy',
            pasteLabel: 'Paste',
            restartLabel: 'Restart',
            archiveLabel: 'Archive',
            rejectLabel: 'Reject',
            makeLiveLabel: 'Make live',
            submitRequestLabel: 'Submit request',
            createLabel: 'Create',
            deleteLabel: 'Delete',
            manageLabel: 'Manage',
            manageRoleTemplatesLabel: 'Manage ${rolerequestSingularLowerAlias} templates',
            moreLabel: 'More',
            newWorkspaceLabel: 'New Workspace',
            saveAsNewWorkspaceLabel: 'Save as a new workspace',
            manageMyWorkspacesLabel: 'Manage My Workspaces',
            privateWorkspacesLabel: 'Private Workspaces',
            publicWorkspacesLabel: 'Public Workspaces',
            noPublicWorkspacesCreatedLabel: 'No public workspaces have been created',
            noPrivateWorkspacesCreatedLabel: 'No private workspaces have been created',
            newPlanLabel: 'New Plan',
            saveAsNewPlanLabel: 'Save as a new plan',
            manageMyPlansLabel: 'Manage My Plans',
            privatePlansLabel: 'Private Plans',
            publicPlansLabel: 'Public Plans',
            saveChangesLabel: 'Save changes',
            saveChangesToPublicLabel: 'Save changes to public',
            noPublicPlansCreatedLabel: 'No public plans have been created',
            noPrivatePlansCreatedLabel: 'No private plans have been created',
            noRoleTemplatesCreatedLabel: 'No templates have been added',
            customDateRangeLabel: 'Custom date range',
            dayLabel: 'Day',
            '5daysLabel': '5 Days',
            '7daysLabel': '7 Days',
            '10daysLabel': '10 Days',
            weekLabel: 'Week',
            '2weeksLabel': '2 Weeks',
            '4weeksLabel': '4 Weeks',
            '6weeksLabel': '6 Weeks',
            monthLabel: 'Month',
            '2monthsLabel': '2 Months',
            '3monthsLabel': '3 Months',
            '6monthsLabel': '6 Months',
            yearLabel: 'Year',
            weekendsLabel: 'Weekends',
            potentialConflicts: 'Show Potential Conflicts',
            baseFilterLabel: 'View jobs',
            rollForwardLabel: 'Duplicate',
            rollForwardTooltipText: 'Copy the selected ${bookingEntityAlias} to another ${jobEntityAlias} or date',
            byNameSuffix: 'by name',
            byRequirementSuffix: 'by requirements',
            findResourcesLabel: 'Find ${resourceEntityAlias}...',
            findResourceToolTipText: 'Find ${resourceEntityAlias} based on criteria (f)',
            showMenuTooltipText: 'Unassigned ${rolerequestPluralCapitalAlias} are now hidden by default. Use the \'Show\' menu to change this.',
            showInViewLabel: 'Show in ${pluralViewNameAlias} view',
            restorePlansLabel: 'Reset view',
            restorePlanTooltipText: 'Reset the current view to its original state. Use the dropdown on the right to save this view as a plan.'
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: 'Past ${subRowEntityAlias}',
            hideFutureEntityLabel: 'Future ${subRowEntityAlias}',
            hideUnassignedRowsEntityLabel: 'Unassigned rows',
            hideUnassignedBookingsEntityLabel: 'Unassigned ${bookingPluralLowerCase}',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inactive ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Show ${subRowEntityAlias} for which there are only ${bookingPluralLowerCase} that end on or before today',
            hideFutureEntitiesExplanation: 'Show ${subRowEntityAlias} for which there are only ${bookingPluralLowerCase} that start after the end of the visible date range',
            hideRolesExplanation: 'Show ${subRowEntityAlias} for which there are ${rolePluralLowerCase}',
            hideDraftRolesExplanation: 'Show ${roleSingularCapitalized} drafts',
            hideRequestedRolesExplanation: 'Show ${rolePluralCapitalized} requests which could become live ${bookingPluralLowerCase}',
            toggleShowUnassignedRoles: 'Unassigned ${rolePluralLowerCase}',
            toggleShowRolesByName: '${rolePluralCapitalized} by name',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} by requirements',
            hideJobTimelineToggleLabel: '${jobEntityAlias} timeline',
            hideJobMilestonesToggleLabel: '${jobEntityAlias} milestones',
            hideJobTimelineExplanation: 'Show ${jobEntityAlias} start and end dates',
            hideJobMilestonesExplanation: 'Show ${jobEntityAlias} specific dates'
        },
        selectionBar: {
            editAllButtonLabel: 'Edit',
            deleteAllButtonLabel: 'Delete',
            editButtonLabel: 'Edit',
            editRoleByNameButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by name',
            editRoleByCriteriaButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by requirements',
            deleteButtonLabel: 'Delete',
            archiveAllButtonLabel: 'Archive',
            archiveButtonLabel: 'Archive',
            restartAllButtonLabel: 'Restart',
            restartButtonLabel: 'Restart',
            submitRequestAllButtonLabel: 'Submit request',
            submitRequestButtonLabel: 'Submit request',
            createButtonLabel: 'Create',
            makeLiveSingularButtonLabel: 'Make live',
            makeLivePluralButtonLabel: 'Make live',
            insufficientRightsToEditAndDelete: 'Insufficient permission to edit/delete these ${entityAlias}',
            insufficientActionRights: 'Insufficient permissions to perform the action for all ${entityAlias}',
            selectedLabel: 'selected',
            maxBookingsSuffix: 'max',
            rollForwardLabel: 'Duplicate',
            rollForwardTooltipText: 'Copy the selected ${bookingEntityAlias} to another ${jobEntityAlias} or date',
            showInView: 'Show in ${pluralViewNameAlias} view'
        },
        barOptions: {
            defaultLabel: 'Default',
            mediumLabel: 'Medium',
            expandedLabel: 'Expanded'
        },
        showLabelsOnBarsLabel: 'Show label on bars',
        legendLabel: 'Legend',
        barFieldsLabel: '${barSingularAlias} bar fields',
        colourSchemeLabel: 'Colour theme',
        customColourThemeLabel: 'Custom colour theme',
        customColourSchemeLabel: 'Custom ${barSingularAlias} colour theme',
        editedSuffix: 'edited',
        createdSuffix: 'created',
        deletedSuffix: 'deleted',
        archivedSuffix: 'archived',
        restartedSuffix: 'restarted',
        rejectedSuffix: 'rejected',
        requestedSuffix: 'requested',
        liveSuffix: 'progressed to',
        publishedRoleSuffix: 'published to ${marketplaceAlias}',
        scheduleRoleForPublishingSuffix: 'scheduled for publishing to ${marketplaceAlias}',
        publicationEditedSuffix: 'publication edited',
        publicationRemovedSuffix: 'removed from ${marketplaceAlias}',
        applyButtonText: 'Apply',
        searchLabel: 'Search',
        notLoadedLabel: 'not loaded',
        notLoadedValueLabel: 'not loaded value',
        goToPageLabel: 'Go to page',
        legend: {
            legendTitle: 'Legend',
            coloursColumnSubTitle: 'Bar colours are based on the',
            barTypes: {
                draftRoles: '${rolerequestSingularCapitalAlias} drafts',
                roleRequestsToLiveBookings: '${rolerequestSingularCapitalAlias} requests which could become live ${bookingPluralLowerAlias}',
                unconfirmed: 'Unconfirmed',
                planned: 'Planned',
                excludesNonWorkingDays: 'Excludes non-working days',
                includesNonWorkingDays: 'Includes non-working days',
                inConflict: 'In conflict',
                startDateNonWorking: 'Start date on hidden weekend',
                endDateNonWorking: 'End date on hidden weekend',
                bothDatesNonWorking: 'Start and end dates on hidden weekend',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} requests which have become live ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Colour theme',
                barTypesTabTitle: 'Bar types',
                milestonesTabTitle: '${jobSingularAlias} details'
            },
            jobDetailsLabels: {
                milestonesColumnTitle: 'Milestones',
                timelineColumnTitle: 'Timeline',
                linesColumnTitle: 'Lines',
                normalLineLabel: '${jobSingularAliasLinesSection} with start and end dates',
                dashedLineLabel: '${jobSingularAliasLinesSection} with a missing start and/or end dates',
                lineEndsColumnTitle: 'Line ends',
                onscreenDatesLabel: 'Onscreen ${jobSingularAliasLineEndsSection} start/end date',
                offscreenDatesLabel: 'Offscreen ${jobSingularAliasLineEndsSection} start/end dates',
                statesColumnTitle: 'States',
                incompletedStateLabel: 'Incomplete',
                completedStateLabel: 'Completed',
                overduedStateLabel: 'Overdue'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'No colour rules added for'
        },
        plans: {
            manageMyPlansLabel: 'Manage My Plans',
            newPlanLabel: 'New Plan',
            privatePlansColumnTitle: 'My Plans',
            copyPlanLabel: 'Copy plan',
            readOnlyLabel: 'Read-only',
            editAccessLabel: 'Edit access',
            renameLabel: 'Rename',
            deleteLabel: 'Delete',
            moveToPublicLabel: 'Move to public',
            makeCopyLabel: 'Make a copy',
            makePublicCopyLabel: 'Make a public copy',
            makePrivateCopyLabel: 'Make a private copy',
            moveToPrivateLabel: 'Move to private',
            privatePlansLabel: 'Private Plans',
            publicPlansLabel: 'Public Plans',
            manageMyWorkspacesLabel: 'Manage My Workspaces',
            newWorkspaceLabel: 'New Workspace',
            privateWorkspacesColumnTitle: 'My Workspaces',
            privateWorkspacesLabel: 'Private Workspaces',
            publicWorkspacesLabel: 'Public Workspaces',
            copyWorkspaceLabel: 'Copy workspace',
            editWorkspaceLabel: 'Edit workspace'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Book ${entityAlias}',
            addBookingToJobRecordListCaption: 'Book on ${entityAlias}',
            searchLabel: 'Search',
            sortLabel: 'Sort',
            sortyByLabel: 'Sort by',
            columnsLabel: 'Columns',
            applyButtonText: 'Apply',
            detailsLabel: 'details',
            notLoadedLabel: 'not loaded',
            notLoadedValueLabel: 'not loaded value',
            historyFieldPlaceholder: 'Non-specified',
            pastLabel: 'past',
            lastLoginLabel: 'Last login',
            expandAndCollapseText: 'Expand and collapse row',
            expandAllCaption: 'Expand all',
            collapseAllCaption: 'Collapse all',
            sortLabelButton: 'Sort ${order}',
            resourcesLabel: 'resources',
            jobsLabel: 'jobs',
            calculatingSortCalcFields: 'Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for',
            numberResults: '${rowCount} results',
            resourceLabel: 'Resource',
            jobLabel: 'Job'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Assignment Change',
            outsideJobDatesLabel: 'Outside job dates',
            datesConflictWithBookingLabel: 'Dates conflict with booking',
            bookingConflictLabel: 'Booking conflict',
            inactiveResourceLabel: ' is inactive. Set to an active or unassigned resource to save changes',
            fromLabel: 'From',
            toLabel: 'To',
            noDiaryAssignmentLabel: 'No diary assigned',
            selectMultipleBarsHintPrefix: '+ click',
            selectMultipleBarsHint: 'to select multiple',
            splitBookingBarHintPrefix: 'Hold S',
            splitBookingBar: 'to split ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            customDateRangeLabel: 'Custom date range',
            goToLabel: 'Go To',
            todayLabel: 'Today',
            dayLabel: 'Day',
            '5daysLabel': '5 Days',
            '7daysLabel': '7 Days',
            '10daysLabel': '10 Days',
            weekLabel: 'Week',
            '2weeksLabel': '2 Weeks',
            '4weeksLabel': '4 Weeks',
            '6weeksLabel': '6 Weeks',
            monthLabel: 'Month',
            '2monthsLabel': '2 Months',
            '3monthsLabel': '3 Months',
            '6monthsLabel': '6 Months',
            yearLabel: 'Year',
            customLabel: 'Custom',
            weekendsLabel: 'Weekends',
            prevLabel: 'Previous',
            nextLabel: 'Next'
        },
        multiSelectionTooltip: {
            selectionTooltip: '${entityCount} ${entityAlias} selected',
            mixedSelectionTooltip: '${bookingsCount} ${bookingAlias} and ${rolesCount} ${rolerequestAlias} selected'
        },
        multiSelectionAlert: {
            message: 'Limit reached',
            description: 'You have reached the selection limit of ${maximumItemsCount} items'
        }
    },
    jobsPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Add',
            jobsLabel: 'Jobs',
            filtersLabel: 'Filters',
            manageLabel: 'Manage',
            editLabel: 'Edit',
            jobLabel: 'Job',
            duplicateLabel: 'Duplicate',
            clientLabel: 'Client',
            editDetailsLabel: 'Edit Details',
            baseFilterLabel: 'View',
            viewAllJobsLabel: 'All',
            viewJobsIManageLabel: 'I manage',
            viewJobsActionRequiredLabel: 'Action Required'
        }
    },
    roleInboxPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Add',
            editLabel: 'Edit',
            editRoleByNameButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by name',
            editRoleByCriteriaButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by requirements',
            deleteLabel: 'Delete',
            filtersLabel: 'Filters',
            rolesLabel: 'Roles',
            showLabel: 'Show',
            archiveLabel: 'Archive',
            restartLabel: 'Restart',
            rejectLabel: 'Reject',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} from a template',
            noRoleTemplatesCreatedLabel: 'No templates have been added',
            createLabel: 'Create',
            makeLiveLabel: 'Make live',
            submitRequestLabel: 'Submit request',
            byNameSuffix: 'by name',
            byRequirementSuffix: 'by requirements',
            unassignFromRoleLabel: 'Unassign from ',
            toggleShowUnassignedRoles: 'Unassigned ${rolePluralLowerCase}',
            toggleShowRolesByName: '${rolePluralCapitalized} by name',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} by requirements',
            manageRoleTemplatesLabel: 'Manage ${rolerequestSingularLowerAlias} templates',
            publishToMarketplaceLabel: 'Publish to ${marketplaceAlias}',
            editRolePublicationButtonLabel: 'Edit ${rolerequestSingularLowerAlias} publication',
            removeRolePublicationButtonLabel: 'Remove ${rolerequestSingularLowerAlias} publication'
        }
    },
    marketplacePage: {
        roleCard: {
            startsOnLabel: '${rolerequestSingularCapitalAlias} starts on',
            categoryLabel: 'Category',
            availabilityLabel: 'Your availability for that ${rolerequestSingularLowerAlias} is',
            numberOfResources: 'Number of ${resourcePluralLowerAlias}',
            numberOfFte: 'FTEs',
            systemDetails: 'Published on ${publishedOn} (Updated on ${updatedOn})',
            pendingResourcesNeededText: '${pendingResources} needed',
            defaultRoleName: 'New Role',
            notAvailableLabel: 'Not available'
        },
        commandBarConfig: {
            allLabel: 'All',
            filtersLabel: 'Filters',
            marketplaceLabel: 'Roles board',
            appliedAllLabel: 'All',
            appliedToLabel: 'I have applied to',
            availableForLabel: 'I am available for'
        },
        entityWindow: {
            roleApplicationSubmitted: 'Application submitted',
            roleApplicationWithdrawn: 'Application has been withdrawn'
        }
    },
    previewEntityPage: {
        sharePopoverTitle: 'Share this ${roleAlias}'
    },
    tableViewPage: {
        commandBarConfig: {
            pageTitle: 'Table View',
            addLabel: 'Add',
            editLabel: 'Edit',
            viewLabel: 'View',
            showLabel: 'Show',
            increaseDateRangeLabel: 'Increase date range',
            decreaseDateRangeLabel: 'Decrease date range',
            dateRangeLabel: 'Date range',
            goToTodayLabel: 'Go to today',
            goToDateLabel: 'Go to date',
            jobsLabel: 'Jobs',
            resourcesLabel: 'Resources',
            filtersLabel: 'Filters',
            bookingLabel: 'Booking',
            roleLabel: 'Role',
            jobLabel: 'Job',
            clientLabel: 'Client',
            cutLabel: 'Cut',
            copyLabel: 'Copy',
            pasteLabel: 'Paste',
            createLabel: 'Create',
            deleteLabel: 'Delete',
            manageLabel: 'Manage',
            moreLabel: 'More',
            newPlanLabel: 'New Plan',
            saveAsNewPlanLabel: 'Save as a new plan',
            manageMyPlansLabel: 'Manage My Plans',
            privatePlansLabel: 'Private Plans',
            publicPlansLabel: 'Public Plans',
            saveChangesLabel: 'Save changes',
            saveChangesToPublicLabel: 'Save changes to public',
            noPublicPlansCreatedLabel: 'No public plans have been created',
            noPrivatePlansCreatedLabel: 'No private plans have been created',
            customDateRangeLabel: 'Custom date range',
            dayLabel: 'Day',
            '5daysLabel': '5 Days',
            '7daysLabel': '7 Days',
            '10daysLabel': '10 Days',
            weekLabel: 'Week',
            '2weeksLabel': '2 Weeks',
            '4weeksLabel': '4 Weeks',
            '6weeksLabel': '6 Weeks',
            monthLabel: 'Month',
            '2monthsLabel': '2 Months',
            '3monthsLabel': '3 Months',
            '6monthsLabel': '6 Months',
            yearLabel: 'Year',
            weekendsLabel: 'Weekends',
            potentialConflicts: 'Show Potential Conflicts',
            baseFilterLabel: 'View jobs',
            findResourcesLabel: 'Find ${resourceEntityAlias}...',
            findResourceToolTipText: 'Find ${resourceEntityAlias} based on criteria (F)',
            showMenuTooltipText: '' // This is empty to prevent the Show menu tooltip from showing on tableview page as it is not needed for now.
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: 'Past ${subRowEntityAlias}',
            hideFutureEntityLabel: 'Future ${subRowEntityAlias}',
            hideInactiveEntityLabel: 'Inactive ${resourcePluralCapitalized}',
            hideRolesLabel: '${rolePluralCapitalized}',
            hidePastEntitiesExplanation: 'Show ${subRowEntityAlias} for which there are only ${bookingPluralLowerCase} that end on or before today',
            hideFutureEntitiesExplanation: 'Show ${subRowEntityAlias} for which there are only ${bookingPluralLowerCase} that start after the end of the visible date range'
        },
        selectionBar: {
            editAllButtonLabel: 'Edit',
            deleteAllButtonLabel: 'Delete',
            editButtonLabel: 'Edit',
            deleteButtonLabel: 'Delete',
            createButtonLabel: 'Create',
            makeLiveSingularButtonLabel: 'Make live',
            makeLivePluralButtonLabel: 'Make all live',
            insufficientRightsToEditAndDelete: 'Insufficient permission to edit/delete these ${entityAlias}',
            insufficientActionRights: 'Insufficient permissions to perform the action for all ${entityAlias}',
            selectedLabel: 'selected',
            maxBookingsSuffix: 'max'
        },
        legendLabel: 'Legend',
        barFieldsLabel: '${barSingularAlias} bar fields',
        colourSchemeLabel: 'Colour theme',
        customColourThemeLabel: 'Custom colour theme',
        customColourSchemeLabel: 'Custom ${barSingularAlias} colour theme',
        editedSuffix: 'edited',
        createdSuffix: 'created',
        deletedSuffix: 'deleted',
        applyButtonText: 'Apply',
        searchLabel: 'Search',
        notLoadedLabel: 'not loaded',
        notLoadedValueLabel: 'not loaded value',
        legend: {
            legendTitle: 'Legend',
            coloursColumnSubTitle: 'Bar colours are based on the',
            barTypes: {
                draftRoles: '${rolerequestSingularCapitalAlias} drafts',
                roleRequestsToLiveBookings: '${rolerequestSingularCapitalAlias} requests which could become live ${bookingPluralLowerAlias}',
                unconfirmed: 'Unconfirmed',
                planned: 'Planned',
                excludesNonWorkingDays: 'Excludes non-working days',
                includesNonWorkingDays: 'Includes non-working days',
                inConflict: 'In conflict',
                startDateNonWorking: 'Start date on hidden weekend',
                endDateNonWorking: 'End date on hidden weekend',
                bothDatesNonWorking: 'Start and end dates on hidden weekend',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} requests which have become live ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Colour theme',
                barTypesTabTitle: 'Bar types',
                milestonesTabTitle: '${jobSingularAlias} details'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'No colour rules added for'
        },
        plans: {
            manageMyPlansLabel: 'Manage My Plans',
            newPlanLabel: 'New Plan',
            privatePlansColumnTitle: 'My Plans',
            copyPlanLabel: 'Copy plan',
            editPlanLabel: 'Edit plan',
            readOnlyLabel: 'Read-only',
            editAccessLabel: 'Edit access',
            renameLabel: 'Rename',
            deleteLabel: 'Delete',
            moveToPublicLabel: 'Move to public',
            makeCopyLabel: 'Make a copy',
            makePublicCopyLabel: 'Make a public copy',
            makePrivateCopyLabel: 'Make a private copy',
            moveToPrivateLabel: 'Move to private',
            privatePlansLabel: 'Private Plans',
            publicPlansLabel: 'Public Plans'
        },
        recordsList: {
            addBookingToJobRecordListCaption: 'Book ${entityAlias}',
            addBookingToJobLabel: 'Book ${entityAlias} to ${jobName}',
            addBookingToResourceRecordListCaption: 'Book on a ${entityAlias}',
            addBookingToResourceLabel: 'Book on ${entityAlias} to ${resourceName}',
            searchLabel: 'Search',
            sortLabel: 'Sort',
            sortyByLabel: 'Sort by',
            columnsLabel: 'Columns',
            applyButtonText: 'Apply',
            detailsLabel: 'details',
            notLoadedLabel: 'not loaded',
            notLoadedValueLabel: 'not loaded value',
            historyFieldPlaceholder: 'Non-specified',
            pastLabel: 'past',
            lastLoginLabel: 'Last login',
            expandAndCollapseText: 'Expand and collapse row',
            expandAllCaption: 'Expand all',
            collapseAllCaption: 'Collapse all',
            sortLabelButton: 'Sort ${order}',
            resourcesLabel: 'Resources',
            jobsLabel: 'Jobs',
            calculatingSortCalcFields: 'Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for',
            resourceLabel: 'Resource',
            jobLabel: 'Job'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Assignment Change',
            outsideJobDatesLabel: 'Outside job dates',
            datesConflictWithBookingLabel: 'Dates conflict with booking',
            bookingConflictLabel: 'Booking conflict',
            inactiveResourceLabel: ' is inactive. Set to an active or unassigned resource to save changes',
            fromLabel: 'From',
            toLabel: 'To',
            noDiaryAssignmentLabel: 'No diary assigned',
            selectMultipleBarsHintPrefix: '+ click',
            selectMultipleBarsHint: 'to select multiple'
        },
        dateBar: {
            goToLabel: 'Go To Date',
            todayLabel: 'Today',
            monthLabel: 'Month',
            '1monthsLabel': '1 Months',
            '2monthsLabel': '2 Months',
            '3monthsLabel': '3 Months',
            '4monthsLabel': '4 Months'
        }
    },
    rolegroupListPage: {
        emptyStateBoldLabel: 'No scenarios',
        emptyStateLabel: 'Create and compare scenarios for this job',
        actionRequiredLabel: 'action required',
        rolesLabel: 'roles',
        actionsRequiredLabel: 'actions required'
    },
    rolegroupDetailsPage: {
        emptyStateLabel: 'Your list of ${rolerequestPluralLowerAlias} will be shown here',
        resourceInactiveString: 'Set to an active ${resourceSingularLowerAlias} to progress ${rolerequestSingularLowerAlias}.',
        addRoleText: 'Add ${rolerequestSingularLowerAlias}',
        addRoleByNameLabel: '${rolerequestSingularCapitalAlias} by name',
        addRoleByRequirementsLabel: '${rolerequestSingularCapitalAlias} by requirements',
        roleFromTemplateLabel: '${rolerequestSingularCapitalAlias} from a template',
        noRoleTemplatesCreatedLabel: 'No templates have been added',
        manageRoleTemplates: 'Manage ${rolerequestSingularLowerAlias} templates',
        notLoadedLabel: 'Not loaded',
        noValuesLoadedLabel: 'Not loaded value',
        applyText: 'Apply',
        searchText: 'Search',
        actionButtonLabel: 'Actions',
        viewDetails: 'View details',
        defaultRoleName: 'New Role',
        noRoleGroupSetLabel: 'No ${rolerequestgroupSingularLowerAlias} set',
        noResourcesMeetCriteriaText: 'Try changing or removing some requirements, and then save changes.',
        noResourcesFoundAdditionalText: 'Recently updated skills data may take up to 24 hours to reflect in suggestions.',
        moveToButtonLabel: 'Move to...',
        noMatchesFoundTopText: 'No matches found',
        unsavedChangesToRoleText: 'Unsaved changes to ${rolerequestSingularLowerAlias}',
        suggestionsNotUpToDateText: 'The suggestions are not up to date, save changes before suggesting ${resourcePluralLowerAlias}.',
        assignResource: 'Assign to ${rolerequestSingularLowerAlias}',
        unassignResource: 'Unassign from ${rolerequestSingularLowerAlias}',
        addToShortlist: 'Add to shortlist',
        removeFromShortlist: 'Remove from shortlist',
        shortlist: 'Shortlist',
        shortlisted: 'Shortlisted',
        notShortListed: 'Not Shortlisted',
        shortlistedBy: 'Shortlisted by',
        maxShortlistReachedFirstRow: 'Limited to',
        maxShortlistReachedSecondRow: '6 resources',
        hiddenSuggestionsTopText: 'Hidden suggestions',
        hiddenSuggestionsText: 'The suggested ${resourcePluralLowerAlias} are hidden, as they are based on some requirements you do not have sufficient permissions to see.',
        tailMessage: 'Couldn\'t find a suitable match? Try changing or removing some role requirements.',
        editButtonLabel: 'Edit ${rolerequestSingularCapitalAlias}',
        multipleRolesSelectedTopText: 'Multiple ${rolerequestPluralLowerAlias} selected',
        multipleRolesSelectedBodyText: 'To view suggested ${resourcePluralLowerAlias}, select only one ${rolerequestSingularCapitalAlias} by requirements.',
        saveAsTemplateLabel: 'Save as template',
        potentialConflictsTooltip: 'Potential conflicts with existing {bookingSingularLowerAlias}',
        maxAllowedRolesLabel: 'Max 100 ${rolerequestPluralLowerAlias}',
        commandBarConfig: {
            editLabel: 'Edit',
            duplicateLabel: 'Duplicate',
            deleteLabel: 'Delete'
        }
    },
    roleRequirementsSection: {
        addCriteriaButtonLabel: 'Add requirements',
        resourceAttributesLabel: '${resourceSingularCapitalAlias} attributes',
        mustMeetCriteriaDescription: 'Must meet',
        mustMeetCriteriaExplanation: '${resourcePluralCapitalAlias} that don\'t meet these requirements will not be suggested',
        criteriaEmptyStateMessage: 'No requirements added',
        removeRequirementLabel: 'Remove',
        resourceSkillsLabel: '${resourceSingularCapitalAlias} skills',
        searchLabel: 'Search',
        applyButtonText: 'Apply',
        removeSkillsText: 'Clear Skills',
        removeFilterText: 'Remove Skills',
        levelText: 'Level',
        levelsText: 'Levels',
        anySkillsText: 'Any',
        oneOfSkillsText: 'One of...',
        saveSkillsText: 'Save',
        cancelSkillsText: 'Cancel'
    },
    entityWindow: {
        basicDetailsSectionTitle: 'Basic details',
        requirementsSectionTitle: 'Requirements',
        milestonesSectionTitle: 'Milestones',
        cMeSectionTitle: 'C-me traits',
        workHistoryTitle: 'Recent work',
        workDetailsSectionTitle: 'Work details',
        budgetSectionTitle: 'Budget',
        budgetDetailsSectionTitle: 'Budget details',
        timeAndFinancialsSectionTitle: 'Time and financials',
        revenueSectionTitle: 'Revenue',
        costsSectionTitle: 'Costs',
        profitSectionTitle: 'Profit',
        hoursSectionTitle: 'Hours',
        planningSectionTitle: 'Planning',
        previousRelatedJob: 'Previous Related Job',
        nextRelatedJob: 'Next Related Job',
        contactSectionTitle: 'Contact',
        emplyomentDetailsSectionTitle: 'Employment details',
        skillsSectionTitle: 'Skills',
        systemInfoSectionTitle: 'System info',
        timeAllocationTitle: 'Time allocation',
        projectHealthTitle: 'Project health',
        fixedTimeSectionSuffix: 'hrs',
        loadingSectionSuffix: '% of working hours',
        timeSectionSuffix: 'hours booked in total',
        hoursInTotalSuffix: 'hours in total',
        hoursPerDaySuffix: 'hours per day',
        FTESuffix: 'FTE',
        resourcesSuffix: '${resourcePluralLowerAlias}',
        nameLabel: 'Name',
        updatedToLabel: 'updated to',
        fromLabel: 'from',
        numberOfResourcesPrefix: 'Number of resources',
        chargeRateFieldsControlTitle: 'Charge rate',
        bookingResourceChargeRateLabel: 'Resource charge rate',
        bookingOverriddenChargeRateLabel: 'Use different charge rate',
        bookingCustomChargeRateLabel: 'Use custom rate',
        bookingRevenueRatesRowTitle: 'Revenue',
        bookingCostRatesRowTitle: 'Cost',
        bookingProfitRatesRowTitle: 'Profit',
        bookingViewModeChargeRatesTitle: 'Rates',
        bookingOwnResourceChargeModeLabel: 'Resource charge rate',
        bookingDifferentResourceChargeModeLabel: 'Different charge rate',
        bookingCustomChargeModeLabel: 'Custom rate',
        rolerequestDescriptionPlaceholder: 'e.g Project Manager',
        rolerequestOwnResourceChargeModeLabel: 'Resource charge rate',
        rolerequestDifferentResourceChargeModeLabel: 'Different charge rate',
        rolerequestCustomChargeModeLabel: 'Custom rate',
        rolerequestResourceChargeRateLabel: 'Resource charge rate',
        rolerequestOverriddenChargeRateLabel: 'Use different charge rate',
        rolerequestCustomChargeRateLabel: 'Use custom rate',
        rolerequestRevenueRatesRowTitle: 'Revenue',
        rolerequestCostRatesRowTitle: 'Cost',
        rolerequestProfitRatesRowTitle: 'Profit',
        roleByNameWindowTitle: '${rolerequestCapitalEntityAlias} by name',
        manageRoleTemplatesWindowTitle: 'Manage My Templates',
        roleTemplateWindowTitle: '${rolerequestCapitalEntityAlias} template',
        roleByRequirementWindowTitle: '${rolerequestCapitalEntityAlias} by requirements',
        dateRangeLabel: 'Date range',
        datesRequiredLabel: 'Dates required',
        nonWorkSectionFieldText: 'Include non-working days',
        bookingSectionTitle: 'Booking',
        rolerequestSectionTitle: 'Requirements',
        rolerequestGroupSectionTitle: 'Role Group',
        jobSectionTitle: 'Job',
        resourceSectionTitle: 'Resource',
        clientSectionTitle: 'Client',
        bookingSectionTitlePlural: 'Bookings',
        jobSectionTitlePlural: 'Jobs',
        resourceSectionTitlePlural: 'Resources',
        clientSectionTitlePlural: 'Clients',
        rolerequestSectionTitlePlural: 'Requirements',
        rolerequestGroupSectionTitlePlural: 'Role Groups',
        moreInfoButtonLabel: 'More info',
        duplicateLabel: 'Duplicate',
        assignToRoleButtonLabel: 'Assign to',
        unassignFromRoleButtonLabel: 'Unassign from',
        rejectButtonLabel: 'Reject',
        restartButtonLabel: 'Restart',
        archiveButtonLabel: 'Archive',
        editButtonLabel: 'Edit',
        applyButtonLabel: 'Apply',
        closeButtonLabel: 'Close',
        withdrawButtonLabel: 'Withdraw application',
        editRoleByNameButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by name',
        editRoleByCriteriaButtonLabel: 'Edit ${rolerequestSingularCapitalAlias} by requirements',
        createButtonLabel: 'Create',
        createEntityTitle: 'Create ${entityTitleAlias}',
        editEntityTitle: 'Edit ${entityTitleAlias}',
        makeLiveSingularButtonLabel: 'Make live',
        makeLivePluralButtonLabel: 'Make all live',
        submitRequestButtonLabel: 'Submit request',
        addBookingLabel: 'Add booking',
        cancelButtonLabel: 'Cancel',
        saveChangesButtonLabel: 'Save changes',
        saveAllButtonLabel: 'Save All',
        discardChangesButtonLabel: 'Discard changes',
        progressButtonLabel: 'Progress',
        deleteButtonLabel: 'Delete',
        editAllButtonLabel: 'Edit all',
        archiveAllButtonLabel: 'Archive all',
        restartAllButtonLabel: 'Restart all',
        submitRequestAllButtonLabel: 'Submit requests',
        deleteAllButtonLabel: 'Delete all',
        newButtonLabel: 'Create',
        viewButtonLabel: 'View',
        compareButtonLabel: 'Compare',
        createTemplateLabel: 'Create template',
        roleTemplateLabel: '${rolerequestSingularCapitalAlias} template',
        rolePublicationWindowTitle: '${rolerequestCapitalEntityAlias} publication',
        insufficientActionRights: 'Insufficient permissions to perform the action for all',
        addNewRoleLabel: 'Add a new ${rolerequestSingularLowerAlias}',
        roleListBodyEmptyStateLabel: 'You can add a named ${resourceSingularLowerAlias}, or enter ${rolerequestSingularLowerAlias} requirements to find suitable ${resourcePluralLowerAlias}',
        manageRoleTemplatesEmptyStateLabel: 'You don\'t have any ${rolerequestSingularLowerAlias} templates.',
        templateDetailsLabel: 'Template Details',
        provideTemplateNameLabel: 'Please provide a name for your template',
        maxLengthValidationMessage: 'Maximum ${maxNameLength} symbols allowed',
        renameLabel: 'Rename',
        createdLabel: 'Created',
        myTemplatesLabel: 'My Templates',
        deleteMultipleBookinngsButtonLabel: 'Detele',
        deleteMultipleRolerequestsButtonLabel: 'Delete',
        bookingStatusFieldExplanation: 'Resource will remain available in the booked time',
        tableViewBookingStatusFieldExplanation: 'Unconfirmed ${bookingSingularLowerAlias} will be visible on ${plannerPageAlias} page. ${resourceSingularCapitalAlias} will remain available in booked time.',
        nonWorkSectionFieldExplanation: 'Contingency hours will be booked on the non-working days',
        jobIsConfidentialFieldExplanation: 'Confidential jobs can only be seen by people who have access',
        rolerequestRolerequestGroupFieldExplanation: 'Leaving this blank will create a role outside of a ${rolerequestgroupSingularLowerAlias}.',
        rolerequestFTEFieldExplanation: '1 Full-Time Equivalent is ${referenceDiaryTime} hours per day',
        resourceSectionFieldExplanation: 'Adding multiple resources will create a ${bookingSingularLowerAlias} for each one',
        jumpToSectionTitle: 'Jump To',
        additionalSectionTitle: 'Additional Section',
        additionalDetailsSectionTitle: 'Additional details',
        commentsSectionTitle: 'Comments',
        roleGroupListSectionTitle: 'Role groups',
        detailsPaneTooltipText: 'The details pane gives you information on the item you have selected. The icons will take you to job, resource or booking information.',
        detailsPaneTooltipTitle: 'Details Pane',
        attachmentsSectionTitle: 'Documents',
        moreOptionsButtonLabel: 'More options',
        bookingBudgetDetailsMessage: 'Budget calculations are using the charge rate that was valid on the first day of the booking.',
        entityCreatedSuffix: 'created',
        entityDeletedSuffix: 'deleted',
        notFoundPrefix: 'value',
        notFoundSuffix: 'not found',
        roleMarketplaceCriteriaMatchExplanation: 'Applicants must match the requirements',
        rolerequestDiaryForEstimationLabel: 'Diary for estimation',
        selectChargeRateLabel: 'Select a charge rate',
        customChargeRateLabel: 'Custom charge rate',
        estimatedBudgetLabel: 'Estimated budget',
        estimatesTabLabel: 'Estimate',
        assignedTotalsTabLabel: 'Assigned totals',
        roleGroupCountLabel: '${roleGroupCount} ${rolerequestgroupPluralCapitalAlias}',
        messages: {
            bookingBudgetDetailsMessageText: 'Budget calculations are using the charge rate that was valid on the first day of the ${bookingSingularLowerAlias}.',
            roleBudgetDetailsMessageText: 'Budget calculations are using the charge rate that was valid on the first day of the ${rolerequestSingularLowerAlias}.',
            roleAssigneesTotalsDifferenceText: 'Actual totals may differ if assignees have different diaries or charge rates to the estimation.',
            bookingMultipleResourcesBudgetDetailsMessageText: `Specific budget rates for each \${bookingSingularLowerAlias} can be seen in their details after creation.
            Budget calculations are using the charge rate that was valid on the first day of the \${bookingSingularLowerAlias}.`,
            roleResourceWarningText: 'Assign a ${resourceSingularLowerAlias} in order to request ${bookingSingularLowerAlias}.',
            roleResourcesContainUnassigedWarningText: `\${bookingSingularCapitalAlias} requests cannot be progressed with
            Unassigned \${resourcePluralLowerAlias}, please discard the 'Unassigned' \${resourceSingularLowerAlias} selection.`,
            criteriaRoleUnassignedResourceText: 'Budget info will be calculated when ${resourcePluralLowerAlias} are assigned to the ${rolerequestSingularLowerAlias}.',
            requirementSectionInsufficientPermissionsText: 'Some requirements are hidden as you do not have sufficient permissions.',
            rolerequestCriteriaDPSuggestionPaneMessageText: 'Assign ${resourcePluralLowerAlias} to the ${rolerequestSingularLowerAlias} through the !{Suggestion pane}.',
            suggestionPaneButtonText: 'Suggestion pane',
            criteriaRoleAssignResourceText: 'Assign ${resourcePluralLowerAlias} to the ${rolerequestSingularLowerAlias} through the Suggestions pane.',
            criteriaRoleAssignedResourceChangeMessageText: 'Change the assignment to the ${rolerequestSingularLowerAlias} through the Suggestions pane.',
            criteriaRoleAssignResourceToCalculateBudgetText: 'To calculate the budget you need to assign ${resourcePluralLowerAlias} to the ${rolerequestSingularLowerAlias}.',
            criteriaRolePublishMessageText: 'The ${rolerequestSingularLowerAlias} post will end automatically after ${rolerequestSingularLowerAlias} end date - ${endDate}',
            roleApplicationAppliedOnText: 'You have applied to this ${rolerequestSingularLowerAlias} on ${applyDate}.',
            bookingJobOverBudgetMessageText: 'This ${bookingSingularLowerAlias} will take this ${jobSingularLowerAlias} over its budget.',
            bookingResourceOverBudgetMessageText: 'Booking this ${resourceSingularLowerAlias} will take this ${jobSingularLowerAlias} over its budget.',
            bookingJobHoursOverBudgetMessageText: 'This ${bookingSingularLowerAlias} will put this ${jobSingularLowerAlias}\'s Total hours at ${jobHoursPercentageBudget}% of its Budget hours'
        },
        financialInformationSectionTitle: 'Financial information',
        schedulingSectionTitle: 'Scheduling',
        rolesSectionTitle: 'Roles',
        resourceSummaryTitle: 'Summary',
        overlappingBookingsTitle: 'Overlapping bookings and roles',
        saveAsADraft: 'Save as a draft',
        backToSuggestionLabel: 'Back to suggestions',
        suggestLabel: 'Suggest',
        suggestedLabel: 'Suggested',
        forLabel: 'for',
        moveButtonLabel: 'Move',
        moveToModalTitle: 'Move to...',
        searchForLabel: 'Search for a',
        lastRefreshedText: 'Last refreshed',
        SelectionTitleLabel: 'Bulk update of all ',
        SelectionDescriptionLabel: 'Set values or leave blank to clear values',
        SelectionFieldsCaptionLabel: 'Fields',
        shortlistUptoSixText: 'You can shortlist up to 6 resources.',
        manageBudgetLabel: 'Manage budget',
        movePendingFTELabel: 'Move Pending FTEs',
        removePendingFTELabel: 'Remove Pending FTEs',
        movePendingResourcesLabel: 'Move Pending Resources',
        removePendingResourcesLabel: 'Remove Pending Resources',
        publishToMarketplaceLabel: 'Publish to ${pageAlias}',
        publishRoleLabel: 'Publish ${rolerequestSingularLowerAlias}',
        roleMarketplaceCategoryPlaceholder: 'Add category',
        saveAsTemplateLabel: 'Save as template',
        editRolePublicationButtonLabel: 'Edit ${rolerequestSingularLowerAlias} publication',
        removeRolePublicationButtonLabel: 'Remove ${rolerequestSingularLowerAlias} publication',
        emptyState: {
            noRoleGroupItemsCoincidenceMessage: 'To view ${rolerequestgroupSingularLowerAlias} details, select ${rolerequestPluralLowerAlias} that are from the same ${rolerequestgroupSingularLowerAlias}.',
            noRoleGroupItemsCoincidenceContent: 'Multiple ${rolerequestgroupPluralLowerAlias} in selection'
        },
        createScenarioLabel: 'Create ${rolerequestgroupSingularLowerAlias}',
        editScenarioLabel: 'Edit ${rolerequestgroupSingularLowerAlias}',
        openScenarioButtonLabel: 'Open ${rolerequestgroupSingularCapitalAlias}',
        fieldValueCaption: {
            budget: 'of budget',
            target: 'of target'
        }
    },
    resourceSummarySection: {
        availabilityText: 'Availability',
        suitabilityText: 'Suitability',
        hoursSuffix: 'h',
        andLabel: 'and'
    },
    suggestedResources: {
        addToShortlist: 'Add to shortlist',
        matchTextSuffix: 'match',
        isATextMessage: 'is a ',
        skillsLabel: 'Skills',
        workExperienceLabel: 'Work experience',
        skillWithPrefix: 'Skill with',
        similarityTextSuffix: 'similarity:',
        suitabilityText: 'Suitability',
        mixedSortOptionText: 'Suitability & availability',
        sortOrderText: 'Sort Order: ',
        suggestionsLabel: 'Suggestions',
        forLabel: 'for',
        appliedOn: 'Applied on',
        lastRefreshedText: 'Last refreshed',
        refreshLabel: 'Refresh',
        loadingLabel: 'Loading...',
        noRelevantAISuggestionDataText: 'Insufficient skill or experience data for a suitability score',
        infoBannerMessage: 'All results meet the ${resourceSingularLowerAlias} attribute requirements.',
        aiSuggestionsLabel: 'AI Suggestions',
        aiToggleTooltipText: 'Toggle this on to use AI suggestions.'
    },
    common: {
        selectRowTextAriaLabel: 'Select row for ${name}',
        maximumFieldLengthValidationMessage: 'Maximum ${maximumFieldSize} characters',
        daysString: 'days',
        singleDayString: 'day',
        workingString: 'working',
        nonWorkingString: 'non-working',
        hoursString: 'hours',
        FTEstring: 'FTE',
        pendingString: 'pending',
        hoursPerDaySuffix: 'hours per day',
        excludeNonWorkingString: 'excludes non-working days',
        includeNonWorkingString: 'includes non-working days',
        addPrefix: 'Add',
        newPrefix: 'New',
        allPrefix: 'All',
        addAnotherPrefix: 'Add another',
        clickToEditSuffix: '(click to edit)',
        insufficientPermissionsToEditSuffix: 'Insufficient permissions to edit',
        historyFieldPlaceholder: 'Non-specified',
        noValueMessage: 'No ${fieldInfoAlias} set',
        milestoneHistoryFieldPlaceholder: 'No milestone set',
        startingLabel: 'Starting',
        endingLabel: 'Ending',
        dueDate: 'Due date',
        nameLabel: 'Name',
        restrictedLabel: 'Restricted',
        milestonesSectionTitle: 'Milestones',
        milestonesSectionTitleNameDueDate: 'Milestones name due date',
        milestonePlaceholder: 'e.g. Project approval',
        markAsCompletedLabel: 'Mark as Completed',
        noString: 'No',
        setString: 'set',
        totalSuffix: 'Total',
        hourlySuffix: 'hourly',
        noResultsFoundMessage: 'No results found',
        noResultsMessage: 'No results',
        checkedMessage: 'Yes',
        uncheckedMessage: 'No',
        shownLabel: 'Show',
        hiddenLabel: 'Hide',
        jobUtitilizationInfo: 'Exclude bookings from utilisation',
        excludeValue: 'Exclude',
        includeValue: 'Include',
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'was found with this name.',
        noOptionsSetSuffix: 'category options set by your Administator',
        showMorePrefix: 'Show',
        showMoreSuffix: 'more',
        showLessText: 'Show less',
        seeMoreText: 'See more',
        detailsTabLabel: 'Details',
        historyTabLabel: 'History',
        editAllTabLabel: 'Edit All',
        roleListLabel: 'Role list',
        newTemplateLabel: 'New Template',
        audit: {
            sortLabel: 'Sort',
            showMoreText: 'Show more',
            backToTopText: 'Back to top',
            oldValuePrefix: 'from',
            actorPrefix: 'by',
            startingText: 'starting',
            timeLineActionCreateAlias: 'added',
            timeLineActionUpdateAlias: 'updated',
            unassignedValue: 'Unassigned',
            levelString: 'level',
            timeLineActionUpdateSuffixAlias: 'to',
            timeLineActionDeleteAlias: 'removed',
            timeLineActionRemoveAlias: 'removed',
            falseString: 'No',
            trueString: 'Yes',
            anyLevelString: 'All levels',
            resourceNotFoundCaption: '${resourceSingularCapitalAlias} not found',
            templateTexts: {
                historyRecordCreateText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionCreateAlias}',
                historyRecordUpdateText: '${alias} ${timeLineActionUpdateAlias} ${timeLineActionUpdateSuffixAlias} ${valueDescription} ${oldValuePrefix} ${oldValueDescription} ${startingText} ${valueStartDate}',
                historyRecordDeleteText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionRemoveAlias}'
            },
            auditSectionTitles: {
                projectHealthTitle: 'Project health:'
            }
        },
        lastUpdatedLabel: 'Last updated',
        updatedByLabel: 'by',
        noDiaryOnDatesLabel: 'No diary',
        noOverlappingBookings: 'No overlapping bookings',
        archivedLabel: 'Archive',
        restartedLabel: 'Restart',
        requestedLabel: 'Request',
        batchRequestedLabel: 'requests',
        rejectedLabel: 'Reject',
        expiredLabel: 'Expire',
        draftLabel: 'Draft',
        liveLabel: 'Live',
        actionsDropdownLabel: 'Actions',
        unassignedPlaceholder: 'Unassigned',
        availabilityText: 'Availability',
        movePendingFTELabel: 'Move pending FTEs',
        removePendingFTELabel: 'Remove pending FTEs',
        movePendingResourcesLabel: 'Move pending ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Remove pending ${resourcePluralLowerAlias}',
        rolerequestLoadingExplanation: 'of ${resourcePluralLowerAlias} capacity',
        noRateSetLabel: 'No rate set',
        resourcesString: '${resourcePluralLowerAlias}',
        potentialConflictsTooltip: 'Potential conflicts with existing ${booking}',
        addFiltersButtonLabel: 'Add filters',
        singleDayUnit: 'day',
        daysUnit: 'days',
        singleWeekUnit: 'week',
        weeksUnit: 'weeks',
        singleMonthUnit: 'month',
        monthsUnit: 'months',
        singleYearUnit: 'year',
        yearsUnit: 'years',
        revertToStartAndEndDates: 'Revert to start and end dates',
        searchToSelect: 'Search to select',
        fieldMandatoryText: 'This field is mandatory',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        estimatesTooltip: 'Estimate of ${rolerequestSingularLowerAlias} budget before resources are assigned.',
        assigneesTotalsTooltip: 'Actual ${rolerequestSingularLowerAlias} budget with current assignees.',
        editLabel: 'Edit button',
        closeLabel: 'Close button',
        deleteLabel: 'Delete button',
        addLabel: 'Add button',
        quantityAria: 'Enter ${fieldAlias}',
        criteriaRoleBudgetDescription: 'Estimated values are used in ${rolerequestgroupSingularLowerAlias} budget totals until the ${rolerequestSingularLowerAlias} is fully staffed',
        noResourcesAssigned: 'No ${resourcePluralLowerAlias} assigned',
        noRoleGroupSetLabel: 'No ${rolerequestgroupSingularLowerAlias} set',
        avatarAltText: 'Profile picture for ${resourceName}',
        copyOfPrefix: 'Copy of',
        ascendingSort: 'Ascending',
        descendingSort: 'Descending',
        selectionListSort: 'Selection list sort',
        deleteLabelText: 'Delete',
        deleteLabelItemText: 'Delete ${item}',
        scrollDownText: 'Scroll down',
        scrollBarText: 'Scroll bar',
        scrollUpText: 'Scroll up',
        cancelChangesLabel: 'Cancel changes',
        saveChangesLabel: 'Save changes',
        fullScreenButtonLabel: 'full page view',
        clearNameLabel: 'Clear ${fieldName} field',
        collapseLeftNavigation: 'Collapse left navigation',
        expandLeftNavigation: 'Expand left navigation',
        fieldAriaLabel: 'Enter ${fieldAlias}',
        selectAllRolesLabel: 'Select all roles',
        expandRowLabel: 'Expand row',
        collapseRowLabel: 'Collapse row',
        floatingActionBarLabel: '${entity} ${fieldName} may be different for this date range',
        floatingActionBarButtonLabel: 'Update sort order'
    },
    tableOptions: {
        displayDensityOptionTitle: 'Display Density',
        chooseDetailsOptionTitle: 'Choose Details',
        compactDensityOptionTitle: 'Compact',
        defaultDensityOptionTitle: 'Default',
        expandedDensityOptionTitle: 'Expanded',
        searchLabel: 'Search',
        applyButtonText: 'Apply',
        notLoadedLabel: 'not loaded',
        notLoadedValueLabel: 'not loaded value',
        tableOptionsLabel: 'Table options'
    },
    dataGrid: {
        sortLabel: 'Sort',
        sortyByLabel: 'Sort By',
        pageOptionSuffix: ' per page',
        noItemsMessage: 'You don\'t have any jobs',
        noRolesItemsMessage: 'You don\'t have any roles',
        newJob: 'New job',
        noMatchingItemsMessage: 'No jobs found matching the filters',
        noMatchingRolesItemsMessage: 'No roles found matching the filters',
        noMatchingItemsContent: 'Try changing your filters.',
        noChargetypeSet: 'No charge type set',
        noRoleGroupItemsMessage: 'No scenarios',
        noRoleGroupItemsContent: 'Create and compare scenarios for this job',
        noResourceFoundMessage: 'No ${resourceEntityAlias} found matching the criteria',
        noMarketplaceRolesPublished: 'There aren\'t any ${rolerequestPluralLowerAlias} published',
        showingCaption: 'Showing ${pageRolesCount} of ${totalRolesCount} ${rolerequestAlias}',
        entityPageOptionSuffix: '${rolerequestPluralLowerAlias} per page',
        noResultsMessage: 'No results found',
        tryAdjustingFiltersMessage: 'Try adjusting your search or filter',
        sortLabelButton: 'Sort ${order}',
        sortAscending: 'Ascending',
        sortDescending: 'Descending',
        operationLogEmptyMessage: 'Operation log is empty',
        operationLogEmptyContent: 'This log is currently empty with no recorded operations.',
        operationLogSuccess: 'Operation complete',
        operationLogIncomplete: 'Operation completed with exceptions',
        operationLogCancelled: 'Operation cancelled by',
        operationLogFailed: 'Operation failed',
        cancelOperation: 'Cancel operation',
        undoOperation: 'Undo operation'
    },
    filterPane: {
        anyLevelLabel: 'Any level',
        selectLabel: 'Select',
        filterSuffix: 'view',
        headingTitle: 'Filters',
        applyButtonText: 'Apply',
        discardChangesText: 'Reset filters',
        applyButtonTooltipText: 'Please add filters and fill in all inputs to enable.',
        resetButtonTooltipText: 'Reset filters to its original state.',
        showMoreButtonText: 'Show more',
        maxDateRangeMessage: 'Maximum of 5 date ranges',
        startDateLabel: 'Start',
        endDateLabel: 'End',
        fromDateLabel: 'From',
        toDateLabel: 'To',
        searchLabel: 'Search',
        searchToSelectLabel: 'Search to select',
        textOperatorLabel: 'Like',
        loadingLabel: 'loading',
        betweenLabel: 'between',
        clearFiltersText: 'Clear filters',
        removeFilterText: 'Remove filter',
        notLoadedLabel: 'not loaded',
        notLoadedValueLabel: 'not loaded value',
        levelText: 'Level',
        levelsText: 'Levels',
        resetFilterText: 'Reset all',
        clearAllFiltersText: 'Clear all',
        numberResults: '${rowCount} results',
        allLabel: 'All',
        yesLabel: 'Yes',
        noLabel: 'No',
        addLabel: 'Add ${fieldAlias}',
        removeLabel: 'Remove ${fieldAlias}',
        removeFilterButtonLabel: 'Remove the date range from ${startDate} to ${endDate}',
        typeHereLabel: 'Type here',
        hiddenFiltersMessage: 'Some filters could not be applied. This may be due to your user permissions, or an invalid field/value. Saving this workspace will',
        hiddenFiltersBoldMessage: 'remove any hidden filters.',
        operators: {
            DB_OPERATORS: {
                LESS_THAN: 'LessThan',
                LESS_THAN_OR_EQUAL: 'LessThanOrEqual',
                EQUALS: 'Equals',
                GREATER_THAN_OR_EQUAL: 'GreaterThanOrEqual',
                GREATER_THAN: 'GreaterThan',
                LIKE: 'Like',
                CONTAINS: 'Contains'
            },
            NUMERIC_OPERATORS_ALIAS: {
                LessThan: 'less than',
                LessThanOrEqual: 'at most',
                Equals: 'equal to',
                GreaterThanOrEqual: 'at least',
                GreaterThan: 'more than'
            },
            NUMERIC_PARAMETER_OPERATORS_ALIAS: {
                LessThan: 'less than',
                LessThanOrEqual: 'at most',
                Equals: 'equal to',
                GreaterThanOrEqual: 'at least',
                GreaterThan: 'more than'
            },
            TEXT_OPERATORS_ALIAS: {
                Like: 'contains'
            },
            MULTY_VALUES_OPERATORS_ALIAS: {
                Contains: 'is one of'
            },
            DATE_OPERATORS_ALIAS: {
                LessThanOrEqual: 'up to',
                GreaterThanOrEqual: 'from'
            },
            DATE_SENSITIVE_OPERATORS_ALIAS: {
                GreaterThanOrEqual: 'at least',
                LessThanOrEqual: 'at most'
            },
            SKILL_OPERATORS_ALIAS: {
                Equals: 'is'
            },
            CHECKBOX_OPERATORS_ALIAS: {
                Equals: 'on'
            },
            BOOLEAN_OPERATORS_ALIAS: {
                Equals: 'is'
            }
        },
        advancedFilterOperators: {
            TEXT: {
                Contains: 'Contains',
                IsBlank: 'Is blank',
                IsNotBlank: 'Is not blank'
            },
            NUMERIC: {
                Is: 'Is',
                IsNot: 'Is not',
                IsMoreThan: 'Is more than',
                IsLessThan: 'Is less than',
                IsBlank: 'Is blank',
                IsNotBlank: 'Is not blank'
            },
            BOOLEAN: {
                Is: 'Is',
                IsNot: 'Is not'
            },
            SKILL: {
                Is: 'Is',
                IsNot: 'Is not',
                IsBlank: 'Is blank',
                IsNotBlank: 'Is not blank'
            }
        },
        logicalOperators: {
            and: 'And',
            or: 'Or'
        },
        uiFilterOperators: {
            Is: 'is',
            IsNot: 'is not',
            IsMoreThan: 'is more than',
            IsLessThan: 'is less than',
            Contains: 'contains',
            UpTo: 'up to',
            From: 'from',
            IsNotBlank: 'is not blank',
            IsBlank: 'is blank',
            IsMoreOrEqual: 'is more or equal',
            IsLessOrEqual: 'is less or equal'
        },
        filterFieldMessages: {
            resource_has_skill_resource_skill_levelAlias: 'Skills',
            resource_guidAlias: 'Resource Name',
            availabilityAlias: 'Availability',
            utilisationAlias: 'Utilisation',
            departmentAlias: 'resource_current_department_guidDepartment',
            resource_manager_resource_guidAlias: 'Line Manager',
            resource_location_guidAlias: 'Location',
            resource_localgrade_guidAlias: 'Grade',
            resource_resourcetype_guidAlias: 'Employment type',
            resource_rolenameAlias: 'Title',
            resource_booking_countAlias: 'Booking count',
            'Charge RateAlias': 'Charge Rate',
            job_guidAlias: 'Job Name',
            job_startAlias: 'Job Start',
            job_endAlias: 'Job End',
            job_client_guidAlias: 'Client',
            job_engagementlead_resource_guidAlias: 'Line Manager',
            job_location_guidAlias: 'Job location',
            job_jobstatus_guidAlias: 'Job status',
            job_codeAlias: 'Job reference code',
            job_chargetype_guidAlias: 'Charge type',
            booking_is_assignedAlias: 'Unassigned',
            booking_startAlias: 'Booking Start',
            booking_endAlias: 'Booking End',
            booking_bookingtype_guidAlias: 'Booking status',
            booking_notesAlias: 'Booking Notes',
            booking_workactivity_guidAlias: 'Work Activity',
            updatedonAlias: 'updated on',
            updatedbyAlias: 'updated by resource',
            createdonAlias: 'created on',
            createdbyAlias: 'created by resource'
        }
    },
    talentProfilePage: {
        profileTitle: 'My profile',
        shareProfileCaption: 'Share profile',
        uploadLabel: 'Upload documents',
        changeProfielPictureText: 'Change profile picture',
        viewOtherProfile: 'View other profile',
        viewMyProfile: 'View my profile',
        cMeProfileTitle: 'C-Me Profile',
        editDetailsLabel: 'Edit details',
        updateAvatarWindowMessages: {
            headerTitle: 'Update profile picture',
            applyBtnTitle: 'Apply',
            removeBtnTitle: 'Remove picture',
            cancelBtnTitle: 'Cancel',
            uploadAreaText: 'Click or drag file to this area to upload',
            fileTypesString: 'File types: ',
            dragControlLabel: 'Drag',
            zoomControlLabel: 'Zoom'
        },
        recommendationTitle: 'Recommendations',
        recommendationAlertHeader: 'Update your skills and get noticed',
        recommendationAlertDescription: 'Here are some skills you could add to your profile',
        skillApproval: 'Your changes will be sent to manager for approval. Changes to skill preferences do not require approval.',
        additionalDetailsSectionTitle: 'Additional details',
        messages: {
            fileTooLargeLabel: 'Document upload failed: File is too large',
            fileTypeForbiddenLabel: 'Document upload failed: File type is not allowed',
            noFilesUploadedLabel: 'You don\'t have any documents uploaded',
            uploadsLimitReachedLabel: 'Document limit reached: delete documents to upload more'
        },
        uploadDocumentsWindowMessages: {
            headerTitle: 'Upload document',
            uploadBtnTitle: 'Upload',
            cancelBtnTitle: 'Cancel',
            uploadAreaText: 'Click or drag file to this area to upload',
            fileTypesString: 'Accepted formats: ',
            documentType: 'Type of document',
            expiryDate: 'Expiry date',
            maxFileSize: 'Max ${maxFileSize} MB per file'
        }
    },
    prompts: {
        createOrSaveAsNewPlanPrompt: {
            title: 'New plan',
            placeholder: 'Insert plan name',
            helpMessage: 'Please provide a name for your plan',
            okText: 'Save',
            cancelText: 'Cancel',
            name: 'Name',
            type: 'Type',
            access: 'Access',
            editAccess: 'Edit access',
            readOnlyAccess: 'Read-only',
            subHeading: 'Save as a new private or public plan',
            privatePlan: 'Private plan',
            publicPlan: 'Public plan',
            maxLengthValidationMessage: 'Maximum ${maxNameLength} symbols allowed',
            newPlanLabel: 'New Plan',
            switchOnLabel: 'Switch On'
        },
        createOrSaveAsNewWorkspacePrompt: {
            title: 'New workspace',
            placeholder: 'Insert workspace name',
            helpMessage: 'Please provide a name for your workspace',
            okText: 'Save',
            cancelText: 'Cancel',
            name: 'Name',
            type: 'Type',
            access: 'Access',
            editAccess: 'Edit access',
            readOnlyAccess: 'Read-only',
            subHeading: 'Save as a new private or public workspace',
            privatePlan: 'Private workspace',
            publicPlan: 'Public workspace',
            maxLengthValidationMessage: 'Maximum ${maxNameLength} symbols allowed',
            newPlanLabel: 'New Workspace',
            switchOnLabel: 'Switch On'
        },
        extendJobRangePrompt: {
            okText: 'Yes, amend job dates',
            cancelText: 'No, cancel move',
            title: 'Extend the job?',
            message: 'Do you wish to extend the job period to allow for the movement of this booking?',
            detailsText: 'New job dates for'
        },
        deleteBookingPrompt: {
            title: 'Delete',
            message: 'Do you wish to delete this',
            okText: 'Yes, delete the',
            cancelText: 'No, keep the',
            noEntityDescriptionPrefix: 'No',
            noEntityDescriptionSuffix: 'description'
        },
        makePlanPrivatePrompt: {
            okText: 'Make plan private',
            cancelText: 'Keep plan public',
            makeString: 'Make',
            privatePlanString: 'a private plan',
            planTypeMessageStart: 'This plan is currently',
            messageFirstPart: 'If you make this plan private,',
            messageBold: 'it will no longer be available to others.'
        },
        makeWorkspacePrivatePrompt: {
            okText: 'Make workspace private',
            cancelText: 'Keep workspace public',
            makeString: 'Make',
            privatePlanString: 'a private workspace',
            planTypeMessageStart: 'This workspace is currently',
            messageFirstPart: 'If you make this workspace private,',
            messageBold: 'it will no longer be available to others.'
        },
        deletePlanPrompt: {
            okText: 'Yes, delete plan',
            cancelText: 'No, keep plan',
            deleteString: 'Delete',
            planString: 'plan',
            workspaceTypeMessage: 'This plan is currently',
            publicDeleteMessageStart: 'If you delete this plan',
            publicDeleteMessageEnd: 'it will no longer be available to others',
            question: 'Do you wish to permanently delete this plan?'
        },
        deleteWorkspacePrompt: {
            okText: 'Yes, delete workspace',
            cancelText: 'No, keep workspace',
            deleteString: 'Delete',
            planString: 'workspace',
            workspaceTypeMessage: 'This workspace is currently',
            publicDeleteMessageStart: 'If you delete this workspace',
            publicDeleteMessageEnd: 'it will no longer be available to others',
            question: 'Do you wish to permanently delete this workspace?'
        },
        deleteRoleTemplatePrompt: {
            okText: 'Yes, delete template',
            cancelText: 'No, keep template',
            warning: 'This cannot be undone.',
            question: 'Do you wish to permanently delete this ${rolerequestSingularLowerAlias} template?',
            title: 'Delete ${roleTemplateDescription} template?'
        },
        renameRoleTemplatePrompt: {
            okText: 'Rename template',
            cancelText: 'Keep old name',
            question: 'You have renamed the template. Do you wish to save this change?',
            title: 'Rename template',
            fromText: 'from ',
            toText: 'to '
        },
        deleteClientPrompt: {
            title: 'Delete client?',
            message: 'Deleting this client will permanently remove them from the entire system including all associated jobs. It will not be possible to assign this client to future jobs. \n Do you wish to continue?',
            okText: 'Yes, delete client',
            cancelText: 'No, keep client'
        },
        deleteJobPrompt: {
            title: 'Delete',
            okText: 'Delete',
            cancelText: 'Keep',
            thereString: 'There',
            isString: 'is',
            areString: 'are',
            andString: 'and',
            onString: 'on',
            thisString: 'this',
            theseString: 'these',
            withString: 'with',
            betweenString: 'between',
            onThisJobString: 'on this job',
            deletingThisJobWillAlsoDeleteString: 'Deleting this job will also delete',
            doYouWishToPermanentlyDeleteString: 'Do you wish to permanently delete',
            messages: {
                deleteJobTitle: 'Delete ${jobDescription}?',
                deleteJobLabel: 'Delete ${jobAlias}, ${bookingAlias}, ${roleGroupAlias} and ${roleRequestAlias}?'
            }
        },
        deleteRolePrompt: {
            title: 'Delete',
            message: 'Do you wish to delete this',
            okText: 'Yes, delete the',
            cancelText: 'No, keep the ',
            noEntityDescriptionPrefix: 'No',
            noEntityDescriptionSuffix: 'description',
            defaultRoleName: 'New Role',
            noRoleGroupSetLabel: 'No ${rolerequestgroupSingularLowerAlias} set'
        },
        removeRolePublicationPrompt: {
            title: 'Remove ${rolerequestSingularLowerAlias} from ${roleBoardPageAlias}',
            message: 'There are ${countOfResources} ${resourcePluralLowerAlias} applied to this ${rolerequestSingularLowerAlias}. Removing the ${rolerequestSingularLowerAlias} from ${roleBoardPageAlias} will also remove their applications.',
            question: 'Do you wish to remove ${rolePublicationDescription} from ${roleBoardPageAlias}?',
            confirmation: 'Remove ${resourcePluralLowerAlias} applications and ${rolerequestSingularLowerAlias} from ${roleBoardPageAlias}?',
            okText: 'Remove ${rolerequestSingularLowerAlias}',
            cancelText: 'Keep ${rolerequestSingularLowerAlias}',
            defaultRoleName: 'New Role'
        },
        unsavedChangesPrompt: {
            message: 'You have unsaved changes on this page. Do you still want to leave?',
            saveLabel: 'Save Changes',
            discardLabel: 'Discard Changes',
            cancelLabel: 'Cancel'
        },
        renamePlanPrompt: {
            okText: 'Rename plan',
            cancelText: 'Keep old name',
            title: 'Rename plan',
            message: 'You have renamed the plan. Do you wish to save this change?',
            oldNamePrefix: 'from',
            newNamePrefix: 'to'
        },
        renameWorkspacePrompt: {
            okText: 'Rename workspace',
            cancelText: 'Keep old name',
            title: 'Rename workspace',
            message: 'You have renamed the workspace. Do you wish to save this change?',
            oldNamePrefix: 'from',
            newNamePrefix: 'to'
        },
        saveChangesPrompt: {
            okText: 'Continue without saving',
            cancelText: 'Cancel',
            title: 'Unsaved Changes',
            message: 'is a',
            planSuffix: 'plan',
            saveAsPrivatePlanButtonLabel: 'Save as a private',
            saveChangesToPublicButtonLabel: 'Save changes to public'
        },
        saveWorkspaceChangesPrompt: {
            okText: 'Continue without saving',
            cancelText: 'Cancel',
            title: 'Unsaved Changes',
            message: 'is a',
            planSuffix: 'workspace',
            saveAsPrivatePlanButtonLabel: 'Save as a private',
            saveChangesToPublicButtonLabel: 'Save changes to public'
        },
        deleteResourceSkillPrompt: {
            okText: 'Yes, remove skill',
            cancelText: 'No, keep skill',
            selectedSkill: 'Selected skill',
            title: 'Remove skill?',
            messagePrefix: 'Do you wish to remove ',
            messageSuffix: ' from the profile?'
        },
        deleteCommentPrompt: {
            okText: 'Delete comment',
            cancelText: 'Keep comment',
            title: 'Delete Comment?'
        },
        singleCreateBookingErrorPrompt: {
            title: 'Error creating ${entitySingularLower}',
            message: 'There was an error creating the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to create this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleCreateJobErrorPrompt: {
            title: 'Error creating ${entitySingularLower}',
            message: 'There was an error creating the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to create this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleCreateClientErrorPrompt: {
            title: 'Error creating ${entitySingularLower}',
            message: 'There was an error creating the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to create this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleCreateRolegroupErrorPrompt: {
            title: 'Error creating ${entitySingularLower}',
            message: 'There was an error creating the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to create this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singlePublishRoleErrorPrompt: {
            title: 'Error publishing ${entitySingularLower}',
            message: 'There was an error publishing the following ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Insufficient permissions to publish this ${entitySingularLower} to ${marketplaceAlias}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit publishing details',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleEditRolePublicationErrorPrompt: {
            title: 'Error editing ${entitySingularLower} publication',
            message: 'There was an error publishing the following ${entitySingularLower} publication:',
            insufficientPermissionsMessage: 'Insufficient permissions to edit this ${entitySingularLower} publication',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleRemoveRolePublicationErrorPrompt: {
            title: 'Error removing ${entitySingularLower} publication',
            message: 'There was an error removing the following ${entitySingularLower} publication:',
            insufficientPermissionsMessage: 'Insufficient permissions to remove this ${entitySingularLower} publication from ${marketplaceAlias}',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleEditBookingErrorPrompt: {
            title: 'Error editing ${entitySingularLower}',
            message: 'There was an error editing the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to editing this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleEditJobErrorPrompt: {
            title: 'Error editing ${entitySingularLower}',
            message: 'There was an error editing the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to editing this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleEditClientErrorPrompt: {
            title: 'Error editing ${entitySingularLower}',
            message: 'There was an error editing the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to editing this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleEditResourceErrorPrompt: {
            title: 'Error editing profile',
            message: 'There was an error editing the following profile:',
            insufficientPermissionsMessage: 'Insufficient permissions to editing this profile',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit profile',
            discardButtonLabel: 'Discard profile'
        },
        singleSaveTemplateErrorPrompt: {
            title: 'Error creating ${entitySingularLower} template',
            message: 'There was an error creating the following ${entitySingularLower} template:',
            insufficientPermissionsMessage: 'Insufficient permissions to create ${entitySingularLower} template',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower} template',
            defaultRoleTemplateName: 'New ${entitySingularUpper}'
        },
        singleDeleteErrorPrompt: {
            title: 'Error deleting ${entitySingularLower} template',
            message: 'There was an error deleting the following ${entitySingularLower} template:',
            insufficientPermissionsMessage: 'Insufficient permissions to delete ${entitySingularLower} template',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower} template',
            defaultRoleTemplateName: 'New ${entitySingularUpper}'
        },
        singleEditErrorPrompt: {
            title: 'Error editing ${entitySingularLower} template',
            message: 'There was an error editing the following ${entitySingularLower} template:',
            insufficientPermissionsMessage: 'Insufficient permissions to edit ${entitySingularLower} template',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower} template',
            defaultRoleTemplateName: 'New ${entitySingularUpper}'
        },
        singleEditRolegroupErrorPrompt: {
            title: 'Error editing ${entitySingularLower}',
            message: 'There was an error editing the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to edit this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleDeleteBookingErrorPrompt: {
            title: 'Error deleting ${entitySingularLower}',
            message: 'There was an error deleting the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to deleting this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleDeleteJobErrorPrompt: {
            title: 'Error deleting ${entitySingularLower}',
            message: 'There was an error deleting the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to deleting this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleDeleteClientErrorPrompt: {
            title: 'Error deleting ${entitySingularLower}',
            message: 'There was an error deleting the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to deleting this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleDeleteRolegroupErrorPrompt: {
            title: 'Error deleting ${entitySingularLower}',
            message: 'There was an error deleting the following ${entitySingularLower}',
            insufficientPermissionsMessage: 'Insufficient permissions to deleting this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            editButtonLabel: 'Edit ${entitySingularLower}',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        carouselCreateErrorPrompt: {
            title: 'Error editing ${entityString} (${failedCount})',
            successfulCountMessage: 'Successfully edited ${succeededCount} of ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Insufficient permissions to edit this ${entityString}',
            retry: 'Retry',
            errorSectionMessage: 'There was an error editing the following ${entityString}',
            edit: 'Edit ${entityString}',
            cancel: 'Discard ${entityString}',
            close: 'Close'
        },
        carouselRollForwardCreateErrorPrompt: {
            title: 'Error creating ${entityString} (${failedCount})',
            successfulCountMessage: 'Successfully created ${succeededCount} of ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Insufficient permissions to create this ${entityString}',
            retry: 'Retry',
            errorSectionMessage: 'There was an error creating the following ${entityString}',
            edit: 'Back to \'Roll Forward\' options',
            cancel: 'Discard failed ${entityString}',
            close: 'Close'
        },
        carouselEditErrorPrompt: {
            title: 'Error editing ${entityString} (${failedCount})',
            successfulCountMessage: 'Successfully edited ${succeededCount} of ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Insufficient permissions to edit this ${entityString}',
            retry: 'Retry',
            errorSectionMessage: 'There was an error editing the following ${entityString}',
            edit: 'Edit ${entityString}',
            cancel: 'Discard ${entityString}',
            close: 'Close'
        },
        carouselDeleteErrorPrompt: {
            title: 'Error deleting ${entityString} (${failedCount})',
            successfulCountMessage: 'Successfully deleted ${succeededCount} of ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Insufficient permissions to delete this ${entityString}',
            retry: 'Retry',
            errorSectionMessage: 'There was an error deleting the following ${entityString}',
            edit: 'Edit ${entityString}',
            cancel: 'Discard ${entityString}',
            close: 'Close'
        },
        batchedCreateEntityErrorPrompt: {
            successfulCountMessage: 'Successfully created ${succeededCount} of ${attemptedCount} ${entityString}.',
            insufficientPermissionsMessage: 'Insufficient permissions to create this ${entityString}',
            title: 'Error creating ${entityString}',
            errorSectionMessage: 'There was an error creating the following ${entityString}',
            retryButtonLabel: 'Retry',
            cancelButtonLabel: 'Cancel',
            editEntitiesButtonLabel: 'Edit ${entityString}',
            discardEntitiesButtonLabel: 'Discard ${entityString}',
            closeDialogButtonLabel: 'Close',
            tryAgainMessage: 'Do you wish to try again?',
            requests: 'requests'
        },
        deleteMultipleBookingsPrompt: {
            title: 'Delete ${bookingsAliasUpper}? (${bookingsCount})',
            message: 'Do you wish to delete ${pronounString} ${bookingsAliasLower}?',
            okText: 'Yes, delete the ${bookingsAliasUpper}',
            cancelText: 'No, keep the ${bookingsAliasUpper}',
            close: 'Close',
            theseString: 'these',
            thatString: 'that'
        },
        cantPasteBarPrompt: {
            message: 'Select a cell to paste your ${barAliasLower} to'
        },
        deleteMultipleRolerequestsPrompt: {
            title: 'Delete ${rolerequestsAliasUpper}? (${rolerequestsCount})',
            message: 'Do you wish to delete ${pronounString} ${rolerequestsAliasLower}?',
            okText: 'Yes, delete the ${rolerequestsAliasUpper}',
            cancelText: 'No, keep the ${rolerequestsAliasUpper}',
            close: 'Close',
            theseString: 'these',
            thatString: 'that'
        },
        moveRolerequestTimeAllocationPrompt: {
            title: 'Move pending ${pluralFieldName} from ${rolerequestDescription}',
            message: 'Any pending ${pluralFieldName} requested will be moved to a new ‘Draft’  ${rolerequestsSingularLower}, and this  ${rolerequestsSingularLower} will be set to ‘Live’. Assignees who are ‘Requested’ will be unassigned.',
            warningMessage: 'This cannot be undone.',
            okText: 'Move pending ${pluralFieldName}',
            cancelText: 'Keep request open',
            close: 'Close',
            FTEs: 'FTEs'
        },
        removeRolerequestTimeAllocationPrompt: {
            title: 'Remove pending ${pluralFieldName} from ${rolerequestDescription}',
            message: 'Any pending ${pluralFieldName} requested will be removed, and the ${rolerequestsSingularLower} will be set to ‘Live’. Assignees who are ‘Requested’ will be unassigned.',
            warningMessage: 'This cannot be undone.',
            okText: 'Remove pending ${pluralFieldName}',
            cancelText: 'Keep request open',
            close: 'Close',
            FTEs: 'FTEs'
        },
        createRolegroupModal: {
            placeholder: 'New ${roleGroupAlias}',
            title: 'Create ${roleGroupAlias}',
            nameDescriptor: 'Name',
            createLabel: 'Create',
            cancelLabel: 'Cancel',
            currentValue: '${jobDescription} ${roleGroupAlias} ${currentSubsequentNumber}',
            helpMessage: 'Please provide a name for your ${roleGroupAlias}',
            maxLengthValidationMessage: 'Maximum ${maxNameLength} symbols allowed'
        },
        saveAsTemplateModal: {
            placeholder: 'New ${roleAlias} Template',
            title: 'New Template',
            headerTitle: 'Name and save the new ${roleAlias} template.',
            nameDescriptor: 'Name',
            createLabel: 'Save',
            cancelLabel: 'Cancel',
            currentValue: '${rolerequestDescription}',
            defaultNewRole: 'New ${roleAlias}',
            helpMessage: 'Please provide a name for your ${rolerequestDescription} template',
            maxLengthValidationMessage: 'Maximum ${maxNameLength} symbols allowed'
        },
        progressRolesWindow: {
            title: 'Error progressing ${roleAlias}'
        },
        deleteRoleGroupPrompt: {
            title: 'Delete ${roleGroupDescription}?',
            roleGroupInfoMessage: 'There are <bold>${rolesNumber}</bold> ${roleAliasPlural} including <bold>${roleRequests}</bold> requests in this ${roleGroupAliasSingular} between <bold>${roleStartDate}</bold> - <bold>${roleEndDate}</bold>. Deleting this ${roleGroupAliasSingular} will also delete these ${roleAliasPlural} and requests.',
            shouldDeleteQuestion: 'Do you wish to permanently delete <bold>${roleGroupDescription}</bold>?',
            checkboxText: 'Delete ${roleGroupAliasSingular}, ${roleAliasPlural}, and Requests.',
            cancelMessage: 'Keep ${roleGroupAliasSingular}',
            confirmMessage: 'Delete ${roleGroupAliasSingular}'
        },
        extendJobRangeDetailsPagePrompt: {
            okText: 'Yes, amend job dates',
            cancelText: 'No, cancel scheduling',
            title: 'Extend the job?',
            message: 'Do you wish to extend the job period to allow the scheduling of',
            roleTailMessage: 'this role?',
            rolesTailMessage: 'these roles?',
            detailsText: 'New job dates for'
        },
        singleMoveResourceRolegroupErrorPrompt: {
            title: 'Error moving pending ${resourcePluralLower}',
            message: 'There was an error moving the pending ${resourcePluralLower} for the following ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Insufficient permissions to move this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleRemoveResourceRolegroupErrorPrompt: {
            title: 'Error removing pending ${resourcePluralLower}',
            message: 'There was an error removing following ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Insufficient permissions to move this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleMoveFTERolegroupErrorPrompt: {
            title: 'Error moving pending FTEs',
            message: 'There was an error moving the pending FTEs for the following ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Insufficient permissions to move this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        singleRemoveFTERolegroupErrorPrompt: {
            title: 'Error removing pending FTEs',
            message: 'There was an error removing the pending FTEs for the following ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Insufficient permissions to move this ${entitySingularLower}',
            retryButtonLabel: 'Retry',
            discardButtonLabel: 'Discard ${entitySingularLower}'
        },
        updateRolerequestStatusWindow: {
            title: 'Error updating ${roleAlias} status'
        },
        publishRoleErrorPrompt: {
            title: 'Error publishing ${roleAlias}'
        },
        editRolePublicationPrompt: {
            title: 'Error editing ${roleAlias} publication'
        },
        withdrawRoleApplicationPrompt: {
            title: 'Confirm withdrawal',
            question: 'You will no longer be considered for this ${rolerequestSingularLowerAlias}.',
            warning: 'Are you sure you want to withdraw your application?',
            okText: 'Yes, withdraw my application',
            cancelText: 'No, keep my application'
        },
        tableViewHoursValidationPrompt: {
            title: 'Invalid hours',
            message: 'You can book between 0 and 168 hours per week.',
            okText: 'Ok',
            tooltipText: 'Press Esc to close'
        },
        tableViewCellEditErrornPrompt: {
            title: 'Error saving changes',
            errorMessage: 'Making updates on the ${tableViewPageAlias} page may require a combination of creating, editing and deleting ${bookingPluralForm} in the background.',
            contactMessage: 'Please contact your administrator to ensure you have the required permissions.',
            discardText: 'Discard Changes',
            retryText: 'Retry'
        },
        splitBookingsErrorPrompt: {
            title: 'Error saving changes',
            question: 'Selected ${bookingSingularLowerAlias} may have been deleted or edited by someone else, or you have insufficient permissions. Splitting ${bookingPluralLowerAlias} requires a combination of creating and editing ${bookingPluralLowerAlias} in the backround.',
            warning: 'Please refresh the page and try again, or contact your administrator.',
            cancelText: 'OK'
        },
        setPasswordConfirmationPrompt: {
            title: 'Confirmation required',
            question: 'Changing the password will invalidate existing integrations. Are you sure?',
            okText: 'Yes, I\'m sure',
            cancelText: 'No, go back'
        },
        duplicateJobErrorPrompt: {
            title: 'Error saving changes',
            errorMessage: 'This can be due to one or more of the following reasons:',
            suggestedActions: [
                'This ${jobSingularLowerAlias} has either no ${bookingPluralLowerAlias} within the selected date range',
                'The ${jobSingularLowerAlias} duplication queue is full',
                'You have insufficient permissions for the selected ${bookingPluralLowerAlias} as duplicating a ${jobSingularLowerAlias} requires creating ${bookingPluralLowerAlias} in the background.'
            ],
            contactMessage: 'Please refresh the page and try again, or contact your administrator.',
            okText: 'Ok'
        },
        duplicateRoleGroupErrorPrompt: {
            title: 'Error creating ${rolerequestPluralLowerAlias}',
            errorMessage: 'You have insufficient permissions to create these ${rolerequestPluralLowerAlias}.',
            contactMessage: 'Please refresh the page and try again, or contact your administrator.',
            okText: 'Retry',
            cancelText: 'Cancel'
        },
        deleteOperationLogPrompt: {
            title: 'Delete items created in this operation?',
            shouldDeleteQuestion: 'Do you wish to permanently delete items created in this operation? If new jobs were created, any bookings, scenarios or roles created on these jobs will also be deleted.',
            checkboxText: 'Delete items created in this operation',
            cancelMessage: 'Keep items',
            confirmMessage: 'Delete items'
        },
        confirmMassDuplicatePrompt: {
            title: 'Duplicating',
            message: 'Your operation has been queued. You can track its status in the Operation log.',
            closeText: 'OK'
        },
        updateSeriesBookingPrompt: {
            message: 'These bookings will be deleted and recreated with updated details.',
            okText: 'Continue',
            cancelText: 'Cancel'
        }
    },
    comments: {
        editedFlag: 'edited',
        editButtonLabel: 'Edit',
        deleteButtonLabel: 'Delete',
        confirmEditButtonLabel: 'Update',
        cancelButtonLabel: 'Cancel',
        createButtonLabel: 'Add comment',
        createPlaceholder: 'Start typing a comment',
        showMoreButtonLabel: 'Show more comments'
    },
    navigation: {
        title: 'Help',
        contactSupport: 'Contact support',
        helpPageLink: 'Help documentation',
        keyboardShortcuts: 'Keyboard shortcuts',
        legend: 'Legend',
        operationLogButtonLabel: 'Operation log',
        notifications: 'Notifications',
        settings: 'Settings',
        logout: 'Log out'
    },
    skills: {
        noAddedSkills: 'No skills added',
        noRecommendations: 'No new recommendations',
        recommendationTitle: 'Recommendations',
        mySkillsLabel: 'My skills',
        approvalRequestsLabel: 'Approval requests',
        noSkillPendingRequestsLabel: 'No skill update requests',
        noSkillApprovalHistoryLabel: 'No skill approval historic requests',
        skillApprovalHistoryLabel: 'Historic requests are automatically removed after 1 year',
        skillTagLabel: 'Tag',
        skillCategoryLabel: 'Categories',
        defaultSelectedSection: 'All skill categories',
        noMatchingSkillsText: 'No matching skills found.',
        searchPlaceholder: 'Search for a skill',
        saveButtonLabel: 'Add skills',
        cancelButtonLabel: 'Cancel',
        headerTitle: 'Add skills',
        primarySaveButtonLabel: 'Save',
        skillsToAddSectionName: 'Skills to add',
        addSkillsButtonLabel: 'Add skills',
        editSkillsButtonLabel: 'Edit skills',
        skillsSectionTitle: 'Skills',
        expandAllCaption: 'Expand all',
        collapseAllCaption: 'Collapse all',
        singularSkillString: 'skill',
        pluralSkillsString: 'skills',
        markDeletedMessage: 'Marked for deletion. Will be deleted when you save the changes.',
        cancelDeletionMessage: 'Cancel deletion',
        skillLevelDeletedMessage: 'The skill level has been deleted for this skill. A new level needs to be set.',
        validationRequiredText: 'is required',
        validationLessThanText: 'cannot be less than',
        validationGreaterThanText: 'cannot be greater than',
        validationIntegerNumberText: 'should be whole number',
        maxCharactersPrefix: 'Maximum',
        maxCharactersSuffix: 'characters',
        tagsPrefixText: 'Tags:',
        markedForDeletionMessage: 'Marked for deletion. Will be deleted when you save the changes.',
        deleteLabel: 'Delete ${skillName}',
        cancelDeletionSkillLabel: 'Cancel Deletion of ${skillName}',
        noValueMessage: 'No ${fieldInfoAlias} set',
        insufficientPermissionsToEditSuffix: 'Insufficient permissions to edit',
        searchSkillFilterCascaderPlaceholder: 'Select skills and levels',
        noManagerToApproveMessage:'You do not have manager to approve your skills'
    },
    pages: {
        plannerPage: 'Plans',
        adminSettings: 'Settings',
        jobsPage: 'Jobs',
        talentProfile: 'Talent Profile',
        translation: 'My Profile',
        report: 'Report',
        logout: 'Logout',
        collapseText: 'Collapse',
        expandText: 'Expand',
        errorMessages: {
            goBackText: 'Go back to your last page',
            goToText: 'Go to',
            errorCodeMessagePrefix: 'Error',
            defaultHeaderText: 'Something went wrong',
            closeText: 'Close',
            '401': {
                headerText: 'Unauthorized.',
                message: 'Your session has expired. Please log in again.'
            },
            '403': {
                headerText: 'Access denied.',
                message: 'Insufficient permissions.'
            },
            '404': {
                headerText: 'We\'re sorry.',
                message: 'We can\'t seem to find the page you\'re looking for.'
            },
            error: {
                headerText: 'We\'re sorry.',
                message: 'Something seems to have gone wrong.',
                errorCodeMessage: 'There was a problem. Try reloading the page.'
            },
            calculateFteError: {
                headerText: 'Could not calculate FTE',
                message: 'Please select a valid FTE reference diary for this job.'
            }
        }
    },
    manageEntityLookupWindow: {
        searchForLabel: 'Search for a',
        showLessLabel: 'Show less',
        showMoreLabels: {
            prefix: 'Show',
            suffix: 'more'
        },
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'was found with this name.'
    },
    contextualMenu: {
        createEntity: 'Create',
        editEntity: 'Edit',
        deleteEntity: 'Delete',
        copyEntity: 'Copy',
        rollForwardEntity: 'Duplicate',
        cutEntity: 'Cut',
        pasteEntity: 'Paste',
        clearEntity: 'Clear',
        setDateRange: 'Set date range',
        loadingCaption: '...loading',
        restart: 'Restart',
        archive: 'Archive',
        reject: 'Reject',
        makeLive: 'Make live',
        submitRequest: 'Submit request',
        rollForwardTooltipText: 'Copy the selected ${bookingEntityAlias} to another ${jobEntityAlias} or date',
        unassignResource: 'Unassign from ${rolerequestSingularLowerAlias}',
        createCriteriaRole: 'Create ${rolerequestSingularCapitalAlias} by requirements',
        createRoleByName: 'Create ${rolerequestSingularCapitalAlias} by name',
        movePendingResources: 'Move pending ${resourcePluralLowerAlias}',
        removePendingResources: 'Remove pending ${resourcePluralLowerAlias}',
        movePendingFTEs: 'Move pending FTEs',
        removePendingFTEs: 'Remove pending FTEs',
        manageBudget: 'Manage budget',
        saveAsTemplate: 'Save as template',
        showInViewLabel: 'Show in ${pluralViewNameAlias} view'
    },
    detailsPane: {
        paneLabel: 'pane',
        inModalLabel: 'pane in Modal',
        showPanePrefixLabel: 'Show',
        showPaneSuffixLabel: 'pane',
        suggestionsSingular: 'Suggestion',
        suggestionsPlural: 'Suggestions'
    },
    blankValues: {
        notFoundString: 'not found',
        dateNotFoundString: 'Date not found',
        noChargeTypeSetString: 'No charge type set',
        unspecifiedString: 'unspecified',
        noString: 'No',
        setString: 'set'
    },
    hotKeysHelpWindow: {
        generalLabel: 'General',
        helpWindowTitle: 'Keyboard shortcuts',
        bookingsLabel: '${bookingPluralCapitalAlias}',
        jobsLabel: '${jobPluralCapitalAlias}',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        rolesLabel: '${rolerequestPluralCapitalAlias}',
        dateManipulationLabel: 'Date manipulation',
        menusLabel: 'Menus',
        filtersLabel: 'Filters',
        viewOptionsLabel: 'View options',
        createLabel: 'Create',
        helpLabel: 'Help',
        helpDocumentationLabel: 'Help Documentation',
        orLabel: 'OR',
        plannerPage: {
            addLabel: 'Add',
            editLabel: 'Edit',
            cutLabel: 'Cut',
            copyLabel: 'Copy',
            pasteLabel: 'Paste',
            deleteLabel: 'Delete',
            restartLabel: 'Restart',
            archiveLabel: 'Archive',
            rejectLabel: 'Reject',
            makeLiveLabel: 'Make live',
            submitRequestLabel: 'Submit request',
            expandDateRangeLabel: 'Expand date range',
            reduceDateRangeLabel: 'Reduce date range',
            setRangeTo5DayLabel: 'Set date range to 5 Day',
            setRangeTo7DaysLabel: 'Set date range to 7 Days',
            setRangeTo10DaysLabel: 'Set date range to 10 Days',
            setRangeTo2WeekLabel: 'Set date range to 2 week',
            setRangeTo4WeeksLabel: 'Set date range to 4 weeks',
            setRangeTo6WeeksLabel: 'Set date range to 6 weeks',
            setRangeTo2MonthsLabel: 'Set date range to 2 Months',
            setRangeTo3MonthsLabel: 'Set date range to 3 Months',
            setRangeTo6MonthsLabel: 'Set date range to 6 Months',
            setRangeTo1YearLabel: 'Set date range to 1 Year',
            goToTodayLabel: 'Go to today',
            addMenuLabel: 'Add Menu',
            editMenuLabel: 'Edit Menu',
            viewMenuLabel: 'View Menu',
            addJobLabel: 'Add a ${jobSingularLowerAlias}',
            findResourcesLabel: 'Find ${resourcePluralLowerAlias}',
            defaultDensityLabel: 'Set display density to default',
            mediumDensityLabel: 'Set display density to medium',
            expandedDensityLabel: 'Set display density to expanded',
            helpWindowLabel: 'Help window',
            openLegendLabel: 'Open colour scheme legend',
            showHideWeekendsLabel: 'Show / hide weekends',
            showHidePotentialConflictsLabel: 'Show / hide Potential Conflicts',
            addRoleByName: 'Add ${rolerequestSingularCapitalAlias} by name',
            addRoleByRequirements: 'Add ${rolerequestSingularCapitalAlias} by requirements',
            editRoleByNameLabel: 'Edit ${rolerequestSingularCapitalAlias} by name',
            editRoleByCriteriaLabel: 'Edit ${rolerequestSingularCapitalAlias} by requirements',
            rollForwardLabel: 'Duplicate',
            splitBookingLabel: 'Split ${bookingSingularLowerAlias}',
            movePendingResources: 'Move pending resources',
            removePendingResources: 'Remove pending resources',
            movePendingFTEs: 'Move pending FTEs',
            removePendingFTEs: 'Remove pending FTEs',
            showInViewLabel: 'Show in ${jobPluralLowerAlias}/${resourcePluralLowerAlias} view',
            dragToSelect: 'Drag to select'
        },
        jobsPage: {
            addLabel: 'Add',
            editLabel: 'Edit',
            deleteLabel: 'Delete',
            addMenuLabel: 'Add menu',
            editMenuLabel: 'Edit menu',
            compactDensityLabel: 'Set display density to compact',
            defaultDensityLabel: 'Set display density to default',
            expandedDensityLabel: 'Set display density to expanded',
            helpWindowLabel: 'Help window'
        },
        roleInboxPage: {
            movePendingResources: 'Move pending resources',
            removePendingResources: 'Remove pending resources',
            makeLiveLabel: 'Make live',
            submitRequestLabel: 'Submit request',
            restartLabel: 'Restart',
            rejectLabel: 'Reject',
            deleteLabel: 'Delete',
            archiveLabel: 'Archive',
            addRoleByName: 'Add ${rolerequestSingularLowerAlias} by name',
            addRoleByRequirements: 'Add ${rolerequestSingularLowerAlias} by requirements',
            editRoleByNameLabel: 'Edit ${rolerequestSingularCapitalAlias} by name',
            editRoleByCriteriaLabel: 'Edit ${rolerequestSingularCapitalAlias} by requirements',
            helpWindowLabel: 'Help window',
            publishToMarketplaceLabel: 'Publish to ${marketplaceAlias}'
        },
        adminSettingsPage: {
            addNewItemLabel: 'Add new item',
            helpWindowLabel: 'Help window'
        }
    },
    validationMessages: {
        unableToSaveChanges: 'Unable to save changes. Please review highlighted errors.',
        mandatoryFieldsNotCompleted: 'Mandatory fields must be completed',
        formHasErrors: 'This form has errors',
        activeUserText: 'is inactive. Set to an active or unassigned resource to save changes',
        fieldMandatoryText: 'This field is mandatory',
        mandatoryText: 'is mandatory',
        minimumText: 'Minimum',
        maximiumText: 'Maximum',
        maxCharactersPrefix: 'Maximum',
        maxCharactersSuffix: 'characters',
        selectValidText: 'Please select a valid',
        invalidNamePrefix: 'Invalid',
        invalidNameSuffix: 'name',
        integerTypeText: 'is of type integer',
        maxSelectedYearText: 'Selected year should be before 10000',
        minSelectedYearText: 'Selected year should be after 0000',
        startDateInvalid: 'Invalid start date provided',
        jobEndBeforeJobStart: 'End cannot be before Start',
        fteMaxValidationText: 'Total FTE exceeds requested amount',
        fteString: 'FTE'
    },
    attachmentsMessages: {
        uploadButtonLabel: 'Upload documents',
        deleteButtonLabel: 'Delete',
        fileTooLargeLabel: 'Document upload failed: File is too large',
        fileTypeForbiddenLabel: 'Document upload failed: File type is not allowed',
        noFilesUploadedLabel: 'You don\'t have any documents uploaded',
        uploadsLimitReachedLabel: 'Document limit reached: delete documents to upload more',
        allowedFormatsLabel: 'Documents can be in the following formats: ${formattedAcceptedFileTypes}',
        maxFileSizeLabel: 'There is a maximum file size of ${defaultMaxFileSizeMb}MB for each document',
        maxUploadsAllowed: 'You can upload a maximum of ${defaultMaxUploadsAllowed} documents'
    },
    treeSelectionMessages: {
        chargeMode: 'Charge Mode',
        revenue: 'Revenue',
        cost: 'Cost',
        profit: 'Profit',
        dateRange: 'Date Range',
        timeAllocation: 'Time Allocation',
        selectFieldsCaption: 'Select fields',
        addButtonCaption: 'Add',
        historyFieldsSuffix: '(overwrite from date)'
    },
    contextualDropdown: {
        detailsLabel: 'details',
        editLabel: 'Edit',
        duplicateLabel: 'Duplicate',
        viewLabel: 'View',
        newLabel: 'New',
        deleteLabel: 'Delete',
        roleByName: '${roleSingularCapitalAlias} by name',
        roleByRequirements: '${roleSingularCapitalAlias} by requirements',
        newRoleLabel: 'New ${roleSingularLowerAlias}',
        editDetailsLabel: 'Edit details',
        archiveLabel: 'Archive',
        restartLabel: 'Restart',
        rejectLabel: 'Reject',
        makeLiveLabel: 'Make live',
        submitRequestLabel: 'Submit request',
        createLabel: 'Create',
        unassignLabel: 'Unassign from',
        createBookingEllipsisLabel: 'Create ${bookingSingularCapitalAlias}...',
        createRoleByNameEllipsisLabel: 'Create ${rolerequestSingularCapitalAlias} by name...',
        editEllipsisLabel: 'Edit...',
        goToProfileEllipsisLabel: 'Go to profile',
        copyProfileUrlEllipsisLabel: 'Copy profile URL',
        movePendingFTE: 'Move pending FTEs',
        removePendingFTE: 'Remove pending FTEs',
        movePendingResourcesLabel: 'Move pending ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Remove pending ${resourcePluralLowerAlias}',
        manageBudgetLabel: 'Manage budget',
        saveAsTemplateLabel: 'Save as template',
        publishToMarketplaceLabel: 'Publish to ${marketplaceAlias}',
        editRolePublicationButtonLabel: 'Edit ${rolerequestSingularLowerAlias} publication',
        removeRolePublicationButtonLabel: 'Remove ${rolerequestSingularLowerAlias} publication',
        detailsJobLabel: '${jobSingularCapitalAlias} details',
        editJobLabel: 'Edit ${jobSingularLowerAlias}',
        duplicateJobLabel: 'Duplicate ${jobSingularLowerAlias}',
        viewRoleRequestGroupLabel: 'Compare ${rolerequestgroupSingularLowerAlias}',
        newRoleRequestGroupLabel: 'Create ${rolerequestgroupSingularLowerAlias}',
        openLabel: 'Open',
        moreOptionsButtonLabel: 'More options'
    },
    progressRolesWindow: {
        totalText: 'Total change to job',
        cancelText: 'Cancel',
        progressRoleLabel: 'Progress role',
        jobLabel: 'Job',
        roleLabel: 'Role',
        dateRangeLabel: 'Date range',
        budgetLabel: 'Budget',
        makeLive: {
            selectMessage: 'Select ${rolePluralAlias} to progress to ${bookingPluralAlias}',
            title: 'Make live',
            submitText: 'Make live'
        },
        submitRequest: {
            selectMessage: 'Select ${rolePluralAlias} to request as ${bookingPluralAlias}',
            title: 'Submit request',
            submitText: 'Submit request'
        }
    },
    progressRoleErrors: {
        alreadyLiveMsg: 'Already live',
        noPermissionsMsg: 'Insufficient permissions'
    },
    rejectRolesWindow: {
        title: 'Reject',
        submitText: 'Reject request',
        cancelText: 'Cancel',
        rejectErrorMessage: 'There was a problem. The action couldn\'t be completed',
        buttonLabel: 'Close',
        rejectReasonText: 'Select reason for rejecting this request',
        errorDialogTitle: 'Error in Role Transition',
        customReasonPlaceholderText: 'Write custom reason for rejecting this role',
        jobLabel: 'Job',
        roleLabel: 'Role',
        dateRangeLabel: 'Date range',
        budgetLabel: 'Budget',
        statusLabel: 'Status'
    },
    carousel: {
        defaultRoleName: 'New ${rolerequestSingularCapitalAlias}',
        ungrouped: 'No ${rolerequestgroupSingularLowerAlias} set'
    },
    rollForwardDialog: {
        title: 'Duplicate ${bookingEntityAlias}',
        submitText: 'Create ${bookingEntityAlias}',
        cancelText: 'Cancel',
        duplicateBooking: '${bookingSingularCapitalAlias} duplicated',
        duplicateBookings: '${bookingPluralLowerAlias} duplicated',
        forwardOptions: {
            alertMessage: 'Copying ${noOfBooking} ${bookingEntityAlias}',
            destinationStartDateLabel: 'Destination start date',
            destinationStartDateLabelError: 'Destination start date is mandatory',
            destinationStartDateLabelErrorDescription: 'Relative positions of selected ${bookingEntityAlias} will be maintained after duplicating.',
            destinationJobLabel: 'Destination ${jobSingularAlias}',
            destinationJobError: 'Destination ${jobSingularAlias} is mandatory',
            destinationBookingTypeLabel: 'Destination ${bookingEntityAlias} type',
            destinationBookingTypeError: 'Destination ${bookingEntityAlias} type is mandatory',
            destinaltionJobExplanation: 'Destination ${jobSingularAlias} ranges will be amended accordingly',
            offsetExplanation: 'From the start of the earliest selected ${bookingEntityAlias}',
            editBookingLabel: 'Edit ${bookingEntityAlias} after duplicating',
            editBookingDescription: 'Opens the editing dialog to make additional changes to the new ${bookingEntityAlias}',
            valuePostfix: '${bookingEntityAlias}',
            keepBookingTypeText: 'Keep ${bookingEntityAlias} type as it is',
            onPrefix: 'On',
            inPrefix: 'In'
        }
    },
    repeatBookingDialog: {
        createRepeatBooking: {
            title: 'Set recurrence',
            submitText: 'Save',
            cancelText: 'Cancel',
            repeatEvery: 'Repeat every',
            repeatUntil: 'Until',
            noRepeatText: 'Does not repeat',
            positiveWholeNumberErrorMessage: 'Please enter a positive whole number'
        },
        editRepeatBooking: {
            title: 'Which recurring bookings to edit?',
            selectedOnly: 'Selected booking only',
            selectedAndFuture: 'Selected and following bookings',
            allBookings: 'All bookings in the series',
            actionLabel: 'Edit series',
            singleBookingMessage: 'You\'re editing a single booking in a recurring series.',
            singleAndFutureBookingsMessage: 'You\'re editing this and all following bookings in a recurring series.',
            allBookingsMessage: 'You\'re editing all bookings in a recurring series.',
            partOfSeriesMessage: 'This booking is part of a recurring series.',
            updateFailureMessage: 'Failed to update booking series.',
            bulkBookingMessage: 'You\'re editing single occurrences of bookings in a recurring series.',
            editedSingleBookingMessage: 'This booking is part of recurring series, but has been edited separately. Some details may be different.'
        },
        deleteRepeatBooking: {
            title: 'Which recurring bookings to delete?',
            cannotBeUndone: 'This cannot be undone.',
            selectedOnly: 'Selected booking only',
            selectedAndFuture: 'Selected and following bookings',
            allBookings: 'All bookings in the series'
        },
        doesNotRepeatText: 'does not repeat',
        repeatsEveryText: 'repeats every',
        on: 'on',
        starting: 'starting',
        until: 'until',
        intervalText: {
            day: 'day',
            days: 'days',
            week: 'week',
            weeks: 'weeks',
            month: 'month',
            months: 'months'
        },
        dayOfWeekText: {
            0: 'Sunday',
            1: 'Monday',
            2: 'Tuesday',
            3: 'Wednesday',
            4: 'Thursday',
            5: 'Friday',
            6: 'Saturday'
        },
        dayText: 'day',
        confirmRepeatBookingPrompt: {
            title: 'Creating recurring bookings',
            message: 'Your operation has been queued. You can track its status in the Operation log.',
            closeText: 'OK'
        },
        auditTrail: {
            recurringIntervalCreated: 'Recurrence series created to Repeat every',
            recurringIntervalEdited: 'Recurrence interval edited to Repeat every',
            recurrentSeries: 'Recurrent series'
        },
        seriesText: 'series'
    },
    jobDuplicateDialog: {
        title: 'Duplicate ${jobSingularLowerAlias}',
        submitText: 'Create ${bookingPluralLowerAlias}',
        cancelText: 'Cancel',
        newPrefix: 'New',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        searchToSelect: 'Search to select',
        changeRangeToIncludeBookingsString: 'Change range to include all ${bookingPluralLowerAlias}?',
        forwardOptions: {
            destinationStartDateLabel: 'Destination start date',
            destinationStartDateLabelError: 'Destination start date is mandatory',
            destinationStartDateLabelErrorDescription: 'Relative positions of ${bookingPluralLowerAlias} will be maintained after duplicating.',
            outOfRangeEntityExplanation: 'It looks like some  ${bookingPluralLowerAlias} are outside the start and end date of this ${jobSingularLowerAlias}.',
            rolesPositionWarning: 'Existing ${rolerequestPluralLowerAlias} will remain on the selected ${jobSingularLowerAlias} and not be duplicated.',
            jobRangeLabelError: 'Destination ${jobSingularLowerAlias} date range is mandatory',
            maximumJobRangeMessage: '${jobSingularCapitalAlias} date range must be within 24 months',
            dateRangeValueMandatory: 'This field is mandatory',
            destinationJobLabel: 'Destination ${jobSingularLowerAlias}',
            destinationJobError: 'Destination ${jobSingularLowerAlias} is mandatory',
            destinationBookingTypeLabel: 'Destination ${bookingSingularLowerAlias} type',
            destinaltionJobExplanation: 'Destination ${jobSingularLowerAlias} ranges will be amended accordingly',
            offsetExplanation: 'From the start of the first ${bookingSingularLowerAlias} in the date range',
            dateRangeForJobLabel: 'Date range for selected ${jobSingularLowerAlias}',
            selectedJobLabel: 'Selected ${jobSingularLowerAlias}',
            destinationBookingTypeError: 'Destination ${bookingSingularLowerAlias} type is mandatory',
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Keep ${bookingEntityAlias} type as it is',
            onPrefix: 'On',
            inPrefix: 'In'
        }
    },
    //mass duplicate settings
    massDuplicateJobs: {
        filterTitle: 'Add filters',
        placeholderLabel: 'Search by ${jobAlias} name...',
        modalTitle: 'Select a ${jobAlias}',
        resultSingular: '${rowCount} result',
        resultPlural: '${rowCount} results',
        upToResults: 'Up to ${rowCount} results',
        massDuplicateJobsTitle: 'Duplicate Data',
        massDuplicateJobsFieldTitle: 'Select jobs for mass duplication',
        massDuplicateJobsSubLabel: '  ',
        saveButtonLabel: 'Duplicate Jobs',
        cancelButtonLabel: 'Clear',
        formHasErrorsMessage: 'This form has errors',
        massDuplicateJobsInfoText: 'Filter out the jobs you want to duplicate.',
        massDuplicateJobsInfoTips1: '<b>Roles</b> in the selected jobs will not be duplicated',
        massDuplicateJobsInfoTips2: 'Relative dates of jobs and bookings will be maintained, e.g. A booking starting 3 days after \nthe job start date will be created 3 days after the start date of the new job',
        massDuplicateJobsInfoTips3: 'Jobs with a valid <i>Next related job</i> <b>will not be duplicated</b>. Only its bookings will be copied \nover to the related job.',
        massDuplicateJobsInfoTips4: 'Bookings assigned to <b>inactive resources</b> will be duplicated and remain assigned to them',
        massDuplicateJobsTextNewBooking: 'Create new bookings based on',
        massDuplicateJobsNewBookingTextBookings: 'Bookings',
        massDuplicateJobsNewBookingTextActuals: 'Actuals',
        massDuplicateJobsNewBookingTipMessage: 'Creates weekly bookings from timesheet data',
        massDuplicateJobsTextDestinationBooking: 'Destination booking type',
        massDuplicateJobsDestinationOption1: 'Planned bookings',
        massDuplicateJobsDestinationOption2: 'Unconfirmed bookings',
        massDuplicateJobsDestinationOption3: 'Keep booking type as it is',
        massDuplicateJobsDaterangeText: 'Select jobs and bookings between',
        massDuplicateJobsNewJobsText: ' Create new jobs',
        forwardOptions: {
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Keep ${bookingEntityAlias} type as it is',
            chooseHowJobsCreatedTitle: 'Choose how jobs and bookings are created',
            chooseHowJobsCreatedTitleExtra: 'Jobs with a <i>Next related job</i> will have the new booking start dates offset from the new job\'s start \ndate by the same amount as on the original job',
            dateRangeForJobLabel: 'Jobs that started between',
            fieldMandatoryText: 'This field is mandatory',
            createNewJobsLabel: 'Create new jobs',
            inPrefix: 'In',
            onPrefix: 'On',
            inPrefixExtra: 'From the start of the first booking in the \nselected date range',
            onPrefixExtra: 'New jobs start on this date. Booking positions \nare maintained relative to the start date.',
            createNewBookingsLabel: 'Create new bookings based on',
            bookings: 'Bookings',
            actuals: 'Actuals',
            destinationBookingTypeLabel: 'Destination booking type',
            plannedBookings: 'Planned bookings',
            unconfirmedBookings: 'Unconfirmed bookings',
            keepBookingType: 'Keep booking type as it is',
            nextRelatedJobsLabel: 'Only duplicate jobs with a valid Next related job',
            newJobNamesLabel: 'New job names',
            explanationNewJobNames: 'If the original job has a Next related job selected, the job will not be renamed',
            newJobsNamesDefaultOption: 'Default',
            newJobsNamesDefaultOptionExample: 'Example: Copy of Aqua Audit 2023',
            newJobsNamesOriginalOption: 'Use original name',
            newJobsNamesReplaceOption: 'Replace',
            newJobsNamesReplaceWithOption: 'with',
            newJobsNamesReplaceOptionExample: 'Example: Aqua Audit 2023 becomes Aqua Audit 2024',
            newJobsNamesCantFindTextToReplaceLabel: 'If we can\'t find the text to replace, the default name will be used (Copy of...)'
        },
        massDuplicateJobsReviewTitle: 'Summary: ${totalNumberOfJobs} jobs selected to roll forward',
        massDuplicateJobsReviewPoint1: '${totalNumberOfBookings} total bookings selected',
        massDuplicateJobsReviewPoint2: '${totalNumberOfConfirmedHours} confirmed hours',
        massDuplicateJobsReviewPoint3: '${totalNumberOfUnconfirmedHours} unconfirmed hours',
        massDuplicateJobsReviewPoint4: '${totalNumberOfJobsWithoutNextJob} new jobs will be created',
        massDuplicateJobsReviewPoint5: '${totalNumberOfJobsWithNextJob} jobs with a Next related job will only have its bookings or actuals rolled forward',
        massDuplicateJobsReviewSummaryButtonLabel: 'Refresh Summary'
    },
    roleGroupDuplicateDialog: {
        title: 'Duplicate ${rolerequestgroupSingularLowerAlias}',
        submitText: 'Duplicate ${rolerequestgroupSingularLowerAlias}',
        cancelText: 'Cancel',
        forwardOptions: {
            scenarioNameLabel: '${rolerequestgroupSingularCapitalAlias} name',
            destinationJobLabel: 'Destination ${jobSingularLowerAlias}',
            destinationJobError: 'Destination ${jobSingularLowerAlias} is mandatory',
            destinationStartDateLabel: 'Destination start date',
            destinationStartDateLabelError: 'Destination start date is mandatory',
            destinationStartDateLabelErrorDescription: 'Relative positions of ${rolerequestPluralLowerAlias} will be maintained after duplicating.',
            destinaltionStartDateExplanation: 'from the start of the first ${rolerequestSingularLowerAlias} in the ${rolerequestgroupSingularLowerAlias}',
            newRoleGroupDescriptionLabel: 'Description',
            onPrefix: 'On',
            inPrefix: 'In',
            scenarioNameError: 'This field is mandatory'
        }
    },
    peopleFinderDialog: {
        createBookingText: 'Create ${bookingEntityAlias}',
        createRoleText: 'Create ${roleEntityAlias} by name',
        closeText: 'Close',
        filterTitle: 'Add filters',
        infoToolTipText: 'Active date range in plan',
        refreshButtonLabel: 'Refresh',
        profileUrlCopied: 'Profile URL copied',
        plannerPage: {
            title: 'Find ${resourceEntityAlias}',
            emptyFinderText: 'Select criteria to find ${resourceEntityAlias}',
            resourceFoundInRangeText: '${resourcePluralCapitalAlias} found during <bold>${startDate} - ${endDate}</bold> will be shown here.',
            summaryText: '<bold>${resourceCount} results</bold> for range'
        },
        profilePage: {
            title: 'View other profile',
            emptyFinderText: 'Select criteria to find profile',
            summaryText: '<bold>${resourceCount} results</bold>'
        }
    },
    jobFilterDialog: {
        filterTitle: 'Add filters',
        placeholderLabel: 'Search by ${jobAlias} name...',
        modalTitle: 'Select a ${jobAlias}',
        resultSingular: '${rowCount} result',
        resultPlural: '${rowCount} results',
        upToResults: 'Up to ${rowCount} results'
    },
    operationsLogDialog: {
        heading: 'Operation log',
        dataGridExplanations: 'Records operations performed on our site'
    },
    expandedOperationsLogDialog: {
        heading: 'Job Duplication',
        jobsRollForwarded: 'jobs rolled forward',
        bookingsCreated: 'total bookings created',
        newJobsCreated: 'new jobs created',
        jobsDuplicated: 'jobs with a Next related job only had its bookings or actuals rolled forward'
    },
    actionBarWithFooterButtons: {
        saveButtonLabel: 'Save changes',
        cancelButtonLabel: 'Cancel',
        formHasErrorsMessage: 'This form has errors'
    },
    cMeSection: {
        title: 'C-me traits'
    },
    educationSection: {
        formConfiguration: {
            education: 'Education',
            dialogConfig: {
                saveText: 'Save',
                cancelText: 'Cancel'
            },
            institutionLabel: 'Institution',
            institutionError: 'Institution is mandatory',
            fieldLabel: 'Field',
            fieldError: 'Field is mandatory',
            degreeLabel: 'Degree',
            noResultsFoundMessage: 'No degree options set by your Administrator',
            startDateLabel: 'Start date',
            endDateLabel: 'End date',
            endFieldError: 'End date cannot be before Start date',
            endDateDescription: 'End date may refer to expected graduation month/year',
            detailsLabel: 'Details',
            addInstitutionPlaceholder: 'Add Institution',
            addFieldPlaceholder: 'Add Field',
            addDegreePlaceholder: 'Add Degree',
            addDetailsPlaceholder: 'Add Details',
            maxCharErrorPrefix: 'Maximum',
            maxCharErrorSuffix: 'characters are allowed'
        },
        addEducationButtonLabel: 'Add education',
        editEducationButtonLabel: 'Edit education'
    },
    experienceSection: {
        formConfiguration: {
            experience: 'Experience',
            companyLabel: 'Company',
            roleLabel: 'Role',
            locationLabel: 'Location',
            startDateLabel: 'Start date',
            endDateLabel: 'End date',
            detailsLabel: 'Details',
            endDateFieldError: 'End date cannot be before Start date',
            roleError: 'Role is mandatory',
            companynameError: 'Company name is mandatory',
            maxCharErrorPrefix: 'Maximum',
            maxCharErrorSuffix: 'characters are allowed',
            addDetailsPlaceholder: 'Add details',
            addCompanyPlaceholder: 'Add company',
            addRolePlaceholder: 'Add role',
            addLocationPlaceholder: 'Add location',
            dialogConfig: {
                saveText: 'Save',
                cancelText: 'Cancel'
            }
        },
        addExperienceButtonLabel: 'Add experience',
        editExperienceButtonLabel: 'Edit experience'
    },
    cookieConsentBanner: {
        accept: 'Accept',
        title: 'This website uses cookies',
        info: 'We use cookies to improve your experience and for analytics purposes. By clicking "Accept", you consent to the use of these cookies. For more information on how we use cookies and to learn about your privacy rights, please read our <cookie>Cookie Policy</cookie> and <privacy>Privacy Policy</privacy>.'
    },
    banners: {
        maintenanceStatusBanner: {
            dismissLabel: 'Dismiss',
            singleDayInfo: '<bold>Scheduled maintenance on ${startDate}, from ${startTime} to ${endTime}</bold>. Retain will be temporarily unavailable as we improve our site.',
            multiDayInfo: '<bold>Scheduled maintenance from ${startDate}, at ${startTime} to ${endDate}, at ${endTime}</bold>. Retain will be temporarily unavailable as we improve our site.'
        },
        jobsPageBookmarkBanner: {
            dismissLabel: 'Dismiss',
            description: 'We have changed the URL of this page. Update your bookmark if needed'
        }
    },
    lastLoginSection: {
        minutesAgo: '${timeAgo}m ago',
        hoursAgo: '${timeAgo}h ago',
        daysAgo: '${timeAgo}d ago',
        now: 'Now',
        lastLoginLabel: 'Last login'
    },
    longRunningTaskBanners: {
        duplicateJob: {
            processing: {
                title: 'Duplicate \'${jobDescription}\' ${progress}% complete...',
                content: {
                    message: 'We\'ll notify you once we are ready.',
                    progressSeparator: 'of'
                }
            },
            completed: {
                title: 'Your ${jobSingularLowerAlias} is ready',
                content: {
                    message: 'Duplicated \'${jobDescription}\' successfully.'
                }
            },
            failed: {
                title: 'Failed to duplicate ${jobSingularLowerAlias}',
                content: {
                    message: 'Duplicate \'${jobDescription}\' failed.',
                    retry: 'Retry'
                }
            },
            queued: {
                title: 'Your duplicate ${jobSingularLowerAlias} has been queued',
                content: {
                    message: 'We\'ll notify you once we are ready.'
                }
            },
            multiple: {
                title: 'Your duplicate ${jobSingularLowerAlias} has been queued',
                content: {
                    message: 'Multiple operations are queued.',
                    button: 'View Operation log'
                }
            }
        },
        longRunningjobIndicatorTooltipMessage: 'There\'s a long-running operation currently in progress for this ${entityAlias}.'
    },
    cMeProfiling: {
        aboutcMeColours: 'About C-me colours',
        about: 'About',
        cMeColourProfilingDialog: {
            dialogTitle: 'About C-me colour profiling',
            topMessageLine1: 'Human behaviours can be complicated to describe. We\'ve partnered with C-me, a behaviour profiling service that associates behaviours with colours. A resource\'s C-me data tells you about their preferred ways of doing things, expressed in the language of four colours.',
            topMessageLine2: 'We automatically update a resource\'s skills with traits from their most dominant colour.',
            redBox: {
                title: 'Red',
                boxList: [
                    'Action oriented',
                    'Assertive',
                    'Competitive',
                    'Decisive',
                    'Determined',
                    'Fast paced',
                    'Strategic'
                ]

            },
            yellowBox: {
                title: 'Yellow',
                boxList: [
                    'Dynamic presenter',
                    'Energetic',
                    'Flexible',
                    'Imaginative',
                    'Inspirational',
                    'Optimistic',
                    'Spontaneous'
                ]
            },
            greenBox: {
                title: 'Green',
                boxList: [
                    'Collaborative',
                    'Democratic',
                    'Diplomatic',
                    'Empathetic',
                    'Non-judgemental',
                    'Patient',
                    'Values driven'
                ]
            },
            blueBox: {
                title: 'Blue',
                boxList: [
                    'Analytical',
                    'Disciplined',
                    'Methodical',
                    'Organised',
                    'Precise',
                    'Systematic',
                    'Thorough'
                ]
            }
        }
    },
    summaryPage: {
        personalGreeting: 'Hello',
        customiseButtonLabel: 'Customise',
        doneButtonLabel: 'Done',
        arrangeWidgetsText: 'Click and drag widgets to rearrange them.',
        welcomeGreeting: 'Welcome to Retain',
        summaryDurationOptionLabels: {
            4: 'Next 4 weeks',
            6: 'Next 6 weeks',
            12: 'Next 12 weeks'
        },
        widgets: {
            yourRequests: {
                title: 'Your requests',
                emptyStateMessage: 'No requests submitted by you for this time period'
            },
            plannedHours: {
                title: 'Planned hours',
                emptyStateMessage: ''
            },
            ongoingJobs: {
                title: 'Ongoing ${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            actionRequired: {
                title: '${rolerequestPluralCapitalAlias} to action',
                emptyStateMessage: 'No ${rolerequestPluralLowerAlias} to action in this time period'
            },
            jobsOverBudgetDetails: {
                title: '${jobPluralCapitalAlias} over budget',
                emptyStateMessage: 'No ongoing ${jobPluralLowerAlias} over budget'
            },
            chargeableUtilisation: {
                title: 'Chargeable utilisation',
                emptyStateMessage: ''
            },
            utilisation: {
                title: 'Utilisation',
                emptyStateMessage: ''
            },
            pendingRequests: {
                title: '${rolerequestPluralCapitalAlias} to action',
                subTitleUnit: 'hours',
                emptyStateMessage: ''
            },
            unassignedBookingsDetails: {
                title: 'Unassigned ${bookingPluralLowerAlias}',
                emptyStateMessage: 'No unassigned ${bookingPluralLowerAlias} in this time period'
            },
            unassignedBookings: {
                title: 'Unassigned ${bookingPluralLowerAlias}',
                subTitleUnit: '${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            upcomingBookingsDetails: {
                title: 'Your upcoming ${bookingPluralLowerAlias}',
                emptyStateMessage: 'No ${bookingPluralLowerAlias} assigned to you in this time period'
            }
        },
        widgetDetailsTotalsUnit: {
            bookings: '${bookingPluralLowerAlias}',
            jobs: '${jobPluralLowerAlias}',
            hours: 'hours',
            planned: 'planned',
            unconfirmed: 'unconfirmed',
            availability: 'availability',
            requests: 'requests',
            rejected: 'Rejected',
            requested: 'Requested',
            draft: 'Draft',
            live: 'Live'
        },
        budgetConsumedText: 'Budget consumed',
        pageTitle: 'Summary',
        configurationPane: {
            arrangeButtonLabel: 'Arrange',
            searchPlaceholder: 'Search',
            openOnLoginLabel: 'Open this page on login',
            emptySearchMessage: 'No results were found.',
            sectionTitles: {
                personal: 'Personal',
                bookings: '${bookingPluralCapitalAlias}',
                jobs: '${jobPluralCapitalAlias}',
                resources: '${resourcePluralCapitalAlias}',
                roles: '${rolerequestPluralCapitalAlias}'
            }
        },
        explainSummaryPageTextSecurity: 'Choose widgets they can add to their Summary page. Personal widgets are available to all users. \n\nWidgets will not show records and fields hidden for this security profile.'
    },
    listPage: {
        pageTitle: 'Lists'
    }
};
