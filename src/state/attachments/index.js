import { TABLE_NAMES } from '../../constants';
import { DEFAULT_MAX_FILE_SIZE_MB, DEFAULT_MAX_UPLOADS_ALLOWED, DEFAULT_ALLOWED_FILE_TYPES } from '../../constants/attachmentsConsts';

const config = {
    maxCount: DEFAULT_MAX_UPLOADS_ALLOWED,
    maxFileSizeMB: DEFAULT_MAX_FILE_SIZE_MB,
    allowedFileTypes: DEFAULT_ALLOWED_FILE_TYPES.split(',')
};

export default {
    map: {},
    config: {
        [TABLE_NAMES.RESOURCE]: config,
        [TABLE_NAMES.JOB]: config,
        [TABLE_NAMES.CLIENT]: config,
        [TABLE_NAMES.BOOKING]: config
    }
};

export const createAttachment = (tableName, attachment) => {
    return {
        id: attachment[`${tableName}attachment_guid`],
        name: attachment[`${tableName}attachment_filename`],
        size: attachment[`${tableName}attachment_filesize`],
        entityId: attachment[`${tableName}attachment_${tableName}_guid`],
        createdBy: attachment[`${tableName}attachment_createdby_resource_guid`],
        createdOn: attachment[`${tableName}attachment_createdon`],
        data: attachment[`${tableName}attachment_filedata`],
        documentType: attachment[`${tableName}attachment_documenttype`],
        expiryDate: attachment[`${tableName}attachment_expirydate`]
    };
};

export const createAttachments = (tableName, files = []) => files.map(file => createAttachment(tableName, file));