export default {
    loading: true,
    skillsConfigurationSections:[],
    skillsConfigurationSectionDetails:{},
    updatedSkillsConfigurationSectionDetails: {},
    initialskillsConfigurationSectionDetails:{},
    sortTableRowOrder:'',
    skillOnExpandedKeys: {},
    skillOnFieldsExpandedKeys: {},
    hasSectionLevelError: false,
    isNewListItemAdded: false,
    customFields: [],
    formattingFields:[],
    activeCurrency: {},
    currentEntity:{},
    skillEntityTypes:[],
    librarySections:{
        initialSkillSections:[],
        selectedSkillRowKeys:[],
        skillSearchResult:{}
    }
};