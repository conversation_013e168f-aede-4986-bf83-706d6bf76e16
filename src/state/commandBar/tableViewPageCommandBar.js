import { SWITCH_LABEL_TYPES } from '../../constants/plannerConsts';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES, COMMAND_BAR_MENUS_SECTION_KEYS } from '../../constants/commandBarConsts';
import HOT_KEYS_ACTION_TYPES from '../hotKeys/plannerPage/actionTypes';
import { getPrimaryHotKeyDescription } from '../../utils/hotKeys';
import { plannerPageHotKeysConfig } from '../hotKeys/plannerPage/hotKeysConfig';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { COMMAND_BAR_ACTION_ELEMENT_TYPES, PAGE_VIEW_SETTINGS } from '../../constants/globalConsts';
import { TABLE_VIEW_PAGE_ACTIONS } from '../../constants/tableViewPageConsts';

const pageTitleSection = {
    pageTitle: '##key##pageTitle###Table View',
    pageIcon: 'table',
    type: 'PageTitle'
};

// use functionalAccessName when action tableName is known
// use functionalAccessType when action tableName is selection dependant
const add = {
    key: COMMAND_BAR_MENUS_SECTION_KEYS.ADD,
    visible: false,
    label: '##key##addLabel###Add',
    type: 'Menu',
    closedIcon: "down",
    openIcon: "up",
    className: 'addButtonCB',
    items: [
        {
            label: '##key##bookingLabel###Booking',
            type: 'MenuItem',
            onClickActionType: TABLE_VIEW_PAGE_ACTIONS.CREATE_BOOKING,
            functionalAccessName: 'CreateBooking',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            //Remove the comment when the hot keys are implemented
            //hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_BOOKING]),
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'booking',
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##jobLabel###Job',
            type: 'MenuItem',
            onClickActionType: TABLE_VIEW_PAGE_ACTIONS.CRAETE_JOB,
            functionalAccessName: 'CreateJob',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            //Remove the comment when the hot keys are implemented
            //hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_JOB]),
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'job',
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##clientLabel###Client',
            type: 'MenuItem',
            onClickActionType: TABLE_VIEW_PAGE_ACTIONS.CREATE_CLIENT,
            functionalAccessName: 'CreateClient',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'client',
                singularForm : true,
                labelTemplate : 'default'
            }
        }
    ],
};


const plans = {
    //this section is dynamically populated
    key: COMMAND_BAR_MENUS_SECTION_KEYS.PLANS,
    className: 'plans-dropdown',
    plansMessages: {
        newPlanLabel: '##key##newPlanLabel###New Plan',
        manageMyPlansLabel: '##key##manageMyPlansLabel###Manage My Plans',
        privatePlansLabel: '##key##privatePlansLabel###Private Plans',
        publicPlansLabel: '##key##publicPlansLabel###Public Plans',
        saveChangesLabel: '##key##saveChangesLabel###Save changes',
        saveChangesToPublicLabel: '##key##saveChangesToPublicLabel###Save changes to public',
        noPublicPlansCreatedLabel: '##key##noPublicPlansCreatedLabel###No public plans have been created',
        noPrivatePlansCreatedLabel: '##key##noPrivatePlansCreatedLabel###No private plans have been created',
        saveAsNewPlanLabel: '##key##saveAsNewPlanLabel###Save as a new plan'
    }
};

const jobsResRadioGroup = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.SWITCH_VIEW,
    allocatedWidth : 400,
    id: PAGE_VIEW_SETTINGS,
    onClickActionType: TABLE_VIEW_PAGE_ACTIONS.VIEW_SETTINGS,
    buttonView: true,
    buttonStyle: "solid",
    options: [
        {
            value: "job",
            label: "##key##jobsLabel###Jobs",
            options: {
                isEntityDependant: true,
                tableName: "job",
                singularForm: false,
                labelTemplate : 'default'
            }
        },
        {
            value: "resource",
            label: "##key##resourcesLabel###Resources",
            options: {
                isEntityDependant: true,
                tableName: "resource",
                singularForm: false,
                labelTemplate : 'default'
            },
            checked: true
        }
    ]
};

const filtersAction = {
    label: '##key##filtersLabel###Filters',
    actionKey: 'filters',
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.QUICK_ACTION,
    allocatedWidth : 40,
    onClickActionType: TABLE_VIEW_PAGE_ACTIONS.TOGGLE_FILTER_PANE,
    icon: 'filter',
    size: 'large',
    showBadge: true,
    badgeClassName: 'filtersBadgeCount'
};

const hideRecords = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.ACTION_WITH_COMPONENT,
    componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.HIDE_RECORDS_OPTIONS,
    allocatedWidth: 70,
    closedIcon: "down",
    openIcon: "up",
    label: '##key##showLabel###Show',
    icon: 'eye',
    tooltipTextLabel: 'showMenuTooltipText',
    items: [
        {
            type: 'MenuItemWithComponent',
            label: '##key##pastLabel###Hide past',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideHistoricRecordsToggle',
            onClickActionType: TABLE_VIEW_PAGE_ACTIONS.TOGGLE_HIDE_HISTORIC_RECORDS,
            size: 'small',
            className: 'hideRecordsRowWithExplanation',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideHistoricEntityLabel',
                capitalized: false,
                valuePropName: 'hideHistoricRecords',
                labelType: SWITCH_LABEL_TYPES.DEFAULT
            },
            explanationOptions: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: true,
                explanationTemplate: 'hidePastEntitiesExplanation'
            },
            visible: true
        },
        {
            componentType: 'Divider',
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##futureLabel###Hide future ',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideFutureRecordsToggle',
            onClickActionType: TABLE_VIEW_PAGE_ACTIONS.TOGGLE_HIDE_FUTURE_RECORDS,
            size: 'small',
            className: 'hideRecordsRowWithExplanation',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideFutureEntityLabel',
                capitalized: false,
                valuePropName: 'hideFutureRecords',
                labelType: SWITCH_LABEL_TYPES.DEFAULT

            },
            explanationOptions: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: true,
                explanationTemplate: 'hideFutureEntitiesExplanation'
            },
            visible: true
        },
        {
            componentType: 'Divider',
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##inactiveLabel###Inactive Resource',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideInactiveResourcesToggle', 
            onClickActionType: TABLE_VIEW_PAGE_ACTIONS.TOGGLE_HIDE_INACTIVE_RESOURCES,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideInactiveEntityLabel',
                capitalized: false,
                valuePropName: 'hideInactiveResources',
                labelType: SWITCH_LABEL_TYPES.DEFAULT
            },
            visible: true
        }
    ]
};

export const tableViewCommandBarConfig = {
    pageTitleSection,
    menusSection: [
        add, plans
    ],
    actionsSection: [
        jobsResRadioGroup, hideRecords, filtersAction
    ]
};