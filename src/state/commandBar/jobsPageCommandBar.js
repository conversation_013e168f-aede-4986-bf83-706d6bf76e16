import { JOBS_PAGE_ACTIONS } from '../../constants/jobsPageConsts';
import HOT_KEYS_ACTION_TYPES from '../hotKeys/jobsPage/actionTypes';
import { getPrimaryHotKeyDescription } from '../../utils/hotKeys';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { jobsPageHotKeysConfig } from '../hotKeys/jobsPage/hotKeysConfig';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES } from '../../constants/commandBarConsts';
import { FILTER_FIELD_NAMES, PAGE_VIEW_SETTINGS, TABLE_NAMES } from '../../constants/globalConsts';
import {
    COMMAND_BAR_ACTION_ELEMENT_TYPES,
    BASE_FILTER_OPTIONS,
    DEFAULT_BASE_FILTER_OPTION
} from '../../constants/globalConsts';
import { CREATE_FNAS_PER_TABLENAME, EDIT_FNAS_PER_TABLENAME } from '../../constants/tablesConsts';
import { LIST_PAGE_ACTIONS } from '../../constants/listPageConsts';

const { MENU, MENU_ITEM, DIVIDER, RADIO_GROUP_ELEMENT } = COMMAND_BAR_MENUS_COMPONENT_TYPES;

const add = {
    label: '##key##addLabel###Add',
    type: MENU,
    closedIcon: 'down',
    openIcon: 'up',
    key: 'addSection',
    visible: false,
    items: [
        {
            label: '##key##jobLabel###Job',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.CREATE_JOB,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.JOB],
            showEllipsis: true,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            hotKeyDescription: getPrimaryHotKeyDescription(jobsPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_JOB]),
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.JOB,
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##clientLabel###Client',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.CREATE_CLIENT,
            showEllipsis: true,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.CLIENT],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            options: {
                isEntityDependant: true,
                tableName : TABLE_NAMES.CLIENT,
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##scenarioLabel###Scenario',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.CREATE_ROLE_GROUP,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.ROLEREQUESTGROUP,
                singularForm : true,
                labelTemplate : 'default'
            }
        }
    ]
};

const edit = {
    label: '##key##editLabel###Edit',
    type: MENU,
    closedIcon: 'down',
    openIcon: 'up',
    key: 'editSection',
    visible: false,
    items: [
        {
            label: '##key##editLabel###Edit Job',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.EDIT_JOB,
            functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.JOB],
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            hotKeyDescription: getPrimaryHotKeyDescription(jobsPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.EDIT_JOB]),
            options: {
                isEntityDependant: true,
                labelTemplate: 'editEntity',
                tableName: TABLE_NAMES.JOB,
                pluralFormEllipsisSuffix: true
            }
        },
        {
            label: '##key##duplicateJob###Duplicate',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.DUPLICATE_JOB,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            hotKeyDescription: getPrimaryHotKeyDescription(jobsPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.DUPLICATE_JOB]),
            options: {
                isEntityDependant: true,
                labelTemplate: 'rollForwardEntity',
                tableName: TABLE_NAMES.JOB,
                capitalized: false,
                pluralFormEllipsisSuffix: true
            }
        },
        { type: DIVIDER },
        {
            label: '##key##manageLabel###Manage',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.MANAGE_JOBS,
            showEllipsis: true,
            functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.JOB],
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            selectionIndependentAccess: true,
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.JOB,
                singularForm : false,
                labelTemplate : 'manageEntity'
            }
        },
        {
            label: '##key##manageLabel###Manage',
            type: MENU_ITEM,
            onClickActionType: JOBS_PAGE_ACTIONS.MANAGE_CLIENTS,
            showEllipsis: true,
            functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.CLIENT],
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            selectionIndependentAccess: true,
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.CLIENT,
                singularForm : false,
                labelTemplate : 'manageEntity'
            }
        }
    ]
};

const baseFilter = {
    closedIcon: 'down',
    openIcon: 'up',
    label: '##key##baseFilterLabel###View jobs',
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.ACTION_WITH_COMPONENT,
    componentType: RADIO_GROUP_ELEMENT,
    allocatedWidth : 170,
    iconLeft: 'eyeFilterIcon',
    icon: 'eye',
    size: 'large',
    onClickActionType: JOBS_PAGE_ACTIONS.TOGGLE_BASE_FILTER,
    valuePropName: 'selectedBaseFilter',
    className: 'baseFilterRadioButton',
    filterPayload: [
        {
            baseFilterOption: DEFAULT_BASE_FILTER_OPTION
        },
        {
            value: 1,
            filter: FILTER_FIELD_NAMES.JOB_I_MANAGE,
            baseFilterOption: BASE_FILTER_OPTIONS.I_MANAGE
        },
        {
            value: 0,
            filter: FILTER_FIELD_NAMES.JOB_ACTION_REQUIRED,
            baseFilterOption: BASE_FILTER_OPTIONS.ACTION_REQUIRED
        }
    ],
    items: [
        {
            value: DEFAULT_BASE_FILTER_OPTION,
            label: '##key##viewAllJobsLabel###All',
            className: 'jobsPageBaseFilterRow'
        },
        {
            value: BASE_FILTER_OPTIONS.I_MANAGE,
            label: '##key##viewJobsIManageLabel###I manage',
            className: 'jobsPageBaseFilterRow'
        },
        {
            value: BASE_FILTER_OPTIONS.ACTION_REQUIRED,
            label: '##key##viewJobsActionRequiredLabel###Action Required',
            className: 'jobsPageBaseFilterRow'
        }
    ],
    options: {
        isEntityDependant: true,
        tableName: TABLE_NAMES.JOB,
        singularForm: false,
        labelTemplate: 'manageEntity'
    }
};

const filtersAction = {
    label: '##key##filtersLabel###Filters',
    actionKey: 'filters',
    type: 'QuickAction',
    allocatedWidth: 40,
    onClickActionType: JOBS_PAGE_ACTIONS.TOGGLE_FILTER_PANE,
    icon: 'filter',
    size: 'large',
    showBadge: true,
    badgeClassName: 'filtersBadgeCount'
};

const pageTitleSection = { pageTitle: '##key##jobsLabel###Jobs', pageIcon: 'job', type: 'PageTitle', options: {
    isEntityDependant : true,
    tableName : TABLE_NAMES.JOB,
    singularForm : false
} };

const jobsResRadioGroup = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.SWITCH_VIEW,
    allocatedWidth : 400,
    id: PAGE_VIEW_SETTINGS,
    onClickActionType: LIST_PAGE_ACTIONS.VIEW_SETTINGS,
    buttonView: true,
    buttonStyle: 'solid',
    options: [
        {
            value: TABLE_NAMES.JOB,
            label: '##key##jobsLabel###Jobs',
            options: {
                isEntityDependant: true,
                tableName: '',
                singularForm: false,
                labelTemplate : 'default',
                tooltipLabel: '##key##jobsLabel###Jobs'
            },
            checked: true,
            icon: 'job'
        },
        {
            value: TABLE_NAMES.RESOURCE,
            label: '##key##resourcesLabel###Resources',
            options: {
                isEntityDependant: true,
                tableName: '',
                singularForm: false,
                labelTemplate : 'default',
                tooltipLabel: '##key##resourcesLabel###Resources'
            },
            icon: 'user'
        }
    ]
};

export const jobsPageCommandBarConfig = {
    pageTitleSection,
    menusSection: [add, edit],
    actionsSection: [baseFilter, jobsResRadioGroup, filtersAction]
};
