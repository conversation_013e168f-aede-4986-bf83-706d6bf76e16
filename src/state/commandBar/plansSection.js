import * as actionTypes from '../../actions/actionTypes';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES } from '../../constants/commandBarConsts';

const { DIVIDER, MENU_ITEM } = COMMAND_BAR_MENUS_COMPONENT_TYPES;

const getMenuItemLabel = (labelTemplate, label) => {
    return labelTemplate ? { labelTemplate, label } : { label };
};

export const getPlansSectionMenuItem = (menuItemProps) => {
    const { labelTemplate, label, onClickActionType = null, plansSectionPlanGuid, style = {}, icon = null, className = '' } = menuItemProps;

    return {
        ...getMenuItemLabel(labelTemplate, label),
        type: MENU_ITEM,
        onClickActionType,
        plansSectionPlanGuid,
        style,
        icon,
        className
    };
};

export const getPlansSectionSaveChangesButton = (selectedWorkspaceAccessType, labels) => {
    let button = {};

    if (selectedWorkspaceAccessType === 'private') {
        button = {
            label: labels.saveChangesLabel,
            labelTemplate: 'saveChangesLabel',
            type: MENU_ITEM,
            onClickActionType: actionTypes.SAVE_WORKSPACE_SETTINGS
        };
    } else if (selectedWorkspaceAccessType === 'public') {
        button = {
            label: labels.saveChangesToPublicLabel,
            labelTemplate: 'saveChangesToPublicLabel',
            type: MENU_ITEM,
            onClickActionType: actionTypes.SAVE_WORKSPACE_SETTINGS
        };
    }

    return button;
};

export const getPlansSectionSaveAsNewPlanButton = (selectedWorkspaceAccessType, labels, listPageAndBulkUpdateFeatureFlag) => {
    let button = {};

    if (listPageAndBulkUpdateFeatureFlag) {
        button = {
            label: labels.saveAsNewWorkspaceLabel,
            labelTemplate: 'saveAsNewWorkspaceLabel',
            type: MENU_ITEM,
            onClickActionType: actionTypes.DIGEST_SAVE_AS_NEW_PLAN
        };

    } else {
        button = {
            label: labels.saveAsNewPlanLabel,
            labelTemplate: 'saveAsNewPlanLabel',
            type: MENU_ITEM,
            onClickActionType: actionTypes.DIGEST_SAVE_AS_NEW_PLAN
        };
    }

    return button;
};

export function plansSectionModelCreator(label, selectedWorkspaceAccessType, mostRecentPlans, privatePlans, publicPlans, messages, listPageAndBulkUpdateFeatureFlag) {
    const {
        newPlanLabel,
        manageMyPlansLabel,
        privatePlansLabel,
        publicPlansLabel,
        saveChangesLabel,
        saveChangesToPublicLabel,
        noPublicPlansCreatedLabel,
        noPrivatePlansCreatedLabel,
        saveAsNewPlanLabel,
        newWorkspaceLabel,
        manageMyWorkspacesLabel,
        privateWorkspacesLabel,
        publicWorkspacesLabel,
        noPublicWorkspacesCreatedLabel,
        noPrivateWorkspacesCreatedLabel,
        saveAsNewWorkspaceLabel
    } = messages;

    if (listPageAndBulkUpdateFeatureFlag) {
        if (publicPlans.length === 0) {
            publicPlans.push(getPlansSectionMenuItem({ labelTemplate: 'noPublicWorkspacesCreatedLabel', label: noPublicWorkspacesCreatedLabel }));
        }
        if (privatePlans.length === 0) {
            privatePlans.push(getPlansSectionMenuItem({ labelTemplate: 'noPrivateWorkspacesCreatedLabel', label: noPrivateWorkspacesCreatedLabel }));
        }

        return {
            label,
            type: 'Menu',
            closedIcon: 'down',
            openIcon: 'up',
            manageMyPlansWindowVisible: false,
            icon: 'layout',
            triggerSubMenuAction: 'click',
            items: [
                {
                    label: newWorkspaceLabel,
                    labelTemplate: 'newWorkspaceLabel',
                    type: MENU_ITEM,
                    onClickActionType: actionTypes.DIGEST_CREATE_WORKSPACE,
                    icon: {
                        type: 'plus',
                        position: 'left'
                    }
                },
                { type: DIVIDER },
                getPlansSectionSaveChangesButton(selectedWorkspaceAccessType, { saveChangesLabel, saveChangesToPublicLabel }),
                getPlansSectionSaveAsNewPlanButton(selectedWorkspaceAccessType, { saveAsNewWorkspaceLabel }, listPageAndBulkUpdateFeatureFlag),
                { type: DIVIDER },
                {
                    label: manageMyWorkspacesLabel,
                    labelTemplate: 'manageMyWorkspacesLabel',
                    type: MENU_ITEM,
                    onClickActionType: actionTypes.MANAGE_WORKSPACES_SECTION.SET_VISIBILITY,
                    showEllipsis: true
                },
                { type: DIVIDER },
                ...mostRecentPlans,
                { type: DIVIDER },
                {
                    label: privateWorkspacesLabel,
                    labelTemplate: 'privateWorkspacesLabel',
                    type: 'SubMenu',
                    items: privatePlans,
                    scroll: true
                },
                {
                    label: publicWorkspacesLabel,
                    labelTemplate: 'publicWorkspacesLabel',
                    type: 'SubMenu',
                    items: publicPlans,
                    scroll: true
                }
            ]
        };
    } else {
        if (publicPlans.length === 0) {
            publicPlans.push(getPlansSectionMenuItem({ labelTemplate: 'noPublicPlansCreatedLabel', label: noPublicPlansCreatedLabel }));
        }
        if (privatePlans.length === 0) {
            privatePlans.push(getPlansSectionMenuItem({ labelTemplate: 'noPrivatePlansCreatedLabel', label: noPrivatePlansCreatedLabel }));
        }

        return {
            label,
            type: 'Menu',
            closedIcon: 'down',
            openIcon: 'up',
            manageMyPlansWindowVisible: false,
            icon: 'layout',
            triggerSubMenuAction: 'click',
            items: [
                {
                    label: newPlanLabel,
                    labelTemplate: 'newPlanLabel',
                    type: MENU_ITEM,
                    onClickActionType: actionTypes.DIGEST_CREATE_WORKSPACE,
                    icon: {
                        type: 'plus',
                        position: 'left'
                    }
                },
                { type: DIVIDER },
                getPlansSectionSaveChangesButton(selectedWorkspaceAccessType, { saveChangesLabel, saveChangesToPublicLabel }),
                getPlansSectionSaveAsNewPlanButton(selectedWorkspaceAccessType, { saveAsNewPlanLabel }),
                { type: DIVIDER },
                {
                    label: manageMyPlansLabel,
                    labelTemplate: 'manageMyPlansLabel',
                    type: MENU_ITEM,
                    onClickActionType: actionTypes.MANAGE_WORKSPACES_SECTION.SET_VISIBILITY,
                    showEllipsis: true
                },
                { type: DIVIDER },
                ...mostRecentPlans,
                { type: DIVIDER },
                {
                    label: privatePlansLabel,
                    labelTemplate: 'privatePlansLabel',
                    type: 'SubMenu',
                    items: privatePlans,
                    scroll: true
                },
                {
                    label: publicPlansLabel,
                    labelTemplate: 'publicPlansLabel',
                    type: 'SubMenu',
                    items: publicPlans,
                    scroll: true
                }
            ]
        };
    }
}

export function plansSectionModelModify(config) {
    const { plansMessages, items } = config;

    const modifiedItemLabels = items.reduce((accumulator, item) => {
        const itemLabelTemplate = item.labelTemplate;
        if (item.items) {
            item = plansSectionModelModify({ ...item, plansMessages });
        }
        if (itemLabelTemplate) {
            item.label = plansMessages[itemLabelTemplate];
        }
        accumulator.push(item);

        return accumulator;
    }, []);

    return {
        ...config,
        items: modifiedItemLabels
    };
}