import { PLANNER_ACTIONS, PLANNER_DATE_TOGLE_OPTIONS, SWITCH_LABEL_TYPES, TOOLTIP_DETAIL_TYPES } from '../../constants/plannerConsts';
import { ENTITY_ACTION_KEYS } from '../../constants/entityAccessConsts';
import { COMMAND_BAR_CUSTOM_CONDITION_TYPES, COMMAND_BAR_MENUS_COMPONENT_TYPES, COMMAND_BAR_MENUS_SECTION_KEYS, COMMAND_BAR_PROP_KEY_TOGGLE_VALUE, commonBaseViewRolesFiltersItemProps } from '../../constants/commandBarConsts';
import HOT_KEYS_ACTION_TYPES from '../hotKeys/plannerPage/actionTypes';
import { getPrimaryHotKeyDescription } from '../../utils/hotKeys';
import { plannerPageHotKeysConfig } from '../hotKeys/plannerPage/hotKeysConfig';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { COMMAND_BAR_ACTION_ELEMENT_TYPES, PAGE_VIEW_SETTINGS, TABLE_NAMES } from '../../constants/globalConsts';
import { CREATE_FNAS_PER_TABLENAME, EDIT_FNAS_PER_TABLENAME, FUNCTIONAL_ACCESS_TYPES, MANAGE_ROLE_TEMPLATES_FNA, ROLEREQUEST_WORKFLOW_ACCESS_TYPES } from '../../constants/tablesConsts';
import { ROLE_ITEM_STATUS_KEYS } from '../../constants/rolesConsts';
import { ROLEREQUEST_HASCRITERIA_FIELD_FILTER_VALUES } from '../../constants/roleInboxPageConsts';
import { PEOPLE_FINDERS_DIALOG } from '../../actions/actionTypes';
import { ROLEREQUEST_FIXEDTIME_VALUES } from '../../constants/fieldConsts';
import { ACTIONS_MENU_COMPONENTS } from '../../constants/actionsMenuConsts';
import { RESTORE_PLANNER_VIEW } from '../../actions/rotatePlannerViewActions';

const { 
    SHOW_UNASSIGNED_FILTER_VALUE = true,
    SHOW_ROLES_BY_NAME_FILTER_VALUE = true,
    SHOW_ROLES_BY_CRITERIA_FILTER_VALUE = true
} = ROLEREQUEST_HASCRITERIA_FIELD_FILTER_VALUES;

const pageTitleSection = {
    pageTitle: '##key##pageTitle###Planner',
    pageIcon: 'plans-page',
    type: 'PageTitle'
};

const { MENU_ITEM, DIVIDER } = COMMAND_BAR_MENUS_COMPONENT_TYPES;

const {
    HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROW_GRID_SELECTION,
    HIDE_EDIT_BOOKINGS_ACTION,
    NO_ROLES_SELECTED,
    MULTIPLE_ROLES_SELECTED,
    HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROLE_BAR_SELECTION,
    HIDE_CRITERIA_ROLE_CREATE_OPTIONS_ON_BAR_SELECTION
} = COMMAND_BAR_CUSTOM_CONDITION_TYPES;

// use functionalAccessName when action tableName is known
// use functionalAccessType when action tableName is selection dependant
const add = {
    key: COMMAND_BAR_MENUS_SECTION_KEYS.ADD,
    visible: false,
    label: '##key##addLabel###Add',
    type: 'Menu',
    closedIcon: 'down',
    openIcon: 'up',
    triggerSubMenuAction: 'click',
    className: 'addButtonCB',
    items: [
        {
            label: '##key##bookingLabel###Booking',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CREATE_BOOKING,
            functionalAccessName: 'CreateBooking',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_BOOKING]),
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'booking',
                singularForm : true,
                labelTemplate : 'default'
            },
            customHideActionConditions: [
                { type: HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROW_GRID_SELECTION },
                { type: HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROLE_BAR_SELECTION }
            ]
        },
        {
            label: '##key##byNameSuffix###by name',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CREATE_ROLE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_ROLE]),
            showEllipsis: true,
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                labelTemplate: 'addRole'
            },
            customHideActionConditions: [
                { type: HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROW_GRID_SELECTION },
                { type: HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROLE_BAR_SELECTION }
            ]
        },
        {
            label: '##key##byRequirementSuffix###by requirements',
            type: MENU_ITEM,
            onClickActionType: PLANNER_ACTIONS.CREATE_ROLE_BY_REQUIREMENT,
            selectionIndependentAccess: true,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_ROLE_BY_REQUIREMENTS]),
            showEllipsis: true,
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                labelTemplate: 'addRole'
            },
            customHideActionConditions: [
                { type: HIDE_CRITERIA_ROLE_CREATE_OPTIONS_ON_BAR_SELECTION }
            ]
        },
        {
            labelPropName: 'rolefromTemplateLabel',
            type: ACTIONS_MENU_COMPONENTS.SUB_MENU_WITH_COMPONENT,
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.ROLE_FROM_TEMPLATE,
            icon: 'right',
            options: {
                isEntityDependant: true,
                tableName: 'rolerequest',
                labelTemplate: 'addRole'
            }
        },
        {
            label: '##key##jobLabel###Job',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CREATE_JOB,
            functionalAccessName: 'CreateJob',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ADD_JOB]),
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'job',
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##clientLabel###Client',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CREATE_CLIENT,
            functionalAccessName: 'CreateClient',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'client',
                singularForm : true,
                labelTemplate : 'default'
            }
        },
        {
            label: '##key##scenarioLabel###Scenario',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CREATE_ROLE_GROUP,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            showEllipsis: true,
            options: {
                isEntityDependant : true,
                tableName : 'rolerequestgroup',
                singularForm : true,
                labelTemplate : 'default'
            }
        }
    ]
};

const edit = {
    key: COMMAND_BAR_MENUS_SECTION_KEYS.EDIT,
    visible: false,
    label: '##key##editLabel###Edit',
    type: 'Menu',
    closedIcon: 'down',
    openIcon: 'up',
    items: [
        {
            label: '##key##submitRequestLabel###Submit request',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.SUBMIT_REQUEST,
            actionKey: ENTITY_ACTION_KEYS.SUBMIT_REQUEST,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_SUBMIT_REQUEST,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.SUBMIT_REQUEST]),
            options: {
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                isSelectionDependant: true,
                multipleSelectionLabelPropName: 'submitRequestLabel'
            }
        },
        {
            label: '##key##makeLiveLabel###Make live',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.MAKE_LIVE,
            actionKey: ENTITY_ACTION_KEYS.MAKE_LIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.MAKE_LIVE]),
            options: {
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                isSelectionDependant: true,
                multipleSelectionLabelPropName: 'makeLiveLabel'
            }
        },
        {
            label: '##key##restartLabel###Restart',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.RESTART_ROLE,
            actionKey: ENTITY_ACTION_KEYS.RESTART,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_RESTART,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.RESTART_ROLE]),
            options: {
                multipleSelectionLabelPropName: 'restartLabel',
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                isSelectionDependant: true
            }
        },
        {
            label: '##key##rejectLabel###Reject',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.REJECT_ROLE,
            actionKey: ENTITY_ACTION_KEYS.REJECT,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REJECT,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.REJECT_ROLE]),
            options: {
                multipleSelectionLabelPropName: 'rejectLabel',
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: '##key##archiveLabel###Archive',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.ARCHIVE_ROLE,
            actionKey: ENTITY_ACTION_KEYS.ARCHIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_ARCHIVE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ARCHIVE_ROLE]),
            options: {
                multipleSelectionLabelPropName: 'archiveLabel',
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                isSelectionDependant: true
            }
        },
        {
            label: '##key##movePendingResourcesLabel###Move pending resources',
            type: MENU_ITEM,
            onClickActionType: PLANNER_ACTIONS.MOVE_PENDING_RESOURCES,
            actionKey: ENTITY_ACTION_KEYS.MOVE_PENDING_RESOURCES,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MOVE_PENDING,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.MOVE_PENDING_TIME_ALLOCATION]),
            className: 'commandBarEditMenuItem',
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST,
                fixedTimeValue: ROLEREQUEST_FIXEDTIME_VALUES.ROLEREQUEST_RESOURCE_DEMAND
            },
            customHideActionConditions: [
                { type: NO_ROLES_SELECTED },
                { type: MULTIPLE_ROLES_SELECTED }
            ]
        },
        {
            label: '##key##removePendingResourcesLabel###Remove pending resources',
            type: MENU_ITEM,
            onClickActionType: PLANNER_ACTIONS.REMOVE_PENDING_RESOURCES,
            actionKey: ENTITY_ACTION_KEYS.REMOVE_PENDING_RESOURCES,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REMOVE_PENDING,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.REMOVE_PENDING_TIME_ALLOCATION]),
            className: 'commandBarEditMenuItem',
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST,
                fixedTimeValue: ROLEREQUEST_FIXEDTIME_VALUES.ROLEREQUEST_RESOURCE_DEMAND
            },
            customHideActionConditions: [
                { type: NO_ROLES_SELECTED },
                { type: MULTIPLE_ROLES_SELECTED }
            ]
        },
        {
            label: '##key##movePendingFTELabel###Move pending FTEs',
            type: MENU_ITEM,
            onClickActionType: PLANNER_ACTIONS.MOVE_PENDING_RESOURCES,
            actionKey: ENTITY_ACTION_KEYS.MOVE_PENDING_FTEs,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MOVE_PENDING,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.MOVE_PENDING_TIME_ALLOCATION]),
            className: 'commandBarEditMenuItem',
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST,
                fixedTimeValue: ROLEREQUEST_FIXEDTIME_VALUES.ROLEREQUEST_FTE
            },
            customHideActionConditions: [
                { type: NO_ROLES_SELECTED },
                { type: MULTIPLE_ROLES_SELECTED }
            ]
        },
        {
            label: '##key##removePendingFTELabel###Remove pending FTEs',
            type: MENU_ITEM,
            onClickActionType: PLANNER_ACTIONS.REMOVE_PENDING_RESOURCES,
            actionKey: ENTITY_ACTION_KEYS.REMOVE_PENDING_FTEs,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REMOVE_PENDING,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.REMOVE_PENDING_TIME_ALLOCATION]),
            className: 'commandBarEditMenuItem',
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST,
                fixedTimeValue: ROLEREQUEST_FIXEDTIME_VALUES.ROLEREQUEST_FTE
            },
            customHideActionConditions: [
                { type: NO_ROLES_SELECTED },
                { type: MULTIPLE_ROLES_SELECTED }
            ]
        },
        {
            type: DIVIDER ,
            functionalAccessType: EDIT_FNAS_PER_TABLENAME,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true
            },
            customHideActionConditions: [
                { type: NO_ROLES_SELECTED }
            ]
        },
        {
            label: '##key##editLabel###Edit Booking',
            type: MENU_ITEM,
            functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING],
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            onClickActionType: PLANNER_ACTIONS.EDIT_BOOKING_BARS,
            actionKey: ENTITY_ACTION_KEYS.EDIT,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.EDIT_BOOKING_BARS]),
            options: {
                isEntityDependant: true,
                labelTemplate: 'editEntity',
                tableName: TABLE_NAMES.BOOKING,
                capitalized: true,
                pluralFormEllipsisSuffix: true
            },
            customHideActionConditions: [
                { type: HIDE_EDIT_BOOKINGS_ACTION }
            ]
        },
        {
            label: '##key##rollForwardLabel###Duplicate booking',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.ROLL_FORWARD,
            functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING],
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            actionKey: ENTITY_ACTION_KEYS.ROLL_FORWARD,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ROLL_FORWARD]),
            options: {
                isEntityDependant: true,
                labelTemplate: 'rollForwardEntity',
                tableName: TABLE_NAMES.BOOKING,
                capitalized: false,
                pluralFormEllipsisSuffix: true
            },
            tooltipProps: {
                showIcon: true,
                iconType: 'info',
                tooltipClassName: 'commandBarTooltipWrapper',
                tooltipPlacement: 'right',
                tooltipTitleComponentType: TOOLTIP_DETAIL_TYPES.ROLL_FORWARD
            }
        },
        {
            label: '##key##editRoleByNameLabel###Edit Role by name',
            type: MENU_ITEM,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            onClickActionType: PLANNER_ACTIONS.EDIT_ROLES_BY_NAME_BARS,
            actionKey: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_NAME,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.EDIT_ROLES_BY_NAME]),
            className: 'commandBarEditMenuItem',
            options: {
                useLabelWithCounters: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                pluralFormEllipsisSuffix: true
            }
        },
        {
            label: '##key##editRoleByCriteriaLabel###Edit Role by requirements',
            type: MENU_ITEM,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            onClickActionType: PLANNER_ACTIONS.EDIT_ROLES_BY_REQUIREMENTS_BARS,
            actionKey: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_CRITERIA,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.EDIT_ROLES_BY_REQUIREMENTS]),
            className: 'commandBarEditMenuItem',
            options: {
                useLabelWithCounters: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                pluralFormEllipsisSuffix: true
            }
        },
        {
            type: DIVIDER ,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true
            }
        },
        {
            label: '##key##cutLabel###Cut',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CUT_BAR,
            functionalAccessType: FUNCTIONAL_ACCESS_TYPES.EDIT,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.CUT,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.CUT_BAR]),
            options: {
                isEntityDependant : false,
                isTableSelectionDependant: true
            }
        },
        {
            label: '##key##copyLabel###Copy',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.COPY_BAR,
            functionalAccessType: FUNCTIONAL_ACCESS_TYPES.CREATE,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            actionKey: ENTITY_ACTION_KEYS.COPY,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.COPY_BAR]),
            options: {
                isEntityDependant : false,
                isTableSelectionDependant: true
            }
        },
        {
            label: '##key##pasteLabel###Paste',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.PASTE_BAR,
            actionKey: ENTITY_ACTION_KEYS.PASTE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.PASTE_BAR]),
            options: {
                isEntityDependant : false,
                isTableSelectionDependant: true
            }
        },
        {
            label: '##key##deleteLabel###Delete',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.DELETE_BAR,
            functionalAccessType: FUNCTIONAL_ACCESS_TYPES.DELETE,
            entityAccess: ENTITY_ACCESS_TYPES.DELETE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.DELETE]),
            options: {
                isEntityDependant : false,
                isSelectionDependant: true,
                multipleSelectionLabelPropName: 'deleteLabel',
                isTableSelectionDependant: true
            }
        },
        {
            type: DIVIDER ,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true
            }
        },
        {
            label: '##key##manageLabel###Manage',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.MANAGE_JOBS,
            showEllipsis: true,
            functionalAccessName: 'EditJob',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            selectionIndependentAccess: true,
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.JOB,
                singularForm : false,
                labelTemplate : 'manageEntity'
            }
        },
        {
            label: '##key##manageLabel###Manage',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.MANAGE_CLIENTS,
            showEllipsis: true,
            functionalAccessName: 'EditClient',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            selectionIndependentAccess: true,
            options: {
                isEntityDependant : true,
                tableName : TABLE_NAMES.CLIENT,
                singularForm : false,
                labelTemplate : 'manageEntity'
            }
        },
        {
            labelPropName: 'manageRoleTemplatesLabel',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.MANAGE_ROLE_TEMPLATES,
            showEllipsis: true,
            functionalAccessName: MANAGE_ROLE_TEMPLATES_FNA,
            selectionIndependentAccess: true,
            options: {
                tableName : TABLE_NAMES.ROLEREQUEST,
                labelTemplate : 'manageEntity',
                usePropName: true
            }
        }
    ]
};

const view = {
    key: COMMAND_BAR_MENUS_SECTION_KEYS.VIEW,
    visible: false,
    label: '##key##viewLabel###View',
    type: 'Menu',
    closedIcon: 'down',
    openIcon: 'up',
    triggerSubMenuAction: 'click',
    items: [
        {
            label: '##key##barsLabel###Bar options',
            type: 'SubMenuWithComponent',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.BAR_OPTIONS,
            icon: 'right'
        },
        {
            label: '##key##potentialConflictsLabel###Show Potential Conflicts',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_POTENTIAL_CONFLICTS,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.TOGGLE_POTENTIAL_CONFLICTS]),
            className: 'hidePotentialConflicts',
            icon: {
                type: 'check',
                position: 'left'
            },
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hidePotentialConflictsEntity',
                capitalized: true,
                valuePropName: 'hidePotentialConflicts',
                isPropertyDependant: true
            }
        },
        { type: DIVIDER },
        {
            label: '##key##increaseDateRangeLabel###Increase date range',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CHANGE_DATE_TOGGLE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ZOOM_IN]),
            options: {
                canIncreaseRange: true
            }
        },
        {
            label: '##key##decreaseDateRangeLabel###Decrease date range',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.CHANGE_DATE_TOGGLE,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.ZOOM_OUT]),
            options: {
                canIncreaseRange: false
            }
        },
        {
            label: '##key##dateRangeLabel###Date range',
            type: 'SubMenu',
            items: [
                ...PLANNER_DATE_TOGLE_OPTIONS,
                { type: DIVIDER },
                {
                    label: '##key##customDateRangeLabel###Custom date range',
                    type: 'SubMenuWithComponent',
                    componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.CUSTOM_DATE_RANGE_PICKER,
                    onClickActionType: PLANNER_ACTIONS.GO_TO_DATE_RANGE,
                    icon: 'right',
                    endDateOffset: 1,
                    placement: 'bottomLeft',
                    className: 'view-custom-date-range'
                }
            ]
        },
        { type: DIVIDER },
        {
            label: '##key##goToTodayLabel###Go to today',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.GO_TO_TODAY,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.GO_TO_TODAY])
        },
        {
            label: '##key##goToDateLabel###Go to date',
            type: 'SubMenuWithComponent',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.GO_TO_DATE,
            onClickActionType: PLANNER_ACTIONS.GO_TO_DATE,
            icon: 'right',
            className: 'goToDatePickerSubMenu'
        },
        {
            labelPropName: 'showInViewLabel',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.SHOW_IN_VIEW,
            options: {
                usePropName: true
            },
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.SHOW_IN_VIEW])
        },
        { type: DIVIDER },
        {
            label: '##key##weekendsLabel###Weekends',
            type: 'MenuItem',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_WEEKENDS,
            hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.TOGGLE_WEEKENDS]),
            className: 'hideWeekendsToggle',
            icon: {
                type: 'check',
                position: 'left'
            },
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideWeekendsEntity',
                capitalized: true,
                valuePropName: 'hideWeekends',
                isPropertyDependant: true
            }
        }
    ]
};

const plans = {
    //this section is dynamically populated
    key: COMMAND_BAR_MENUS_SECTION_KEYS.PLANS,
    className: 'plans-dropdown',
    overlayClassName: 'plans-dropdown-popup',
    plansMessages: {
        newPlanLabel: '##key##newPlanLabel###New Plan',
        manageMyPlansLabel: '##key##manageMyPlansLabel###Manage My Plans',
        privatePlansLabel: '##key##privatePlansLabel###Private Plans',
        publicPlansLabel: '##key##publicPlansLabel###Public Plans',
        saveChangesLabel: '##key##saveChangesLabel###Save changes',
        saveChangesToPublicLabel: '##key##saveChangesToPublicLabel###Save changes to public',
        noPublicPlansCreatedLabel: '##key##noPublicPlansCreatedLabel###No public plans have been created',
        noPrivatePlansCreatedLabel: '##key##noPrivatePlansCreatedLabel###No private plans have been created',
        saveAsNewPlanLabel: '##key##saveAsNewPlanLabel###Save as a new plan'
    },
    workspacesMessages: {
        newWorkspaceLabel: '##key##newWorkspaceLabel###New Workspace',
        manageMyWorkspacesLabel: '##key##manageMyWorkspacesLabel###Manage My Workspaces',
        privateWorkspacesLabel: '##key##privateWorkspacesLabel###Private Workspaces',
        publicWorkspacesLabel: '##key##publicWorkspacesLabel###Public Workspaces',
        saveChangesLabel: '##key##saveChangesLabel###Save changes',
        saveChangesToPublicLabel: '##key##saveChangesToPublicLabel###Save changes to public',
        noPublicWorkspacesCreatedLabel: '##key##noPublicWorkspacesCreatedLabel###No public workspaces have been created',
        noPrivateWorkspacesCreatedLabel: '##key##noPrivateWorkspacesCreatedLabel###No private workspaces have been created',
        saveAsNewWorkspaceLabel: '##key##saveAsNewWorkspaceLabel###Save as a new workspace'
    }
};

const jobsResRadioGroup = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.SWITCH_VIEW,
    allocatedWidth : 400,
    id: PAGE_VIEW_SETTINGS,
    onClickActionType: PLANNER_ACTIONS.VIEW_SETTINGS,
    buttonView: true,
    buttonStyle: 'solid',
    options: [
        {
            value: 'job',
            label: '##key##jobsLabel###Jobs',
            options: {
                isEntityDependant: true,
                tableName: 'job',
                singularForm: false,
                labelTemplate : 'default'
            },
            icon: 'job'
        },
        {
            value: 'resource',
            label: '##key##resourcesLabel###Resources',
            options: {
                isEntityDependant: true,
                tableName: 'resource',
                singularForm: false,
                labelTemplate : 'default'
            },
            checked: true,
            icon: 'user'
        }
    ]
};

const filtersAction = {
    label: '##key##filtersLabel###Filters',
    actionKey: 'filters',
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.QUICK_ACTION,
    allocatedWidth : 40,
    onClickActionType: PLANNER_ACTIONS.TOGGLE_FILTER_PANE,
    icon: 'filter',
    size: 'large',
    showBadge: true,
    badgeClassName: 'filtersBadgeCount'
};

const hideRecords = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.ACTION_WITH_COMPONENT,
    componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.HIDE_RECORDS_OPTIONS,
    allocatedWidth: 70,
    closedIcon: 'down',
    openIcon: 'up',
    label: '##key##showLabel###Show',
    icon: 'eye',
    tooltipTextLabel: 'showMenuTooltipText',
    items: [
        {
            type: 'MenuItemWithComponent',
            label: '##key##pastLabel###Hide past',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideHistoricRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_HISTORIC_RECORDS,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideHistoricEntityLabel',
                capitalized: false,
                valuePropName: 'hideHistoricRecords',
                labelType: SWITCH_LABEL_TYPES.DEFAULT
            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##futureLabel###Hide future ',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideFutureRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_FUTURE_RECORDS,
            size: 'small',
            className: 'hideRecordsRowWithExplanation',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideFutureEntityLabel',
                capitalized: false,
                valuePropName: 'hideFutureRecords',
                labelType: SWITCH_LABEL_TYPES.DEFAULT

            },
            explanationOptions: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: true,
                explanationTemplate: 'hideFutureEntitiesExplanation'
            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##unassignedRowsLabel###Hide unassigned rows ',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideUnassignedRowsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROWS_RECORDS,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideUnassignedRowsEntityLabel',
                capitalized: false,
                valuePropName: 'hideUnassignedResourceRows',
                labelType: SWITCH_LABEL_TYPES.DEFAULT

            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true,
            dividerFor: PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROWS_RECORDS
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##jobTimelineToggleLabel###timeline ',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideJobTimelineToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_JOB_TIMELINE,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: true,
                labelTemplate: 'hideJobTimelineToggleLabel',
                capitalized: true,
                valuePropName: 'hideJobTimeline',
                labelType: SWITCH_LABEL_TYPES.DEFAULT

            },
            explanationOptions: {
                isEntityDependant: false,
                singularForm: true,
                capitalized: false,
                explanationTemplate: 'hideJobTimelineExplanation'
            },
            visible: true,
            hideInViews: [TABLE_NAMES.RESOURCE]
        },
        {
            componentType: DIVIDER,
            visible: true,
            hideInViews: [TABLE_NAMES.RESOURCE]
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##jobMilestonesToggleLabel###milestones ',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideJobMilestonesToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_JOB_MILESTONEE,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: true,
                labelTemplate: 'hideJobMilestonesToggleLabel',
                capitalized: true,
                valuePropName: 'hideJobMilestones',
                labelType: SWITCH_LABEL_TYPES.DEFAULT

            },
            explanationOptions: {
                isEntityDependant: false,
                singularForm: true,
                capitalized: false,
                explanationTemplate: 'hideJobMilestonesExplanation'
            },
            visible: true,
            hideInViews: [TABLE_NAMES.RESOURCE]
        },
        {
            componentType: DIVIDER,
            visible: true,
            hideInViews: [TABLE_NAMES.RESOURCE]

        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##hideRolesLabel###Roles ',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideRolesRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_ROLES_RECORDS,
            size: 'small',
            className: 'hideRecordsRowWithExplanation',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideRolesLabel',
                capitalized: false,
                valuePropName: 'hideRolesRecords',
                labelType: SWITCH_LABEL_TYPES.DEFAULT
            },
            explanationOptions: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: true,
                explanationTemplate: 'hideRolesExplanation'
            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            ...commonBaseViewRolesFiltersItemProps,
            label: null,
            id: 'hideRolesRecordsToggle',
            className: 'hideRecordsSubRow',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROLES,
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'toggleShowUnassignedRoles',
                capitalized: false,
                valuePropName: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_UNASSIGNED_ROLES,
                labelType: SWITCH_LABEL_TYPES.DEFAULT,
                disablePropKey: 'hideRolesRecords'
            },
            actionContext: {
                toggleFieldShowValue: SHOW_UNASSIGNED_FILTER_VALUE
            }
        },
        {
            componentType: DIVIDER,
            visible: true,
            dividerFor: PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROLES
        },
        {
            ...commonBaseViewRolesFiltersItemProps,
            label: null,
            id: 'hideRolesRecordsToggle',
            className: 'hideRecordsSubRow',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_ROLES_BY_NAME,
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'toggleShowRolesByName',
                capitalized: false,
                valuePropName: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_NAME,
                labelType: SWITCH_LABEL_TYPES.DEFAULT,
                disablePropKey: 'hideRolesRecords'
            },
            actionContext: {
                toggleFieldShowValue: SHOW_ROLES_BY_NAME_FILTER_VALUE
            }
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            ...commonBaseViewRolesFiltersItemProps,
            id: 'hideRolesRecordsToggle',
            className: 'hideRecordsSubRow',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_ROLES_BY_REQUIREMENTS,
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'toggleShowRolesByRequirements',
                capitalized: false,
                valuePropName: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_ROLES_BY_CRITERIA,
                labelType: SWITCH_LABEL_TYPES.DEFAULT,
                disablePropKey: 'hideRolesRecords'
            },
            actionContext: {
                toggleFieldShowValue: SHOW_ROLES_BY_CRITERIA_FILTER_VALUE
            }
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: null,
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideRolesRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_DRAFT_ROLES_RECORDS,
            size: 'small',
            className: 'hideRecordsSubRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: false,
                valuePropName: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_DRAFT_ROLES,
                labelType: SWITCH_LABEL_TYPES.ROLE,
                disablePropKey: 'hideRolesRecords'
            },
            actionContext: {
                toggleTargetRoleStatus: ROLE_ITEM_STATUS_KEYS.DRAFT
            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: null,
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideRolesRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_REQUESTED_ROLES_RECORDS,
            size: 'small',
            className: 'hideRecordsSubRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: false,
                valuePropName: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_REQUESTED_ROLES,
                labelType: SWITCH_LABEL_TYPES.ROLE,
                disablePropKey: 'hideRolesRecords'
            },
            actionContext: {
                toggleTargetRoleStatus: ROLE_ITEM_STATUS_KEYS.REQUESTED
            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: null,
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideRolesRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_LIVE_BARS,
            size: 'small',
            className: 'hideRecordsSubRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: false,
                valuePropName: COMMAND_BAR_PROP_KEY_TOGGLE_VALUE.HIDE_LIVE_BARS,
                labelType: SWITCH_LABEL_TYPES.ROLE,
                disablePropKey: 'hideRolesRecords'
            },
            actionContext: {
                toggleTargetRoleStatus: ROLE_ITEM_STATUS_KEYS.LIVE
            },
            visible: true
        },
        {
            componentType: DIVIDER,
            visible: true
        },
        {
            type: 'MenuItemWithComponent',
            label: '##key##inactiveLabel###Inactive Resource',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideInactiveResourcesToggle', 
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_INACTIVE_RESOURCES,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideInactiveEntityLabel',
                capitalized: false,
                valuePropName: 'hideInactiveResources',
                labelType: SWITCH_LABEL_TYPES.DEFAULT
            },
            visible: true
        }
    ]
};

const findResourcesAction = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.BUTTON,
    label: '##key##findResourcesLabel###Find resources...',
    buttonType: 'secondary',
    allocatedWidth : 50,
    onClickActionType: PEOPLE_FINDERS_DIALOG.OPEN,
    hotKeyDescription: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.LAUNCH_PEOPLE_FINDER]),
    icon: 'people-finder',
    btnClassName: 'peopleFinderBtnStyle',
    size: 'medium',
    toolTipTextKey: 'findResourceToolTipText',
    options: {
        isEntityDependant: true,
        tableName: 'resource',
        singularForm: false,
        capitalized: false,
        labelTemplate : 'default'
    }
};

const restoreWorkspaceAction = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.BUTTON,
    label: '##key##restorePlansLabel###Restore view',
    buttonType: 'primary',
    allocatedWidth : 50,
    onClickActionType: RESTORE_PLANNER_VIEW,
    icon: 'reset-icon',
    btnClassName: 'restoreWorkSpaceBtn',
    size: 'medium',
    toolTipTextKey: 'restorePlanTooltipText'
};

export const commandBarConfig = {
    pageTitleSection,
    menusSection: [
        add, edit, view, plans
    ],
    actionsSection: [
        jobsResRadioGroup, hideRecords, filtersAction, findResourcesAction, restoreWorkspaceAction
    ],
    autoComplete: {
    }
};