import React from 'react';
import { ActionsSection } from '../../src/lib/commandBar/actionsSection';
import { QuickAction } from '../../src/lib/commandBar/quickAction';
import { SwitchViewElement } from '../../src/lib/commandBar/switchViewElement';
import { Switch } from '../../src/lib/commandBar/switch';
import { ActionElement } from '../../src/lib/commandBar/actionElement';
import { ActionWithComponent } from '../../src/lib/commandBar/actionWithComponent';
import { ConnectedBarOptions } from '../../src/connectedComponents/connectedBarOptions';
import { shallow } from 'enzyme';
import { shallowToJson } from 'enzyme-to-json';
import { PLANNER_ACTIONS, SWITCH_LABEL_TYPES } from '../../src/constants/plannerConsts';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES } from '../../src/constants/commandBarConsts';
import { PAGE_VIEW_SETTINGS } from '../../src/constants/globalConsts';

const quickActions = {
    title: 'Display density',
    type: 'QuickAction',
    actionKey: '',
    icon: 'unordered-list',
    size: 'large'
};

describe('<ActionsSection/> tests', () => {
    let wrapper;
    beforeEach(() => {
        wrapper = shallow(
            <ActionsSection>
                <QuickAction {...quickActions}></QuickAction>
                <QuickAction {...quickActions}></QuickAction>
            </ActionsSection>

        );
    });

    describe("Shapshots", () => {
        it("should render the same with given props", () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        });
    });

    describe('Rendering', () => {

        it('should render two <QuickAction/> when two rows passed', () => {
            expect(wrapper.find('QuickAction')).toHaveLength(2);
        });
    });
});

describe('<QuickAction/> tests', () => {
    const mockOnAction = jest.fn();
    const filterActionProps = {
        onAction: mockOnAction,
        config: {
            label: 'Filters',
            type: 'QuickAction',
            onClickActionType: 'toggle-filter-pane',
            icon: 'unordered-list',
            size: 'large',
        },
        actionProps: {
            filterHidden: true
        }
    };

    let wrapper;
    beforeEach(() => {
        wrapper = shallow(
            <QuickAction {...filterActionProps}></QuickAction>
        );
    });

    describe("Shapshots", () => {
        it("should render the same with given props", () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        });
    });

    describe('Rendering', () => {

        it('should render one <Icon/> when props passed', () => {
            expect(wrapper.find('Icon')).toHaveLength(1);
        });
    });

    describe('onAction', () => {
        it('should call  props.onAction upon click', () => {

            wrapper.simulate('click');

            expect(mockOnAction).toHaveBeenCalledTimes(1);

            wrapper.unmount();
        });
    });
});
//Radio group tests
describe('<RadioGroup/> tests', () => {
    const mockOnAction = jest.fn();
    const jobsResRadioGroup = {
        config: {
            type: 'RadioGroup',
            id: PAGE_VIEW_SETTINGS,
            onClickActionType: 'view-settings',
            buttonView: true,
            size: 'large',
            buttonStyle: "solid",
            options: [
                { value: 'resource', icon: 'user', label: 'Show Resources', checked: true },
                { value: 'job', icon: 'tool', label: 'Show Jobs' }
            ]
        },
        onAction: mockOnAction,
        actionProps: {
            masterRecTableName: "job"
        },
        getEntityInfo: (table) => {
            if(table == 'job'){
                return { name: 'job', pluralAlias: 'jobs', sigularAlias: 'job' }
            } else {
                return { name: 'resource', pluralAlias: 'resources', sigularAlias: 'resource' }
                
            }
        },
    };

    let wrapper;
    beforeEach(() => {
        wrapper = shallow(
            <SwitchViewElement {...jobsResRadioGroup}></SwitchViewElement>
        );
    });

    describe("Shapshots", () => {
        it("should render the same with given props", () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        });
    });

    describe('Rendering', () => {
        it('should render two <Icon/> when props passed', () => {
            expect(wrapper.find('Icon')).toHaveLength(2);
        });
    });

});

describe('<Switch /> tests', () => {
    const mockOnAction = jest.fn();
    const switchProps = {
        config:    {
            type: 'MenuItemWithComponent',
            label: '##key##pastLabel###Hide past',
            componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
            id: 'hideHistoricRecordsToggle',
            onClickActionType: PLANNER_ACTIONS.TOGGLE_HIDE_HISTORIC_RECORDS,
            size: 'small',
            className: 'hideRecordsRow',
            options: {
                isEntityDependant: true,
                singularForm: false,
                labelTemplate: 'hideHistoricEntityLabel',
                capitalized: false,
                valuePropName: 'hideHistoricRecords',
                labelType: SWITCH_LABEL_TYPES.DEFAULT
            },
            explanationOptions: {
                isEntityDependant: true,
                singularForm: false,
                capitalized: true,
                explanationTemplate: 'hidePastEntitiesExplanation'
            }
        },
        actionProps: {
            subRecTableName: 'job',
            hideHistoricRecords: true,
        },
        onAction: mockOnAction,
        getEntityInfo: () => {
            return { name: 'job', pluralAlias: 'jobs', sigularAlias: 'job' }
        },
        staticMessages: {
            checkedMessage: 'Yes',
            uncheckedMessage: 'No',
            switchLabel: 'Label',
            explanationLabel: 'Explanation'
        }
    };

    let wrapper;
    beforeEach(() => wrapper = shallow(<Switch {...switchProps} />));

    describe('Snaphots', () => {
        it('should render the same with the given props', () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        });
    });

    describe('Rendering', () => {
        it('should render one <Switch /> when props are passed', () => {
            expect(wrapper.find('Switch')).toHaveLength(1);
        });

        it('should have hideHistoricRecords set to true', () => {
            expect(wrapper.find('Switch').prop('checked')).toBe(true);
        });
    });

    describe('onAction', () => {
        it('should call  props.onAction upon click', () => {
            wrapper.simulate('click');

            expect(mockOnAction).toHaveBeenCalledTimes(1);
            wrapper.unmount();
        });
    });
});

describe('<ActionWithComponent/> tests', () => {
    const validComponentProps = {
        config: {
            label: 'Display density',
            type: 'ActionWithComponent',
            componentType: 'BarOptions',
            icon: 'snippets',
            size: 'large',
            hideFieldPicker: true
        },
        actionProps: {
            components: {
                'BarOptions': ConnectedBarOptions
            }
        },
        staticMessages: {}
    };

    let wrapper;
    beforeEach(() => {
        wrapper = shallow(
            <ActionWithComponent {...validComponentProps}></ActionWithComponent>
        );
    });

    describe("Shapshots", () => {
        it("should render the same with given props", () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        });
    });

    describe('Rendering', () => {
        it('should render one <Dropdown/> when valid props for specific component ConnectedBarOptions passed', () => {
            expect(wrapper.find('Dropdown')).toHaveLength(1);
        });

        it('should return an empty div if an invalid component type is passed', () => {
            const invalidComponentProps = {
                ...validComponentProps,
                config: {
                    ...validComponentProps.config,
                    componentType: 'TestComponent'
                }
            };

            wrapper = shallow(<ActionWithComponent {...invalidComponentProps}></ActionWithComponent>);

            expect(wrapper.find('div')).toHaveLength(1);
        });
    });
});

// Action element tests
describe('<ActionElement/> tests', () => {
    const mockOnAction = jest.fn();
    const jobsResRadioGroup = {
        config: {
            type: 'SwitchView',
            id: PAGE_VIEW_SETTINGS,
            onClickActionType: 'view-settings',
            buttonView: true,
            size: 'large',
            buttonStyle: "solid",
            options: [
                { value: 'resource', icon: 'user', label: 'Show Resources', checked: true },
                { value: 'job', icon: 'tool', label: 'Show Jobs' }
            ]
        },
        onAction: mockOnAction,
        actionProps: {
            masterRecTableName: "job"
        }
    };

    let wrapper;
    beforeEach(() => {
        wrapper = shallow(
            <ActionElement {...jobsResRadioGroup}></ActionElement>
        );
    });

    describe("Shapshots", () => {
        it("should render the same with given props", () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        });
    });
});