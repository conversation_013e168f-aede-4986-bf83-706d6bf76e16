/*eslint-disable*/
import React from 'react';
import RadioGroup from '../../src/lib/radioGroup/radioGroup';
import {shallow} from 'enzyme';
import {shallowToJson} from 'enzyme-to-json';
import { PAGE_VIEW_SETTINGS } from '../../src/constants/globalConsts';

const validProps = {
    id: PAGE_VIEW_SETTINGS,
    defaultValue: 'resource',
    size: 'large',
    buttonStyle:"solid",
    onChange: ()=>{return null},
    radioOptions: [
        {value: 'resource', icon:'user', text: 'Show Resources'},
        {value: 'job', icon:'tool', text: 'Show Jobs'}
    ],
    buttonView: true
}

describe('<RadioGroup/> test', () => {
    let wrapper;
    let instance;
    beforeEach(() => {
        wrapper = shallow(<RadioGroup {...validProps}/>);
        instance = wrapper.instance();
    })

    describe('Rendering', () => {
        it('should render <Radio.Group/> containing two <Radio.Button/>', () => {
            expect(wrapper.find('.radioGroup').find('ForwardRef(RadioButton)')).toHaveLength(2);
        })
    }) 

    describe("Shapshots", () => {
        it("should render the same with given props", () => {
            expect(shallowToJson(wrapper)).toMatchSnapshot();
        })
    })
})