import { ROLE_WORKFLOW_SETTINGS_FIELDS } from '../../src/constants/workflowSettingsConsts';
import {
    getRoleTypeWorkflowsStateSelector,
    getWorkflowSettingsChangedFieldsSelector,
    getWorkflowUiFormDataSelector,
    getWorkflowFieldFormValueSelector,
    getWorkflowFormHasErrorsSelector,
    getStaticMessagesSelector,
    getMultiValueLinkedFieldListItemSelector,
    getWorkflowFieldsToPatchSelector,
    getWorkflowFieldsExplanationsSelector
} from '../../src/selectors/adminSettingSelectors/workflowsSelectors';

const {
    APPROVER_SELECTED_RESOURCES,
    ASSIGNER_SELECTED_RESOURCES,
    REQUESTER_SELECTED_RESOURCES,
    APPROVER_SELECTED_SECURITY_PROFILES,
    ASSIGNER_SELECTED_SECURITY_PROFILES,
    REQUESTER_SELECTED_SECURITY_PROFILES
} = ROLE_WORKFLOW_SETTINGS_FIELDS;

const mockedState = {
    "internationalization": {
        "translation": {
            "workflowsSettings": {
                "approverSelectedResourcesInvalidValue": `Max 20 resources. If more approvers are needed, consider using 'Selected security profiles' instead.`,
                "assignerSelectedResourcesInvalidValue": `Max 20 resources. If more assigners are needed, consider using 'Selected security profiles' instead.`,
                "requesterSelectedResourcesInvalidValue": `Max 20 resources. If more requesters are needed, consider using 'Selected security profiles' instead.`,
                "selectedSecurityProfilesInvalidValue": "Max 20 security profiles"
            },
            "common": {
                "showMorePrefix": "Show",
                "showMoreSuffix": "more",
                "showLessText": "Show less"
            }
        }
    }
}

describe('workflowsSelectors tests', () => {
    describe('getRoleTypeWorkflowsStateSelector tests', () => {
        it('should return empty object if incorrect state is passed', () => {
            const state = { adminSetting: { workflows: {} } };

            expect(getRoleTypeWorkflowsStateSelector(state)).toEqual({});
        });

        it('should return correct state for role type', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            dataLoaded: false,
                            workflowSettings: {},
                            actorsConditions: {},
                            approverTypes: [],
                            assignerTypes: [],
                            config: {},
                            uiFormData: {},
                            changedFields: [],
                            autoComplete: {},
                            tableDatas: {}
                        }
                    }
                }
            };

            expect(getRoleTypeWorkflowsStateSelector(state, 'roleByCriteria')).toEqual({
                dataLoaded: false,
                workflowSettings: {},
                actorsConditions: {},
                approverTypes: [],
                assignerTypes: [],
                config: {},
                uiFormData: {},
                changedFields: [],
                autoComplete: {},
                tableDatas: {}
            });
        });
    });

    describe('getWorkflowSettingsChangedFieldsSelector tests', () => {
        it('returns properly the changed fields from state', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            changedFields: ['field_name_1', 'field_name_2']
                        }
                    }
                }
            };

            expect(getWorkflowSettingsChangedFieldsSelector(state, 'roleByCriteria')).toEqual(['field_name_1', 'field_name_2']);
        });
    });

    describe('getWorkflowUiFormDataSelector tests', () => {
        const state = {
            adminSetting: {
                workflows: {
                    roleByCriteria: {
                        uiFormData: {
                            canApproveDeleteArchived: { fieldName: 'canApproveDeleteArchived', value: true, hasError: false },
                            canSubmitRequestDraft: { fieldName: 'canSubmitRequestDraft', value: true, hasError: false }
                        }
                    }
                }
            }
        };

        expect(getWorkflowUiFormDataSelector(state, 'roleByCriteria')).toEqual({
            canApproveDeleteArchived: { fieldName: 'canApproveDeleteArchived', value: true, hasError: false },
            canSubmitRequestDraft: { fieldName: 'canSubmitRequestDraft', value: true, hasError: false }
        });
    });

    describe('getWorkflowFieldFormValueSelector tests', () => {
        it('should return correct data for a specified field', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            uiFormData: {
                                canApproveDeleteArchived: { fieldName: 'canApproveDeleteArchived', value: true, hasError: false },
                                canSubmitRequestDraft: { fieldName: 'canSubmitRequestDraft', value: true, hasError: false }
                            }
                        }
                    }
                }
            };

            expect(getWorkflowFieldFormValueSelector(state, 'roleByCriteria')('canApproveDeleteArchived')).toEqual(true);
        });
    });

    describe('getWorkflowFormHasErrorsSelector tests', () => {
        it('it should return true if a form data field has errors', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            uiFormData: {
                                canApproveDeleteArchived: { fieldName: 'canApproveDeleteArchived', value: true, hasError: false },
                                canSubmitRequestDraft: { fieldName: 'canSubmitRequestDraft', value: true, hasError: false },
                                canDeleteArchived: { fieldName: 'canDeleteArchived', value: true, hasError: true }
                            }
                        }
                    }
                }
            };

            expect(getWorkflowFormHasErrorsSelector(state, 'roleByCriteria')).toEqual(true);
        });
    });

    describe('getStaticMessagesSelector  tests', () => {
        it('should return correct internationalization state', () => {
            const state = {
                internationalization: {
                    translation: {
                        workflowsSettings: {
                            label1: 'value1',
                            label2: 'value2'
                        }
                    }
                }
            };

            expect(getStaticMessagesSelector(state)).toEqual({
                label1: 'value1',
                label2: 'value2'
            });
        });

        it('should return an object of workflowSettings and common section messages', () => {
            const sut = getStaticMessagesSelector(mockedState);

            expect(sut).toEqual({
                "approverSelectedResourcesInvalidValue": `Max 20 resources. If more approvers are needed, consider using 'Selected security profiles' instead.`,
                "assignerSelectedResourcesInvalidValue": `Max 20 resources. If more assigners are needed, consider using 'Selected security profiles' instead.`,
                "requesterSelectedResourcesInvalidValue": `Max 20 resources. If more requesters are needed, consider using 'Selected security profiles' instead.`,
                "selectedSecurityProfilesInvalidValue": "Max 20 security profiles",
                "showMorePrefix": "Show",
                "showMoreSuffix": "more",
                "showLessText": "Show less"
            });
        });
    });

    describe('getMultiValueLinkedFieldListItemSelector tests', () => {
        it('should return empty null data if no record is found', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            tableDatas: {
                                resource: {
                                    guid: 'resource',
                                    tableName: 'resource',
                                    data: [
                                        { resource_guid: 'guid1', resource_description: 'Phil' },
                                        { resource_guid: 'guid2', resource_description: 'Chetna' }
                                    ],
                                    byId: { 'guid1': 0, 'guid2': 1 }
                                }
                            }
                        }
                    }
                }
            };

            expect(getMultiValueLinkedFieldListItemSelector(state, 'roleByCriteria')('guid3', 'resource')).toEqual({ id: null, value: null });
        });

        it('should return correct record data', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            tableDatas: {
                                resource: {
                                    guid: 'resource',
                                    tableName: 'resource',
                                    data: [
                                        { resource_guid: 'guid1', resource_description: 'Phil' },
                                        { resource_guid: 'guid2', resource_description: 'Chetna' }
                                    ],
                                    byId: { 'guid1': 0, 'guid2': 1 }
                                }
                            }
                        }
                    }
                }
            };

            expect(getMultiValueLinkedFieldListItemSelector(state, 'roleByCriteria')('guid2', 'resource')).toEqual({ id: 'guid2', value: 'Chetna' });
        });
    });

    describe('getWorkflowFieldsToPatchSelector tests', () => {
        it('should return correct patch fields', () => {
            const state = {
                adminSetting: {
                    workflows: {
                        roleByCriteria: {
                            uiFormData: {
                                canApproveDeleteArchived: { fieldName: 'canApproveDeleteArchived', value: true, hasError: false },
                                canSubmitRequestDraft: { fieldName: 'canSubmitRequestDraft', value: true, hasError: false }
                            },
                            changedFields: ['canSubmitRequestDraft', 'canDeleteRejected']
                        }
                    }
                }
            };

            expect(getWorkflowFieldsToPatchSelector(state, 'roleByCriteria')).toEqual(['canSubmitRequestDraft']);
        });
    });

    describe('getWorkflowFieldsExplanationsSelector tests', () => {
        it('should return message for selectedResources workflow field', () => {
            const mockedFieldName = APPROVER_SELECTED_RESOURCES;

            const sut = getWorkflowFieldsExplanationsSelector(mockedState);

            const expectedResult = `Max 20 resources. If more approvers are needed, consider using 'Selected security profiles' instead.`;

            expect(sut[mockedFieldName]).toEqual(expectedResult);
        });

        it('should return message for assignerSelectedResources workflow field', () => {
            const mockedFieldName = ASSIGNER_SELECTED_RESOURCES;

            const sut = getWorkflowFieldsExplanationsSelector(mockedState);

            const expectedResult = `Max 20 resources. If more assigners are needed, consider using 'Selected security profiles' instead.`

            expect(sut[mockedFieldName]).toEqual(expectedResult);
        });

        it('should return message for requesterSelectedResources workflow field', () => {
            const mockedFieldName = REQUESTER_SELECTED_RESOURCES;

            const sut = getWorkflowFieldsExplanationsSelector(mockedState);

            const expectedResult = `Max 20 resources. If more requesters are needed, consider using 'Selected security profiles' instead.`;

            expect(sut[mockedFieldName]).toEqual(expectedResult);
        });

        it('should return message for selectedSecurityProfiles workflow field', () => {
            const mockedFieldName = APPROVER_SELECTED_SECURITY_PROFILES;

            const sut = getWorkflowFieldsExplanationsSelector(mockedState);

            const expectedResult = "Max 20 security profiles";

            expect(sut[mockedFieldName]).toEqual(expectedResult);
        });

        it('should return message for assignerSelectedSecurityProfiles workflow field', () => {
            const mockedFieldName = ASSIGNER_SELECTED_SECURITY_PROFILES;

            const sut = getWorkflowFieldsExplanationsSelector(mockedState);

            const expectedResult = "Max 20 security profiles";

            expect(sut[mockedFieldName]).toEqual(expectedResult);
        });

        it('should return message for requesterSelectedSecurityProfiles workflow field', () => {
            const mockedFieldName = REQUESTER_SELECTED_SECURITY_PROFILES;

            const sut = getWorkflowFieldsExplanationsSelector(mockedState);

            const expectedResult = "Max 20 security profiles";

            expect(sut[mockedFieldName]).toEqual(expectedResult);
        });
    });
});