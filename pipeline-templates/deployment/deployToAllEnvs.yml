stages:
  - stage: BuildAndPublish
    displayName: Build and Publish
    jobs:
      - job: build
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/tasks.yml

  ################################# Build correct artifact
  - stage: PrepareSandbox
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-Sbx
    displayName: Create SBX Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self
          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "sbx"

  - stage: PrepareDev
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-Dev
    displayName: Create Dev Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self
          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "dev"

  - stage: PrepareQA2
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-QA2
    displayName: Create QA2 Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "qa2"

  - stage: PrepareQA
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
        - group: Front End Deployment-QA
    displayName: Create QA Release
    jobs:
        - job: Modify
          workspace:
            clean: all
          steps:
            - checkout: self

            - template: /pipeline-templates/continuous-integration/react/publish.yml
              parameters:
                env: "qa"

  - stage: PrepareLDT
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-LDT
    displayName: Create LDT Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "ldt"


  - stage: PrepareUAT
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-UAT
    displayName: Create UAT Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "uat"

  - stage: PrepareDemo
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-Demo
    displayName: Create Demo Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self
          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "demo"

  - stage: PrepareProdAPAC
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-PrdAUE
    displayName: Create APAC Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "PrdAUE"

  - stage: PrepareProdUK
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-ProdUK
    displayName: Create Prod UK Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "ProdUK"

  - stage: PrepareProdUS
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-ProdUS
    displayName: Create Prod US Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "ProdUS"

  - stage: PrepareProdEU
    #condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-ProdEMEA
    displayName: Create Prod EU Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "ProdEMEA"

  - stage: PrepareProdDRAPAC
    condition: eq(1,2)
    dependsOn: BuildAndPublish
    variables:
      - group: Front End Deployment-PrdAUE
    displayName: Create APAC Release
    jobs:
      - job: Modify
        workspace:
          clean: all
        steps:
          - checkout: self

          - template: /pipeline-templates/continuous-integration/react/publish.yml
            parameters:
              env: "PrdAUS"

  ##### Deploy

  - stage: DeploySandbox
    dependsOn: PrepareSandbox
    displayName: Deploy to Sandbox
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "sbx"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "Sandbox"
          azureSubscription: "ADVT Sandbox"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: $(env)
          isProd: false

  - stage: DeployDev
    dependsOn: PrepareDev
    displayName: Deploy to Dev
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "dev"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "Develop"
          azureSubscription: "ADVT Non Prod"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: $(env)
          isProd: false

  - stage: DeployQA
    dependsOn: PrepareQA
    displayName: Deploy to QA
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "qa"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "QA"
          azureSubscription: "ADVT Non Prod"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: $(env)
          isProd: false

  - stage: DeployQA2
    dependsOn: PrepareQA2
    displayName: Deploy to QA2
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "qa2"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "QA2"
          azureSubscription: "ADVT Non Prod"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: $(env)
          isProd: false

  - stage: DeployLDT
    dependsOn: PrepareLDT
    displayName: Deploy to LDT
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "ldt"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "LDT"
          azureSubscription: "ADVT Non Prod"
          resourceGroup: "rg-$(project)$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: $(env)
          isProd: false

  - stage: DeployUAT
    dependsOn: PrepareUAT
    displayName: Deploy to UAT
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "uat"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "UAT"
          azureSubscription: "ADVT Non Prod"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: $(env)
          isProd: false

  - stage: DeployDemo
    dependsOn: PrepareDemo
    displayName: Deploy to Demo
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "dem"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "Demo"
          azureSubscription: "sub-Retain-Demo"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: "dem"
          isProd: true


  - stage: DeployProdAPAC
    dependsOn: PrepareProdAPAC
    displayName: Deploy to APAC
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "prd"
      - name: location
        value: "aue"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "ProdAU"
          azureSubscription: "sub-Retain-Prod-APAC"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: "PrdAUE"
          isProd: true

  - stage: DeployProdUK
    dependsOn: PrepareProdUK
    displayName: Deploy to ProdUK
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "prd"
      - name: location
        value: "uks"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "ProdUK"
          azureSubscription: "sub-Retain-Prod-UK"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: "ProdUK"
          isProd: true


  - stage: DeployProdUS
    dependsOn: PrepareProdUS
    displayName: Deploy to ProdUS
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "prd"
      - name: location
        value: "use"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "ProdUS"
          azureSubscription: "sub-Retain-Prod-US"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: "ProdUS"
          isProd: true

  - stage: DeployProdEU
    dependsOn: PrepareProdEU
    displayName: Deploy to ProdEU
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "prd"
      - name: location
        value: "euw"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "ProdEU"
          azureSubscription: "sub-Retain-Prod-EU"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: "ProdEMEA"
          isProd: true

  - stage: DeployProdDRAPAC
    dependsOn: PrepareProdDRAPAC
    displayName: Deploy to APAC
    variables:
      - name: project
        value: "phoenix"
      - name: env
        value: "prd"
      - name: location
        value: "aue"
    jobs:
      - template: /pipeline-templates/deployment/deployToEnv.yml
        parameters:
          deployment: "ProdAU"
          azureSubscription: "sub-Retain-Prod-APAC"
          resourceGroup: "rg-$(project)-$(env)-$(location)-001"
          slot: "deploy"
          app: "app-$(project)-$(env)-$(location)-frontend-001"
          package: $(Build.BuildId)
          env: "PrdAUS"
          isProd: true