/* eslint-disable */
const path = require('path');
const fs = require("fs");
const HtmlWebPackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const CopyPlugin = require('copy-webpack-plugin');
const lessToJs = require('less-vars-to-js');

const themeVariables = lessToJs(fs.readFileSync(path.join('styles', './ant-theme-vars.less'), 'utf8'));

const index = new HtmlWebPackPlugin({
    template: "./src/index.html",
    filename: "./index.html"
});

const silent_renew = new HtmlWebPackPlugin({
    template: "./src/silent_renew/silent_renew.html",
    filename: "./silent_renew.html"
});

const copyFilesPlugin = new CopyPlugin({
    patterns: [
        { from: './web.config', to: './web.config' },
        { from: './src/favicon.ico', to: './favicon.ico' },
        { from: './sw.js', to: './sw.js' },
    ],
});

const definePlugin = new webpack.DefinePlugin({});

const optimization = {
    splitChunks: {
        cacheGroups: {
            vendors: {
                test: /[\\/]node_modules[\\/]/,
                chunks: 'all',
                minChunks: 1,
                maxInitialRequests: 5,
                minSize: 0,
                reuseExistingChunk: true,
                priority: 5
            },
            microsoft: {
                test: /[\\/]node_modules[\\/](@microsoft)[\\/]/,
                name: 'microsoft',
                chunks: 'all',
                priority: 10,
            },
            react: {
                test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
                name: 'react',
                chunks: 'all',
                priority: 15,
            },
            antd: {
                test: /[\\/]node_modules[\\/](antd|@ant-design)[\\/]/,
                name: 'antd',
                chunks: 'all',
                priority: 20,
            },
            redux: {
                test: /[\\/]src[\\/](reducers|epics|state|actions|selectors)[\\/]/,
                name: 'redux',
                chunks: 'all',
                priority: 25
            },
            commons: {
                name: 'commons',
                chunks: 'initial',
                minChunks: 2
            },
            styles: {
                name: 'styles',
                test: /\.(css|less)$/,
                chunks: 'all',
                enforce: true
            }
        }
    }
};

const config = {
    entry: [
        './src/index.js',
        './src/silent_renew/index.js'
    ],
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: '[name].[contenthash].js',
        chunkFilename: '[name].[chunkhash].chunk.js'
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: 'babel-loader'
            },
            {
                test: /\.less$/i,
                use: [
                    'style-loader',
                    {
                        loader: 'css-loader',
                        options: {
                            modules: {
                                mode: "global",
                            },
                            importLoaders: 1
                        },
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true,
                                modifyVars: themeVariables
                            },
                        }
                    }
                ]
            },
            {
                test: /\.css$/i,
                use: ['style-loader', 
                    {
                        loader: 'css-loader',
                        options: {
                            modules: {
                                mode: "global",
                            },
                        },
                    }
                ]
            },
            {
                test: /\.(eot|svg|ttf|woff|woff2|png)$/i,
                type: 'asset/resource',
            }
        ]
    },
    resolve: {
        extensions: ['.js', '.json', '.less', '.css']
    },
    plugins: [
        definePlugin,
        {
            apply: (compiler) => {
                compiler.hooks.done.tap('WriteStatsPlugin', (stats) => {
                    const wpPath = path.join(__dirname, "dist");
                    if (!fs.existsSync(wpPath)) {
                        fs.mkdirSync(wpPath);
                    }
                    fs.writeFileSync(
                        path.join(wpPath, "stats.json"),
                        JSON.stringify(stats.toJson())
                    );
                });
            }
        },
        index,
        silent_renew,
        copyFilesPlugin
    ],
    optimization,
    devServer: {
        static: {
            directory: path.join(__dirname, 'dist'),
        },
        headers: { "Access-Control-Allow-Origin": "*" },
        historyApiFallback: true,
        port: 8080,
        open: true
    }
};

module.exports = config;